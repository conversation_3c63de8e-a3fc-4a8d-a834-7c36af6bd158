.cr-trustbadgef, .cr-trustbadgea {
	max-width: 100%;
	overflow: hidden;
}
.cr-trustbadgef .cr-trustbadge-border {border: 1px solid #A3D8CD;}
.cr-trustbadgef .cr-trustbadge-border.badge_color_dark {border: 1px solid #8B8B8B;}

#cr_floatingtrustbadge {position: fixed; box-sizing: border-box; right: 0px; bottom: 0px; width: 135px; border-radius: 15px; display: block; overflow: hidden; margin: 30px; filter: drop-shadow(0px 4px 54px rgba(0, 0, 0, 0.25)); z-index: 99999; cursor: pointer;}
#cr_floatingtrustbadge.cr-floatingbadge-big {position: fixed; box-sizing: border-box; right: 0px; bottom: 0px; width: 256px; border-radius: 15px; display: block; overflow: hidden; margin: 0 30px 30px 0; filter: drop-shadow(0px 4px 54px rgba(0, 0, 0, 0.25)); z-index: 99999; cursor: auto;}
#cr_floatingtrustbadge {border: 1px solid;}
#cr_floatingtrustbadge .cr-floatingbadge-background {position: absolute; top: 0; left: 0; height: 100%; width: 100%;}
#cr_floatingtrustbadge .cr-floatingbadge-background div {width: 100%;}
#cr_floatingtrustbadge .cr-floatingbadge-background .cr-floatingbadge-background-top {height: 115px;}
#cr_floatingtrustbadge.cr-floatingbadge-big .cr-floatingbadge-background .cr-floatingbadge-background-top {height: 155px;}
#cr_floatingtrustbadge .cr-floatingbadge-background .cr-floatingbadge-background-middle {height: 31px;}
#cr_floatingtrustbadge.cr-floatingbadge-big .cr-floatingbadge-background .cr-floatingbadge-background-middle {height: 179px;}
#cr_floatingtrustbadge .cr-floatingbadge-background .cr-floatingbadge-background-bottom {height: 27px;}
#cr_floatingtrustbadge.cr-floatingbadge-big .cr-floatingbadge-background .cr-floatingbadge-background-bottom {height: 43px; border-top: 1px solid; border-left: 0px; border-right: 0px; border-bottom: 0px;}
#cr_floatingtrustbadge .cr-floatingbadge-top {position: relative; height: 92px; display: block;}
#cr_floatingtrustbadge.cr-floatingbadge-big .cr-floatingbadge-top {position: relative; height: 124px; display: block;}
#cr_floatingtrustbadge .cr-floatingbadge-top svg {position: absolute; left: 50%; top: 14px; margin-left: -35px;}
#cr_floatingtrustbadge.cr-floatingbadge-big .cr-floatingbadge-top svg {position: absolute; width: 84px; height: 79px; left: 50%; top: 28px; margin-left: -42px;}
#cr_floatingtrustbadge img {margin: 0; width: 100%; position: relative; border: 0; display: block;}
#cr_floatingtrustbadge .cr-floatingbadge-top .cr-floatingbadge-close {position: absolute; top: 5px; right: 5px; width: 20px; height: 20px; cursor: pointer;}
#cr_floatingtrustbadge .cr-floatingbadge-top .cr-floatingbadge-close svg {position: absolute; width: 16px; height: 16px; margin: 2px; top: 0; left: 0; display: block;}
#cr_floatingtrustbadge .cr-floatingbadge-top .cr-floatingbadge-close svg {fill: #F8942D;}
#cr_floatingtrustbadge.cr-floatingbadge-big .cr-floatingbadge-top .cr-floatingbadge-close svg {fill: #FFFFFF;}
#cr_floatingtrustbadge .cr-floatingbadge-top svg {width: 70px; height:65px;}

.cr-badge {
	display: inline-block;
	padding: 40px;
	font-family: 'Open Sans', sans-serif;
	font-size: 0;
	color: #1F1F1F;
	line-height: normal;
	position: relative;
	text-align: left;
	text-decoration:none;
	box-sizing: border-box;
	transform-origin: left;
	visibility: hidden;
}

.cr-badge .badge__nowrap {
	white-space: nowrap;
}

.cr-badge .badge__nowrap-inline {
	display: inline-block;
	white-space: nowrap;
}

.cr-badge .badge__store {
		font-size: 24px;
		font-weight: bold;
		margin-bottom: 12px;
		white-space: nowrap;
}

.cr-badge .badge__stars {
		display: inline-block;
		margin-bottom: 14px;
		margin-right: 24px;
}

.cr-badge .badge__star {
		display: inline-block;
		position: relative;
		width: 36px;
		height: 36px;
		margin-right: 3px;
		background-size: cover;
}

.cr-badge .badge-vs .badge__star {
	display: block;
}

.cr-badge .badge__star-icon {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-size: cover;
		background-repeat: no-repeat;
}

.cr-badge .badge__star-icon_type_empty {
		background-image: url('../img/small-star-empty-light.png');
}

.cr-badge .badge__star-icon_type_fill {
		background-image: url('../img/small-star-full.png');
}

.cr-badge .verified {
		display: inline-block;
		vertical-align: middle;
		background: rgba(23, 157, 130, 0.1);
		height: 36px;
		border-radius: 19px;
		padding: 0 16px;
}

.cr-badge.badge_size_small .verified {
	vertical-align: top;
}

.cr-badge .verified__logo {
		display: inline-block;
		width: 32px;
		height: 32px;
		background-image: url('../img/logo.svg');
		background-size: cover;
		position: relative;
		top: 2px;
		left: -6px;
		margin-right: 2px;
}

.cr-badge .verified__text {
		display: inline-block;
		line-height: 36px;
		vertical-align: top;
		font-size: 18px;
		font-weight: 600;
}

.cr-badge .rating {
		font-size: 17px;
		white-space: nowrap;
}

.cr-badge.badge_size_wide .rating {
		font-size: 16px;
}

.cr-badge rating__store {
	font-weight: 600;
}

.cr-badge .rating__product b {
	font-weight: 600;
}

.cr-badge.badge_color_dark {
	color: #fff;
}

.cr-badge.badge_color_dark .badge__star-icon_type_empty {
		background-image: url('../img/small-star-empty-dark.png');
}

.cr-badge.badge_color_dark .verified {
		background: rgba(255, 255, 255, 0.25);
}

.cr-badge.badge_size_wide {
		padding: 20px;
		color: #6c6c6c;
}

.cr-badge.badge_size_wide .badge__store {
		font-size: 28px;
		margin-bottom: 24px;
}

.cr-badge .badge__info {
	position: relative;
}

.cr-badge .badge__logo {
		position: absolute;
		top: 0;
		left: 0;
		width: 60px;
		height: 60px;
		background-image: url('../img/logo.svg');
		background-size: contain;
		background-repeat: no-repeat;
}

.cr-badge .badge__reviews {
		font-size: 16px;
		padding-left: 70px;
		padding-top: 15px;
}

.cr-badge.badge_size_wide .verified {
		font-size: 16px;
		padding-left: 70px;
		height: 20px;
		background: transparent;
}

.cr-badge.badge_size_wide .badge__stars {
		margin-bottom: 0;
		margin-right: 10px;
		margin-left: 20px;
		margin-top: 0;
		vertical-align: middle;
}

.cr-badge.badge_size_wide .badge__star {
		width: 20px;
		height: 20px;
}

.cr-badge.badge_size_wide .badge__star-icon_type_empty {
		background-image: url('../img/wide-star-empty-light.png');
}

.cr-badge.badge_size_wide .badge__star-icon_type_fill {
		background-image: url('../img/wide-star-full-light.png');
}

.cr-badge.badge_color_dark.badge_size_wide .badge__star-icon_type_fill {
		background-image: url('../img/wide-star-full-dark.png');
}

.cr-badge.badge_color_dark.badge_size_wide .badge__star-icon_type_empty {
		background-image: url('../img/wide-star-empty-dark.png');
}

.cr-badge.badge_size_wide .rating {
		display: inline-block;
		padding-top: 0;
		vertical-align: middle;
		margin-top: 0;
		height: 18px;
}

.cr-badge.badge_size_wide.badge_color_dark {
		color: #fff;
}

.cr-badge.badge_target_mobile {
		position: relative;
		padding: 0;
		margin: 20px;
}

.cr-badge.badge_target_mobile .badge__logo {
		top: 41px;
		left: 4px;
		width: 32px;
		height: 32px;
}

.cr-badge.badge_target_mobile .badge__store {
		font-size: 24px;
		margin-bottom: 0;
		padding-left: 2px;
}

.cr-badge.badge_target_mobile .badge__reviews,
.cr-badge.badge_target_mobile .verified {
		padding-left: 48px;
		display: block;
}

.cr-badge.badge_target_mobile .badge__stars {
		padding-left: 2px;
		margin-left: 0;
		margin-top: 0;
}

.cr-badge.badge_target_mobile .rating {
		font-size: 15px;
		margin-top: 2px;
}

.cr-badge.badge_size_wide .rating b {
		display: inline-block;
		margin-left: 8px;
}

.cr-badge.badge_size_small {
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
}

.cr-badge .badge__link, .cr-badge__wrap .badge__link {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 1;
}

/* internal container wrapper */
.cr-badge__wrap {
	display: inline-block;
	position: relative;
}

.cr-badge.badge-vs {
		position: relative;
		color: #4d4d4d;
		padding: 20px;
		display: inline-block;
		flex-flow: row nowrap;
		text-align: left;
		font-family: sans-serif;
		border-radius: 15px;
}

.cr-badge.badge-vs .cr-badge-vs-flex {
	display: flex;
	flex-direction: row;
}

.cr-badge.badge-vs .badge__store {
		font-size: 25px;
		font-weight: 500;
		margin-bottom: 5px;
		line-height: 1.2;
		max-width: 500px;
		text-overflow: ellipsis;
		overflow: hidden;
}

.cr-badge.badge-vs .badge__logo {
		background-image: url('../img/logo-vs.svg');
		height: 80px;
		width: 80px;
		position: relative;
		display: inline-block;
		margin-right: 20px;
		flex: 0 0 auto;
}

.cr-badge.badge-vs .badge__details {
		display: flex;
		flex-flow: row nowrap;
		align-items: center;
}

.cr-badge.badge-vs .badge__rating-container {
		display: flex;
		flex-flow: column nowrap;
		justify-content: space-evenly;
		font-family: sans-serif;
		font-weight: normal;
}

.cr-badge.badge-vs .badge__rating-line {
		display: flex;
		flex-flow: row nowrap;
		justify-content: space-between;
}

.cr-badge.badge-vs .badge__stars {
	margin: 0;
	padding: 0;
	display: flex;
	align-items: center;
}

.cr-badge.badge-vs .badge__star {
		width: 15px;
		height: 15px;
		border-spacing: 0;
		display: flex;
}

.cr-badge.badge-vs .badge__star-icon--empty {
		background-image: url('../img/star-stroke.svg');
		background-position: left;
		border-spacing: 0;
}

.cr-badge.badge-vs .badge__star-icon--fill {
		background-image: url('../img/star-fill.svg');
		background-position: left;
		border-spacing: 0;

}

.cr-badge.badge-vs .badge__rating.rating {
		font-size: 14px;
		line-height: 1.5;
		justify-content: space-between;
		display: flex;
		flex: 1 1 0;
		margin: -1px 0 0;
		padding: 0;
		height: 20px;
}

.cr-badge.badge-vs .badge__rating.rating span:last-child {
	padding-left: 25px;
	font-weight: bold;
}

.cr-badge .badge__rating + .badge__stars {
	margin-left: 15px;
}

.cr-badge.badge-vs .badge__reviews {
	border-radius: 30px;
	background-color: #F2F2F2;
	display: block;
	font-size: 18px;
	line-height: 1;
	padding: 6px 25px 5px;
	margin-left: 15px;
	border: 1px solid #DFDFDF99;
}

.cr-badge.badge-vs.badge_color_dark {
		color: #e5e5e5;
		border-color: transparent;
}

.cr-badge.badge-vs.badge_color_dark .badge__reviews {
	background-color: #24242499;
	border-color: transparent;
}

.badge_size_compact.cr-badge,
.badge--wide-mobile.cr-badge {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	font-family: sans-serif;
}

.badge_size_compact.cr-badge,
.badge--wide-mobile.cr-badge {
	display: block;
	flex-direction: column;
	visibility: visible;
}

.badge_size_compact.cr-badge .badge__verified,
.badge--wide-mobile.cr-badge .badge__verified {
	font-size: 10px;
	font-weight: 700;
	color: #4c4c4c;
	text-align: center;
	margin-bottom: 5px;
	margin-top: 5px;
}

.badge--wide-mobile.cr-badge .badge__verified {
	font-size: 16px;
	color: #fff;
	margin-bottom: 13px;
}

.badge--wide-mobile.cr-badge .badge__store {
	min-height: 50px;
	margin-bottom: 15px;
	color: #4d4d4d;
	font-size: 28px;
	font-weight: 700;
	line-height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 10px;
}

.badge--wide-mobile.cr-badge .badge__store span {
	min-width: 234px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	text-align: center;
}

.cr-badge.badge_size_compact .badge__middle,
.cr-badge.badge--wide-mobile .badge__middle {
	padding: 10px 0 9px;
}

.cr-badge.badge_size_compact .badge__stars,
.cr-badge.badge--wide-mobile .badge__stars {
	display: flex;
	justify-content: center;
	width: 100%;
	margin: 0;
}

.cr-badge.badge_size_compact .badge__star,
.cr-badge.badge--wide-mobile .badge__star {
	position: relative;
	width: 14px;
	height: 13px;
	margin: 0 2px;
	color: #fff;
}

.cr-badge.badge_size_compact .badge__star svg,
.cr-badge.badge--wide-mobile .badge__star svg {
	display: block;
}

.cr-badge.badge_size_compact .badge__star-fill-container,
.cr-badge.badge--wide-mobile .badge__star-fill-container {
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
	width: 14px;
	height: 13px;
}

.rtl .cr-badge.badge_size_compact .badge__star-fill-container,
.rtl .cr-badge.badge--wide-mobile .badge__star-fill-container {
	left: auto;
	right: 0;
}

.cr-badge.badge_size_compact .badge__star-fill-container svg,
.cr-badge.badge--wide-mobile .badge__star-fill-container svg {
	height: 13px;
}

.cr-badge.badge_size_compact .badge__rating,
.cr-badge.badge--wide-mobile .badge__rating {
	font-size: 12px;
	text-align: center;
	color: #4c4c4c;
}

.cr-badge.badge_size_compact .badge__rating b,
.cr-badge.badge--wide-mobile .badge__rating b  {
	margin-left: 8px;
}

.cr-badge.badge_size_compact .badge__reviews,
.cr-badge.badge--wide-mobile .badge__reviews {
	padding: 8px 0 6px;
	font-size: 10px;
	text-align: center;
	border-radius: 0 0 15px 15px;
	color: #4d4d4d;
}

.cr-badge.badge--wide-mobile .badge__middle {
	padding-top: 0px;
	padding-bottom: 20px;
	background-color: transparent;
}

.cr-badge.badge--wide-mobile .badge__stars {
	margin-bottom: 5px;
}

.cr-badge.badge--wide-mobile .badge__star {
	color: #f8942d;
}

.cr-badge.badge--wide-mobile .badge__reviews {
	font-size: 13px;
	border-top: 1px solid transparent;
	min-height: 37px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0;
}

.cr-badge.badge--wide-mobile .mb21 {
	margin-bottom: 21px;
}

.cr-badge.badge_size_compact.badge_color_dark .badge__middle,
.cr-badge.badge--wide-mobile.badge_color_dark .badge__middle {
	border: none;
	box-sizing: border-box;
}

.cr-badge.badge_size_compact.badge_color_dark .badge__star,
.cr-badge.badge--wide-mobile.badge_color_dark .badge__star {
	color: #f8942d;
}

.cr-badge.badge_size_compact.badge_color_dark .badge__reviews,
.cr-badge.badge--wide-mobile.badge_color_dark .badge__reviews {
	border: none;
}

.cr-badge.badge_size_compact.badge_color_dark .badge__verified,
.cr-badge.badge--wide-mobile.badge_color_dark .badge__verified,
.cr-badge.badge_size_compact.badge_color_dark .badge__reviews,
.cr-badge.badge--wide-mobile.badge_color_dark .badge__reviews,
.cr-badge.badge_size_compact.badge_color_dark .badge__rating,
.cr-badge.badge--wide-mobile.badge_color_dark .badge__rating,
.cr-badge.badge_size_compact.badge_color_dark .badge__store,
.cr-badge.badge--wide-mobile.badge_color_dark .badge__store {
	color: #e5e5e5;
}

.cr-placeholder-tb {
	background-color: #FAFAFA;
	width: 100%;
	padding: 20px;
}

.cr-placeholder-tb .cr-placeholder-tb-fr {
	width: 50%;
	height: 40px;
	margin: 0 0 20px 0;
	background-color: #DCDEE2;
	animation: cr-placeholder-load 3s infinite;
}
.cr-placeholder-tb .cr-placeholder-tb-sr {
	width: 100%;
	height: 60px;
	background-color: #DCDEE2;
	animation: cr-placeholder-load 3s infinite;
}

@media all and (max-width: 600px) {
	#cr_floatingtrustbadge, #cr_floatingtrustbadge.cr-floatingbadge-big {display: none;}
}

@keyframes cr-placeholder-load {
	0% {
		background-color: #DCDEE2;
	}
	50% {
		background-color: #ededed;
	}
	100% {
		background-color: #DCDEE2;
	}
}

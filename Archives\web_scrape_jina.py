# https://requests.readthedocs.io/en/latest/
import requests


class SEOConfig:

    # Configuration Jina AI
    JINA_API_KEY: str = "jina_c48087b47e8b4d7287845618e94849f6ruE6PCDIuumEaoybR1p8DGe2bRxq"
    JINA_TIMEOUT: int = 30
    JINA_MAX_RETRIES: int = 3


def web_scrape_jina(url: str) -> str:
    """
    Récupère le contenu textuel d'une page web en utilisant l'API Jina AI.

    Cette fonction prend une URL en entrée, la soumet à l'API Jina AI (https://jina.ai/api-dashboard)
    pour en extraire le contenu principal sous forme de texte brut (Markdown).
    Elle gère les erreurs de requête et retourne un message d'erreur en cas d'échec.

    Args:
        url (str): L'URL de la page web à scraper.

    Returns:
        str: Le contenu textuel de la page en cas de succès, ou un message d'erreur si la requête échoue.
    """
    jina_url = f"https://r.jina.ai/{url}"
    headers = {
        "X-No-Cache": "false",
        "X-With-Generated-Alt": "true",
        "Authorization": f"Bearer {SEOConfig.JINA_API_KEY}",
    }


    for attempt in range(SEOConfig.JINA_MAX_RETRIES):
        try:
            response = requests.get(
                url=jina_url, headers=headers, timeout=SEOConfig.JINA_TIMEOUT
            )
            response.raise_for_status()

            content = response.text
            return content

        except requests.Timeout:
            if attempt == SEOConfig.JINA_MAX_RETRIES - 1:
                return f"❌ Erreur: Timeout après {SEOConfig.JINA_MAX_RETRIES} tentatives pour {url}"

        except requests.HTTPError as e:
            return f"❌ Erreur HTTP {e.response.status_code} lors du scraping de {url}"

        except requests.RequestException as e:
            if attempt == SEOConfig.JINA_MAX_RETRIES - 1:
                return f"❌ Erreur de scraping: {str(e)}"

    return f"❌ Échec du scraping après {SEOConfig.JINA_MAX_RETRIES} tentatives"


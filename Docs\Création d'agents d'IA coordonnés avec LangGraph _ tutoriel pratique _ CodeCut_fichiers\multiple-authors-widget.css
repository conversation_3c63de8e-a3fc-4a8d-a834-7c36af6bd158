.pp-multiple-authors-boxes-wrapper .square-dot {
    display: inline-block;
}

.pp-multiple-authors-boxes-wrapper .square-dot::before {
    content: '\25A0';
}

.pp-multiple-authors-boxes-ul {
    list-style: none;
}

.pp-multiple-authors-layout-simple_list ul,
.pp-multiple-authors-layout-boxed ul {
    padding-left: 0;
    margin-left: 0;
}

.pp-multiple-authors-layout-simple_list ul li {
    list-style: none;
    position: relative;
    min-height: 35px;
    line-height: 35px;
    border-bottom: 1px solid #999;
    border-top: 1px solid #999;
    border-bottom: 1px solid #999;
    border-top: 1px solid #999;
    padding: 0.5em 0;
    margin: -1px 0 0 0;
}

.pp-multiple-authors-layout-simple_list .avatar,
.pp-multiple-authors-layout-simple_list .photo {
    float: left;
    margin-right: 10px;
    width: 35px;
    height: 35px;
    display: block;
    min-height: 35px;
}

.pp-multiple-authors-layout-boxed ul li,
.pp-multiple-authors-layout-centered ul li {
    list-style: none;
    position: relative;
    min-height: 120px;
    border: 1px solid #999;
    padding: 1.5em;
    margin-bottom: 1em;
    margin-left: 0;
}

.pp-multiple-authors-layout-boxed .avatar,
.pp-multiple-authors-layout-boxed .photo,
.pp-multiple-authors-layout-centered .avatar,
.pp-multiple-authors-layout-centered .photo {
    width: 80px;
    height: auto;
    border-radius: 50%;
}

.pp-multiple-authors-layout-boxed .multiple-authors-description,
.pp-multiple-authors-layout-centered .multiple-authors-description {
    margin: 10px 0 20px;
}

.pp-multiple-authors-layout-boxed .multiple-authors-name,
.pp-multiple-authors-layout-centered .multiple-authors-name {
    margin-bottom: 10px;
}

.pp-multiple-authors-layout-boxed .multiple-authors-name a,
.pp-multiple-authors-layout-centered .multiple-authors-name a,
.pp-multiple-authors-layout-boxed .name-author-category,
.pp-multiple-authors-layout-centered .name-author-category {
    margin-bottom: 10px;
    font-size: 1.2em;
}

.pp-multiple-authors-layout-boxed .multiple-authors-links,
.pp-multiple-authors-layout-centered .multiple-authors-links {
    margin-bottom: 0;
}

.pp-multiple-authors-layout-boxed .multiple-authors-links a,
.pp-multiple-authors-layout-centered .multiple-authors-links a,
a.ppma-author-field-meta {
    border-radius: 20px;
    min-width: 30px;
    line-height: 30px;
    text-decoration: none;
    display: inline-block;
    margin-right: 3px;
}

a.ppma-author-field-meta i {
    display: inline-block;
    line-height: 1;
    font-style: normal;
    speak: never;
    text-decoration: inherit;
    text-transform: none;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: 20px;
    height: 20px;
    font-size: 20px;
    vertical-align: top;
    text-align: center;
    transition: color .1s ease-in;
}

.pp-multiple-authors-layout-boxed .multiple-authors-links a span,
.pp-multiple-authors-layout-centered .multiple-authors-links a span,
a.ppma-author-field-meta span,
a.ppma-author-field-meta i {
    min-width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
}

.pp-multiple-authors-layout-boxed .multiple-authors-links a span:not(.dashicons),
.pp-multiple-authors-layout-centered .multiple-authors-links a span:not(.dashicons) {
    padding: 0 14px;
}

.ppma-author-field-meta {
    margin-right: 1px;
    overflow-wrap: anywhere;
}

.ppma-author-field-type-wysiwyg {
    white-space: pre-line;
}

.pp-author-boxes-recent-posts {
    margin-top: 10px;
}

.pp-author-boxes-recent-posts .pp-author-boxes-recent-posts-title {
    width: 100%;
    margin-bottom: 20px;
    padding-bottom: 2px;
    border-bottom: 1px dotted #333;
    font-size: 18px;
    text-align: left;
}

.pp-author-boxes-recent-posts .pp-author-boxes-recent-posts-item {
    font-size: 14px;
}

.pp-author-boxes-recent-posts .pp-author-boxes-recent-posts-item span.dashicons {
    margin: 0 0.2rem 0 0;
}

.pp-author-boxes-recent-posts-empty {
    font-size: 12px;
}

.pp-author-boxes-profile-field-item {
    margin-left: 4px;
    margin-right: 4px;
}

.pp-multiple-authors-layout-boxed .multiple-authors-links a:nth-child(1) {
    margin-right: 2px;
}

.pp-multiple-authors-layout-centered .multiple-authors-links a:nth-child(1) {
    margin-right: 1px;
    margin-left: 1px
}

.pp-multiple-authors-layout-boxed .ppma-links {
    position: absolute;
    top: 10px;
    right: 10px;
}

.pp-multiple-authors-layout-centered .ppma-links a,
.pp-multiple-authors-layout-boxed .ppma-links a {
    color: #999;
    text-decoration: none;
    border-bottom: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.pp-multiple-authors-layout-centered ul {
    margin: 0;
    padding: 0;
}

.pp-multiple-authors-layout-centered ul li {
    text-align: center;
}

.pp-multiple-authors-layout-centered img {
    display: inline-block;
}

.pp-multiple-authors-layout-centered p {
    margin: 5px 0;
}

.pp-multiple-authors-layout-inline .avatar,
.pp-multiple-authors-layout-inline .photo {
    width: 30px;
    height: 30px;
    margin: auto 2px auto auto;
    vertical-align: middle;
    display: inline-block;
}

.author-boxes-footer-navigation {
    margin-top: 20px;
    margin-bottom: 100px;
}

.author-boxes-footer-navigation .nav-links .page-numbers {
    margin-right: 10px;
}

.pp-multiple-authors-wrapper .author-index-navigation .page-item {
    cursor: pointer;
}

.pp-multiple-authors-wrapper .author-index-navigation .page-item.active {
    cursor: not-allowed;
    pointer-events: none;
}

.pp-multiple-authors-wrapper .author-index-navigation .page-item.active > a {
  color: #555;
  text-decoration: none;
}

.pp-multiple-authors-wrapper .author-index-navigation {
    margin: 0 0 1rem 0;
    padding: 0;
}

.pp-multiple-authors-wrapper .author-index-navigation li {
    padding: 0.5rem 0.75rem;
    margin: 0;
    border: 1px solid #ffffff;
    display: inline-block;
}

.pp-multiple-authors-index .author-index-header {
    padding-bottom: 5px;
    margin-bottom: 20px;
}

.pp-multiple-authors-index .author-index-header:after {
    content: "";
    display: table;
    clear: both
}

.pp-multiple-authors-index .author-index-header a {
    float: right
}

.pp-multiple-authors-index .author-list-head {
    font-size: 24px;
    font-weight: 700;
    color: black;
    float: left;
    margin: 10px 0;
}

.pp-multiple-authors-index .author-index-authors:after {
    content: "";
    display: table;
    clear: both
}

.pp-multiple-authors-index .tease-author {
    margin-right: 50px;
    float: left;
}

.pp-multiple-authors-index .author-index-authors ul {
    list-style: none;
    padding: 0;
    margin: 0;
    *zoom: 1;
}

.pp-multiple-authors-index .author-index-authors ul li {
    margin: 0 0 20px 0;
    padding: 0;
}

body:not(.theme-twentytwenty):not(.theme-twentytwentyone) .pp-multiple-authors-index.alignwide,
body:not(.theme-twentytwenty):not(.theme-twentytwentyone) .pp-multiple-authors-recent.alignwide {
  margin: 0 !important;
  max-width: 100% !important;
}

.pp-multiple-authors-recent .ppma-row {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-left: -8px;
    gap: 12px;
}

.pp-multiple-authors-recent .name-row {
    min-width: 100%;
    margin-bottom: 0;
    display: flex;
}

.pp-multiple-authors-recent .name-row a {
    min-width: 100%;
}

.pp-multiple-authors-recent .ppma-row img {
    max-width: 100%;
}

.pp-multiple-authors-recent .name-row h4,
.pp-multiple-authors-recent .name-row img {
    display: inline-block;
    vertical-align: middle;
}

.pp-multiple-authors-recent .name-row h4 {
    max-width: 90%;
    min-width: 80%;
    margin-bottom: 0;
    padding: 15px;
    font-size: 13px;
    text-transform: uppercase;
    color: #1b1b1b;
    border: 1px solid #999;
    border-bottom-color: transparent;
    border-right-color: transparent;
}

.pp-multiple-authors-recent .name-row img {
    width: 20%;
    height: auto;
    border-radius: 50%;
    margin-left: -11%;
}

.preview-shortcode-wrap .pp-multiple-authors-recent .name-row img {
    margin-top: 11px;
    width: 40px !important;
}

.pp-multiple-authors-recent .ppma-row-article-block.main-block {
    border: 1px solid #999;
}

.pp-multiple-authors-recent .post-column .ppma-row-article-block.secondary {
    border-top: 1px solid #999;
}

.pp-multiple-authors-recent .ppma-row-article-block {
    margin-top: -10px;
    padding: 15px;
}

.pp-multiple-authors-recent .ppma-col-12,
.pp-multiple-authors-recent .ppma-col-5,
.pp-multiple-authors-recent .ppma-col-7,
.pp-multiple-authors-recent .ppma-col-md-3,
.pp-multiple-authors-recent .ppma-col-sm-4,
.pp-multiple-authors-recent .ppma-col-sm-12 {
    position: relative;
    width: 100%;
    padding-right: 8px;
    padding-left: 8px;
    margin-right: 15px;
}

.preview-shortcode-wrap .pp-multiple-authors-recent .ppma-col-12,
.preview-shortcode-wrap .pp-multiple-authors-recent .ppma-col-5,
.preview-shortcode-wrap .pp-multiple-authors-recent .ppma-col-7,
.preview-shortcode-wrap .pp-multiple-authors-recent .ppma-col-md-3,
.preview-shortcode-wrap .pp-multiple-authors-recent .ppma-col-sm-4,
.preview-shortcode-wrap .pp-multiple-authors-recent .ppma-col-sm-12 {
    margin-right: unset;
}

.pp-multiple-authors-recent .ppma-col-12 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 100%;
    -moz-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
}

.pp-multiple-authors-recent .ppma-col-5 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 41.66667%;
    -moz-box-flex: 0;
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
}

.pp-multiple-authors-recent .ppma-row-article-block .ppma-col-5 {
    padding-right: 0;
}

.pp-multiple-authors-recent .ppma-row-article-block .ppma-col-5 a {
    display: block;
    background-size: cover;
    background-position: center center;
}

.pp-multiple-authors-recent .ppma-col-7 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 58.33333%;
    -moz-box-flex: 0;
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
}

.pp-multiple-authors-recent .ppma-row-article-block .ppma-col-7 {
    padding-left: 10px;
}

.pp-multiple-authors-recent .featured-image-col {
    margin-bottom: 20px;
}

.pp-multiple-authors-recent .ppma-row-article-block a.headline {
    font-size: 15px;
    line-height: 130% !important;
    display: block;
    text-decoration: none;
}

.pp-multiple-authors-recent .ppma-row-article-block.secondary .text {
    padding-top: 10px;
    padding-bottom: 15px;
}

.pp-multiple-authors-recent .ppma-row-article-block.secondary .text .no-post {
    font-size: 11px;
}

.pp-multiple-authors-recent .all-author-post-link a {
    text-decoration: none;
}

.pp-multiple-authors-recent .ppma-row-article-block .ppma-col-7 a.headline {
    font-size: 16px;
}

.pp-multiple-authors-recent .ppma-row-article-block {
    margin-top: -10px;
    padding: 15px;
}

.pp-multiple-authors-recent .ppma-row-article-block.secondary,
.pp-multiple-authors-recent .ppma-row-article-block.secondary .ppma-col-12 {
    padding: 0;
}

.pp-multiple-authors-recent .article-cta {
    display: block;
    width: 100%;
    text-align: center;
    margin-top: 15px;
    margin-bottom: 0px;
    padding: 0;
}

.pp-multiple-authors-recent .article-cta p {
    font-size: 11px;
    text-transform: uppercase;
    color: #1b1b1b;
    letter-spacing: 1.5px;
    font-weight: 300;
    border-bottom: 1px double #446c76;
    border-top: 1px double #446c76;
    margin-bottom: 0;
    padding: 12px 0;
}

.pp-multiple-authors-recent .article-cta p span {
    font-weight: 400;
}

.pp-multiple-authors-searchbox {
    margin-bottom: 20px;
    display: flex;
}

.pp-multiple-authors-searchbox form {
    display: flex;
}

.pp-multiple-authors-layout-inline .pp-author-boxes-name::after {
    display: none !important;
}

.editor-styles-wrapper .wp-block .pp-multiple-authors-boxes-wrapper a {
    text-decoration: none;
}

@media (min-width: 576px) {
    .pp-multiple-authors-recent .ppma-col-sm-4 {
        -webkit-box-flex: 0;
        -webkit-flex: 0 0 33.33333%;
        -moz-box-flex: 0;
        -ms-flex: 0 0 33.33333%;
        flex: 0 0 33.33333%;
        max-width: 33.33333%;
    }
}

@media (min-width: 768px) {
    .pp-multiple-authors-recent .ppma-col-md-3 {
        -webkit-box-flex: 0;
        -webkit-flex: 0 0 50%;
        -moz-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }

    .pp-multiple-authors-recent.multiple-authors-col-1 .name-row img {
        width: 5%;
        margin-left: -20px;
    }

    .pp-multiple-authors-recent .name-row img,
    .pp-multiple-authors-recent.multiple-authors-col-2 .name-row img {
        width: 13%;
    }

    .pp-multiple-authors-recent .name-row h4 {
        min-width: 90%;
    }

    .pp-multiple-authors-recent.multiple-authors-col-1 .name-row h4,
    .pp-multiple-authors-recent.multiple-authors-col-2 .name-row h4 {
        min-width: 90%;
    }

    .pp-multiple-authors-recent.multiple-authors-col-3 .name-row h4 {
        min-width: 80%;
    }

    .pp-multiple-authors-recent.multiple-authors-col-4.name-row h4 {
        min-width: 70%;
    }

    .pp-multiple-authors-recent .ppma-author-entry,
    .pp-multiple-authors-recent .ppma-author-entry {
        margin-bottom: 20px;
    }
}

@media (min-width:769px) {

    .pp-multiple-authors-layout-boxed ul li {
        display: flex;
        flex-wrap: wrap;
    }

    .pp-multiple-authors-layout-boxed ul li > div:nth-child(1) {
        flex: 0 0 110px;
    }

    .pp-multiple-authors-layout-boxed ul li > div:nth-child(2) {
        flex: 1;
    }
}

@media (max-width: 600px) {
    .pp-multiple-authors-index .author-index-authors ul:after {
        content: "";
        display: table;
        clear: both
    }
}

@media (min-width: 601px) and (max-width: 899px) {
    .pp-multiple-authors-index .author-index-authors ul:after {
        content: "";
        display: table;
        clear: both
    }
}

@media (min-width: 900px) {
    .pp-multiple-authors-index .author-index-authors ul:after {
        content: "";
        display: table;
        clear: both
    }
}

/*  Elementor Custom Skin    */
/*  https://www.dudaster.com */
.swiper-container.swiper-initialized .ecs-posts{
   overflow:visible; 
}
.ecs-post-loop .elementor-page-title{
  display:block;
}

.ecs-loop-preview{
  height:100%;
  width:100%;
  background:lightgray;
  border:solid 1px gray;
  padding:5px;
}
.ecs-loop-preview h3{
  font-size:16px;
  font-weight:500;
  
}
.ecs-loop-preview span{
  font-size:12px;
}
.ecs-loop-preview .ecs-image-holder{
    width: 100%;
    line-height: 113px;
    background-color: #808080;
    font-size: 45px;
    text-align: center;
    vertical-align: middle;
    font-weight: 900;
    color: #929191;
}

.ecs-link-wrapper{
  cursor: pointer;
}
.elementor-widget-ele-loop-item, .elementor-widget-ele-loop-item > .elementor-widget-container,
.elementor-element-edit-mode.elementor-widget-ele-loop-item, .elementor-element-edit-mode.elementor-widget-ele-loop-item > .elementor-widget-container{
  
    height: 100%;

}
/* Fix for Elementor 3.4 */

.elementor-widget-posts .ecs-posts .elementor-button-wrapper {
    margin-top: 0;
}

/* Fix astra css issues */

.ecs-post-loop .ast-article-single{
    background-color: none;
    border-bottom:none;
    margin: 0;
    padding: 0;
}
.ecs-post-loop.ast-article-post{
    width:100%;
}
/* fix storeforent css issues*/

.theme-storefront.elementor-editor-active #primary.content-area{
	float:none;
} 

/* Lazy Load Animations */

.elementor-editor-active .ecs-lazyload a,.elementor-editor-preview .ecs-lazyload a{
  display:none;
}
.animation-hidden .ecs-lazy-load-animation{
  height:0;
  visibility:hidden;
}

/* loading bar progresion */
/*
<div class="barload-wrapper  ecs-lazy-load-animation"><div class="barload-border ecs-ll-brcolor"><div class="barload-whitespace"><div class="barload-line ecs-ll-bgcolor"></div></div></div></div>
*/
.barload-wrapper {
	width: 195px;
	height: 1em;
	position: relative;
	display:inline-block;
  font-size:16px;
  padding:10px;
}

.barload-border {
	border: 3px solid rgb(34,34,34);
	height: 100%;
	width: 100%;
	position: relative;
	
	top: -50%;
	padding: 4px 3px;
}

.barload-whitespace {
	overflow: hidden;
	height: 100%;
	width: 100%;
	margin: 0 auto;
	overflow: hidden;
	position: relative;
}

.barload-line {
	position: absolute;
	height: 100%;
	width: 100%;
	background-color: rgb(0,0,0);
	animation: barload-slide 2.75s steps(40) infinite;
		-o-animation: barload-slide 2.75s steps(40) infinite;
		-ms-animation: barload-slide 2.75s steps(40) infinite;
		-webkit-animation: barload-slide 2.75s steps(40) infinite;
		-moz-animation: barload-slide 2.75s steps(40) infinite;
}



@keyframes barload-slide {
	0% {
		left: -100%;
	}
	
	100% {
		left: 100%;
	}
}

@-o-keyframes barload-slide {
	0% {
		left: -100%;
	}
	
	100% {
		left: 100%;
	}
}

@-ms-keyframes barload-slide {
	0% {
		left: -100%;
	}
	
	100% {
		left: 100%;
	}
}

@-webkit-keyframes barload-slide {
	0% {
		left: -100%;
	}
	
	100% {
		left: 100%;
	}
}

@-moz-keyframes barload-slide {
	0% {
		left: -100%;
	}
	
	100% {
		left: 100%;
	}
}
/* four ball moving from left to right*/
/*
<div class="ballsload-container ecs-lazy-load-animation"><div class="ecs-ll-bgcolor"></div><div class="ecs-ll-bgcolor"></div><div class="ecs-ll-bgcolor"></div><div class="ecs-ll-bgcolor"></div></div>
*/

.barload-wrapper ,.barload-wrapper * {
  box-sizing:content-box;
} 
.ballsload-container{
    font-size:16px;
    padding:10px;
    position:relative;
	}
	
.ballsload-container div {
	width: 1em;
	height: 1em;
	position: absolute;
	background-color: rgb(0,0,0);
	top: 0;
	border-radius: 50%;
}

.ballsload-container div:nth-child(1) {

	animation: ballsload-move 2.3s infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-o-animation: ballsload-move 2.3s infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-ms-animation: ballsload-move 2.3s infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-webkit-animation: ballsload-move 2.3s infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-moz-animation: ballsload-move 2.3s infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
}

.ballsload-container div:nth-child(2) {

	animation: ballsload-move 2.3s 172.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-o-animation: ballsload-move 2.3s 172.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-ms-animation: ballsload-move 2.3s 172.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-webkit-animation: ballsload-move 2.3s 172.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-moz-animation: ballsload-move 2.3s 172.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
}

.ballsload-container div:nth-child(3) {

	animation: ballsload-move 2.3s 345ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-o-animation: ballsload-move 2.3s 345ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-ms-animation: ballsload-move 2.3s 345ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-webkit-animation: ballsload-move 2.3s 345ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-moz-animation: ballsload-move 2.3s 345ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
}

.ballsload-container div:nth-child(4) {

	animation: ballsload-move 2.3s 517.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-o-animation: ballsload-move 2.3s 517.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-ms-animation: ballsload-move 2.3s 517.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-webkit-animation: ballsload-move 2.3s 517.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
		-moz-animation: ballsload-move 2.3s 517.5ms infinite cubic-bezier(0.2, 0.64, 0.81, 0.23);
}



@keyframes ballsload-move {
	0% {
		left: 0%;
	}
	100% {
		left: 100%;
	}
}

@-o-keyframes ballsload-move {
	0% {
		left: 0%;
	}
	100% {
		left: 100%;
	}
}

@-ms-keyframes ballsload-move {
	0% {
		left: 0%;
	}
	100% {
		left: 100%;
	}
}

@-webkit-keyframes ballsload-move {
	0% {
		left: 0%;
	}
	100% {
		left: 100%;
	}
}

@-moz-keyframes ballsload-move {
	0% {
		left: 0%;
	}
	100% {
		left: 100%;
	}
}


/* one ball sliding left to right */
/*
<div id="movingBallG" class="ecs-lazy-load-animation"><div class="movingBallLineG  ecs-ll-bgcolor"></div><div id="movingBallG_1" class="movingBallG ecs-ll-bgcolor"></div></div>
*/

#movingBallG{
	position:relative;
	width:250px;
	height:19px;
	display:inline-block;
  font-size:16px;
}

.movingBallLineG{
	position:absolute;
	left:0px;
	top:0.5em;
	height:0.25em;
	width:250px;
	background-color:rgb(0,0,0);
}

.movingBallG{
	background-color:rgb(0,0,0);
	position:absolute;
	top:0;
	left:0;
	width:1.188em;
	height:1.188em;
	border-radius:100px;
		-o-border-radius:100px;
		-ms-border-radius:100px;
		-webkit-border-radius:100px;
		-moz-border-radius:100px;
	animation-name:bounce_movingBallG;
		-o-animation-name:bounce_movingBallG;
		-ms-animation-name:bounce_movingBallG;
		-webkit-animation-name:bounce_movingBallG;
		-moz-animation-name:bounce_movingBallG;
	animation-duration:1.5s;
		-o-animation-duration:1.5s;
		-ms-animation-duration:1.5s;
		-webkit-animation-duration:1.5s;
		-moz-animation-duration:1.5s;
	animation-iteration-count:infinite;
		-o-animation-iteration-count:infinite;
		-ms-animation-iteration-count:infinite;
		-webkit-animation-iteration-count:infinite;
		-moz-animation-iteration-count:infinite;
	animation-direction:normal;
		-o-animation-direction:normal;
		-ms-animation-direction:normal;
		-webkit-animation-direction:normal;
		-moz-animation-direction:normal;
}



@keyframes bounce_movingBallG{
	0%{
		left:0px;
	}

	50%{
		left:230px;
	}

	100%{
		left:0px;
	}
}

@-o-keyframes bounce_movingBallG{
	0%{
		left:0px;
	}

	50%{
		left:230px;
	}

	100%{
		left:0px;
	}
}

@-ms-keyframes bounce_movingBallG{
	0%{
		left:0px;
	}

	50%{
		left:230px;
	}

	100%{
		left:0px;
	}
}

@-webkit-keyframes bounce_movingBallG{
	0%{
		left:0px;
	}

	50%{
		left:230px;
	}

	100%{
		left:0px;
	}
}

@-moz-keyframes bounce_movingBallG{
	0%{
		left:0px;
	}

	50%{
		left:230px;
	}

	100%{
		left:0px;
	}
}


/* loading 3 dots moving form left to write */

/* 
<div class="lds-ellipsis ecs-lazy-load-animation"><div class="ecs-ll-bgcolor"></div><div class="ecs-ll-bgcolor"></div><div class="ecs-ll-bgcolor"></div><div class="ecs-ll-bgcolor"></div></div> 
*/

.lds-ellipsis {
  display: inline-block;
  position: relative;
  width: 8em;
  height: 1em;
  font-size:13px;
}
.lds-ellipsis div {
  position: absolute;
  top: 0px;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  background: #000;
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}
.lds-ellipsis div:nth-child(1) {
  left: 1em;
  animation: lds-ellipsis1 0.6s infinite;
}
.lds-ellipsis div:nth-child(2) {
  left: 1em;
  animation: lds-ellipsis2 0.6s infinite;
}
.lds-ellipsis div:nth-child(3) {
  left: 4em;
  animation: lds-ellipsis2 0.6s infinite;
}
.lds-ellipsis div:nth-child(4) {
  left: 7em;
  animation: lds-ellipsis3 0.6s infinite;
}
@keyframes lds-ellipsis1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes lds-ellipsis3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}
@keyframes lds-ellipsis2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(3em, 0);
  }
}






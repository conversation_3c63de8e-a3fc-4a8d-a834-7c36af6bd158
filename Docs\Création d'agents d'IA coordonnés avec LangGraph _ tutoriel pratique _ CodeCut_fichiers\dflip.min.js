!function(){var e={101:function(){var e,t,i;function n(e,t){return null!=t&&"undefined"!=typeof Symbol&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}(t=t||(e=[],{getAll:function(){return e},removeAll:function(){e=[]},add:function(t){e.push(t)},remove:function(t){var i=e.indexOf(t);-1!==i&&e.splice(i,1)},update:function(t){if(0===e.length)return!1;var i=0;for(t=null!=t?t:window.performance.now();i<e.length;)e[i].update(t)?i++:e.splice(i,1);return!0}})).Tween=function(e){var i={},o={},s={},a=1e3,r=0,l=!1,h=!1,u=!1,p=0,c=null,d=t.Easing.Linear.None,f=t.Interpolation.Linear,g=[],v=null,m=!1,y=null,b=null,w=null;for(var P in e)i[P]=parseFloat(e[P],10);this.to=function(e,t){return null!=t&&(a=t),o=e,this},this.start=function(a){for(var r in t.add(this),h=!0,m=!1,c=(null!=a?a:window.performance.now())+p,o){if(n(o[r],Array)){if(0===o[r].length)continue;o[r]=[e[r]].concat(o[r])}null!=i[r]&&(i[r]=e[r],!1===n(i[r],Array)&&(i[r]*=1),s[r]=i[r]||0)}return this},this.stop=function(){return h&&(t.remove(this),h=!1,null!=w&&w.call(e),this.stopChainedTweens()),this},this.stopChainedTweens=function(){for(var e=0,t=g.length;e<t;e++)g[e].stop()},this.complete=function(){return h&&(t.remove(this),h=!1,null!=b&&b.call(e),this.completeChainedTweens()),this},this.completeChainedTweens=function(){for(var e=0,t=g.length;e<t;e++)g[e].complete()},this.delay=function(e){return p=e,this},this.repeat=function(e){return r=e,this},this.yoyo=function(e){return l=e,this},this.easing=function(e){return d=null==e?d:e,this},this.interpolation=function(e){return f=e,this},this.chain=function(){return g=arguments,this},this.onStart=function(e){return v=e,this},this.onUpdate=function(e){return y=e,this},this.onComplete=function(e){return b=e,this},this.onStop=function(e){return w=e,this},this.update=function(t){if(t<c)return!0;for(h in!1===m&&(null!=v&&v.call(e),m=!0),w=(w=(t-c)/a)>1?1:w,P=d(w),o)if(null!=i[h]){var h,w,P,S=i[h]||0,E=o[h];n(E,Array)?e[h]=f(E,P):("string"==typeof E&&(E=E.startsWith("+")||E.startsWith("-")?S+parseFloat(E,10):parseFloat(E,10)),"number"==typeof E&&(e[h]=S+(E-S)*P))}if(null!=y&&y.call(e,P),1===w){if(r>0){for(h in isFinite(r)&&r--,s){if("string"==typeof o[h]&&(s[h]=s[h]+parseFloat(o[h],10)),l){var x=s[h];s[h]=o[h],o[h]=x}i[h]=s[h]}l&&(u=!u),c=t+p}else{null!=b&&b.call(e);for(var C=0,T=g.length;C<T;C++)g[C].start(c+a);return!1}}return!0}},t.Easing={Linear:{None:function(e){return e}},Quadratic:{In:function(e){return e*e},Out:function(e){return e*(2-e)},InOut:function(e){return(e*=2)<1?.5*e*e:-.5*(--e*(e-2)-1)}},Quartic:{In:function(e){return e*e*e*e},Out:function(e){return 1- --e*e*e*e},InOut:function(e){return(e*=2)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2)}},Sinusoidal:{In:function(e){return 1-Math.cos(e*Math.PI/2)},Out:function(e){return Math.sin(e*Math.PI/2)},InOut:function(e){return .5*(1-Math.cos(Math.PI*e))}},Cubic:{In:function(e){return e*e*e},Out:function(e){return--e*e*e+1},InOut:function(e){return(e*=2)<1?.5*e*e*e:.5*((e-=2)*e*e+2)}}},t.Interpolation={Linear:function(e,i){var n=e.length-1,o=n*i,s=Math.floor(o),a=t.Interpolation.Utils.Linear;return i<0?a(e[0],e[1],o):i>1?a(e[n],e[n-1],n-o):a(e[s],e[s+1>n?n:s+1],o-s)},Bezier:function(e,i){for(var n=0,o=e.length-1,s=Math.pow,a=t.Interpolation.Utils.Bernstein,r=0;r<=o;r++)n+=s(1-i,o-r)*s(i,r)*e[r]*a(o,r);return n},Utils:{Linear:function(e,t,i){return(t-e)*i+e},Bernstein:function(e,i){var n=t.Interpolation.Utils.Factorial;return n(e)/n(i)/n(e-i)},Factorial:(i=[1],function(e){var t=1;if(i[e])return i[e];for(var n=e;n>1;n--)t*=n;return i[e]=t,t}),CatmullRom:function(e,t,i,n,o){var s=(i-e)*.5,a=(n-t)*.5,r=o*o;return o*r*(2*t-2*i+s+a)+(-3*t+3*i-2*s-a)*r+s*o+t}}},window.TWEEN=t}},t={};function i(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,i),s.exports}i.amdO={},function(){"use strict";function e(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}function t(e,t){return null!=t&&"undefined"!=typeof Symbol&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}function n(t){return function(t){if(Array.isArray(t))return e(t)}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||o(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,i){if(t){if("string"==typeof t)return e(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return e(t,i)}}var s,a,r=new WeakMap,l=new WeakMap;function h(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t(e,h))return e;if(!t(this,h))return new h(e,i);if(r.set(this,[]),this.length=0,!e)return this;var n=[];return"string"==typeof e?n=e.trim().startsWith("<")&&e.trim().endsWith(">")?(n=Array.from(document.createRange().createContextualFragment(e.trim()).children)).map(function(e){return e.cloneNode(!0)}):Array.from(document.querySelectorAll(e)):e.nodeType||e===window||e===document?n=[e]:void 0!==e.length&&(n=Array.from(e)),this._setElements(n,i),this}h.prototype._getElements=function(){return r.get(this)||[]},h.prototype._setElements=function(e,t){var i=this;return r.set(this,e),this.length=e.length,Object.keys(this).forEach(function(e){isNaN(e)||delete i[e]}),e.forEach(function(e,o){if(t.hasOwnProperty("class")){var s;(s=e.classList).add.apply(s,n(t.class.trim().split(" ")))}t.hasOwnProperty("id")&&(e.id=t.id),t.hasOwnProperty("title")&&(e.title=t.title),t.hasOwnProperty("html")&&(e.innerHTML=t.html),i[o]=e}),this},h.prototype.on=function(e,t,i){var n,o;return"string"==typeof t&&"function"==typeof i?(o=t,n=i):(n=t,o=null),this._getElements().forEach(function(t){if(o){var i=function(e){if(e.target){var t=e.target.closest(o);t&&(e.originalEvent=e,n.call(t,e))}};t.addEventListener(e,i),t._queryHandlers||(t._queryHandlers={}),t._queryHandlers[e]||(t._queryHandlers[e]=[]),t._queryHandlers[e].push({original:n,wrapped:i,selector:o})}else{var s=function(e){e.originalEvent=e,n.call(t,e)};t.addEventListener(e,s),t._queryHandlers||(t._queryHandlers={}),t._queryHandlers[e]||(t._queryHandlers[e]=[]),t._queryHandlers[e].push({original:n,wrapped:s})}}),this},h.prototype.off=function(){return this._getElements().forEach(function(e){e._queryHandlers&&(Object.keys(e._queryHandlers).forEach(function(t){e._queryHandlers[t].forEach(function(i){e.removeEventListener(t,i.wrapped)})}),e._queryHandlers={})}),this},h.prototype.trigger=function(e){return this._getElements().forEach(function(t){if("string"==typeof e&&"function"==typeof t[e])t[e]();else{var i=new Event(e,{bubbles:!0});t.dispatchEvent(i)}}),this},h.prototype.addClass=function(e){return e&&this._getElements().forEach(function(t){if(t.classList){var i;(i=t.classList).add.apply(i,n(e.trim().split(" ")))}}),this},h.prototype.removeClass=function(e){return e&&this._getElements().forEach(function(t){if(t.classList){var i;(i=t.classList).remove.apply(i,n(e.trim().split(" ")))}}),this},h.prototype.hasClass=function(e){return this._getElements().some(function(t){return t.classList&&t.classList.contains(e)})},h.prototype.toggleClass=function(e,t){return this._getElements().forEach(function(i){void 0===t?i.classList.toggle(e):t?i.classList.add(e):i.classList.remove(e)}),this},h.prototype.css=function(e){var t=["width","height","min-width","min-height","max-width","max-height","margin","marginTop","marginRight","marginBottom","marginLeft","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","top","right","bottom","left","border-width","border-top-width","border-right-width","border-bottom-width","border-left-width","fontSize","lineHeight"];return this._getElements().forEach(function(i){Object.keys(e).forEach(function(n){var o=e[n];null!=o&&""!==o&&!isNaN(parseFloat(o))&&isFinite(o)&&t.includes(n)&&0!==o&&"0"!==o&&"number"==typeof o&&(o+="px"),i.style[n]=o})}),this},h.prototype.prepend=function(e){return e&&this._getElements().forEach(function(i){"string"==typeof e?i.insertAdjacentHTML("afterbegin",e):t(e,h)?e._getElements().forEach(function(e){i.insertBefore(e,i.firstChild)}):e.nodeType&&i.insertBefore(e,i.firstChild)}),this},h.prototype.append=function(e){return e&&this._getElements().forEach(function(i){"string"==typeof e?i.insertAdjacentHTML("beforeend",e):t(e,h)?e._getElements().forEach(function(e){i.contains(e)||i.appendChild(e)}):e.nodeType&&!i.contains(e)&&i.appendChild(e)}),this},h.prototype.appendTo=function(e){var i=t(e,h)?e._getElements()[0]:"string"==typeof e?document.querySelector(e):e;return i&&this._getElements().forEach(function(e){i.contains(e)||i.appendChild(e)}),this},h.prototype.after=function(e){return this._getElements().forEach(function(i){"string"==typeof e?i.insertAdjacentHTML("afterend",e):t(e,h)?e._getElements().forEach(function(e){i.parentNode.insertBefore(e,i.nextSibling)}):e.nodeType&&i.parentNode.insertBefore(e,i.nextSibling)}),this},h.prototype.html=function(e){return void 0===e?this._getElements()[0]?this._getElements()[0].innerHTML:"":(this._getElements().forEach(function(t){t.innerHTML=e}),this)},h.prototype.text=function(e){return void 0===e?this._getElements()[0]?this._getElements()[0].textContent:"":(this._getElements().forEach(function(t){t.textContent=e}),this)},h.prototype.find=function(e){var t=[];return this._getElements().forEach(function(i){var o=i.querySelectorAll(e);t.push.apply(t,n(Array.from(o)))}),new h(t)},h.prototype.children=function(e){var t=[];return this._getElements().forEach(function(i){Array.from(i.children).forEach(function(i){(!e||i.matches(e))&&t.push(i)})}),new h(t)},h.prototype.is=function(e){return this._getElements().some(function(t){return t.matches(e)})},h.prototype.closest=function(e){var t=[];return this._getElements().forEach(function(i){var n=i.closest(e);n&&!t.includes(n)&&t.push(n)}),new h(t)},h.prototype.contains=function(e){var i=t(e,h)?e._getElements()[0]:e;return this._getElements().some(function(e){return e.contains(i)})},h.prototype.siblings=function(e){var t=[];return this._getElements().forEach(function(i){var o=Array.from(i.parentElement.children).filter(function(e){return e!==i});e&&(o=o.filter(function(t){return t.matches(e)})),t.push.apply(t,n(o))}),new h(t)},h.prototype.parent=function(e){var t=this._getElements().map(function(e){return e.parentElement}).filter(function(e){return null!==e});return e&&(t=t.filter(function(t){return t.matches(e)})),new h(t)},h.prototype.each=function(e){return this._getElements().forEach(function(t,i){e.call(t,i,t)}),this},h.prototype.map=function(e){return this._getElements().map(function(t,i){return e.call(t,i,t)}),this},h.prototype.get=function(){return this._getElements()},h.prototype.attr=function(e,t){return 1==arguments.length?this._getElements()[0]?this._getElements()[0].getAttribute(e):void 0:(void 0===t?this._getElements().forEach(function(t){t.removeAttribute(e)}):this._getElements().forEach(function(i){i.setAttribute(e,t)}),this)},h.prototype.removeAttr=function(e){return this._getElements().forEach(function(t){t.removeAttribute(e)}),this},h.prototype.data=function(e,t){if(void 0===t){var i=this._getElements()[0];if(!i)return;var n=l.get(i)||{};if(void 0!==n[e])return n[e];var o=e.replace(/-([a-z])/g,function(e,t){return t.toUpperCase()});return i.dataset[o]}return this._getElements().forEach(function(i){if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)i.setAttribute("data-".concat(e),t);else if(null===t){i.removeAttribute("data-".concat(e));var n=l.get(i);n&&void 0!==n[e]&&(delete n[e],l.set(i,n))}else if((void 0===t?"undefined":t&&"undefined"!=typeof Symbol&&t.constructor===Symbol?"symbol":typeof t)=="object"){var o=l.get(i)||{};o[e]=t,l.set(i,o)}}),this},h.prototype.height=function(e){if(void 0===e){if(0===this._getElements().length)return 0;var t=this._getElements()[0];return t===window?window.innerHeight:t.offsetHeight}return this._getElements().forEach(function(t){t.style.height="number"==typeof e?e+"px":e}),this},h.prototype.width=function(e){if(void 0===e){if(0===this._getElements().length)return 0;var t=this._getElements()[0];return t===window?window.innerWidth:t.offsetWidth}return this._getElements().forEach(function(t){t.style.width="number"==typeof e?e+"px":e}),this},h.prototype.remove=function(){return this._getElements().forEach(function(e){e.parentNode&&e.parentNode.removeChild(e)}),this._setElements([]),this},h.prototype.val=function(e){return void 0===e?this._getElements()[0]?this._getElements()[0].value:"":(this._getElements().forEach(function(t){t.value=e}),this)},h.prototype.hide=function(){return this._getElements().forEach(function(e){e.style.display="none"}),this},h.prototype.show=function(){return this._getElements().forEach(function(e){e.style.display="block"}),this},h.prototype.ready=function(e){document.addEventListener("DOMContentLoaded",e)},h.prototype.scrollTop=function(e){return void 0===e?this._getElements()[0]?this._getElements()[0].scrollTop:0:(this._getElements().forEach(function(t){t.scrollTop=e}),this)},h.prototype.change=function(e){return this._getElements().forEach(function(t){t.addEventListener("change",e)}),this},h.prototype.click=function(){return this._getElements().forEach(function(e){"function"==typeof e.click&&e.click()}),this},h.extend=function(e,i){for(var n=arguments.length,s=Array(n>2?n-2:0),a=2;a<n;a++)s[a-2]=arguments[a];if(!i)return{};var r=!0,l=!1,u=void 0;try{for(var p,c=s[Symbol.iterator]();!(r=(p=c.next()).done);r=!0){var d=p.value;if(d){var f=!0,g=!1,v=void 0;try{for(var m,y=Object.entries(d)[Symbol.iterator]();!(f=(m=y.next()).done);f=!0){var b,w=(b=m.value,function(e){if(Array.isArray(e))return e}(b)||function(e,t){var i,n,o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var s=[],a=!0,r=!1;try{for(o=o.call(e);!(a=(i=o.next()).done)&&(s.push(i.value),2!==s.length);a=!0);}catch(e){r=!0,n=e}finally{try{a||null==o.return||o.return()}finally{if(r)throw n}}return s}}(b,2)||o(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),P=w[0],S=w[1];if(t(S,h))i[P]=S;else switch(Object.prototype.toString.call(S)){case"[object Object]":"[object Object]"===Object.prototype.toString.call(i[P])?i[P]=h.extend(i[P],S):i[P]=h.extend({},S);break;case"[object Array]":i[P]=h.extend(Array(S.length),S);break;default:i[P]=S}}}catch(e){g=!0,v=e}finally{try{f||null==y.return||y.return()}finally{if(g)throw v}}}}}catch(e){l=!0,u=e}finally{try{r||null==c.return||c.return()}finally{if(l)throw u}}return i};var u={jQuery:null,version:"2.3.75",autoDetectLocation:!0,_isHashTriggered:!1,slug:void 0,locationVar:"dearViewerLocation",locationFile:void 0,MOUSE_CLICK_ACTIONS:{NONE:"none",NAV:"nav"},ARROW_KEYS_ACTIONS:{NONE:"none",NAV:"nav"},MOUSE_DBL_CLICK_ACTIONS:{NONE:"none",ZOOM:"zoom"},MOUSE_SCROLL_ACTIONS:{NONE:"none",ZOOM:"zoom",NAV:"nav"},PAGE_SCALE:{PAGE_FIT:"fit",PAGE_WIDTH:"width",AUTO:"auto",ACTUAL:"actual",MANUAL:"manual"},READ_DIRECTION:{LTR:"ltr",RTL:"rtl"},TURN_DIRECTION:{LEFT:"left",RIGHT:"right",NONE:"none"},INFO_TYPE:{INFO:"info",ERROR:"error"},FLIPBOOK_PAGE_MODE:{SINGLE:"single",DOUBLE:"double",AUTO:"auto"},FLIPBOOK_SINGLE_PAGE_MODE:{ZOOM:"zoom",BOOKLET:"booklet",AUTO:"auto"},FLIPBOOK_PAGE_SIZE:{AUTO:"auto",SINGLE:"single",DOUBLE_INTERNAL:"dbl_int",DOUBLE:"dbl",DOUBLE_COVER_BACK:"dbl_cover_back"},LINK_TARGET:{NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4},CONTROLS_POSITION:{HIDDEN:"hidden",TOP:"top",BOTTOM:"bottom"},TURN_CORNER:{TL:"tl",TR:"tr",BL:"bl",BR:"br",L:"l",R:"r",NONE:"none"},REQUEST_STATUS:{OFF:"none",ON:"pending",COUNT:"counting"},TEXTURE_TARGET:{THUMB:0,VIEWER:1,ZOOM:2},FLIPBOOK_CENTER_SHIFT:{RIGHT:1,LEFT:-1,NONE:0},FLIPBOOK_COVER_TYPE:{NONE:"none",PLAIN:"plain",BASIC:"basic",RIDGE:"ridge"}};function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}u.fakejQuery=!1,"undefined"==typeof jQuery?(u.fakejQuery=!0,u.jQuery=h):u.jQuery=jQuery,u._defaults={is3D:!0,has3DShadow:!0,color3DCover:"#aaaaaa",color3DSheets:"#fff",cover3DType:u.FLIPBOOK_COVER_TYPE.NONE,flexibility:.9,drag3D:!1,height:"auto",autoOpenOutline:!1,autoOpenThumbnail:!1,showDownloadControl:!0,showSearchControl:!0,showPrintControl:!0,enableSound:!0,duration:800,pageRotation:0,flipbook3DTiltAngleUp:0,flipbook3DTiltAngleLeft:0,readDirection:u.READ_DIRECTION.LTR,pageMode:u.FLIPBOOK_PAGE_MODE.AUTO,singlePageMode:u.FLIPBOOK_SINGLE_PAGE_MODE.AUTO,flipbookFitPages:!1,backgroundColor:"transparent",flipbookHardPages:"none",openPage:1,annotationClass:"",maxTextureSize:3200,minTextureSize:256,rangeChunkSize:524288,disableAutoFetch:!0,disableStream:!0,disableFontFace:!1,icons:{altnext:"df-icon-arrow-right1",altprev:"df-icon-arrow-left1",next:"df-icon-arrow-right1",prev:"df-icon-arrow-left1",end:"df-icon-last-page",start:"df-icon-first-page",share:"df-icon-share","outline-open":"df-icon-arrow-right","outline-close":"df-icon-arrow-down",help:"df-icon-help",more:"df-icon-more",download:"df-icon-download",zoomin:"df-icon-add-circle",zoomout:"df-icon-minus-circle",resetzoom:"df-icon-minus-circle",fullscreen:"df-icon-fullscreen","fullscreen-off":"df-icon-fit-screen",fitscreen:"df-icon-fit-screen",thumbnail:"df-icon-grid-view",outline:"df-icon-list",close:"df-icon-close",doublepage:"df-icon-double-page",singlepage:"df-icon-file",print:"df-icon-print",play:"df-icon-play",pause:"df-icon-pause",search:"df-icon-search",sound:"df-icon-volume","sound-off":"df-icon-volume",facebook:"df-icon-facebook",google:"df-icon-google",twitter:"df-icon-twitter",whatsapp:"df-icon-whatsapp",linkedin:"df-icon-linkedin",pinterest:"df-icon-pinterest",mail:"df-icon-mail"},text:{toggleSound:"Turn on/off Sound",toggleThumbnails:"Toggle Thumbnails",toggleOutline:"Toggle Outline/Bookmark",previousPage:"Previous Page",nextPage:"Next Page",toggleFullscreen:"Toggle Fullscreen",zoomIn:"Zoom In",zoomOut:"Zoom Out",resetZoom:"Reset Zoom",pageFit:"Fit Page",widthFit:"Fit Width",toggleHelp:"Toggle Help",search:"Search in PDF",singlePageMode:"Single Page Mode",doublePageMode:"Double Page Mode",downloadPDFFile:"Download PDF File",gotoFirstPage:"Goto First Page",gotoLastPage:"Goto Last Page",print:"Print",play:"Start AutoPlay",pause:"Pause AutoPlay",share:"Share",close:"Close",mailSubject:"Check out this FlipBook",mailBody:"Check out this site {{url}}",loading:"Loading",thumbTitle:"Thumbnails",outlineTitle:"Table of Contents",searchTitle:"Search",searchPlaceHolder:"Search",analyticsEventCategory:"DearFlip",analyticsViewerReady:"Document Ready",analyticsViewerOpen:"Document Opened",analyticsViewerClose:"Document Closed",analyticsFirstPageChange:"First Page Changed"},share:{facebook:"https://www.facebook.com/sharer/sharer.php?u={{url}}&t={{mailsubject}}",twitter:"https://twitter.com/share?url={{url}}&text={{mailsubject}}",mail:void 0,whatsapp:"https://api.whatsapp.com/send/?text={{mailsubject}}+{{url}}&app_absent=0",linkedin:"https://www.linkedin.com/shareArticle?url={{url}}&title={{mailsubject}}",pinterest:"https://www.pinterest.com/pin/create/button/?url={{url}}&media=&description={{mailsubject}}"},allControls:"altPrev,pageNumber,altNext,play,outline,thumbnail,zoomIn,zoomOut,zoom,fullScreen,share,download,search,pageMode,startPage,endPage,sound,search,print,more",moreControls:"download,pageMode,pageFit,startPage,endPage,sound",leftControls:"outline,thumbnail",rightControls:"fullScreen,share,download,more",hideControls:"",hideShareControls:"",controlsPosition:u.CONTROLS_POSITION.BOTTOM,paddingTop:20,paddingLeft:15,paddingRight:15,paddingBottom:20,enableAnalytics:!1,zoomRatio:2,maxDPI:2,fakeZoom:1,pageScale:u.PAGE_SCALE.PAGE_FIT,controlsFloating:!0,sideMenuOverlay:!0,enableAnnotation:!0,enableAutoLinks:!0,arrowKeysAction:u.ARROW_KEYS_ACTIONS.NAV,clickAction:u.MOUSE_CLICK_ACTIONS.NAV,dblClickAction:u.MOUSE_DBL_CLICK_ACTIONS.NONE,mouseScrollAction:u.MOUSE_SCROLL_ACTIONS.NONE,linkTarget:u.LINK_TARGET.BLANK,soundFile:"sound/turn2.mp3",imagesLocation:"images",imageResourcesPath:"images/pdfjs/",popupThumbPlaceholder:"data:image/svg+xml,"+escape('<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 210 297"><rect width="210" height="297" style="fill:#f1f2f2"/><circle cx="143" cy="95" r="12" style="fill:#e3e8ed"/><polygon points="131 138 120 149 95 124 34 184 176 185 131 138" style="fill:#e3e8ed"/></svg>'),cMapUrl:"js/libs/cmaps/",logo:"",logoUrl:"",sharePrefix:"",pageSize:u.FLIPBOOK_PAGE_SIZE.AUTO,backgroundImage:"",pixelRatio:window.devicePixelRatio||1,spotLightIntensity:.22,ambientLightColor:"#fff",ambientLightIntensity:.8,shadowOpacity:.1,slug:void 0,headerElementSelector:void 0,onReady:function(e){},onPageChanged:function(e){},beforePageChanged:function(e){},onCreate:function(e){},onCreateUI:function(e){},onFlip:function(e){},beforeFlip:function(e){},autoPDFLinktoViewer:!1,autoLightBoxFullscreen:!1,thumbLayout:"book-title-hover",cleanupAfterRender:!0,canvasWillReadFrequently:!0,providerType:"pdf",loadMoreCount:-1,autoPlay:!1,autoPlayDuration:1e3,autoPlayStart:!1,popupBackGroundColor:"#eee",mockupMode:!1,instantTextureProcess:!1,cachePDFTexture:!1,pdfVersion:"default"},u.defaults={},u.jQuery.extend(!0,u.defaults,u._defaults),u.viewers={},u.providers={},u.openFileOptions={},u.executeCallback=function(){};var c=u.jQuery,d="WebKitCSSMatrix"in window||document.body&&"MozPerspective"in document.body.style,f="onmousedown"in window,g=u.utils={mouseEvents:f?{type:"mouse",start:"mousedown",move:"mousemove",end:"mouseup"}:{type:"touch",start:"touchstart",move:"touchmove",end:"touchend"},html:{div:"<div></div>",a:"<a>",input:"<input type='text'/>",select:"<select></select>"},getSharePrefix:function(){return g.getSharePrefixes()[0]},getSharePrefixes:function(){return(u.defaults.sharePrefix+",dflip-,flipbook-,dearflip-,dearpdf-").split(",").map(function(e){return e.trim()})},toRad:function(e){return e*Math.PI/180},toDeg:function(e){return 180*e/Math.PI},ifdef:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return null==e?t:e},createBtn:function(e,t,i){var n=c(g.html.div,{class:"df-ui-btn df-ui-"+e,title:i,html:void 0!==i?"<span>"+i+"</span>":""});return void 0!==t&&t.indexOf("<svg")>-1?n.html(t.replace("<svg",'<svg xmlns="http://www.w3.org/2000/svg" ')):n.addClass(t),n},transition:function(e,t){return e?t/1e3+"s ease-out":"0s none"},display:function(e){return e?"block":"none"},resetTranslate:function(){return g.translateStr(0,0)},bgImage:function(e){return null==e||"blank"===e?"":' url("'+e+'")'},translateStr:function(e,t){return d?" translate3d("+e+"px,"+t+"px, 0px) ":" translate("+e+"px, "+t+"px) "},httpsCorrection:function(e){try{if(null==e)return null;if("string"!=typeof e)return e;var t=window.location;if(t.href.split(".")[0]===e.split(".")[0])return e;e.split("://")[1].split("/")[0].replace("www.","")===t.hostname.replace("www.","")&&e.indexOf(t.hostname.replace("www.",""))>-1&&(t.href.indexOf("https://")>-1?e=e.replace("http://","https://"):t.href.indexOf("http://")>-1&&(e=e.replace("https://","http://")),t.href.indexOf("://www.")>-1&&-1===e.indexOf("://www.")&&(e=e.replace("://","://www.")),-1===t.href.indexOf("://www.")&&e.indexOf("://www.")>-1&&(e=e.replace("://www.","://")))}catch(t){console.log("Skipping URL correction: "+e)}return e},rotateStr:function(e){return" rotateZ("+e+"deg) "},lowerPowerOfTwo:function(e){return Math.pow(2,Math.floor(Math.log(e)/Math.LN2))},nearestPowerOfTwo:function(e,t){return Math.min(t||2048,Math.pow(2,Math.ceil(Math.log(e)/Math.LN2)))},getFullscreenElement:function(){return document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement},hasFullscreenEnabled:function(){return document.fullscreenEnabled||document.mozFullScreenEnabled||document.webkitFullscreenEnabled||document.msFullscreenEnabled},fixMouseEvent:function(e){if(!e)return e;var t=e.originalEvent||e;if(!t.changedTouches||!(t.changedTouches.length>0))return e;var i=c.event.fix(e),n=t.changedTouches[0];return i.clientX=n.clientX,i.clientY=n.clientY,i.pageX=n.pageX,i.touches=t.touches,i.pageY=n.pageY,i.movementX=n.movementX,i.movementY=n.movementY,i},limitAt:function(e,t,i){return e<t?t:e>i?i:e},distOrigin:function(e,t){return g.distPoints(0,0,e,t)},distPoints:function(e,t,i,n){return Math.sqrt(Math.pow(i-e,2)+Math.pow(n-t,2))},angleByDistance:function(e,t){var i=t/2,n=g.limitAt(e,0,t);return n<i?g.toDeg(Math.asin(n/i)):90+g.toDeg(Math.asin((n-i)/i))},calculateScale:function(e,t){var i=g.distPoints(e[0].x,e[0].y,e[1].x,e[1].y);return g.distPoints(t[0].x,t[0].y,t[1].x,t[1].y)/i},getVectorAvg:function(e){return{x:e.map(function(e){return e.x}).reduce(g.sum)/e.length,y:e.map(function(e){return e.y}).reduce(g.sum)/e.length}},sum:function(e,t){return e+t},getTouches:function(e,t){return t=t||{left:0,top:0},Array.prototype.slice.call(e.touches).map(function(e){return{x:e.pageX-t.left,y:e.pageY-t.top}})},getScriptCallbacks:[],getScript:function(e,t,i,n){var o,s=g.getScriptCallbacks[e];function a(){o.removeEventListener("load",r,!1),o.removeEventListener("readystatechange",r,!1),o.removeEventListener("complete",r,!1),o.removeEventListener("error",l,!1),o.onload=o.onreadystatechange=null,o=null,o=null}function r(e,t){if(null!=o&&(t||!o.readyState||/loaded|complete/.test(o.readyState))){if(!t){for(var n=0;n<s.length;n++)s[n]&&s[n](),s[n]=null;i=null}a()}}function l(){i(),a(),i=null}if(0===c("script[src='"+e+"']").length){(s=g.getScriptCallbacks[e]=[]).push(t),o=document.createElement("script");var h=document.body.getElementsByTagName("script")[0];o.async=!0,o.setAttribute("data-cfasync","false"),!0===n&&o.setAttribute("type","module"),null!=h?(h.parentNode.insertBefore(o,h),h=null):document.body.appendChild(o),o.addEventListener("load",r,!1),o.addEventListener("readystatechange",r,!1),o.addEventListener("complete",r,!1),i&&o.addEventListener("error",l,!1),o.src=e+("MS"===g.prefix.dom?"?"+Math.random():"")}else s.push(t)},detectScriptLocation:function(){if(void 0===window[u.locationVar])c("script").each(function(){var e=c(this)[0].src;if((e.indexOf("/"+u.locationFile+".js")>-1||e.indexOf("/"+u.locationFile+".min.js")>-1||e.indexOf("js/"+u.locationFile+".")>-1)&&(e.indexOf("https://")>-1||e.indexOf("http://")>-1)){var t=e.split("/");window[u.locationVar]=t.slice(0,-2).join("/")}});else if(-1==window[u.locationVar].indexOf(":")){var e=document.createElement("a");e.href=window[u.locationVar],window[u.locationVar]=e.href,e=null}void 0!==window[u.locationVar]&&window[u.locationVar].length>2&&"/"!==window[u.locationVar].slice(-1)&&(window.window[u.locationVar]+="/")},disposeObject:function(e){return e&&e.dispose&&e.dispose(),e=null},log:function(){for(var e,t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];!0===u.defaults.enableDebugLog&&window.console&&(e=console).log.apply(e,function(e){if(Array.isArray(e))return p(e)}(i)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return p(e,void 0);var i=Object.prototype.toString.call(e).slice(8,-1);if("Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return p(e,void 0)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())},color:{getBrightness:function(e){var t=e.replace("#","").match(/.{1,2}/g).map(function(e){return parseInt(e,16)});return .299*t[0]+.587*t[1]+.114*t[2]},isLight:function(e){return!g.color.isDark(e)},isDark:function(e){return 128>g.color.getBrightness(e)}},isMobile:/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),isIOS:/(iPad|iPhone|iPod)/g.test(navigator.userAgent),isIPad:"iPad"===navigator.platform||void 0!==navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&/Mac/.test(navigator.platform),isMac:navigator.platform.toUpperCase().indexOf("MAC")>=0,isSafari:/constructor/i.test(window.HTMLElement)||"[object SafariRemoteNotification]"===(!window.safari||window.safari.pushNotification).toString(),isIEUnsupported:!!navigator.userAgent.match(/(MSIE|Trident)/),isSafariWindows:function(){return!g.isMac&&g.isSafari},hasWebgl:function(){try{var e=document.createElement("canvas");return!!(window.WebGLRenderingContext&&(e.getContext("webgl")||e.getContext("experimental-webgl")))}catch(e){return!1}}(),hasES2022:void 0!==Array.prototype.at,canSupport3D:function(){var e=!0;try{if(!1==g.hasWebgl)e=!1,console.log("Proper Support for Canvas webgl 3D not detected!");else if(!1==g.hasES2022)e=!1,console.log("Proper Support for 3D not extpected in older browser!");else if(-1!==navigator.userAgent.indexOf("MSIE")||navigator.appVersion.indexOf("Trident/")>0)e=!1,console.log("Proper Support for 3D not detected for IE!");else if(g.isSafariWindows())e=!1,console.log("Proper Support for 3D not detected for Safari!");else{var t=navigator.userAgent.toString().toLowerCase().match(/android\s([0-9\.]*)/i);(t=t?t[1]:void 0)&&(t=parseInt(t,10),!isNaN(t)&&t<9&&(e=!1,console.log("Proper Support for 3D not detected for Android below 9.0!")))}}catch(e){}return e},prefix:(s=window.getComputedStyle(document.documentElement,""),a=Array.prototype.slice.call(s).join("").match(/-(moz|webkit|ms)-/)[1],{dom:"WebKit|Moz|MS".match(RegExp("("+a+")","i"))[1],lowercase:a,css:"-"+a+"-",js:a[0].toUpperCase()+a.substr(1)}),scrollIntoView:function(e,t,i){(t=t||e.parentNode).scrollTop=e.offsetTop+(!1===i?e.offsetHeight-t.offsetHeight:0),t.scrollLeft=e.offsetLeft-t.offsetLeft},getVisibleElements:function(e){var t=e.container,i=e.elements,n=e.visible||[],o=t.scrollTop,s=o+t.clientHeight;if(0==s)return n;var a=0,r=i.length-1,l=i[a],h=l.offsetTop+l.clientTop+l.clientHeight;if(h<o)for(;a<r;){var u=a+r>>1;(h=(l=i[u]).offsetTop+l.clientTop+l.clientHeight)>o?r=u:a=u+1}for(var p=a;p<i.length;p++)if((l=i[p]).offsetTop+l.clientTop<=s)n.push(p+1);else break;return n},getMouseDelta:function(e){var t=0;return null!=e.wheelDelta?t=e.wheelDelta:null!=e.detail&&(t=-e.detail),t},pan:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e.startPoint,o=e.app.zoomValue,s=e.left+(!0===i?0:t.raw.x-n.raw.x),a=e.top+(!0===i?0:t.raw.y-n.raw.y);e.left=Math.ceil(g.limitAt(s,-e.shiftWidth,e.shiftWidth)),e.top=Math.ceil(g.limitAt(a,-e.shiftHeight,e.shiftHeight)),1===o&&(e.left=0,e.top=0),!1===i&&(e.startPoint=t)}};g.isChromeExtension=function(){return 0===window.location.href.indexOf("chrome-extension://")};var v=/\x00+/g,m=/[\x01-\x1F]/g;g.removeNullCharacters=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"!=typeof e?(warn("The argument for removeNullCharacters must be a string."),e):(t&&(e=e.replace(m," ")),e.replace(v,""))},u.hashFocusBookFound=!1,g.detectHash=function(){u.preParseHash=window.location.hash;var e=g.getSharePrefixes();-1==e.indexOf("")&&e.push(""),Array.prototype.forEach.call(e,function(e){var t=u.preParseHash,i="#"+e;if(t&&t.indexOf(i)>=0&&!1===u.hashFocusBookFound){i.length>1&&(t=t.split(i)[1]);var n=t.split("/")[0].replace("#","");if(n.length>0){var o,s=t.split("/")[1];if(null!=s&&(s=s.split("/")[0]),0===(o=c("[data-df-slug="+n+"]")).length&&(o=c("[data-slug="+n+"]")),0===o.length&&(o=c("#df-"+n+",#"+n)),0===o.length&&(o=c("[data-_slug="+n+"]")),o.length>0&&o.is("._df_thumb,._df_button,._df_custom,._df_link,._df_book,.df-element,.dp-element")){o=c(o[0]),u.hashFocusBookFound=!0,s=parseInt(s,10),g.focusHash(o);var a=u.activeLightBox&&u.activeLightBox.app||o.data("df-app");if(null!=a)return a.gotoPage(s),g.focusHash(a.element),!1;null!=s&&o.attr("data-hash-page",s),o.addClass("df-hash-focused",!0),null!=o.data("lightbox")||null!=o.data("df-lightbox")?(u._isHashTriggered=!0,o.trigger("click"),u._isHashTriggered=!1):null!=o.attr("href")&&o.attr("href").indexOf(".pdf")>-1&&o.trigger("click")}}}})},g.focusHash=function(e){var t,i;null===(t=(i=e[0]).scrollIntoView)||void 0===t||t.call(i,{behavior:"smooth",block:"nearest",inline:"nearest"})},g.contain=function(e,t,i,n){var o=Math.min(i/e,n/t);return{width:e*o,height:t*o}},g.containUnStretched=function(e,t,i,n){var o=Math.min(1,i/e,n/t);return{width:e*o,height:t*o}},g.fallbackOptions=function(e){return void 0===e.share.mail&&(e.share.mail="mailto:?subject="+e.text.mailSubject+"&body="+e.text.mailBody),e.openPage&&(e.openPage=parseInt(e.openPage,10)),e};var y=function(e){var t={},i={id:"",thumb:"",openPage:"data-hash-page,df-page,data-df-page,data-page,page",target:"",height:"",showDownloadControl:"data-download",source:"pdf-source,df-source,source",is3D:"webgl,is3d",viewerType:"viewertype,viewer-type",pagemode:""};for(var n in i)for(var o=(n+","+i[n]).split(","),s=0;s<o.length;s++){var a=o[s];if(""!==a){var r=e.data(a);if(null!==r&&""!==r&&void 0!==r||null!==(r=e.attr(a))&&""!==r&&void 0!==r){t[n]=r;break}}}return e.removeAttr("data-hash-page"),t};g.getOptions=function(e){void 0==(e=c(e)).data("df-option")&void 0==e.data("option")&&e.data("df-option","option_"+e.attr("id")),void 0!==e.attr("source")&&e.data("df-source",e.attr("source"));var t=e.data("df-option")||e.data("option"),i=void 0;i=(void 0===t?"undefined":t&&"undefined"!=typeof Symbol&&t.constructor===Symbol?"symbol":typeof t)=="object"?t:null==t||""===t||null==window[t]?{}:window[t];var n=y(e);return c.extend(!0,{},i,n)},g.isTrue=function(e){return"true"===e||!0===e},g.parseInt=function(e){return parseInt(e,10)},g.parseFloat=function(e){return parseFloat(e)},g.parseIntIfExists=function(e){return void 0!==e&&(e=parseInt(e,10)),e},g.parseFloatIfExists=function(e){return void 0!==e&&(e=parseFloat(e)),e},g.parseBoolIfExists=function(e){return void 0!==e&&(e=g.isTrue(e)),e},g.getCurveAngle=function(e,t){var i,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return e?(i=t>135?180-(180-t)*2:t>45?t-45:0,i=g.limitAt(i,n,180)):(i=t<45?2*t:t<135?t+45:180,i=g.limitAt(i,0,180-n)),i},g.sanitizeOptions=function(e){var t,i;if(e.showDownloadControl=g.parseBoolIfExists(e.showDownloadControl),e.showSearchControl=g.parseBoolIfExists(e.showSearchControl),e.showPrintControl=g.parseBoolIfExists(e.showPrintControl),e.flipbook3DTiltAngleLeft=g.parseIntIfExists(e.flipbook3DTiltAngleLeft),e.flipbook3DTiltAngleUp=g.parseIntIfExists(e.flipbook3DTiltAngleUp),e.paddingLeft=g.parseIntIfExists(e.paddingLeft),e.paddingRight=g.parseIntIfExists(e.paddingRight),e.paddingTop=g.parseIntIfExists(e.paddingTop),e.paddingBottom=g.parseIntIfExists(e.paddingBottom),e.duration=g.parseIntIfExists(e.duration),e.rangeChunkSize=g.parseIntIfExists(e.rangeChunkSize),e.maxTextureSize=g.parseIntIfExists(e.maxTextureSize),e.linkTarget=g.parseIntIfExists(e.linkTarget),e.zoomRatio=g.parseFloatIfExists(e.zoomRatio),e.enableAnalytics=g.parseBoolIfExists(e.enableAnalytics),e.autoPlay=g.parseBoolIfExists(e.autoPlay),e.autoPlayStart=g.parseBoolIfExists(e.autoPlayStart),e.autoPlayDuration=g.parseIntIfExists(e.autoPlayDuration),void 0!==e.loadMoreCount&&(e.loadMoreCount=g.parseInt(e.loadMoreCount),(isNaN(e.loadMoreCount)||0===e.loadMoreCount)&&(e.loadMoreCount=-1)),null!=e.source&&(Array===e.source.constructor||Array.isArray(e.source)||(t=e.source,null!=(i=Array)&&"undefined"!=typeof Symbol&&i[Symbol.hasInstance]?!!i[Symbol.hasInstance](t):t instanceof i)))for(var n=0;n<e.source.length;n++)e.source[n]=g.httpsCorrection(e.source[n]);else e.source=g.httpsCorrection(e.source);return e},g.finalizeOptions=function(e){return e},g.urlify=function(e){for(var t,i,n=/[a-zA-Z0-9][^\s,]{3,}\.[^\s,]+[a-zA-Z0-9]/gi,o=[];t=n.exec(e);){var s=t[0];1==(s.match(/@/g)||[]).length?s.match(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,7})+/gi)&&o.push({index:t.index,length:s.length,text:s}):s.match(/[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b[-a-zA-Z0-9@:%_\+.~#?&//=]*/g)&&(0===(i=s.toLowerCase()).indexOf("http:")||0===i.indexOf("https:")||0===i.indexOf("www."))&&o.push({index:t.index,length:s.length,text:s})}return o},g.oldurlify=function(e){return e.replace(/((([A-Za-z]{3,9}:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[.\!\/\\w]*))?)/g,function(e,t,i,n,o){var s=e=e.toLowerCase();if(e.indexOf(":")>0&&-1===e.indexOf("http:")&&-1===e.indexOf("https:"))return g.log("AutoLink Rejected: "+s+" for "+e),e;if(0===e.indexOf("www."))s="http://"+e;else if(0===e.indexOf("http://")||0===e.indexOf("https://"));else if(0===e.indexOf("mailto:"));else if(e.indexOf("@")>0&&(s="mailto:"+e,null===e.match(/(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/)))return g.log("AutoLink Rejected: "+s+" for "+e),e;return g.log("AutoLink: "+s+" for "+e),'<a href="'+s+'" class="df-autolink" target="_blank">'+e+"</a>"})},g.supportsPassive=!1;try{var b=Object.defineProperty({},"passive",{get:function(){g.supportsPassive=!0}});window.addEventListener("testPassive",null,b),window.removeEventListener("testPassive",null,b)}catch(e){}u.parseCSSElements=function(){c(".dvcss").each(function(){var e,t=c(this),i=function(e){for(var t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"dvcss_e_",n=e.classList,o=0;o<n.length;o++)if(0===(t=n[o]).indexOf(i))return t;return null}(t[0]);t.removeClass(i).removeClass("dvcss"),i=i.replace("dvcss_e_","");try{e=JSON.parse(atob(i))}catch(e){}if(e){var n="df_option_"+e.id;window[n]=c.extend(!0,{},window[n],e),t.addClass("df-element"),"none"!==e.lightbox&&(t.attr("data-df-lightbox",void 0===e.lightbox?"custom":e.lightbox),"thumb"==e.lightbox&&t.attr("data-df-thumb",e.pdfThumb),e.thumbLayout&&t.attr("data-df-thumb-layout",e.thumbLayout),e.apl&&t.attr("apl",e.apl)),t.data("df-option",n),t.attr("data-df-slug",e.slug),t.attr("id","df_"+e.id)}})},u.parseThumbs=function(e){e.element.html(""),(null==e.thumbURL||""==e.thumbURL.toString().trim())&&(e.element.addClass("df-thumb-not-found"),e.thumbURL=u.defaults.popupThumbPlaceholder);var t=c("<span class='df-book-title'>").html(e.title),i=c("<div class='df-book-wrapper'>").appendTo(e.element);i.append(c("<div class='df-book-page1'>")),i.append(c("<div class='df-book-page2'>"));var n=c("<div class='df-book-cover'>").append(t).appendTo(i),o=c('<img width="210px" height="297px" class="df-lazy" alt="'+e.title+'"/>');o.attr("data-src",e.thumbURL),o.attr("src",u.defaults.popupThumbPlaceholder),n.prepend(o),u.addLazyElement(o[0]),!0===u.defaults.displayLightboxPlayIcon&&n.addClass("df-icon-play-popup"),"book-title-top"===e.thumbLayout?t.prependTo(e.element):("book-title-bottom"===e.thumbLayout||"cover-title"===e.thumbLayout)&&(e.hasShelf?e.thumbLayout="book-title-fixed":t.appendTo(e.element),!0===u.defaults.displayLightboxPlayIcon&&(e.element.removeClass("df-icon-play-popup"),i.addClass("df-icon-play-popup"))),e.element.addClass("df-tl-"+e.thumbLayout),e.element.attr("title",e.title)},u.initId=10,u.embeds=[],u.activeEmbeds=[],u.removeEmbeds=[],u.removeEmbedsLimit=g.isMobile?1:2,u.parseNormalElements=function(){c(".df-posts").each(function(){if(!1!==u.defaults.loadMoreCount&&-1!==u.defaults.loadMoreCount){var e=c(this);if("true"!==e.data("df-parsed")){e.data("df-parsed","true"),e.attr("df-parsed","true");var t=0,i=e.find(".df-element"),n=i.length;i.each(function(){++t>u.defaults.loadMoreCount&&c(this).attr("skip-parse","true")}),n>u.defaults.loadMoreCount&&e.append("<div class='df-load-more-button-wrapper'><div class='df-load-more-button'>Load More..</div></div>")}}}),u.triggerId=10,c(".df-element").each(function(){var e=c(this);if("true"!==e.attr("skip-parse")&&"true"!==e.data("df-parsed")){e.data("df-parsed","true"),e.attr("df-parsed","true");var t=e.data("df-lightbox")||e.data("lightbox");if(void 0===t)e.addClass("df-lazy-embed"),u.addLazyElement(e[0]);else if(e.addClass("df-popup-"+t),"thumb"===t){var i=e.data("df-thumb-layout")||u.defaults.thumbLayout,n=g.httpsCorrection(e.data("df-thumb"));e.removeAttr("data-thumb").removeAttr("data-thumb-layout");var o=e.html().trim();(void 0===o||""===o)&&(o="Click to Open");var s=e.parent().hasClass("df-has-shelf");u.parseThumbs({element:e,thumbURL:n,title:o,thumbLayout:i,hasShelf:s}),s&&e.after(c("<df-post-shelf>"))}else"button"===t&&u.defaults.buttonClass&&e.addClass(u.defaults.buttonClass);var a=e.attr("data-trigger");null!=a&&a.length>1&&(a=a.split(","),u.triggerId++,a.forEach(function(t){e.attr("df-trigger-id",u.triggerId),c("#"+t).addClass("df-trigger").attr("df-trigger",u.triggerId)}))}}),u.handleLazy=function(){var e;if(u.removeEmbeds.length>u.removeEmbedsLimit&&(e=u.removeEmbeds.shift())){var t=c("[initID='"+e+"']");if(t.length>0){var i=t.data("df-app");if(i){t.attr("data-df-page",i.currentPageNumber),g.log("Removed app id "+e),i.dispose(),i=null;var n=u.activeEmbeds.indexOf(e);n>-1&&u.activeEmbeds.splice(n,1)}}}if(e=u.embeds.shift()){var o=c("[initID='"+e+"']");if(o.length>0){if(o.is("img"))o.hasClass("df-lazy")?(o.attr("src",o.attr("data-src")),o.removeAttr("data-src"),o.removeClass("df-lazy"),u.lazyObserver.unobserve(o[0])):g.log("Prevent this"),u.handleLazy();else{var s=o.data("df-app");null==s?new u.Application({element:o}):s.softInit(),g.log("Created app id "+e),u.activeEmbeds.push(e)}}}u.removeEmbeds.length<=u.removeEmbedsLimit&&0==u.embeds.length&&(u.checkRequestQueue=null)}},u.lazyObserver={observe:function(e){(e=c(e)).is("img")?e.hasClass("df-lazy")&&(e.attr("src",e.attr("data-src")),e.removeAttr("data-src"),e.removeClass("df-lazy")):new u.Application({element:e})}},"function"==typeof IntersectionObserver&&(u.lazyObserver=new IntersectionObserver(function(e,t){e.forEach(function(e){var t,i=c(e.target),n=i.attr("initID");e.isIntersecting?(!i.attr("initID")&&(i.attr("initID",u.initId),n=u.initId.toString(),u.initId++),(t=u.removeEmbeds.indexOf(n))>-1?(u.removeEmbeds.splice(t,1),g.log("Removed id "+n+"from Removal list")):-1==(t=u.embeds.indexOf(n))&&(u.embeds.push(n),g.log("Added id "+n+"to Add list"))):n&&((t=u.embeds.indexOf(n))>-1?(u.embeds.splice(t,1),g.log("Removed id "+n+" from Add list")):-1==(t=u.removeEmbeds.indexOf(n))&&(u.removeEmbeds.push(n),g.log("Added id "+n+" to Removal list"))),w=0,(u.removeEmbeds.length>u.removeEmbedsLimit||u.embeds.length>0)&&null==u.checkRequestQueue&&(u.checkRequestQueue=function(){w++,u.checkRequestQueue&&requestAnimationFrame(function(){u&&u.checkRequestQueue&&u.checkRequestQueue()}),w>20&&(w=0,u.handleLazy())},u.checkRequestQueue())})}));var w=0;u.addLazyElement=function(e){u.lazyObserver.observe(e)},u.parseElements=g.parseElements=function(){u.parseCSSElements(),u.parseNormalElements()},u.initUtils=function(){g.detectScriptLocation();var e=c("body");(g.isSafari||g.isIOS)&&e.addClass("df-ios"),e.on("click",function(){}),e.on("click",".df-posts .df-load-more-button",function(){var e=c(this).closest(".df-posts");if(e.length>0){var t=0;e.find(".df-element").each(function(){var e=c(this);"true"===e.attr("skip-parse")&&(t<u.defaults.loadMoreCount&&e.removeAttr("skip-parse"),t++)}),u.parseNormalElements()}}),u.defaults.shelfImage&&""!=u.defaults.shelfImage&&e.append("<style>.df-has-shelf df-post-shelf:before, .df-has-shelf df-post-shelf:after{background-image: url('"+u.defaults.shelfImage+"');}</style>")};var P=u.jQuery,S=u.utils,E=/*#__PURE__*/function(){var e;function t(e,i){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,t),this.pages=[],this.app=i,this.parentElement=this.app.viewerContainer;var n="df-viewer "+(e.viewerClass||"");this.element=P("<div>",{class:n}),this.parentElement.append(this.element),this.wrapper=P("<div>",{class:"df-viewer-wrapper"}),this.element.append(this.wrapper),this.oldBasePageNumber=0,this.pages=[],this.minZoom=1,this.maxZoom=4,this.swipeThreshold=20,this.stageDOM=null,this.events={},this.arrowKeysAction=e.arrowKeysAction,this.clickAction=e.clickAction,this.scrollAction=e.scrollAction,this.dblClickAction=e.dblClickAction,this.pageBuffer=[],this.pageBufferSize=10}return e=[{key:"init",value:function(){}},{key:"softDispose",value:function(){}},{key:"updateBuffer",value:function(e){}},{key:"pageResetCallback",value:function(e){}},{key:"initCustomControls",value:function(){}},{key:"_getInnerWidth",value:function(){return this.app.dimensions.containerWidth-this.app.dimensions.padding.width-this.app.dimensions.offset.width}},{key:"_getInnerHeight",value:function(){return this.app.dimensions.maxHeight-this.app.dimensions.padding.height}},{key:"_getOuterHeight",value:function(e){return e}},{key:"dispose",value:function(){this.stageDOM&&(this.stageDOM.removeEventListener("mousemove",this.events.mousemove,!1),this.stageDOM.removeEventListener("mousedown",this.events.mousedown,!1),this.stageDOM.removeEventListener("mouseup",this.events.mouseup,!1),this.stageDOM.removeEventListener("touchmove",this.events.mousemove,!1),this.stageDOM.removeEventListener("touchstart",this.events.mousedown,!1),this.stageDOM.removeEventListener("touchend",this.events.mouseup,!1),this.stageDOM.removeEventListener("dblclick",this.events.dblclick,!1),this.stageDOM.removeEventListener("scroll",this.events.scroll,!1),this.stageDOM.removeEventListener("mousewheel",this.events.mousewheel,!1),this.stageDOM.removeEventListener("DOMMouseScroll",this.events.mousewheel,!1)),this.events=null,this.stageDOM=null,this.element.remove()}},{key:"checkDocumentPageSizes",value:function(){}},{key:"getViewerPageNumber",value:function(e){return e}},{key:"getDocumentPageNumber",value:function(e){return e}},{key:"getRenderContext",value:function(e,t){var i=this.app,n=i.provider,o=t.pageNumber,s=S.ifdef(t.textureTarget,u.TEXTURE_TARGET.VIEWER);i.dimensions.pageFit;var a=n.viewPorts[o],r=i.viewer.getTextureSize(t),l=null;if(l=s===u.TEXTURE_TARGET.THUMB?i.thumbSize:Math.floor(r.height),void 0===n.getCache(o,l)){var h=r.height/a.height,p=document.createElement("canvas"),c=this.filterViewPort(e.getViewport({scale:h,rotation:e._pageInfo.rotate+i.options.pageRotation}),o);s===u.TEXTURE_TARGET.THUMB&&(h=c.width/c.height>180/i.thumbSize?180*h/c.width:h*i.thumbSize/c.height,c=this.filterViewPort(e.getViewport({scale:h,rotation:e._pageInfo.rotate+i.options.pageRotation}),o)),p.height=Math.floor(c.height),p.width=Math.floor(c.width);var d=Math.abs(p.width-r.width)/r.width*100;return d>.001&&d<2&&(p.width=Math.floor(r.width),p.height=Math.floor(r.height)),i.viewer.filterViewPortCanvas(c,p,o),{canvas:p,canvasContext:p.getContext("2d",{willReadFrequently:!0===u.defaults.canvasWillReadFrequently}),viewport:c}}}},{key:"filterViewPort",value:function(e,t){return e}},{key:"getViewPort",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.app.provider.viewPorts[e];return t?null!=i?i:this.app.provider.defaultPage.viewPort:i}},{key:"pagesReady",value:function(){}},{key:"onReady",value:function(){}},{key:"filterViewPortCanvas",value:function(e){}},{key:"finalizeAnnotations",value:function(){}},{key:"finalizeTextContent",value:function(){}},{key:"updateTextContent",value:function(e){void 0==e&&(e=this.getBasePage(e)),this.app.provider.processTextContent(e,this.getTextElement(e,!0))}},{key:"isActivePage",value:function(e){return e===this.app.currentPageNumber}},{key:"initEvents",value:function(){var e=this.stageDOM=S.ifdef(this.stageDOM,this.parentElement[0]);e&&(e.addEventListener("mousemove",this.events.mousemove=this.mouseMove.bind(this),!1),e.addEventListener("mousedown",this.events.mousedown=this.mouseDown.bind(this),!1),e.addEventListener("mouseup",this.events.mouseup=this.mouseUp.bind(this),!1),e.addEventListener("touchmove",this.events.mousemove=this.mouseMove.bind(this),!1),e.addEventListener("touchstart",this.events.mousedown=this.mouseDown.bind(this),!1),e.addEventListener("touchend",this.events.mouseup=this.mouseUp.bind(this),!1),e.addEventListener("dblclick",this.events.dblclick=this.dblclick.bind(this),!1),e.addEventListener("scroll",this.events.scroll=this.onScroll.bind(this),!1),e.addEventListener("mousewheel",this.events.mousewheel=this.mouseWheel.bind(this),!1),e.addEventListener("DOMMouseScroll",this.events.mousewheel=this.mouseWheel.bind(this),!1)),this.startTouches=null,this.lastScale=null,this.startPoint=null}},{key:"refresh",value:function(){}},{key:"reset",value:function(){}},{key:"eventToPoint",value:function(e){var t={x:e.clientX,y:e.clientY};return t.x=t.x-this.app.viewerContainer[0].getBoundingClientRect().left,t.y=t.y-this.app.viewerContainer[0].getBoundingClientRect().top,{raw:t}}},{key:"mouseMove",value:function(e){e=S.fixMouseEvent(e),this.pinchMove(e),!0===this.pinchZoomDirty&&e.preventDefault(),this.startPoint&&!0!=this.pinchZoomDirty&&(this.pan(this.eventToPoint(e)),e.preventDefault())}},{key:"mouseDown",value:function(e){e=S.fixMouseEvent(e),this.pinchDown(e),this.startPoint=this.eventToPoint(e)}},{key:"mouseUp",value:function(e){e=S.fixMouseEvent(e),!0===this.pinchZoomDirty&&e.preventDefault();var t=this.eventToPoint(e),i=e.target||e.originalTarget,n=this.startPoint&&t.x===this.startPoint.x&&t.y===this.startPoint.y&&"A"!==i.nodeName;!0===e.ctrlKey&&n&&this.zoomOnPoint(t),this.pinchUp(e),this.startPoint=null}},{key:"pinchDown",value:function(e){null!=e.touches&&2==e.touches.length&&null==this.startTouches&&(this.startTouches=S.getTouches(e),this.app.viewer.zoomCenter=S.getVectorAvg(S.getTouches(e,this.parentElement.offset())),this.lastScale=1)}},{key:"pinchUp",value:function(e){null!=e.touches&&e.touches.length<2&&!0==this.pinchZoomDirty&&(this.app.viewer.lastScale=this.lastScale,this.app.container.removeClass("df-pinch-zoom"),this.updateTemporaryScale(!0),this.app.zoom(),this.lastScale=null,this.app.viewer.canSwipe=!1,this.pinchZoomDirty=!1,this.app.viewer._pinchZoomLastScale=null,this.startTouches=null)}},{key:"pinchMove",value:function(e){if(null!=e.touches&&2==e.touches.length&&null!=this.startTouches){this.pinchZoomDirty=!0,this.app.container.addClass("df-pinch-zoom");var t=S.calculateScale(this.startTouches,S.getTouches(e));this.lastScale,this.lastScale=t,this.app.viewer.pinchZoomUpdateScale=S.limitAt(t,this.app.viewer.minZoom/this.app.zoomValue,this.app.viewer.maxZoom/this.app.zoomValue),this.app.viewer._pinchZoomLastScale!=this.app.viewer.pinchZoomUpdateScale&&(this.app.viewer.pinchZoomRequestStatus=u.REQUEST_STATUS.ON,this.app.viewer._pinchZoomLastScale=this.app.viewer.pinchZoomUpdateScale),e.preventDefault();return}}},{key:"updateTemporaryScale",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!0===e)this.parentElement[0].style.transform="none";else if(this.app.viewer.zoomCenter){var t=this.app.viewer.pinchZoomUpdateScale;this.parentElement[0].style.transformOrigin=this.app.viewer.zoomCenter.x+"px "+this.app.viewer.zoomCenter.y+"px",this.parentElement[0].style.transform="scale3d("+t+","+t+",1)"}}},{key:"pan",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.panRequestStatus=u.REQUEST_STATUS.ON,S.pan(this,e,t)}},{key:"updatePan",value:function(){this.element.css({transform:"translate3d("+this.left+"px,"+this.top+"px,0)"})}},{key:"dblclick",value:function(e){}},{key:"onScroll",value:function(e){}},{key:"mouseWheel",value:function(e){var t=this.app,i=S.getMouseDelta(e),n=!0===e.ctrlKey,o=t.options.mouseScrollAction===u.MOUSE_SCROLL_ACTIONS.ZOOM&&(!0===t.options.isLightBox||!0===t.isFullscreen);n||o?(i>0||i<0)&&(e.preventDefault(),t.viewer.zoomCenter=this.eventToPoint(e).raw,t.zoom(i),t.ui.update()):t.options.mouseScrollAction===u.MOUSE_SCROLL_ACTIONS.NAV&&(i>0?t.next():i<0&&t.prev())}},{key:"zoomOnPoint",value:function(e){this.app.viewer.zoomCenter=e.raw,this.app.zoom(1)}},{key:"getVisiblePages",value:function(){return this.visiblePagesCache=[],{main:this.visiblePagesCache,buffer:[]}}},{key:"getBasePage",value:function(){return this.app.currentPageNumber}},{key:"isFirstPage",value:function(e){return void 0===e&&(e=this.app.currentPageNumber),1===e}},{key:"isLastPage",value:function(e){return void 0===e&&(e=this.app.currentPageNumber),e===this.app.pageCount}},{key:"isEdgePage",value:function(e){return void 0===e&&(e=this.app.currentPageNumber),1===e||e===this.app.pageCount}},{key:"checkRequestQueue",value:function(){var e=u.REQUEST_STATUS;this.panRequestStatus===e.ON&&(this.updatePan(),this.panRequestStatus=e.OFF),this.app.viewer.pinchZoomRequestStatus===e.ON&&(this.app.viewer.updateTemporaryScale(),this.app.viewer.pinchZoomRequestStatus=e.OFF)}},{key:"isAnimating",value:function(){return!1}},{key:"updatePendingStatusClass",value:function(e){void 0===e&&(e=this.isAnimating()),this.app.container.toggleClass("df-pending",e)}},{key:"initPages",value:function(){}},{key:"resize",value:function(){}},{key:"determinePageMode",value:function(){}},{key:"zoom",value:function(){}},{key:"gotoPageCallBack",value:function(){this.requestRefresh()}},{key:"requestRefresh",value:function(){var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];this.app.refreshRequestStatus=!0===e?u.REQUEST_STATUS.ON:u.REQUEST_STATUS.OFF}},{key:"getPageByNumber",value:function(e){var t=this.pages,i=void 0;if(this.app.isValidPage(e)){for(var n=0;n<t.length;n++)if(e===t[n].pageNumber){i=t[n];break}}return i}},{key:"changeAnnotation",value:function(){return!1}},{key:"getAnnotationElement",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.getPageByNumber(e);if(void 0!==i)return void 0===i.annotationElement&&(i.annotationElement=P("<div class='df-link-content'>"),i.contentLayer.append(i.annotationElement)),!0===t&&i.annotationElement.html(""),i.annotationElement[0]}},{key:"getTextElement",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.getPageByNumber(e);if(void 0!==i)return void 0===i.textElement&&(i.textElement=P("<div class='df-text-content'>"),i.contentLayer.append(i.textElement)),!0===t&&(i.textElement.html(""),i.textElement.siblings(".df-auto-link-content").html("")),i.textElement[0]}},{key:"render",value:function(){}},{key:"textureLoadedCallback",value:function(e){}},{key:"handleZoom",value:function(){}},{key:"getTextureSize",value:function(e){console.error("Texture calculation missing!")}},{key:"textureHeightLimit",value:function(e){return S.limitAt(e,1,this.app.dimensions.maxTextureHeight)}},{key:"textureWidthLimit",value:function(e){return S.limitAt(e,1,this.app.dimensions.maxTextureWidth)}},{key:"setPage",value:function(e){S.log("Set Page detected",e.pageNumber);var t=this.getPageByNumber(e.pageNumber);return!!t&&(e.callback=this.textureLoadedCallback.bind(this),t.loadTexture(e),this.updateBuffer(t),!0)}},{key:"cleanPage",value:function(e){return!0}},{key:"validatePageChange",value:function(e){return e!==this.app.currentPageNumber}},{key:"afterControlUpdate",value:function(){}},{key:"searchPage",value:function(e){return{include:!0,label:this.app.provider.getLabelforPage(e)}}}],function(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,e),t}();function x(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function C(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function T(e,t,i){return t&&C(e.prototype,t),i&&C(e,i),e}function k(e){return(k=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function O(e,t){return(O=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var R=u.utils,L=u.jQuery,_=/*#__PURE__*/function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&O(e,t)}(n,e);var t,i=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,i=k(n);return e=t?Reflect.construct(i,arguments,k(this).constructor):i.apply(this,arguments),e&&("object"==(e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e)||"function"==typeof e)?e:function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(this)});function n(e){var t;return x(this,n),(t=i.call(this)).canvasMode=null,e&&e.parentElement&&(t.parentElement=e.parentElement),t.init(),t}return T(n,[{key:"init",value:function(){var e=this.element=L("<div>",{class:"df-page"});e[0].appendChild(this.contentLayer[0]),this.texture=new Image,this.parentElement&&this.parentElement[0].append(e[0])}},{key:"resetContent",value:function(){void 0!==this.annotationElement&&this.annotationElement.html(""),void 0!==this.textElement&&this.textElement.html("")}},{key:"setLoading",value:function(){this.element.toggleClass("df-loading",!0!==this.textureLoaded)}},{key:"loadTexture",value:function(e){var t=this,i=e.texture,n=e.callback;function o(){t.textureSrc=i,t.element.css({backgroundImage:R.bgImage(i)}),t.updateTextureLoadStatus(!0),"function"==typeof n&&n(e)}null===t.canvasMode&&i&&"CANVAS"===i.nodeName&&(t.canvasMode=!0),!0===t.canvasMode?(t.element.find("canvas").remove(),i!==t.textureLoadFallback&&(t.textureSrc=i,t.element.append(L(i))),t.updateTextureLoadStatus(!0),"function"==typeof n&&n(e)):i===t.textureLoadFallback?o():(t.texture.onload=o,t.texture.src=i)}},{key:"updateCSS",value:function(e){this.element.css(e)}},{key:"resetCSS",value:function(){this.element.css({transform:"",boxShadow:"",display:"block"})}}]),n}(/*#__PURE__*/function(){function e(){x(this,e),this.textureLoadFallback="blank",this.textureStamp="-1",this.textureLoaded=!1,this.texture="blank",this.textureSrc="blank",this.pageNumber=void 0,this.contentLayer=L("<div>",{class:"df-page-content"})}return T(e,[{key:"reset",value:function(){this.resetTexture(),this.resetContent()}},{key:"resetTexture",value:function(){this.textureLoaded=!1,this.textureStamp="-1",this.loadTexture({texture:this.textureLoadFallback}),this.contentLayer.removeClass("df-content-loaded")}},{key:"clearTexture",value:function(){this.loadTexture({texture:this.textureLoadFallback})}},{key:"resetContent",value:function(){}},{key:"loadTexture",value:function(e){}},{key:"getTexture",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.textureSrc;return!0===e&&t&&t.cloneNode&&(t=t.cloneNode()).getContext&&t.getContext("2d").drawImage(this.textureSrc,0,0),t}},{key:"setLoading",value:function(){}},{key:"updateTextureLoadStatus",value:function(e){this.textureLoaded=!0===e,R.log((!0===this.textureLoaded?"Loaded ":"Loading ")+this.textureStamp+" for "+this.pageNumber),this.contentLayer.toggleClass("df-content-loaded",!0===e),this.setLoading()}},{key:"changeTexture",value:function(e,t){var i=e+"|"+t;return this.textureStamp!==i&&(R.log("Page "+e+" : texture changed from - "+this.textureStamp+" to "+i),this.textureLoaded=!1,this.textureStamp=i,this.updateTextureLoadStatus(!1),!0)}}]),e}());function N(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function I(e,t,i){return(I="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=A(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(i||e):o.value}})(e,t,i||e)}function A(e){return(A=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function M(e,t){return(M=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var D=u.utils,F=/*#__PURE__*/function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&M(e,t)}(o,e);var t,i,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,i=A(o);return e=t?Reflect.construct(i,arguments,A(this).constructor):i.apply(this,arguments),e&&("object"==(e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e)||"function"==typeof e)?e:N(this)});function o(e,t){var i;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,o),e.viewerClass="df-reader",t.options.mouseScrollAction=u.MOUSE_SCROLL_ACTIONS.NONE,(i=n.call(this,e,t)).app.jumpStep=1,i.minZoom=.25,i.stackCount=i.app.pageCount,i.app.options.paddingLeft=0,i.app.options.paddingRight=0,i.app.options.paddingTop=10,i.app.options.paddingBottom=!0===i.app.options.controlsFloating?20:10,i.app.pageScaling=i.app.options.pageScale,i.acceptAppMouseEvents=!0,i.scrollStatus=u.REQUEST_STATUS.OFF,i.deltaPanX=0,i.deltaPanY=0,t._viewerPrepared(),i.zoomViewer=N(i),i}return i=[{key:"init",value:function(){I(A(o.prototype),"init",this).call(this),this.initEvents(),this.initPages(),this.initScrollBar()}},{key:"initEvents",value:function(){this.stageDOM=this.element[0],I(A(o.prototype),"initEvents",this).call(this)}},{key:"initPages",value:function(){this.stackCount=this.app.pageCount;for(var e=0;e<this.stackCount;e++){var t=new _({parentElement:this.wrapper});t.index=e,t.viewer=this,this.pages.push(t)}}},{key:"initScrollBar",value:function(){this.scrollBar=jQuery("<div class='df-reader-scrollbar'>"),this.scrollBar.appendTo(this.app.container),this.scrollPageNumber=jQuery("<div class='df-reader-scroll-page-number'>").appendTo(this.app.container)}},{key:"afterControlUpdate",value:function(){void 0!==this.scrollBar&&(this.scrollBar[0].innerHTML=this.app.getCurrentLabel(),this.app.provider.pageLabels?this.scrollPageNumber[0].innerHTML=this.app.getCurrentLabel()+"<div>("+this.app.currentPageNumber+" of "+this.app.pageCount+")</div>":this.scrollPageNumber[0].innerHTML=this.app.getCurrentLabel()+"<div>of "+this.app.pageCount+"</div>")}},{key:"updateBuffer",value:function(e){if("-1"!==e.textureStamp&&void 0!==e.pageNumber){for(var t=e.pageNumber,i=e.pageNumber,n=0,o=0;o<this.pageBuffer.length;o++){var s=this.pageBuffer[o].pageNumber;if(t===s){D.log("Page "+t+" already in buffer, skipping");return}Math.abs(this.app.currentPageNumber-s)>Math.abs(this.app.currentPageNumber-i)&&(i=s,n=o)}this.pageBuffer.push(e),this.pageBuffer.length>this.pageBufferSize&&(D.log("Farthest buffer: "+i),this.pageBuffer[n].reset(),this.pageBuffer.splice(n,1))}}},{key:"initCustomControls",value:function(){var e=this.app.ui.controls;e.openRight.hide(),e.openLeft.hide()}},{key:"dispose",value:function(){I(A(o.prototype),"dispose",this).call(this),this.scrollBar&&this.scrollBar.remove(),this.scrollPageNumber&&this.scrollPageNumber.remove(),this.element.remove()}},{key:"_getInnerHeight",value:function(){I(A(o.prototype),"_getInnerHeight",this).call(this);var e=this.app.dimensions.maxHeight-this.app.dimensions.padding.height,t=this.app.dimensions.defaultPage.viewPort,i=this.app.dimensions.containerWidth-20-this.app.dimensions.padding.width;this.app.pageScaling===u.PAGE_SCALE.ACTUAL&&(i=1*this.app.provider.defaultPage.viewPort.width);var n=e;return this.app.pageScaling===u.PAGE_SCALE.PAGE_WIDTH?n=100*t.height:this.app.pageScaling===u.PAGE_SCALE.AUTO?n=1.5*t.height:this.app.pageScaling===u.PAGE_SCALE.ACTUAL&&(n=1*t.height),n-=2,this._containCover=D.contain(t.width,t.height,i,n),n=Math.min(e,this._containCover.height+2),this.app.pageScaleValue=this._containCover.height/t.height,this.app.dimensions.isFixedHeight?e:n}},{key:"handleZoom",value:function(){var e=this.app,t=this.maxZoom=4,i=e.zoomValue;!0===e.pendingZoom&&null!=e.zoomDelta?i=e.zoomDelta>0?i*e.options.zoomRatio:i/e.options.zoomRatio:null!=this.lastScale&&(i*=this.lastScale,this.lastScale=null),i=D.limitAt(i,this.minZoom,t),e.zoomValueChange=i/e.zoomValue,e.zoomChanged=e.zoomValue!==i,e.zoomValue=i}},{key:"resize",value:function(){var e=this.app;e.dimensions;var t=e.dimensions.padding,i=this.shiftHeight=0;this.element.css({top:-i,bottom:-i,right:-0,left:-0,paddingTop:t.top,paddingRight:t.right,paddingBottom:t.bottom,paddingLeft:t.left});for(var n=this.getVisiblePages().main[0]-1,o=(n=this.pages[n].element[0]).getBoundingClientRect(),s=this.parentElement[0].getBoundingClientRect(),a=0;a<this.pages.length;a++){var r=this.pages[a],l=this.getViewPort(a+1,!0),h=r.element[0].style;h.height=Math.floor(l.height*e.pageScaleValue*e.zoomValue)+"px",h.width=Math.floor(l.width*e.pageScaleValue*e.zoomValue)+"px"}if(this.oldScrollHeight!=this.element[0].scrollHeight&&void 0!==this.oldScrollHeight){var u,p=this.element[0].scrollHeight/this.oldScrollHeight;this.skipScrollCheck=!0;var c=n.offsetTop+n.clientTop-(o.top-s.top+n.clientTop)*p,d=n.offsetLeft+n.clientLeft-(o.left-s.left+n.clientLeft)*p;c+=(p-1)*10/2,d+=(p-1)*10/2,this.zoomCenter=null!==(u=this.zoomCenter)&&void 0!==u?u:{x:0,y:0},c+=(p-1)*this.zoomCenter.y,d+=(p-1)*this.zoomCenter.x,this.zoomCenter=null,this.element[0].scrollTop=c,this.element[0].scrollLeft=d,this.skipScrollCheck=!1}this.oldScrollHeight=this.element[0].scrollHeight,this.scrollBar[0].style.transform="none",this.updateScrollBar()}},{key:"onReady",value:function(){this.gotoPageCallBack(),this.oldScrollHeight=this.element[0].scrollHeight}},{key:"refresh",value:function(){for(var e=this.app,t=this.getVisiblePages().main,i=0;i<t.length;i++){var n=void 0,o=t[i];n=this.pages[o-1],o!==n.pageNumber&&(n.resetTexture(),this.app.textureRequestStatus=u.REQUEST_STATUS.ON),n.element.attr("number",o),n.pageNumber=o}this.requestRefresh(!1),e.textureRequestStatus=u.REQUEST_STATUS.ON}},{key:"isAnimating",value:function(){return this.scrollStatus===u.REQUEST_STATUS.ON||this.scrollStatus===u.REQUEST_STATUS.COUNT}},{key:"checkRequestQueue",value:function(){I(A(o.prototype),"checkRequestQueue",this).call(this),this.scrollStatus===u.REQUEST_STATUS.ON&&(this.scrollStatus=u.REQUEST_STATUS.OFF),this.scrollStatus===u.REQUEST_STATUS.COUNT&&(this.scrollStatus=u.REQUEST_STATUS.ON)}},{key:"isActivePage",value:function(e){return void 0!==this.visiblePagesCache&&this.visiblePagesCache.includes(e)}},{key:"getVisiblePages",value:function(){var e=D.getVisibleElements({container:this.element[0],elements:this.wrapper[0].children});return e=0===e.length?[this.app.currentPageNumber]:e.splice(0,this.pageBufferSize),this.visiblePagesCache=e,{main:e,buffer:[]}}},{key:"getPageByNumber",value:function(e){var t=this.pages[e-1];return void 0===t&&D.log("Page Not found for: "+e),t}},{key:"onScroll",value:function(e){for(var t=this.element[0].scrollTop+this.app.dimensions.containerHeight/2,i=this.getVisiblePages().main,n=i[0],o=0;o<i.length;o++){n=i[o];var s=this.pages[n-1].element[0],a=s.offsetTop+s.clientTop;if(a<=t&&s.clientHeight+a>=t)break;if(o>0&&a>t&&s.clientHeight+a>=t){n=i[o-1];break}}this.skipScrollIntoView=!0,this.app.gotoPage(n),this.skipScrollIntoView=!1,this.updateScrollBar(),e.preventDefault&&e.preventDefault(),e.stopPropagation(),this.requestRefresh(),this.scrollStatus=u.REQUEST_STATUS.COUNT,u.handlePopup(this.element,!1)}},{key:"updateScrollBar",value:function(){var e=this.element[0];this.app.container[0],e.scrollLeft;var t=60+(e.offsetHeight-40-60-60)*e.scrollTop/(e.scrollHeight-e.offsetHeight);isNaN(t)&&(t=60),this.scrollBar.lastY=t,this.scrollBar[0].style.transform="translateY("+t+"px)"}},{key:"validatePageChange",value:function(e){}},{key:"gotoPageCallBack",value:function(){if(!0!==this.skipScrollIntoView){var e=this.getPageByNumber(this.app.currentPageNumber);null!=e&&D.scrollIntoView(e.element[0],this.element[0])}this.skipScrollIntoView=!1,this.requestRefresh()}},{key:"getTextureSize",value:function(e){var t=this.app.provider.viewPorts[1];this.app.provider.viewPorts[e.pageNumber]&&(t=this.app.provider.viewPorts[e.pageNumber]);var i=this.app.options.pixelRatio;return{height:t.height*this.app.zoomValue*this.app.pageScaleValue*i,width:t.width*this.app.zoomValue*this.app.pageScaleValue*i}}},{key:"textureLoadedCallback",value:function(e){var t=this.getPageByNumber(e.pageNumber),i=this.app,n=this.getViewPort(e.pageNumber,!0);t.element.height(Math.floor(n.height*i.pageScaleValue*i.zoomValue)).width(Math.floor(n.width*i.pageScaleValue*i.zoomValue))}},{key:"pan",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.startPoint,n=e.raw.y-i.raw.y,o=e.raw.x-i.raw.x;this.deltaPanY+=n,this.deltaPanX+=o,this.panRequestStatus=u.REQUEST_STATUS.ON,!1===t&&(this.startPoint=e)}},{key:"updatePan",value:function(){this.element[0].scrollTop=this.element[0].scrollTop-this.deltaPanY,this.element[0].scrollLeft=this.element[0].scrollLeft-this.deltaPanX,this.deltaPanY=0,this.deltaPanX=0}},{key:"mouseMove",value:function(e){if(this.startPoint&&this.isScrollBarPressed){var t=D.fixMouseEvent(e),i=this.eventToPoint(t),n=this.element[0],s=this.scrollBar.lastY-(this.startPoint.raw.y-i.raw.y);this.scrollBar.lastY=s,n.scrollTop=(s-60)*(n.scrollHeight-n.offsetHeight)/(n.offsetHeight-40-60-60),this.startPoint=i,e.preventDefault();return}e.touches&&e.touches.length<2||I(A(o.prototype),"mouseMove",this).call(this,e)}},{key:"mouseDown",value:function(e){I(A(o.prototype),"mouseDown",this).call(this,e),e.srcElement===this.scrollBar[0]&&(this.isScrollBarPressed=!0,this.scrollBar.addClass("df-active"),this.scrollPageNumber.addClass("df-active"))}},{key:"mouseUp",value:function(e){I(A(o.prototype),"mouseUp",this).call(this,e),(this.isScrollBarPressed=this.scrollBar)&&(this.isScrollBarPressed=!1,this.scrollBar.removeClass("df-active"),this.scrollPageNumber.removeClass("df-active"))}}],function(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(o.prototype,i),o}(E);function z(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function B(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function H(e,t,i){return t&&B(e.prototype,t),i&&B(e,i),e}function U(e,t,i){return(U="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=V(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(i||e):o.value}})(e,t,i||e)}function V(e){return(V=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function j(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&W(e,t)}function W(e,t){return(W=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function q(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var i,n=V(e);return i=t?Reflect.construct(n,arguments,V(this).constructor):n.apply(this,arguments),i&&("object"==(i&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)||"function"==typeof i)?i:function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(this)}}var G=u.jQuery,Z=u.utils,K=/*#__PURE__*/function(e){j(i,e);var t=q(i);function i(e,n){var o,s;return z(this,i),e.viewerClass="df-flipbook "+(e.viewerClass||""),(o=t.call(this,e,n)).isFlipBook=!0,o.sheets=[],o.isRTL=o.app.isRTL,o.foldSense=50,o.isOneSided=!1,o.stackCount=null!==(s=e.stackCount)&&void 0!==s?s:6,o.annotedPage=null,o.pendingAnnotations=[],o.seamPosition=0,o.dragSheet=null,o.drag=null,o.soundOn=!0===e.enableSound,o.soundFile=null,o.minZoom=1,o.maxZoom=4,o.pureMaxZoom=4,(o.app.options.pageSize===u.FLIPBOOK_PAGE_SIZE.AUTO||o.app.options.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_INTERNAL)&&(o.app.checkSecondPage=!0),o.app.pageScaling=u.PAGE_SCALE.PAGE_FIT,e.viewerClass="",o.zoomViewer=new X(e,n),o}return H(i,[{key:"init",value:function(){U(V(i.prototype),"init",this).call(this),this.initSound();var e=this.app;this.pageMode=e.options.pageMode===u.FLIPBOOK_PAGE_MODE.AUTO?Z.isMobile||e.pageCount<=2?u.FLIPBOOK_PAGE_MODE.SINGLE:u.FLIPBOOK_PAGE_MODE.DOUBLE:e.options.pageMode,this.singlePageMode=e.options.singlePageMode||(Z.isMobile?u.FLIPBOOK_SINGLE_PAGE_MODE.BOOKLET:u.FLIPBOOK_SINGLE_PAGE_MODE.ZOOM),this.updatePageMode(),this.rightSheetHeight=this.leftSheetHeight=this._defaultPageSize.height,this.leftSheetWidth=this.rightSheetWidth=this._defaultPageSize.width,this.leftSheetTop=this.rightSheetTop=(this.availablePageHeight()-this._defaultPageSize.height)/2,this.zoomViewer.rightSheetHeight=this.zoomViewer.leftSheetHeight=this._defaultPageSize.height,this.zoomViewer.leftSheetWidth=this.zoomViewer.rightSheetWidth=this._defaultPageSize.width}},{key:"determineHeight",value:function(){}},{key:"initCustomControls",value:function(){U(V(i.prototype),"initCustomControls",this).call(this);var e=this,t=this.app,n=t.ui,o=n.controls,s=t.options.text,a=t.options.icons;o.sound=Z.createBtn("sound",a.sound,s.toggleSound).on("click",function(){e.soundOn=!e.soundOn,n.updateSound()}),n.updateSound=function(){!1===e.soundOn?o.sound.addClass("disabled"):o.sound.removeClass("disabled")},n.updateSound()}},{key:"dispose",value:function(){U(V(i.prototype),"dispose",this).call(this);for(var e=0;e<this.sheets.length;e++){var t=this.sheets[e];t&&t.currentTween&&(t.currentTween.stop(),t.currentTween=null)}this.zoomViewer.dispose(),this.soundFile=null}},{key:"determinePageMode",value:function(){var e=this.app,t=this.pageMode;if(this.app.pageCount<3)this.pageMode=u.FLIPBOOK_PAGE_MODE.SINGLE;else if(this.app.options.pageMode===u.FLIPBOOK_PAGE_MODE.AUTO&&!0!=this.pageModeChangedManually){if(!0===Z.isMobile){if(this.app.dimensions.isAutoHeight&&!1==this.app.dimensions.isFixedHeight){var i=this._calculateInnerHeight(!0),n=this._calculateInnerHeight(!1),o=e.dimensions.stage.innerWidth+(!0!=e.options.sideMenuOverlay&&e.isSideMenuOpen?220:0);this.pageMode=i>1.1*n&&o<768?u.FLIPBOOK_PAGE_MODE.SINGLE:u.FLIPBOOK_PAGE_MODE.DOUBLE,this._calculateInnerHeight()}else{var s=e.dimensions.stage.innerWidth+(!0!=e.options.sideMenuOverlay&&e.isSideMenuOpen?220:0);this.pageMode=e.dimensions.stage.innerHeight>1.1*s&&s<768?u.FLIPBOOK_PAGE_MODE.SINGLE:u.FLIPBOOK_PAGE_MODE.DOUBLE}}this.pageMode!=t&&this.setPageMode({isSingle:this.pageMode==u.FLIPBOOK_PAGE_MODE.SINGLE})}}},{key:"initSound",value:function(){this.soundFile=document.createElement("audio"),this.soundFile.setAttribute("src",this.app.options.soundFile+"?ver="+u.version),this.soundFile.setAttribute("type","audio/mpeg")}},{key:"playSound",value:function(){try{!0===this.app.userHasInteracted&&!0===this.soundOn&&(this.soundFile.currentTime=0,this.soundFile.play())}catch(e){}}},{key:"checkDocumentPageSizes",value:function(){var e=this.app.provider;e.pageSize===u.FLIPBOOK_PAGE_SIZE.AUTO&&(e._page2Ratio&&e._page2Ratio>1.5*e.defaultPage.pageRatio?e.pageSize=u.FLIPBOOK_PAGE_SIZE.DOUBLE_INTERNAL:e.pageSize=u.FLIPBOOK_PAGE_SIZE.SINGLE),e.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_INTERNAL&&(e.pageCount=1===e.numPages?1:2*e.numPages-2),(e.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_COVER_BACK||e.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE)&&(e.pageCount=2*e.numPages)}},{key:"getViewerPageNumber",value:function(e){return this.app.provider.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_INTERNAL&&e>2&&(e=2*e-1),this.app.provider.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_COVER_BACK&&e>2&&(e=2*e-1),e}},{key:"getDocumentPageNumber",value:function(e){return this.app.provider.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_INTERNAL&&e>2?Math.ceil((e-1)/2)+1:this.app.provider.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_COVER_BACK&&e>1?e===this.app.pageCount?1:Math.ceil((e-1)/2)+1:e}},{key:"getViewPort",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2?arguments[2]:void 0;return this.filterViewPort(U(V(i.prototype),"getViewPort",this).call(this,e,t),e,n)}},{key:"isDoubleInternal",value:function(){return this.app.provider.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_INTERNAL}},{key:"isDoubleCoverBack",value:function(){return this.app.provider.pageSize===u.FLIPBOOK_PAGE_SIZE.DOUBLE_COVER_BACK}},{key:"isDoubleInternalPage",value:function(e){return this.isDoubleInternal()&&e>1&&e<this.app.provider.pageCount}},{key:"getDoublePageWidthFix",value:function(e){return this.isDoubleInternalPage(e)||this.isDoubleCoverBack()?2:1}},{key:"isDoublePageFix",value:function(e){var t=!1;return(this.isDoubleCoverBack()||this.isDoubleInternalPage(e))&&(this.app.isRTL?e%2==0&&(t=!0):e%2==1&&(t=!0)),t}},{key:"finalizeAnnotations",value:function(e,t){}},{key:"finalizeTextContent",value:function(e,t){this.app.zoomValue>this.app.viewer.pureMaxZoom&&(this.zoomViewer.leftViewPort&&this.zoomViewer.leftPage.contentLayer[0].style.setProperty("--scale-factor",this.zoomViewer.leftSheetHeight/this.zoomViewer.leftViewPort.height),this.zoomViewer.rightViewPort&&this.zoomViewer.rightPage.contentLayer[0].style.setProperty("--scale-factor",this.zoomViewer.rightSheetHeight/this.zoomViewer.rightViewPort.height))}},{key:"isActivePage",value:function(e){return void 0!==this.visiblePagesCache&&this.visiblePagesCache.includes(e)}},{key:"isSheetCover",value:function(e){var t=this.isBooklet;return 0===e||t&&1===e||e===Math.ceil(this.app.pageCount/(t?1:2))-(t?0:1)}},{key:"isSheetHard",value:function(e){var t=this.app.options.flipbookHardPages;if(this.isBooklet,"cover"===t)return this.isSheetCover(e);if("all"===t)return!0;var i=(","+t+",").indexOf(","+(2*e+1)+",")>-1,n=(","+t+",").indexOf(","+(2*e+2)+",")>-1;return i||n}},{key:"sheetsIndexShift",value:function(e,t,i){e>t?(this.sheets[i-1].skipFlip=!0,this.sheets.unshift(this.sheets.pop())):e<t&&(this.sheets[0].skipFlip=!0,this.sheets.push(this.sheets.shift()))}},{key:"checkSwipe",value:function(e,t){if(!0!==this.pinchZoomDirty&&1===this.app.zoomValue&&!0===this.canSwipe){var i="vertical"==this.orientation?e.y-this.lastPosY:e.x-this.lastPosX;Math.abs(i)>this.swipeThreshold&&(i<0?this.app.openRight():this.app.openLeft(),this.canSwipe=!1,t.preventDefault()),this.lastPosX=e.x,this.lastPosY=e.y}}},{key:"checkCenter",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.app,n=u.FLIPBOOK_CENTER_SHIFT,o=i.currentPageNumber%2==0,s=this.getBasePage(),a=this.isRTL,r=this.isSingle;e=0===s||this.isBooklet?this.isRTL?n.RIGHT:n.LEFT:s===i.pageCount?a?n.LEFT:n.RIGHT:r?a?o?n.LEFT:n.RIGHT:o?n.RIGHT:n.LEFT:n.NONE,!0!==this.centerNeedsUpdate&&(this.centerNeedsUpdate=this.centerShift!==e),this.centerNeedsUpdate&&(this.centerShift=e,this.updateCenter(t),this.centerNeedsUpdate=!1)}},{key:"updateCenter",value:function(){console.log("UpdateCenter: missing implementation.")}},{key:"reset",value:function(){for(var e,t=0;t<this.sheets.length;t++)(e=this.sheets[t]).reset(),e.pageNumber=-1,e.frontPage&&(e.frontPage.pageNumber=-1),e.backPage&&(e.backPage.pageNumber=-1),e.resetTexture();this.annotedPage=null,this.oldBasePageNumber=-1,this.centerShift=null,this.refresh()}},{key:"handleZoom",value:function(){var e=this.app;e.dimensions;var t=this.getLeftPageTextureSize({zoom:!1,isAnnotation:!0}),i=this.getRightPageTextureSize({zoom:!1,isAnnotation:!0});this.maxZoom,e.zoomValue,this.pureMaxZoom=e.dimensions.maxTextureSize/Math.max(t.height,t.width,i.height,i.width);var n=this.maxZoom=e.options.fakeZoom*this.pureMaxZoom,o=e.zoomValue,s=!1,a=!1;n<this.minZoom&&(n=this.maxZoom=this.minZoom),!0===e.pendingZoom&&null!=e.zoomDelta?o=e.zoomDelta>0?o*e.options.zoomRatio:o/e.options.zoomRatio:null!=this.lastScale&&(o*=this.lastScale,this.lastScale=null),o=Z.limitAt(o,this.minZoom,n),e.zoomValueChange=o/e.zoomValue,(e.zoomChanged=e.zoomValue!==o)&&(1===o||1===e.zoomValue)&&(s=1===o,a=1===e.zoomValue),e.zoomValue=o,(a||s)&&(e.container.toggleClass("df-zoom-active",1!==o),a&&this.enterZoom(),s&&this.exitZoom())}},{key:"refresh",value:function(){var e=this.app,t=this.stackCount,i=this.isRTL,n=this.isBooklet,o=this.getBasePage(),s=n?1:2;i&&(o=e.pageCount-o);var a,r=this.oldBasePageNumber,l=Math.ceil(e.pageCount/s),h=Math.floor(t/2);o!==this.oldBasePageNumber&&(this.pageNumberChanged=!0,this.updatePendingStatusClass(!0),this.zoomViewer.reset()),this.sheetsIndexShift(r,o,t);var p=Math.ceil(o/s);for(a=0;a<t;a++){var c=void 0,d=p-h+a;if(i&&(d=l-d-1),null!=(c=this.sheets[a])){c.targetSide=a<h?u.TURN_DIRECTION.LEFT:u.TURN_DIRECTION.RIGHT;var f=c.side!==c.targetSide,g=d!==c.pageNumber,v=f&&!1===c.skipFlip&&1===e.zoomValue;!f&&g&&c.isFlipping&&c.currentTween&&c.currentTween.stop(),c.isHard=this.isSheetHard(d),c.isCover=this.isSheetCover(d),g&&(c.resetTexture(),(this.app.isRTL?c.backPage:c.frontPage).pageNumber=this.isBooklet?d:2*d+1,(this.app.isRTL?c.frontPage:c.backPage).pageNumber=this.isBooklet?-1:2*d+2,e.textureRequestStatus=u.REQUEST_STATUS.ON),c.pageNumber=d,this.refreshSheet({sheet:c,sheetNumber:d,totalSheets:l,zIndex:this.stackCount+(a<h?a-h:h-a),visible:n?i?a<h||c.isFlipping||v:a>=h||c.isFlipping||v:d>=0&&d<l||n&&d===l,index:a,needsFlip:v,midPoint:h})}}this.requestRefresh(!1),e.textureRequestStatus=u.REQUEST_STATUS.ON,this.oldBasePageNumber=o,this.checkCenter(),this.zoomViewer.refresh(),this.pageNumberChanged=!1}},{key:"validatePageChange",value:function(e){if(e===this.app.currentPageNumber)return!1;var t=this.app,i=!this.isFlipping()||void 0===t.oldPageNumber;return(i=i||t.currentPageNumber<e&&t.oldPageNumber<t.currentPageNumber)||t.currentPageNumber>e&&t.oldPageNumber>t.currentPageNumber}},{key:"getVisiblePages",value:function(){for(var e=[],t=this.getBasePage(),i=this.app.zoomValue>1?1:this.isBooklet&&Z.isMobile?Math.min(this.stackCount/2,2):this.stackCount/2,n=0;n<i;n++)e.push(t-n),e.push(t+n+1);return this.visiblePagesCache=e,{main:e,buffer:[]}}},{key:"getBasePage",value:function(e){return(void 0===e&&(e=this.app.currentPageNumber),this.isBooklet)?e:2*Math.floor(e/2)}},{key:"getRightPageNumber",value:function(){return this.getBasePage()+(this.isBooklet?0:this.isRTL?0:1)}},{key:"getLeftPageNumber",value:function(){return this.getBasePage()+(this.isBooklet?0:this.isRTL?1:0)}},{key:"afterFlip",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0],!0!==this.isAnimating()&&(this.pagesReady(),this.updatePendingStatusClass())}},{key:"isFlipping",value:function(){var e=!1;return this.sheets.forEach(function(t){!0===t.isFlipping&&(e=!0)}),e}},{key:"isAnimating",value:function(){return this.isFlipping()}},{key:"mouseWheel",value:function(e){this.app.options.mouseScrollAction===u.MOUSE_SCROLL_ACTIONS.ZOOM?this.zoomViewer.mouseWheel(e):U(V(i.prototype),"mouseWheel",this).call(this,e)}},{key:"checkRequestQueue",value:function(){this.app.zoomValue>1?this.zoomViewer.checkRequestQueue():U(V(i.prototype),"checkRequestQueue",this).call(this)}},{key:"updatePan",value:function(){}},{key:"resetPageTween",value:function(){}},{key:"gotoPageCallBack",value:function(){this.resetPageTween(),1!==this.app.zoomValue&&!0===this.app.options.resetZoomBeforeFlip&&this.app.resetZoom(),this.beforeFlip(),this.requestRefresh()}},{key:"beforeFlip",value:function(){this.app.executeCallback("beforeFlip"),1===this.app.zoomValue&&this.playSound()}},{key:"onFlip",value:function(){this.app.executeCallback("onFlip")}},{key:"getAnnotationElement",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=void 0;return(o=this.app.zoomValue>1||!0===n?this.zoomViewer.getAnnotationElement(e,t):U(V(i.prototype),"getAnnotationElement",this).call(this,e,t))&&(o.parentNode.classList.toggle("df-double-internal",this.isDoubleInternalPage(e)),o.parentNode.classList.toggle("df-double-internal-fix",this.isDoublePageFix(e))),o}},{key:"getTextElement",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.app.zoomValue>1||!0===n?this.zoomViewer.getTextElement(e,t):U(V(i.prototype),"getTextElement",this).call(this,e,t)}},{key:"enterZoom",value:function(){this.exchangeTexture(this,this.zoomViewer)}},{key:"exitZoom",value:function(){this.exchangeTexture(this.zoomViewer,this)}},{key:"exchangeTexture",value:function(e,t){var i=this.getBasePage(),n=e.getPageByNumber(i),o=t.getPageByNumber(i);if(o&&"-1"===o.textureStamp?(o.textureStamp=n.textureStamp,o.loadTexture({texture:n.getTexture(!0)}),Z.log("Texture Exchanging at "+i)):Z.log("Texture Exchanging Bypassed at "+i),!this.isBooklet){var s=e.getPageByNumber(i+1),a=t.getPageByNumber(i+1);a&&"-1"===a.textureStamp?(a.textureStamp=s.textureStamp,a.loadTexture({texture:s.getTexture(!0)}),Z.log("Texture Exchanging at "+(i+1))):Z.log("Texture Exchanging Bypassed at "+(i+1))}t.pagesReady()}},{key:"setPageMode",value:function(e){var t=this.app,i=!0===e.isSingle;this.pageMode=i?u.FLIPBOOK_PAGE_MODE.SINGLE:u.FLIPBOOK_PAGE_MODE.DOUBLE,this.updatePageMode(),t.resizeRequestStart(),t.viewer.pageMode===u.FLIPBOOK_PAGE_MODE.DOUBLE&&t.ui.controls.pageMode?t.ui.controls.pageMode.removeClass(t.options.icons.doublepage).addClass(t.options.icons.singlepage).attr("title",t.options.text.singlePageMode).html("<span>"+t.options.text.singlePageMode+"</span>"):t.ui.controls.pageMode.addClass(t.options.icons.doublepage).removeClass(t.options.icons.singlepage).attr("title",t.options.text.doublePageMode).html("<span>"+t.options.text.doublePageMode+"</span>")}},{key:"updatePageMode",value:function(){this.app.pageCount<3&&(this.pageMode=u.FLIPBOOK_PAGE_MODE.SINGLE),this.isSingle=this.pageMode===u.FLIPBOOK_PAGE_MODE.SINGLE,this.isBooklet=this.isSingle&&this.singlePageMode===u.FLIPBOOK_SINGLE_PAGE_MODE.BOOKLET,this.app.jumpStep=this.isSingle?1:2,this.totalSheets=Math.ceil(this.app.pageCount/(this.isBooklet?1:2)),this.sheets.length>0&&this.reset()}},{key:"setPage",value:function(e){return e.textureTarget===u.TEXTURE_TARGET.ZOOM?this.zoomViewer.setPage(e):U(V(i.prototype),"setPage",this).call(this,e)}},{key:"_calculateInnerHeight",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;void 0===e&&(e=this.pageMode===u.FLIPBOOK_PAGE_MODE.SINGLE);var t=this.app.dimensions.defaultPage.viewPort,i=this.availablePageWidth(!1,!0,e),n=this.app.dimensions.maxHeight-this.app.dimensions.padding.height;"vertical"==this.orientation&&!1==e&&(n/=2),this._defaultPageSize=Z.contain(t.width,t.height,i,n),this._pageFitArea={width:i,height:n};var o=this.app.dimensions.isFixedHeight?n:this._pageFitArea.height;return this.app.dimensions.isAutoHeight&&!1==this.app.dimensions.isFixedHeight&&(o=Math.floor(this._defaultPageSize.height)),o}},{key:"_getInnerHeight",value:function(){var e=this._calculateInnerHeight();return this.app.dimensions.stage.width=this.app.dimensions.stage.innerWidth+this.app.dimensions.padding.width,this.app.dimensions.stage.height=e+this.app.dimensions.padding.height,e}},{key:"availablePageWidth",value:function(){var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;void 0===i&&(i=this.pageMode===u.FLIPBOOK_PAGE_MODE.SINGLE);var n=!0===t?this.app.dimensions.offset.width:0,o=this.app.dimensions.stage.innerWidth+n;return Math.floor((o/=!0===i||"vertical"==this.orientation?1:2)*(e?this.app.zoomValue:1))}},{key:"availablePageHeight",value:function(){var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;void 0===t&&(t=this.pageMode===u.FLIPBOOK_PAGE_MODE.SINGLE);var i=this.app.dimensions.stage.innerHeight;return!1===t&&"vertical"==this.orientation&&(i/=2),Math.floor(i*(e?this.app.zoomValue:1))}},{key:"getTextureSize",value:function(e){var t=this.getViewPort(e.pageNumber,!0),i=this.app.options.pixelRatio,n=Z.contain(t.width,t.height,i*this.availablePageWidth(e.zoom),i*this.availablePageHeight(e.zoom));return{height:(n=Z.containUnStretched(n.width,n.height,this.app.options.maxTextureSize,this.app.options.maxTextureSize)).height,width:n.width}}},{key:"getLeftPageTextureSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.pageNumber=this.getLeftPageNumber(),this.getTextureSize(e)}},{key:"getRightPageTextureSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.pageNumber=this.getRightPageNumber(),this.getTextureSize(e)}},{key:"filterViewPort",value:function(e,t){var i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(void 0!==e){if(!0!=i)return e;var n=e.clone();return n.width=n.width/this.getDoublePageWidthFix(t),n}}},{key:"filterViewPortCanvas",value:function(e,t,i){this.isDoublePageFix(i)&&(e.transform[4]=e.transform[4]-Math.floor(Math.min(t.width,2*e.width-t.width))),e.widthFix=this.isDoubleInternalPage(i)?2:1}},{key:"isClosedPage",value:function(e){return void 0===e&&(e=this.app.currentPageNumber),1===e||e===this.app.jumpStep*Math.ceil(this.app.pageCount/this.app.jumpStep)&&!this.isBooklet}},{key:"isLeftPage",value:function(e){return(void 0===e&&(e=this.app.currentPageNumber),this.isBooklet)?this.isRTL:e%2==(this.isRTL?1:0)}},{key:"cleanPage",value:function(e){if(!this.isDoubleInternalPage(e))return U(V(i.prototype),"cleanPage",this).call(this,e);var t=e+(e%2==1?-1:1);return!1===this.app.provider.requestedPages[e]&&!1===this.app.provider.requestedPages[t]}},{key:"onReady",value:function(){U(V(i.prototype),"onReady",this).call(this)}},{key:"searchPage",value:function(e){return{include:!this.isDoublePageFix(e),label:this.app.provider.getLabelforPage(e)+(this.isDoubleInternalPage(e)?"-"+this.app.provider.getLabelforPage(e+1):"")}}}]),i}(E),X=/*#__PURE__*/function(e){j(i,e);var t=q(i);function i(e,n){var o;return z(this,i),e.viewerClass="df-zoomview "+(e.viewerClass||""),(o=t.call(this,e,n)).viewer=o.app.viewer,o.events={},o.init(),o.initEvents(),o.left=0,o.top=0,o}return H(i,[{key:"init",value:function(){this.leftPage=new _,this.rightPage=new _,this.pages.push(this.leftPage),this.pages.push(this.rightPage),this.leftPage.element.addClass("df-page-back"),this.rightPage.element.addClass("df-page-front"),this.wrapper.append(this.leftPage.element),this.wrapper.append(this.rightPage.element),this.bookShadow=G("<div>",{class:"df-book-shadow"}),this.wrapper.append(this.bookShadow),this.wrapper.addClass("df-sheet")}},{key:"initEvents",value:function(){this.stageDOM=this.element[0],U(V(i.prototype),"initEvents",this).call(this)}},{key:"dispose",value:function(){this.element.remove()}},{key:"resize",value:function(){var e=this.app.dimensions,t=e.padding,i=this.app.viewer.availablePageHeight(),n=this.app.viewer.availablePageWidth(),o=this.fullWidth=n*(this.app.viewer.pageMode===u.FLIPBOOK_PAGE_MODE.SINGLE?1:2),s=e.stage.innerWidth,a=e.stage.innerHeight,r=this.shiftHeight=Math.ceil(Z.limitAt((i-a)/2,0,i)),l=this.shiftWidth=Math.ceil(Z.limitAt((o-s)/2,0,o));1===this.app.zoomValue&&(this.left=0,this.top=0),this.element.css({top:-r,bottom:-r,right:-l,left:-l,paddingTop:t.top,paddingRight:t.right,paddingBottom:t.bottom,paddingLeft:t.left,transform:"translate3d("+this.left+"px,"+this.top+"px,0)"}),this.wrapper.css({width:o,height:i,marginTop:e.height-i-t.height>0?(e.height-t.height-i)/2:0}),this.wrapper.height(i).width(o-o%2),!0===this.app.pendingZoom&&this.zoom(),this.app.viewer.annotedPage=null,this.pagesReady()}},{key:"zoom",value:function(){var e=this.app;if(e.zoomChanged){var t=e.dimensions.origin,i=e.zoomValueChange;if(1===e.zoomValue)this.left=0,this.top=0;else{this.left*=i,this.top*=i,e.viewer.zoomCenter||(e.viewer.zoomCenter={x:t.x,y:t.y});var n={raw:e.viewer.zoomCenter},o={raw:{}},s=(n.raw.x-t.x)*i,a=(n.raw.y-t.y)*i;o.raw.x=t.x+s,o.raw.y=t.y+a,this.startPoint=o,this.pan(n),this.startPoint=null}}e.viewer.zoomCenter=null}},{key:"reset",value:function(){this.leftPage.resetTexture(),this.rightPage.resetTexture()}},{key:"refresh",value:function(){var e=this.app,t=e.viewer,i=t.getBasePage(),n=t.isBooklet?!e.isRTL:e.isRTL,o=n?this.rightPage:this.leftPage,s=n?this.leftPage:this.rightPage;o.pageNumber=i,s.pageNumber=i+1,o.updateCSS({display:0===i?"none":"block"}),s.updateCSS({display:s.pageNumber>e.pageCount||t.isBooklet?"none":"block"})}},{key:"updateCenter",value:function(){if(null!==this&&null!==this.app.viewer){var e=this.app.viewer.centerShift,t=this.app.viewer.isRTL,i=!t&&this.app.currentPageNumber>1||t&&this.app.currentPageNumber<this.app.pageCount?this.leftSheetWidth:this.rightSheetWidth;this.wrapper[0].style.left=e*i/2+"px"}}},{key:"isDoubleInternalPage",value:function(e){return this.app.viewer.isDoubleInternalPage(e)}},{key:"pagesReady",value:function(){if(!this.app.viewer.isFlipping()&&(1!==this.app.zoomValue&&this.app.viewer.updatePendingStatusClass(!1),!1===this.app.options.flipbookFitPages)){var e=this.app.viewer.getBasePage(),t=this.leftViewPort=this.app.viewer.getViewPort(e+(this.app.viewer.isBooklet?0:this.app.viewer.isRTL?1:0)),i=this.rightViewPort=this.app.viewer.getViewPort(e+(this.app.viewer.isBooklet?0:this.app.viewer.isRTL?0:1));if(t){var n=Z.contain(t.width,t.height,this.app.viewer.availablePageWidth(),this.app.viewer.availablePageHeight());this.leftSheetWidth=Math.floor(n.width),this.leftSheetHeight=Math.floor(n.height),this.leftSheetTop=(this.app.viewer.availablePageHeight()-this.leftSheetHeight)/2,this.app.zoomValue>this.app.viewer.pureMaxZoom&&this.leftPage.contentLayer[0].style.setProperty("--scale-factor",this.leftSheetHeight/t.height)}if(i){var o=Z.contain(i.width,i.height,this.app.viewer.availablePageWidth(),this.app.viewer.availablePageHeight());this.rightSheetWidth=Math.floor(o.width),this.rightSheetHeight=Math.floor(o.height),this.rightSheetTop=(this.app.viewer.availablePageHeight()-this.rightSheetHeight)/2,this.app.zoomValue>this.app.viewer.pureMaxZoom&&this.rightPage.contentLayer[0].style.setProperty("--scale-factor",this.rightSheetHeight/i.height)}(void 0!=t||void 0!=i)&&(this.totalSheetsWidth=this.leftSheetWidth+this.rightSheetWidth,this.leftPage.element.height(Math.floor(this.leftSheetHeight)).width(Math.floor(this.leftSheetWidth)).css({transform:"translateY("+Math.floor(this.leftSheetTop)+"px)"}),this.rightPage.element.height(Math.floor(this.rightSheetHeight)).width(Math.floor(this.rightSheetWidth)).css({transform:"translateY("+Math.floor(this.rightSheetTop)+"px)"}))}}},{key:"textureLoadedCallback",value:function(e){this.getPageByNumber(e.pageNumber),this.pagesReady()}}]),i}(E),Q=/*#__PURE__*/function(){function e(t){z(this,e),this.parentElement=t.parentElement,this.isFlipping=!1,this.isOneSided=!1,this.viewer=t.viewer,this.frontPage=null,this.backPage=null,this.pageNumber=void 0,this.animateToReset=null}return H(e,[{key:"init",value:function(){}},{key:"flip",value:function(){}},{key:"frontImage",value:function(e){this.frontPage.loadTexture({texture:e.texture,callback:e.callback})}},{key:"backImage",value:function(e){this.backPage.loadTexture({texture:e.texture,callback:e.callback})}},{key:"resetTexture",value:function(){this.frontPage.resetTexture(),this.backPage.resetTexture()}},{key:"reset",value:function(){this.animateToReset=null,this.isFlipping=!1,this.currentTween=null,this.pendingPoint=null,this.magnetic=!1,this.skipFlip=!0,this.animateToReset=null,this.viewer.dragPage=null,this.viewer.flipPage=null,this.viewer.corner=u.TURN_CORNER.NONE}}]),e}();function Y(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function J(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function $(e,t,i){return t&&J(e.prototype,t),i&&J(e,i),e}function ee(e,t,i){return(ee="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=et(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(i||e):o.value}})(e,t,i||e)}function et(e){return(et=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ei(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&en(e,t)}function en(e,t){return(en=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function eo(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var i,n=et(e);return i=t?Reflect.construct(n,arguments,et(this).constructor):n.apply(this,arguments),i&&("object"==(i&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)||"function"==typeof i)?i:function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(this)}}var es=u.jQuery,ea=u.utils,er=/*#__PURE__*/function(e){ei(i,e);var t=eo(i);function i(e){var n;return Y(this,i),(n=t.call(this,e)).init(),n}return $(i,[{key:"init",value:function(){var e="<div>",t=this.element=es(e,{class:"df-sheet"}),i=this.frontPage=new _;i.element.addClass("df-page-front");var n=this.backPage=new _;n.element.addClass("df-page-back");var o=this.wrapper=es(e,{class:"df-sheet-wrapper"}),s=this.foldInnerShadow=es(e,{class:"df-sheet-fold-inner-shadow"}),a=this.foldOuterShadow=es(e,{class:"df-sheet-fold-outer-shadow"});this.parentElement.append(t),t.append(o),t.append(a),o.append(i.element),o.append(n.element),o.append(s)}},{key:"updateCSS",value:function(e){this.element.css(e)}},{key:"resetCSS",value:function(){this.wrapper.css({transform:""}),this.frontPage.resetCSS(),this.backPage.resetCSS()}},{key:"updateSize",value:function(e,t,i){e=Math.floor(e),t=Math.floor(t),i=Math.floor(i),this.wrapper[0].style.height=this.wrapper[0].style.width=Math.ceil(ea.distOrigin(e,t)*this.viewer.app.zoomValue)+"px",this.element[0].style.height=this.frontPage.element[0].style.height=this.backPage.element[0].style.height=this.foldInnerShadow[0].style.height=t+"px",this.element[0].style.width=this.frontPage.element[0].style.width=this.backPage.element[0].style.width=this.foldInnerShadow[0].style.width=e+"px",this.element[0].style.transform="translateY("+i+"px)"}},{key:"flip",value:function(e){var t=this;if(e=e||t.pendingPoint,null!=t&&null!=t.viewer){t.isFlipping=!0,t.viewer.flipPage=t;var i,n,o=t.viewer.isBooklet,s=t.side===u.TURN_DIRECTION.RIGHT,a=t.viewer.isRTL,r=t.viewer.corner===u.TURN_CORNER.BL||t.viewer.corner===u.TURN_CORNER.BR?t.element.height():0,l=t.viewer.leftSheetWidth+t.viewer.rightSheetWidth,h=0;n=t.end=t&&!0===t.animateToReset?{x:s?l:0,y:r}:{x:s?0:l,y:r},t.flipEasing=t.isHard?TWEEN.Easing.Quadratic.InOut:TWEEN.Easing.Linear.None;var p=t.viewer.app.options.duration;!0===t.isHard?(null!=e&&(h=ea.angleByDistance(e.distance,e.fullWidth)),i=t.init={angle:h*(s?-1:1)},n=t.end=t&&!0===t.animateToReset?{angle:s?0:-0}:{angle:s?-180:180}):null==e?i=t.init=t&&!0===t.animateToReset?{x:s?0:l,y:0}:{x:s?l:0,y:0}:(i=t.init={x:e.x,y:e.y,opacity:1},p=t.viewer.app.options.duration*ea.distPoints(i.x,i.y,n.x,n.y)/t.viewer.fullWidth,p=ea.limitAt(p,t.viewer.app.options.duration/3,t.viewer.duration)),i.index=0,n.index=1,t.isFlipping=!0,o&&(!s&&!a||s&&a)&&(t.element[0].style.opacity=0),!0===t.isHard?t.currentTween=new TWEEN.Tween(i).delay(0).to(n,t.viewer.app.options.duration).onUpdate(function(){t.updateTween(this)}).easing(t.flipEasing).onComplete(t.completeTween.bind(t)).start():null==e?t.currentTween=new TWEEN.Tween(i).delay(0).to(n,t.viewer.app.options.duration).onUpdate(function(){t.updateTween(this)}).easing(TWEEN.Easing.Sinusoidal.Out).onComplete(t.completeTween.bind(t)).start():t.currentTween=new TWEEN.Tween(i).delay(0).to(n,p).onUpdate(function(){t.updateTween(this)}).easing(TWEEN.Easing.Sinusoidal.Out).onComplete(t.completeTween.bind(t)).start()}}},{key:"updatePoint",value:function(e){if(null!=e){var t=this.element.width(),i=this.element.height(),n=this.viewer.corner!==u.TURN_CORNER.NONE?this.viewer.corner:e.corner,o=u.TURN_CORNER,s=this.side===u.TURN_DIRECTION.RIGHT,a=n===o.BL||n===o.BR;e.rx=!0===s?this.viewer.leftSheetWidth+t-e.x:e.x,e.ry=!0===a?i-e.y:e.y;var r=Math.atan2(e.ry,e.rx);r=Math.PI/2-ea.limitAt(r,0,ea.toRad(90));var l=t-e.rx/2,h=e.ry/2,p=Math.max(0,Math.sin(r-Math.atan2(h,l))*ea.distOrigin(l,h)),c=.5*ea.distOrigin(e.rx,e.ry),d=Math.ceil(t-p*Math.sin(r)),f=Math.ceil(p*Math.cos(r)),g=ea.toDeg(r),v=a?s?180+(90-g):180+g:s?g:90-g,m=a?s?180+(90-g):g:s?g+180:v,y=a?s?90-g:g+90:s?v-90:v+180,b=s?t-d:d,w=a?i+f:-f,P=s?-d:d-t,S=a?-i-f:f,E=ea.limitAt(.5*e.distance/t,0,.5),x=ea.limitAt((this.viewer.leftSheetWidth+t-e.rx)*.5/t,.05,.3);this.element.addClass("df-folding");var C=s?this.backPage.element:this.frontPage.element,T=s?this.frontPage.element:this.backPage.element,k=this.foldOuterShadow,O=this.foldInnerShadow;this.wrapper.css({transform:ea.translateStr(b,w)+ea.rotateStr(v)}),T.css({transform:ea.rotateStr(-v)+ea.translateStr(-b,-w)}),C.css({transform:ea.rotateStr(m)+ea.translateStr(P,S),boxShadow:"rgba(0, 0, 0, "+E+") 0px 0px 20px"}),O.css({transform:ea.rotateStr(m)+ea.translateStr(P,S),opacity:x/2,backgroundImage:ea.prefix.css+"linear-gradient( "+y+"deg, rgba(0, 0, 0, 0.25) , rgb(0, 0, 0) "+.7*c+"px, rgb(255, 255, 255) "+c+"px)"}),k.css({opacity:x/2,left:s?"auto":0,right:s?0:"auto",backgroundImage:ea.prefix.css+"linear-gradient( "+(-y+180)+"deg, rgba(0, 0, 0,0) "+c/3+"px, rgb(0, 0, 0) "+c+"px)"})}}},{key:"updateAngle",value:function(e,t){var i=5*this.element.width();this.wrapper.css({perspective:i,perspectiveOrigin:!0===t?"0% 50%":"100% 50%"}),this.element.addClass("df-folding"),this.backPage.updateCSS({display:!0===t?e<=-90?"block":"none":e<90?"block":"none",transform:("MfS"!==ea.prefix.dom?"":"perspective("+i+"px) ")+(!0===t?"translateX(-100%) ":"")+"rotateY("+((!0===t?180:0)+e)+"deg)"}),this.frontPage.updateCSS({display:!0===t?e>-90?"block":"none":e>=90?"block":"none",transform:("MSd"!==ea.prefix.dom?"":"perspective("+i+"px) ")+(!1===t?"translateX(100%) ":"")+"rotateY("+((!1===t?-180:0)+e)+"deg)"})}},{key:"updateTween",value:function(e){var t=this.viewer.isBooklet,i=this.side===u.TURN_DIRECTION.RIGHT,n=this.viewer.isRTL,o=!0===this.animateToReset;!0===this.isHard?(this.updateAngle(e.angle,i),this.angle=e.angle):(this.updatePoint({x:e.x,y:e.y}),this.x=e.x,this.y=e.y),t&&!o&&(this.element[0].style.opacity=i&&!n||!i&&n?e.index>.5?2*(1-e.index):1:e.index<.5?2*e.index:1)}},{key:"completeTween",value:function(){!0===this.isHard?(this.updateAngle(this.end.angle),this.backPage.element.css({display:"block"}),this.frontPage.element.css({display:"block"})):this.updatePoint({x:this.end.x,y:this.end.y}),this.element[0].style.opacity=1,!0!==this.animateToReset&&(this.side=this.targetSide),this.reset(),this.viewer.onFlip(),this.viewer.afterFlip(),this.viewer.requestRefresh()}}]),i}(Q),el=/*#__PURE__*/function(e){ei(i,e);var t=eo(i);function i(e,n){var o,s;return Y(this,i),e.viewerClass=null!==(s=e.viewerClass)&&void 0!==s?s:"df-flipbook-2d",e.skipViewerLoaded=!0,(o=t.call(this,e,n)).bookShadow=es("<div>",{class:"df-book-shadow"}),o.wrapper.append(o.bookShadow),o.corner=u.TURN_CORNER.NONE,n._viewerPrepared(),o}return $(i,[{key:"init",value:function(){ee(et(i.prototype),"init",this).call(this),this.initEvents(),this.initPages()}},{key:"initEvents",value:function(){this.stageDOM=this.element[0],ee(et(i.prototype),"initEvents",this).call(this)}},{key:"dispose",value:function(){ee(et(i.prototype),"dispose",this).call(this),this.element.remove()}},{key:"initPages",value:function(){for(var e=0;e<this.stackCount;e++){var t=new er({parentElement:this.wrapper});t.index=e,t.viewer=this,this.sheets.push(t),this.pages.push(t.frontPage),this.pages.push(t.backPage)}}},{key:"resize",value:function(){ee(et(i.prototype),"resize",this).call(this);var e=this.app.dimensions,t=e.padding,n=this.availablePageHeight(),o=this.availablePageWidth(),s=this.fullWidth=2*o,a=e.width,r=e.height,l=this.shiftHeight=Math.ceil(ea.limitAt((n-r+t.height)/2,0,n)),h=this.shiftWidth=Math.ceil(ea.limitAt((s-a+t.width)/2,0,s));1===this.app.zoomValue&&(this.left=0,this.top=0),this.element.css({top:-l,bottom:-l,right:-h,left:-h,paddingTop:t.top,paddingRight:t.right,paddingBottom:t.bottom,paddingLeft:t.left,transform:"translate3d("+this.left+"px,"+this.top+"px,0)"}),this.wrapper.css({marginTop:Math.max(e.height-n-t.height)/2,height:n,width:s-s%2}),this.zoomViewer.resize(),this.centerNeedsUpdate=!0,this.checkCenter(!0),this.pagesReady()}},{key:"updateCenter",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.centerShift*(this.isRTL,this.isLeftPage()?this.leftSheetWidth:this.rightSheetWidth)/2;this.seamPosition=(-this.app.dimensions.offset.width+this.app.dimensions.containerWidth)/2+t,this.wrapperShift=(this.isSingle?-this.app.dimensions.stage.innerWidth/2:0)+t,this.wrapper[0].style.left=this.wrapperShift+"px",this.wrapper[0].style.transition=e?"none":"",this.zoomViewer.updateCenter()}},{key:"refreshSheet",value:function(e){var t=e.sheet,i=e.sheetNumber;!1===t.isFlipping&&(e.needsFlip?(t.element.addClass("df-flipping"),t.flip()):(t.skipFlip=!1,t.element.removeClass("df-flipping df-quick-turn df-folding df-left-side df-right-side"),t.element.addClass(t.targetSide===u.TURN_DIRECTION.LEFT?"df-left-side":"df-right-side"),t.side=t.targetSide,t.targetSide===u.TURN_DIRECTION.LEFT?t.updateSize(this.leftSheetWidth,this.leftSheetHeight,this.leftSheetTop):t.updateSize(this.rightSheetWidth,this.rightSheetHeight,this.rightSheetTop))),t.visible=e.visible,t.isHard?t.element.addClass("df-hard-sheet"):(t.element.removeClass("df-hard-sheet"),t.frontPage.updateCSS({display:"block"}),t.backPage.updateCSS({display:"block"})),t.updateCSS({display:!0===t.visible?"block":"none",zIndex:e.zIndex}),null==t.pendingPoint&&!1===t.isFlipping&&t.resetCSS(),i!==t.pageNumber&&(t.element.attr("number",i),t.backPage.element.attr("pagenumber",t.backPage.pageNumber),t.frontPage.element.attr("pagenumber",t.frontPage.pageNumber))}},{key:"eventToPoint",value:function(e){e=ea.fixMouseEvent(e);var t,i,n,o,s,a,r,l,h,p=this.wrapper[0].getBoundingClientRect(),c=this.is3D,d=this.sheets,f=(this.app.dimensions,{x:e.clientX,y:e.clientY}),g=this.parentElement[0].getBoundingClientRect();f.x=f.x-g.left,f.y=f.y-g.top,t=(h=this.dragSheet?this.dragSheet.side===u.TURN_DIRECTION.RIGHT:f.x>this.seamPosition)?this.rightSheetWidth:this.leftSheetWidth,n=h?this.rightSheetHeight:this.leftSheetHeight,i=this.rightSheetWidth+this.leftSheetWidth,s=h?this.rightSheetTop:this.leftSheetTop,o=f.x-(this.seamPosition-this.leftSheetWidth),s=f.y-(p.top-g.top)-s,a=this.drag===u.TURN_DIRECTION.NONE?o<t?o:i-o:this.drag===u.TURN_DIRECTION.LEFT?o:i-o,r=h?d[this.stackCount/2]:d[this.stackCount/2-1],l=o<this.foldSense?u.TURN_DIRECTION.LEFT:o>i-this.foldSense?u.TURN_DIRECTION.RIGHT:u.TURN_DIRECTION.NONE;var v,m=s,y=this.foldSense;return v=o>=0&&o<y?m>=0&&m<=y?u.TURN_CORNER.TL:m>=n-y&&m<=n?u.TURN_CORNER.BL:m>y&&m<n-y?u.TURN_CORNER.L:u.TURN_CORNER.NONE:o>=i-y&&o<=i?m>=0&&m<=y?u.TURN_CORNER.TR:m>=n-y&&m<=n?u.TURN_CORNER.BR:m>y&&m<n-y?u.TURN_CORNER.R:u.TURN_CORNER.NONE:u.TURN_CORNER.NONE,{isInsideSheet:o>=0&&o<=i&&m>=0&&m<=n,isInsideCorner:v!==u.TURN_CORNER.NONE&&v!==u.TURN_CORNER.L&&v!==u.TURN_CORNER.R,x:c?f.x:o,y:c?f.y:s,fullWidth:i,sheetWidth:t,sheetHeight:n,rawDistance:i-o,distance:a,sheet:r,drag:l,foldSense:this.foldSense,event:e,raw:f,corner:v}}},{key:"pan",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];ea.pan(this,e,t)}},{key:"mouseMove",value:function(e){var t=this.eventToPoint(e);if(null!=e.touches&&2==e.touches.length){this.pinchMove(e);return}1!==this.app.zoomValue&&null!=this.startPoint&&!0===this.canSwipe&&(this.pan(t),e.preventDefault());var i=this.dragSheet||t.sheet;if(null==this.flipPage&&(null!=this.dragSheet||!0===t.isInsideCorner)){null!=this.dragSheet||(t.y=ea.limitAt(t.y,1,this.availablePageHeight()-1),t.x=ea.limitAt(t.x,1,t.fullWidth-1));var n=null!=this.dragSheet?this.corner:t.corner;if(i.isHard){var o=n===u.TURN_CORNER.BR||n===u.TURN_CORNER.TR,s=ea.angleByDistance(t.distance,t.fullWidth);i.updateAngle(s*(o?-1:1),o)}else i.updatePoint(t);i.magnetic=!0,i.magneticCorner=t.corner,e.preventDefault()}null==this.dragSheet&&null!=i&&!1===t.isInsideCorner&&!0===i.magnetic&&(i.pendingPoint=t,i.animateToReset=!0,i.magnetic=!1,this.corner=i.magneticCorner,i.flip(i.pendingPoint),i.pendingPoint=null),this.checkSwipe(t,e)}},{key:"mouseUp",value:function(e){if(null!=this.startPoint&&(e.touches||0===e.button)){if(null==this.dragSheet&&null!=e.touches&&0==e.touches.length){this.pinchUp(e);return}var t=this.eventToPoint(e),i=e.target||e.originalTarget,n=1===this.app.zoomValue&&t.x===this.startPoint.x&&t.y===this.startPoint.y&&"A"!==i.nodeName;if(!0===e.ctrlKey&&n)this.zoomOnPoint(t);else if(this.dragSheet){e.preventDefault();var o=this.dragSheet;if(o.pendingPoint=t,this.drag=t.drag,n&&(!0===t.isInsideCorner||t.isInsideSheet&&this.clickAction===u.MOUSE_CLICK_ACTIONS.NAV))t.corner.indexOf("l")>-1?this.app.openLeft():this.app.openRight();else{var s=this.getBasePage();t.distance>t.sheetWidth/2&&(t.sheet.side===u.TURN_DIRECTION.LEFT?this.app.openLeft():this.app.openRight()),s===this.getBasePage()&&(o.animateToReset=!0,o.flip(t))}this.dragSheet=null,o.magnetic=!1}else n&&!t.sheet.isFlipping&&t.isInsideSheet&&this.clickAction===u.MOUSE_CLICK_ACTIONS.NAV&&("left"===t.sheet.side?this.app.openLeft():this.app.openRight());this.startPoint=null,this.canSwipe=!1,this.drag=u.TURN_DIRECTION.NONE}}},{key:"mouseDown",value:function(e){if(e.touches||0===e.button){if(null!=e.touches&&2==e.touches.length){this.pinchDown(e);return}var t=this.eventToPoint(e);this.startPoint=t,this.lastPosX=t.x,this.lastPosY=t.y,t.isInsideCorner&&null==this.flipPage?(this.dragSheet=t.sheet,this.drag=t.drag,this.corner=t.corner,0===t.sheet.pageNumber?this.bookShadow.css({width:"50%",left:this.app.isRTL?0:"50%",transitionDelay:""}):t.sheet.pageNumber===Math.ceil(this.app.pageCount/2)-1&&this.bookShadow.css({width:"50%",left:this.app.isRTL?"50%":0,transitionDelay:""})):this.canSwipe=!0}}},{key:"onScroll",value:function(e){}},{key:"resetPageTween",value:function(){for(var e=0;e<this.stackCount;e++){var t=this.sheets[e];t.currentTween&&t.currentTween.complete(!0)}this.requestRefresh()}},{key:"pagesReady",value:function(){if(!this.isFlipping()){if(!1===this.app.options.flipbookFitPages){var e=this.app.viewer.getBasePage(),t=this.leftViewport=this.getViewPort(e+(this.isBooklet?0:this.isRTL?1:0)),i=this.rightViewPort=this.getViewPort(e+(this.isBooklet?0:this.isRTL?0:1));if(t){var n=ea.contain(t.width,t.height,this.availablePageWidth(),this.availablePageHeight());this.leftSheetWidth=Math.floor(n.width),this.leftSheetHeight=Math.floor(n.height),this.leftSheetTop=(this.availablePageHeight()-this.leftSheetHeight)/2}if(i){var o=ea.contain(i.width,i.height,this.availablePageWidth(),this.availablePageHeight());this.rightSheetWidth=Math.floor(o.width),this.rightSheetHeight=Math.floor(o.height),this.rightSheetTop=(this.availablePageHeight()-this.rightSheetHeight)/2}this.totalSheetsWidth=this.leftSheetWidth+this.rightSheetWidth;for(var s=0;s<this.sheets.length;s++){var a=this.sheets[s];a.side===u.TURN_DIRECTION.LEFT?a.updateSize(this.leftSheetWidth,this.leftSheetHeight,this.leftSheetTop):a.updateSize(this.rightSheetWidth,this.rightSheetHeight,this.rightSheetTop)}}this.updateCenter(),this.updatePendingStatusClass()}}},{key:"textureLoadedCallback",value:function(e){this.getPageByNumber(e.pageNumber),this.pagesReady()}}]),i}(K);function eh(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function eu(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ep(e,t,i){return t&&eu(e.prototype,t),i&&eu(e,i),e}function ec(e,t,i){return(ec="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=ed(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(i||e):o.value}})(e,t,i||e)}function ed(e){return(ed=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ef(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&eg(e,t)}function eg(e,t){return(eg=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ev(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var i,n=ed(e);return i=t?Reflect.construct(n,arguments,ed(this).constructor):n.apply(this,arguments),i&&("object"==(i&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)||"function"==typeof i)?i:function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(this)}}var em=u.utils,ey=/*#__PURE__*/function(e){ef(i,e);var t=ev(i);function i(){return eh(this,i),t.apply(this,arguments)}return ep(i,[{key:"init",value:function(){var e=this.element=jQuery("<div>",{class:"df-sheet"});(this.frontPage=new _).element.addClass("df-page-front").appendTo(this.element),(this.backPage=new _).element.addClass("df-page-back").appendTo(this.element),this.parentElement.append(e),this.frontPage.sheet=this.backPage.sheet=this}},{key:"completeTween",value:function(){this.isFlipping=!1,this.viewer.onFlip(),this.viewer.afterFlip(),this.viewer.requestRefresh(),this.element[0].style.opacity=1}},{key:"flip",value:function(e){this.side=this.targetSide,this.completeTween()}},{key:"updateSize",value:function(e,t,i){e=Math.floor(e),t=Math.floor(t),i=Math.floor(i),this.element[0].style.height=this.frontPage.element[0].style.height=t+"px",this.element[0].style.width=this.frontPage.element[0].style.width=e+"px",this.element[0].style.transform="translateX("+this.positionX+"px) translateY("+i+"px)"}}]),i}(er),eb=/*#__PURE__*/function(e){ef(i,e);var t=ev(i);function i(e,n){var o;return eh(this,i),e.viewerClass="df-slider",e.pageMode=u.FLIPBOOK_PAGE_MODE.SINGLE,e.singlePageMode=u.FLIPBOOK_SINGLE_PAGE_MODE.BOOKLET,e.pageSize=u.FLIPBOOK_PAGE_SIZE.SINGLE,(o=t.call(this,e,n)).stackCount=10,o.soundOn=!1,o.foldSense=0,n._viewerPrepared(),o}return ep(i,[{key:"initPages",value:function(){for(var e=0;e<this.stackCount;e++){var t=new ey({parentElement:this.wrapper});t.index=e,t.viewer=this,this.sheets.push(t),this.pages.push(t.frontPage),this.pages.push(t.backPage)}}},{key:"resize",value:function(){ec(ed(i.prototype),"resize",this).call(this),this.skipTransition=!0}},{key:"refreshSheet",value:function(e){var t=e.sheet,i=e.sheetNumber;t.element.toggleClass("df-no-transition",t.skipFlip||this.skipTransition),!1===t.isFlipping&&(e.needsFlip?t.flip():(t.skipFlip=!1,t.element.removeClass("df-flipping df-quick-turn df-folding df-left-side df-right-side"),t.element.addClass(t.targetSide===u.TURN_DIRECTION.LEFT?"df-left-side":"df-right-side"),t.side=t.targetSide)),t.visible=e.visible,t.updateCSS({display:e.sheetNumber>0&&e.sheetNumber<=this.app.pageCount?"block":"none",zIndex:e.zIndex}),i!==t.pageNumber&&(t.element.attr("number",i),t.backPage.element.attr("pagenumber",t.backPage.pageNumber),t.frontPage.element.attr("pagenumber",t.frontPage.pageNumber))}},{key:"refresh",value:function(){ec(ed(i.prototype),"refresh",this).call(this),this.skipTransition=!1}},{key:"eventToPoint",value:function(e){var t=ec(ed(i.prototype),"eventToPoint",this).call(this,e);return t.isInsideSheet=jQuery(e.srcElement).closest(".df-page").length>0,t.isInsideCorner=!1,t}},{key:"initCustomControls",value:function(){var e=this.app.ui.controls;e.pageMode&&e.pageMode.hide()}},{key:"setPageMode",value:function(e){e.isSingle=!0,ec(ed(i.prototype),"setPageMode",this).call(this,e)}},{key:"pagesReady",value:function(){if(!this.isFlipping()){var e=0,t=0,i=this.app;this.stackCount;for(var n=[],o=i.currentPageNumber,s=0;s<this.stackCount/2;s++)n.push(o+s),n.push(o-s-1);for(var a=0;a<this.stackCount;a++){var r=n[a];if(this.getPageByNumber(r)){var l=this.getPageByNumber(r).sheet,h=this.getViewPort(l.pageNumber,!0),u=em.contain(h.width,h.height,this.availablePageWidth(),this.availablePageHeight());i.currentPageNumber===l.pageNumber&&(this.leftSheetWidth=this.rightSheetWidth=Math.floor(u.width)),i.currentPageNumber>l.pageNumber?(e-=Math.floor(u.width)+10,l.positionX=e):(l.positionX=t,t+=Math.floor(u.width)+10);var p=(this.availablePageHeight()-u.height)/2;l.updateSize(Math.floor(u.width*i.zoomValue),Math.floor(u.height*i.zoomValue),p)}}this.updateCenter(),this.updatePendingStatusClass()}}}]),i}(el);function ew(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function eP(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function eS(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function eE(e,t,i){return t&&eS(e.prototype,t),i&&eS(e,i),e}function ex(e){return(ex=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eC(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&ek(e,t)}function eT(e,t){return null!=t&&"undefined"!=typeof Symbol&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}function ek(e,t){return(ek=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function eO(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var i,n=ex(e);return i=t?Reflect.construct(n,arguments,ex(this).constructor):n.apply(this,arguments),i&&("object"==(i&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)||"function"==typeof i)?i:ew(this)}}var eR={};function eL(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e_(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function eN(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function eI(e,t,i){return t&&eN(e.prototype,t),i&&eN(e,i),e}function eA(e,t,i){return(eA="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=eM(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(i||e):o.value}})(e,t,i||e)}function eM(e){return(eM=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eD(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&eF(e,t)}function eF(e,t){return(eF=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ez(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var i,n=eM(e);return i=t?Reflect.construct(n,arguments,eM(this).constructor):n.apply(this,arguments),i&&("object"==(i&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)||"function"==typeof i)?i:eL(this)}}eR.init=function(){if(!0!==eR.initialized){var e=window.THREE;eR={init:function(){},initialized:!0,GEOMETRY_TYPE:{PLANE:0,BOX:1,MODEL:2},MATERIAL_FACE:{FRONT:5,BACK:4},WHITE_COLOR:new e.Color("white"),defaults:{anisotropy:8,maxTextureSize:2048,groundTexture:"blank",color:16777215,shininess:15,width:210,height:297,depth:.2,segments:150,textureLoadFallback:"blank"},textureLoader:new e.TextureLoader,clearChild:function(e){var t,i=e.material;if(e.parent.remove(e),e=g.disposeObject(e),null!=i){if(null==i.length)i.map&&(t=i.map,i.dispose(),t.dispose()),i.bumpMap&&(t=i.bumpMap,i.dispose(),t.dispose()),i.normalMap&&(t=i.normalMap,i.dispose(),t.dispose());else for(var n=0;n<i.length;n++)i[n]&&(i[n].map&&(t=i[n].map,i[n].dispose(),t.dispose()),i[n].bumpMap&&(t=i[n].bumpMap,i[n].dispose(),t.dispose()),i[n].normalMap&&(t=i[n].normalMap,i[n].dispose(),t.dispose())),i[n]=null;i=null,t=null}},loadImage:function(t,i,n,o,s){if(null==i){var a=null==t.material[n]?null:t.material[n][o]?t.material[n][o].sourceFile:null;return null==a?null:a.indexOf("data:image")>-1?null:a}var r=null;return"CANVAS"===i.nodeName||"IMG"===i.nodeName?((r=new e.Texture(i)).needsUpdate=!0,eR.loadTexture(r,t,o,n),"function"==typeof s&&s(t,r)):"blank"!==i?(r=null==i?null:eR.textureLoader.load(i,function(e){e.sourceFile=i,eR.loadTexture(e,t,o,n),"function"==typeof s&&s(t,e)},void 0,function(){null==r.image&&eR.loadImage(t,eR.defaults.textureLoadFallback,n,o),eR.loadTextureFailed()}))&&(r.mapping=e.UVMapping):(eR.loadTexture(null,t,o,n),"function"==typeof s&&s(t,r)),0},loadTexture:function(t,i,n,o){if(t){var s=t.image;t.naturalWidth=s.naturalWidth,t.naturalHeight=s.naturalHeight,t.needsUpdate=!0,void 0!=i.textureRotation&&(t.rotation=e.MathUtils.degToRad(i.textureRotation),t.center=i.textureCenter)}null!==t&&"map"===n&&(t.anisotropy=0,eR.defaults.anisotropy>0&&(t.anisotropy=eR.defaults.anisotropy),!0===e.skipPowerOfTwo&&(t.minFilter=e.LinearFilter,t.magFilter=e.LinearFilter),t.name=new Date().toTimeString()),eR.clearTexture(i.material[o][n]),i.material[o][n]=t,"bumpMap"===n&&(i.material[o].bumpScale=i.sheet.getBumpScale(o)),i.material[o].needsUpdate=!0},loadTextureFailed:function(){return null},clearTexture:function(e){e&&(e.image&&"CANVAS"===e.image.nodeName&&(e.image.remove&&e.image.remove(),delete e.image),e=g.disposeObject(e))}},e.skipPowerOfTwo=!0;var t=/*#__PURE__*/function(t){eC(n,t);var i=eO(n);function n(t){eP(this,n);var o,s=t.width||eR.defaults.width,a=t.height||eR.defaults.height,r=t.color||eR.defaults.color,l=t.segments||eR.defaults.segments,h=t.depth||eR.defaults.depth,u={color:r,flatShading:!1,shininess:t.shininess||eR.defaults.shininess},p=new e.MeshPhongMaterial(u),c=[p,p,p,p,new e.MeshPhongMaterial(u),new e.MeshPhongMaterial(u)];return(o=i.call(this,new e.BoxGeometry(s,a,h,l,1,1),c)).material[5].transparent=!0,o.material[4].transparent=!0,o.baseType="Paper",o.type="Paper",o.castShadow=!0,o.receiveShadow=!0,t.parent3D.add(ew(o)),o}return eE(n,[{key:"loadImage",value:function(e,t,i){eR.loadImage(this,e,t,"map",i)}},{key:"frontImage",value:function(e,t){eR.loadImage(this,e,eR.MATERIAL_FACE.FRONT,"map",t)}},{key:"backImage",value:function(e,t){eR.loadImage(this,e,eR.MATERIAL_FACE.BACK,"map",t)}},{key:"loadBump",value:function(e){eR.loadImage(this,e,eR.MATERIAL_FACE.FRONT,"bumpMap",null),eR.loadImage(this,e,eR.MATERIAL_FACE.BACK,"bumpMap",null)}},{key:"loadNormalMap",value:function(e,t){if(void 0!==t){eR.loadImage(this,e,t,"normalMap",null);return}eR.loadImage(this,e,eR.MATERIAL_FACE.FRONT,"normalMap",null),eR.loadImage(this,e,eR.MATERIAL_FACE.BACK,"normalMap",null)}}]),n}(e.Mesh),i=/*#__PURE__*/function(e){eC(i,e);var t=eO(i);function i(e){var n;return eP(this,i),(n=t.call(this,e)).receiveShadow=!0,n.frontImage(eR.defaults.groundTexture),n.backImage(eR.defaults.groundTexture),n.type="Ground",n}return i}(t),n=/*#__PURE__*/function(t){eC(o,t);var n=eO(o);function o(t){eP(this,o);var s,a=ew(s=n.call(this));a.canvas=t.canvas||document.createElement("canvas"),a.canvas=s.canvas,a.camera=new e.PerspectiveCamera(20,a.width/a.height,4,5e4),a.renderer=new e.WebGLRenderer({canvas:a.canvas,antialias:!0,alpha:!0}),a.renderer.setPixelRatio(t.pixelRatio),a.renderer.setSize(a.width,a.height),a.renderer.setClearColor(16777215,0),a.renderer.shadowMap.enabled=!0,a.renderer.shadowMap.type=1,a.ground=new i({color:16777215,height:a.camera.far/10,width:a.camera.far/10,segments:2,parent3D:a}),a.ambientLight=new e.AmbientLight(4473924),a.add(a.ambientLight);var r=a.spotLight=new e.DirectionalLight(16777215,.25);return r.position.set(0,1,0),!1!==t.castShadow&&(r.castShadow=!0,r.shadow.camera.near=200,r.shadow.camera.far=2e3,r.shadow.camera.top=1350,r.shadow.camera.bottom=-1350,r.shadow.camera.left=-1350,r.shadow.camera.right=1350,r.shadow.radius=2,r.shadow.mapSize.width=1024,r.shadow.mapSize.height=1024,r.shadow.needsUpdate=!0),a.add(r),a.animateCount=0,a.renderCount=0,a.camera.position.set(-300,300,300),a.camera.lookAt(new e.Vector3(0,0,0)),s}return eE(o,[{key:"resizeCanvas",value:function(e,t){this.renderer.setSize(e,t),this.camera.aspect=e/t,this.camera.updateProjectionMatrix()}},{key:"render",value:function(){this.animateCount++,this.renderer.render(this,this.camera),null!=this.stats&&this.stats.update()}},{key:"clearMaterials",value:function(){for(var e=this.children.length,t=e-1;t>=0;t--){var i=this.children[t];if(i.baseType&&"Paper"===i.baseType&&i.material){if(i.material.length)for(var n=0;n<i.material.length;n++)i.material[n].needsUpdate=!0;else i.material.needsUpdate=!0}}}},{key:"clearChild",value:function(){this.spotLight.shadow.map=g.disposeObject(this.spotLight.shadow.map),this.spotLight.castShadow=!1,this.clearMaterials();for(var e=this.children.length,t=e-1;t>=0;t--){var i=this.children[t];if(i.children&&i.children.length>0)for(var n=i.children.length-1;n>=0;n--)eR.clearChild(i.children[n]);eR.clearChild(i),i=null}this.render()}}]),o}(e.Scene);eR.Paper=t,eR.Stage=n;var o=/*#__PURE__*/function(e){eC(i,e);var t=eO(i);function i(e){var n;return eP(this,i),(n=t.call(this)).element=e,n.element.style.position="absolute",n.addEventListener("removed",function(){null!==this.element.parentNode&&this.element.parentNode.removeChild(this.element)}),n}return i}(e.Object3D);e.CSS3DObject=o;var s=/*#__PURE__*/function(e){eC(i,e);var t=eO(i);function i(e){return eP(this,i),t.call(this,e)}return i}(e.CSS3DObject);e.CSS3DSprite=s,e.MathUtils&&(e.Math=e.MathUtils),e.CSS3DRenderer=function(){g.log("THREE.CSS3DRenderer",e.REVISION);var t,i,n,o,s=new e.Matrix4,a={camera:{fov:0,style:""},objects:{}},r=document.createElement("div");r.style.overflow="hidden",r.style.WebkitTransformStyle="preserve-3d",r.style.MozTransformStyle="preserve-3d",r.style.oTransformStyle="preserve-3d",r.style.transformStyle="preserve-3d",this.domElement=r;var l=document.createElement("div");l.style.WebkitTransformStyle="preserve-3d",l.style.MozTransformStyle="preserve-3d",l.style.oTransformStyle="preserve-3d",l.style.transformStyle="preserve-3d",r.appendChild(l),this.setClearColor=function(){},this.getSize=function(){return{width:t,height:i}},this.setSize=function(e,s){t=e,i=s,n=t/2,o=i/2,r.style.width=e+"px",r.style.height=s+"px",l.style.width=e+"px",l.style.height=s+"px"};var h=function(e){return Math.abs(e)<Number.EPSILON?0:e},u=function(e){var t=e.elements;return"matrix3d("+h(t[0])+","+h(-t[1])+","+h(t[2])+","+h(t[3])+","+h(t[4])+","+h(-t[5])+","+h(t[6])+","+h(t[7])+","+h(t[8])+","+h(-t[9])+","+h(t[10])+","+h(t[11])+","+h(t[12])+","+h(-t[13])+","+h(t[14])+","+h(t[15])+")"},p=function(e){var t=e.elements;return"translate3d(-50%,-50%,0) matrix3d("+h(t[0])+","+h(t[1])+","+h(t[2])+","+h(t[3])+","+h(-t[4])+","+h(-t[5])+","+h(-t[6])+","+h(-t[7])+","+h(t[8])+","+h(t[9])+","+h(t[10])+","+h(t[11])+","+h(t[12])+","+h(t[13])+","+h(t[14])+","+h(t[15])+")"},c=function(t,i){if(eT(t,e.CSS3DObject)){eT(t,e.CSS3DSprite)?(s.copy(i.matrixWorldInverse),s.transpose(),s.copyPosition(t.matrixWorld),s.scale(t.scale),s.elements[3]=0,s.elements[7]=0,s.elements[11]=0,s.elements[15]=1,n=p(s)):n=p(t.matrixWorld);var n,o=t.element,r=a.objects[t.id];(void 0===r||r!==n)&&(o.style.WebkitTransform=n,o.style.MozTransform=n,o.style.oTransform=n,o.style.transform=n,a.objects[t.id]=n),o.parentNode!==l&&l.appendChild(o)}for(var h=0,u=t.children.length;h<u;h++)c(t.children[h],i)};this.render=function(t,s){var h=.5/Math.tan(e.Math.degToRad(.5*s.fov))*i;a.camera.fov!==h&&(r.style.WebkitPerspective=h+"px",r.style.MozPerspective=h+"px",r.style.oPerspective=h+"px",r.style.perspective=h+"px",a.camera.fov=h),t.updateMatrixWorld(),null===s.parent&&s.updateMatrixWorld(),s.matrixWorldInverse.invert?s.matrixWorldInverse.copy(s.matrixWorld).invert():s.matrixWorldInverse.getInverse(s.matrixWorld);var p="translate3d(0,0,"+h+"px)"+u(s.matrixWorldInverse)+" translate3d("+n+"px,"+o+"px, 0)";a.camera.style!==p&&(l.style.WebkitTransform=p,l.style.MozTransform=p,l.style.oTransform=p,l.style.transform=p,a.camera.style=p),c(t,s)}}}};var eB=u.jQuery,eH=u.utils,eU=/*#__PURE__*/function(e){eD(i,e);var t=ez(i);function i(e){var n;return e_(this,i),(n=t.call(this,e)).flexibility=e.flexibility,n.sheetAngle=180,n.curveAngle=0,n.parent3D=e.parent3D,n.segments=e.segments||50,n.width=e.width||100,n.height=e.height||100,n.depth=e.depth||.5,n.matColor="white",n.fallbackMatColor=eR.WHITE_COLOR,n.init(),n.bumpScale=[0,0,0,0,1,1],n}return eI(i,[{key:"init",value:function(){this.element=new eR.Paper({parent3D:this.parent3D,segments:this.segments,depth:this.depth,height:this.height,width:this.width,flatShading:0===this.flexibility}),this.element.sheet=this,this.frontPage=new ej({sheet:this,face:5}),this.backPage=new ej({sheet:this,face:4}),this.reset(),this.updateAngle()}},{key:"setMatColor",value:function(e,t){if(this.matColor=new THREE.Color(e),void 0===t)for(var i=0;i<6;i++)this.element.material[i].color=this.matColor;else this.element.material[t].color=this.matColor}},{key:"getBumpScale",value:function(e){return this.bumpScale[e]}},{key:"resetMatColor",value:function(e,t){this.element.material[e].color=t?this.matColor:this.fallbackMatColor}},{key:"frontImage",value:function(e,t){this.element.frontImage(e,t)}},{key:"backImage",value:function(e,t){this.element.backImage(e,t)}},{key:"updateAngle",value:function(){if(void 0!==this.viewer&&null!==this.viewer){var e=!0===this.isHard?0:this.flexibility,t=("vertical"===this.viewer.orientation?this.height:this.width)*(1-Math.sin(e/2*(e/2))/2-e/20);this.element.scale.y=("vertical"===this.viewer.orientation?this.width:this.height)/this.element.geometry.parameters.height;var i=this.segments,n=t/1,o=n*e,s=[],a=[],r=[],l=[],h=[],u=[],p=this.depth,c=0,d=[];d.push(c),h[0]=[],u[0]=[];var f=this.sheetAngle*Math.PI/180;"vertical"!==this.viewer.orientation&&(this.element.position.x=-Math.cos(f)*this.viewer.pageOffset),"vertical"===this.viewer.orientation&&(this.element.position.y=Math.cos(f)*this.viewer.pageOffset);var g=!0===this.isHard?f:this.curveAngle*Math.PI/180,v=this.sheetAngle*Math.PI/180,m=v-Math.PI/2,y=Math.sin(m)*p/2;h[0][0]=h[0][1]=new THREE.Vector3(-n*Math.cos(f),0,Math.sin(f)*n-y),u[0][0]=u[0][1]=new THREE.Vector3(h[0][0].x-Math.cos(m)*p,0,h[0][0].z+2*y),h[0][1]=new THREE.Vector3(-n/2*Math.cos(g),0,n/2*Math.sin(g)-y),u[0][1]=new THREE.Vector3(h[0][1].x-Math.cos(m)*p,0,h[0][1].z+2*y),v=(45+this.sheetAngle/2)*Math.PI/180,h[0][2]=new THREE.Vector3(-Math.cos(v)*o/2,0,Math.sin(v)*o-y),u[0][2]=new THREE.Vector3(h[0][2].x+Math.cos(m)*p,0,h[0][2].z+2*y),5e-4>Math.abs(u[0][2].x-0)&&(u[0][2].x=0),h[0][3]=new THREE.Vector3(0,0,-y),u[0][3]=new THREE.Vector3(h[0][3].x-Math.cos(m)*p,0,h[0][3].z+2*y),5e-4>Math.abs(u[0][3].x-0)&&(u[0][3].x=0);for(var b=0;b<1;b++){var w=Math.max(this.segments-1,1);s[b]=new THREE.CubicBezierCurve3(h[b][0],h[b][1],h[b][2],h[b][3]),r[b]=s[b].getPoints(w),w>2&&r[b].push(new THREE.Vector3().copy(r[b][w]));for(var P=void 0,S=r[b][0],E=1;E<r[b].length;E++)c+=(P=r[b][E]).distanceTo(S),d.push(c),S=P;a[b]=new THREE.CubicBezierCurve3(u[b][0],u[b][1],u[b][2],u[b][3]),l[b]=a[b].getPoints(w),w>2&&l[b].push(new THREE.Vector3().copy(l[b][w]))}var x=this.element.geometry;if(void 0!==x.attributes){var C=x.attributes.position,T=x.attributes.uv,k=i+1;C.setZ(0,r[0][i].z),C.setZ(2,r[0][i].z),C.setX(0,r[0][i].x),C.setX(2,r[0][i].x),C.setZ(1,l[0][i].z),C.setZ(3,l[0][i].z),C.setX(1,l[0][i].x),C.setX(3,l[0][i].x),C.setZ(5,r[0][0].z),C.setZ(7,r[0][0].z),C.setX(5,r[0][0].x),C.setX(7,r[0][0].x),C.setZ(4,l[0][0].z),C.setZ(6,l[0][0].z),C.setX(4,l[0][0].x),C.setX(6,l[0][0].x);for(var O=0;O<1;O++)for(var R=0;R<k;R++)C.setZ(8+0*k+R,r[0][R].z),C.setX(8+0*k+R,r[0][R].x),C.setZ(8+1*k+R,l[0][R].z),C.setX(8+1*k+R,l[0][R].x),C.setZ(8+2*k+R,r[0][R].z),C.setX(8+2*k+R,r[0][R].x),C.setZ(8+3*k+R,l[0][R].z),C.setX(8+3*k+R,l[0][R].x),C.setZ(8+4*k+R,r[0][R].z),C.setX(8+4*k+R,r[0][R].x),C.setZ(8+5*k+R,r[0][R].z),C.setX(8+5*k+R,r[0][R].x),T.setX(8+4*k+R,d[R]/c),T.setX(8+5*k+R,d[R]/c),C.setZ(8+6*k+R,l[0][i-R].z),C.setX(8+6*k+R,l[0][i-R].x),C.setZ(8+7*k+R,l[0][i-R].z),C.setX(8+7*k+R,l[0][i-R].x),T.setX(8+6*k+R,1-d[i-R]/c),T.setX(8+7*k+R,1-d[i-R]/c);x.computeBoundingBox(),this.element.scale.x=1*n/c,x.computeBoundingSphere(),C.needsUpdate=!0,T.needsUpdate=!0,x.computeVertexNormals()}else{var L=x.vertices,_=i-1,N=8;L[0].z=L[2].z=r[0][i].z,L[0].x=L[2].x=r[0][i].x,L[1].z=L[3].z=l[0][i].z,L[1].x=L[3].x=l[0][i].x,L[5].z=L[7].z=r[0][0].z,L[5].x=L[7].x=r[0][0].x,L[4].z=L[6].z=l[0][0].z,L[4].x=L[6].x=l[0][0].x;for(var I=0;I<1;I++)for(var A=1;A<i;A++)L[N].z=L[N+3*_].z=l[0][A].z,L[N].x=L[N+3*_].x=l[0][A].x,L[N+_].z=L[N+2*_].z=r[0][A].z,L[N+_].x=L[N+2*_].x=r[0][A].x,N++;for(var M=x.faceVertexUvs[0],D=x.faces,F=0,z=0;z<M.length;z++)if(D[z].materialIndex===eR.MATERIAL_FACE.BACK){var B=d[F]/c;z%2==0?(M[z][0].x=M[z][1].x=M[z+1][0].x=B,F++):M[z-1][2].x=M[z][1].x=M[z][2].x=B}else if(D[z].materialIndex===eR.MATERIAL_FACE.FRONT){var H=1-d[F]/c;z%2==0?(M[z][0].x=M[z][1].x=M[z+1][0].x=H,F--):M[z-1][2].x=M[z][1].x=M[z][2].x=H}x.computeBoundingBox(),this.element.scale.x=1*n/c,x.computeBoundingSphere(),x.verticesNeedUpdate=!0,x.computeFaceNormals(),x.computeVertexNormals(),x.uvsNeedUpdate=!0,x.normalsNeedUpdate=!0}s.forEach(function(e){}),a.forEach(function(e){}),l.forEach(function(e){}),r.forEach(function(e){})}}},{key:"flip",value:function(e,t){var i=this,n=i.viewer.isBooklet;!0===i.isCover&&(0===e&&(e=2.5*i.viewer.flexibility),180===e&&(e-=2.5*i.viewer.flexibility));var o=t-e,s=e>90,a=i.viewer.isRTL,r=s?i.backPage.pageNumber:i.frontPage.pageNumber,l=this.viewer.getViewPort(r);l&&(l=eH.contain(l.width,l.height,i.viewer.availablePageWidth(),i.viewer.availablePageHeight()));var h=-(i.viewer.has3DCover&&i.viewer.isClosedPage()?i.viewer.coverExtraWidth:0),u=-(i.viewer.has3DCover&&i.viewer.isClosedPage()?i.viewer.coverExtraHeight:0);i.init={angle:e,height:s?i.viewer.rightSheetHeight:i.viewer.leftSheetHeight,width:s?i.viewer.rightSheetWidth:i.viewer.leftSheetWidth,index:s&&!a||!s&&a?1:0,_index:0},i.first={angle:e+o/4,index:s&&!a||!s&&a?1:.25},i.mid={angle:e+2*o/4,index:.5},i.mid2={angle:e+3*o/4,index:s&&!a||!s&&a?.25:1},i.end={angle:t,index:s&&!a||!s&&a?0:1,height:u+(l?l.height:i.height),width:h+(l?l.width:i.width)},i.isFlipping=!0;var p=function(e){i.sheetAngle=e.angle,i.curveAngle=i.isHard?e.angle:eH.getCurveAngle(s,e.angle),!0===i.isHard?(i.flexibility=0,i.isCover&&i.viewer.flipCover(i)):i.flexibility=e.angle<90?i.leftFlexibility:i.rightFlexibility,i.element.position.z=(e.angle<90?i.leftPos:i.rightPos)+i.depth,n&&(i.element.material[5].opacity=i.element.material[4].opacity=e.index,i.element.castShadow=e.index>.5),i.height=e.height,i.width=e.width,i.updateAngle(!0)};n&&(!s&&!a||s&&a)&&(i.element.material[5].opacity=i.element.material[4].opacity=0,i.element.castShadow=!1),i.currentTween=new TWEEN.Tween(i.init).to({angle:[i.first.angle,i.mid.angle,i.mid2.angle,i.end.angle],index:[i.first.index,i.mid.index,i.mid2.index,i.end.index],_index:1,height:i.end.height,width:i.end.width},i.viewer.app.options.duration*Math.abs(o)/180).onUpdate(function(e){p(this,e)}).easing(TWEEN.Easing.Sinusoidal.Out).onStop(function(){i.currentTween=null,i.isFlipping=!1,i.isCover&&(i.viewer.leftCover.isFlipping=!1,i.viewer.rightCover.isFlipping=!1),i.element.material[5].opacity=i.element.material[4].opacity=1}).onComplete(function(){i.updateAngle(),i.element.material[5].opacity=i.element.material[4].opacity=1,i.element.castShadow=!0,i.isFlipping=!1,i.isCover&&(i.viewer.leftCover.isFlipping=!1,i.viewer.rightCover.isFlipping=!1),i.side=i.targetSide,i.viewer.onFlip(),i.viewer.afterFlip(),i.currentTween=null,i.viewer&&i.viewer.requestRefresh&&i.viewer.requestRefresh()}).start(),i.currentTween.update(window.performance.now())}}]),i}(Q),eV=/*#__PURE__*/function(e){eD(i,e);var t=ez(i);function i(e,n){var o,s,a,r,l;return e_(this,i),e.viewerClass="df-flipbook-3d",(o=t.call(this,e,n)).pageOffset=5,o.spiralCount=20,o.groundDistance=null!==(s=e.groundDistance)&&void 0!==s?s:2,o.hasSpiral="true"===e.hasSpiral||!0===e.hasSpiral,o.flexibility=eH.limitAt(null!==(a=e.flexibility)&&void 0!==a?a:.9,0,10),o.hasSpiral&&(o.flexibility=0),0===o.flexibility&&(e.sheetSegments=8),o.drag3D=eH.isTrue(e.drag3D),o.texturePowerOfTwo=!eH.isMobile&&(null===(r=e.texturePowerOfTwo)||void 0===r||r),o.color3DSheets=null!==(l=o.app.options.color3DSheets)&&void 0!==l?l:"white",o.midPosition=0,o.initMOCKUP(function(){n._viewerPrepared()}),o}return eI(i,[{key:"initMOCKUP",value:function(e){var t=this.app;"undefined"==typeof THREE?(t.updateInfo(t.options.text.loading+" WEBGL 3D ..."),"function"==typeof window.define&&window.define.amd&&window.requirejs?(window.requirejs.config({paths:{three:t.options.threejsSrc.replace(".js","")},shim:{three:{exports:"THREE"}}}),window.require(["three"],function(t){return window.THREE=t,eR.init(),"function"==typeof e&&e(),t})):"function"==typeof window.define&&window.define.amd?window.require(["three",t.options.threejsSrc.replace(".js","")],function(t){t(function(){eR.init(),"function"==typeof e&&e()})}):eH.getScript(t.options.threejsSrc+"?ver="+u.version,function(){eR.init(),"function"==typeof e&&e()},function(){t.updateInfo("Unable to load THREE.js...")})):(eR.init(),"function"==typeof e&&e())}},{key:"init",value:function(){var e=this.app;eA(eM(i.prototype),"init",this).call(this),e.provider.defaultPage.pageRatio,this.pageScaleX=1,this.initDepth(),this.initStage(),this.initPages(),this.initEvents(),this.render()}},{key:"updatePageMode",value:function(){eA(eM(i.prototype),"updatePageMode",this).call(this);var e=this.app;this.has3DCover=e.options.cover3DType!==u.FLIPBOOK_COVER_TYPE.NONE&&e.pageCount>7&&!this.isBooklet,this.has3DCover&&"none"===e.options.flipbookHardPages&&(e.options.flipbookHardPages="cover")}},{key:"initDepth",value:function(){var e,t;this.sheetDepth=this.pageScaleX*(null!==(e=this.app.options.sheetDepth)&&void 0!==e?e:.5),this.sheetSegments=null!==(t=this.app.options.sheetSegments)&&void 0!==t?t:20,this.coverDepth=2*this.sheetDepth,this.sheetsDepth=Math.min(10,this.app.pageCount/4)*this.sheetDepth}},{key:"initStage",value:function(){var e=this.stage=new eR.Stage({pixelRatio:this.app.options.pixelRatio});(e.canvas=eB(e.renderer.domElement).addClass("df-3dcanvas")).appendTo(this.element),e.camera.position.set(0,0,600),e.camera.lookAt(new THREE.Vector3(0,0,0)),this.camera=e.camera,e.spotLight.position.set(-220,220,550),e.spotLight.castShadow=!eH.isMobile&&this.app.options.has3DShadow,e.spotLight.shadow&&(e.spotLight.shadow.bias=-.005),e.ambientLight.color=new THREE.Color("#fff"),e.ambientLight.intensity=.82;var t=new THREE.ShadowMaterial;t.opacity=this.app.options.shadowOpacity,e.ground.oldMaterial=e.ground.material,e.ground.material=t,e.ground.position.z=this.has3DCover?-6:-4,e.selectiveRendering=!0;var i=e.cssRenderer=new THREE.CSS3DRenderer;eB(i.domElement).css({position:"absolute",top:0,pointerEvents:"none"}).addClass("df-3dcanvas df-csscanvas"),this.element[0].appendChild(i.domElement),e.cssScene=new THREE.Scene,this.wrapper.remove(),this.wrapper=new THREE.Group,this.stage.add(this.wrapper),this.wrapper.add(e.ground),this.bookWrapper=new THREE.Group,this.bookWrapper.name="bookwrapper",this.wrapper.add(this.bookWrapper),this.bookHelper=e.bookHelper=new THREE.BoxHelper(this.bookWrapper,16776960),e.add(this.bookHelper),this.bookHelper.visible=!1,this.cameraWrapper=new THREE.Group,this.cameraWrapper.add(e.camera),e.add(this.cameraWrapper),this.app.renderRequestStatus=u.REQUEST_STATUS.ON}},{key:"initPages",value:function(){for(var e={parent3D:this.bookWrapper,viewer:this,segments:this.sheetSegments,depth:this.sheetDepth,flexibility:this.flexibility},t=0;t<this.stackCount;t++){var i=new eU(e);i.index=t,i.viewer=this,this.sheets.push(i),i.setMatColor(this.color3DSheets),this.pages.push(i.frontPage),this.pages.push(i.backPage),this.stage.cssScene.add(i.frontPage.cssPage),this.stage.cssScene.add(i.backPage.cssPage)}e.depth=this.sheetsDepth,e.segments=1,e.flexibility=0,this.leftSheets=new eU(e),this.rightSheets=new eU(e),this.leftSheets.setMatColor(this.color3DSheets),this.rightSheets.setMatColor(this.color3DSheets),e.depth=this.coverDepth,this.leftCover=new eU(e),this.rightCover=new eU(e),this.leftCover.isHard=!0,this.rightCover.isHard=!0,this.set3DCoverNormal(),this.setcolor3DCover(this.app.options.color3DCover),this.stage.cssScene.add(this.leftCover.frontPage.cssPage),this.stage.cssScene.add(this.rightCover.backPage.cssPage),this.zoomViewer.leftPage.element.css({backgroundColor:this.color3DSheets}),this.zoomViewer.rightPage.element.css({backgroundColor:this.color3DSheets}),"vertical"===this.orientation&&this.bookWrapper.children.forEach(function(e){e.rotateZ(THREE.MathUtils.degToRad(-90)),e.textureCenter=new THREE.Vector2(.5,.5),e.textureRotation=90}),this.initSpiral()}},{key:"initSpiral",value:function(){this.hasSpiral=!1}},{key:"set3DCoverNormal",value:function(){}},{key:"setcolor3DCover",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]}},{key:"initEvents",value:function(){this.stageDOM=this.element[0],eA(eM(i.prototype),"initEvents",this).call(this)}},{key:"dispose",value:function(){eA(eM(i.prototype),"dispose",this).call(this),this.stage&&(this.stage.clearChild(),this.stage.cssRenderer.domElement.parentNode.removeChild(this.stage.cssRenderer.domElement),this.stage.cssRenderer=null,this.stage.orbitControl=eH.disposeObject(this.stage.orbitControl),this.stage.renderer=eH.disposeObject(this.stage.renderer),eB(this.stage.canvas).remove(),this.stage.canvas=null,this.stage=eH.disposeObject(this.stage)),this.centerTween&&this.centerTween.stop&&this.centerTween.stop()}},{key:"render",value:function(){this.stage.render(),this.stage.cssRenderer.render(this.stage.cssScene,this.stage.camera)}},{key:"resize",value:function(){eA(eM(i.prototype),"resize",this).call(this);var e=this,t=e.app,n=e.stage,o=t.dimensions;o.padding,e.isSingle;var s=this.availablePageWidth(),a=this.availablePageHeight();n.resizeCanvas(o.stage.width,o.stage.height),n.cssRenderer.setSize(o.stage.width,o.stage.height),this.pageScaleX=Math.max(Math.max(s,a)/400,1),this.initDepth(),this.sheets.forEach(function(t){t.depth=e.sheetDepth}),t.refreshRequestStart();var r=this.refSize=Math.min(a,s);this.coverExtraWidth=("vertical"==e.orientation?2:1)*r*.025,this.coverExtraHeight=("vertical"==e.orientation?1:2)*r*.025,!0!==this.has3DCover&&(this.coverExtraWidth=0,this.coverExtraHeight=0),e.zoomViewer.resize(),e.cameraPositionDirty=!0,e.centerNeedsUpdate=!0,e.checkCenter(!0),e.pagesReady(),this.pageOffset=(this.hasSpiral?6:0)*Math.min(this._defaultPageSize.width,this._defaultPageSize.height)/1e3}},{key:"fitCameraToCenteredObject",value:function(e,t,i,n){var o=new THREE.Box3;o.setFromObject(t),new THREE.Vector3;var s=new THREE.Vector3;o.getSize(s);var a=this.coverExtraHeight,r=2*this.coverExtraWidth;this.isClosedPage()&&(r=0,a=0),s.x=s.x-r+this.app.dimensions.padding.width,s.y=s.y-a+this.app.dimensions.padding.height;var l=e.fov*(Math.PI/180),h=2*Math.atan(Math.tan(l/2)*e.aspect),u=Math.max(s.z/2+Math.abs(s.x/2/Math.tan(h/2)),s.z/2+Math.abs(s.y/2/Math.tan(l/2)));void 0!==i&&0!==i&&(u*=i),e.position.set(0,0,u);var p=o.min.z,c=p<0?-p+u:u-p;e.far=3*c,e.updateProjectionMatrix(),void 0!==n&&(n.target=new THREE.Vector3(0,0,0),n.maxDistance=2*c)}},{key:"updateShadowSize",value:function(){}},{key:"refresh",value:function(){var e=this.app,t=this.getBasePage();this.refreshRequested=!0;var n=1/e.pageCount*t,o=this.isRTL?1-n:n,s=Math.min(this.stackCount,this.totalSheets),a=eH.limitAt(this.totalSheets,this.stackCount,2*this.stackCount),r=this.isBooklet?0:this.flexibility/a;this.leftFlexibility=r*(1-o),this.rightFlexibility=r*o,this.midPosition=.5*s*this.sheetDepth,eA(eM(i.prototype),"refresh",this).call(this);var l=!0===this.has3DCover;this.leftCover.element.visible=this.rightCover.element.visible=this.leftSheets.element.visible=this.rightSheets.element.visible=l,this.wrapper.position.z=-this.midPosition;var h=0,u=0,p=this.isRTL,c=this.isFirstPage(),d=this.isLastPage(),f=this.isLeftClosed=this.isClosedPage()&&(p&&d||!p&&c),g=this.isRightClosed=this.isClosedPage()&&(!p&&d||p&&c);if(l){this.leftSheets.depth=p?this.sheetsDepth*(1-this.getBasePage()/e.pageCount):this.sheetsDepth*t/e.pageCount,this.leftSheets.element.visible=p?e.pageCount-this.getBasePage()>2:t>2,h-=this.leftSheets.depth/2,this.leftSheets.element.position.z=h,h-=this.coverDepth+(this.leftSheets.element.visible?this.leftSheets.depth/2:0)+3*this.coverDepth,this.leftCover.depth=this.rightCover.depth=this.coverDepth;var v=Math.max(this.leftSheetHeight,this.rightSheetHeight);g&&(v=this.leftSheetHeight),f&&(v=this.rightSheetHeight),!0!==this.leftCover.isFlipping&&(this.leftCover.element.position.z=f?this.midPosition+this.coverDepth:h+this.coverDepth/2,this.leftCover.element.position.z=Math.max(this.leftCover.element.position.z,-(.05*this.refSize)),this.leftCover.element.position.x=0,this.leftSheets.sheetAngle=this.leftCover.sheetAngle=f?180:0,this.leftSheets.curveAngle=this.leftCover.curveAngle=f?180:0,!0===this.rightCover.isFlipping||(this.leftCover.height=v,this.leftCover.width=this.leftCover.sheetAngle<90?this.leftSheetWidth:this.rightSheetWidth,this.isClosedPage()||(this.leftCover.width+=this.coverExtraWidth,this.leftCover.height+=this.coverExtraHeight)),this.leftSheets.updateAngle(),this.leftCover.updateAngle()),this.rightSheets.depth=this.sheetsDepth-this.leftSheets.depth,this.rightSheets.element.visible=p?t>2:e.pageCount-this.getBasePage()>2,u-=this.rightSheets.depth/2,this.rightSheets.element.position.z=u,u-=this.coverDepth+(this.rightSheets.element.visible?this.rightSheets.depth/2:0)+3*this.coverDepth,!0!==this.rightCover.isFlipping&&(this.rightCover.element.position.z=g?this.midPosition+this.coverDepth:u+this.coverDepth/2,this.rightCover.element.position.z=Math.max(this.rightCover.element.position.z,-(.05*this.refSize)),this.rightCover.element.position.x=0,this.rightSheets.sheetAngle=this.rightCover.sheetAngle=g?0:180,this.rightSheets.curveAngle=this.rightCover.curveAngle=g?0:180,!0===this.leftCover.isFlipping||(this.rightCover.height=v,this.rightCover.width=this.rightCover.sheetAngle<90?this.leftSheetWidth:this.rightSheetWidth,this.isClosedPage()||(this.rightCover.width+=this.coverExtraWidth,this.rightCover.height+=this.coverExtraHeight)),this.rightSheets.updateAngle(),this.rightCover.updateAngle()),this.updateSheets(),this.stage.ground.position.z=Math.min(h,u)-this.refSize*this.groundDistance/100,this.stage.ground.position.z=Math.max(this.stage.ground.position.z,-(.1*this.refSize))}else this.stage.ground.position.z=-this.midPosition-15*this.sheetDepth;!0===this.cameraPositionDirty&&this.updateCameraPosition(),this.refreshSpiral()}},{key:"refreshSpiral",value:function(){}},{key:"updateCameraPosition",value:function(){var e=this.app,t=this.stage,i=e.dimensions,n=i.padding,o=1/(2*Math.tan(Math.PI*t.camera.fov*.5/180)/(i.stage.height/e.zoomValue))+2.2;this.updateShadowSize(),this.stage.spotLight.position.x=-(330*this.pageScaleX),this.stage.spotLight.position.y=330*this.pageScaleX,this.stage.spotLight.position.z=550*this.pageScaleX,this.stage.spotLight.shadow.camera.far=1200*this.pageScaleX,this.stage.spotLight.shadow.camera.updateProjectionMatrix();var s=(n.top-n.bottom)/e.zoomValue/2,a=-(n.left-n.right)/e.zoomValue/2;t.camera.position.z!==o&&!0===e.pendingZoom&&(t.camera.position.z=o),1===e.zoomValue&&(this.bookWrapper.rotation.set(0,0,0),this.bookHelper.rotation.set(0,0,0),this.cameraWrapper.rotation.set(0,0,0),0!==e.options.flipbook3DTiltAngleUp||0!==e.options.flipbook3DTiltAngleLeft?(t.camera.aspect=i.stage.width/i.stage.height,t.camera.updateProjectionMatrix(),this.bookWrapper.rotateZ(THREE.Math.degToRad(-e.options.flipbook3DTiltAngleLeft)),this.bookWrapper.rotateX(THREE.Math.degToRad(-e.options.flipbook3DTiltAngleUp)),"vertical"==this.orientation?this.bookWrapper.scale.y=1/(this.isSingle?2:1):this.bookWrapper.scale.x=1/(this.isSingle?2:1),this.bookHelper.update(),this.fitCameraToCenteredObject(t.camera,this.bookWrapper),this.bookWrapper.rotation.set(0,0,0),this.bookWrapper.scale.x=1,this.bookWrapper.scale.y=1,t.camera.position.set(a,s,t.camera.position.z+t.ground.position.z),this.camera.aspect=i.stage.width/i.stage.height,this.camera.updateProjectionMatrix(),this.cameraWrapper.rotateX(THREE.Math.degToRad(e.options.flipbook3DTiltAngleUp)),this.cameraWrapper.rotateZ(THREE.Math.degToRad(e.options.flipbook3DTiltAngleLeft))):t.camera.position.set(a,s,o)),t.camera.updateProjectionMatrix(),this.app.renderRequestStatus=u.REQUEST_STATUS.ON,this.cameraPositionDirty=!1}},{key:"refreshSheet",value:function(e){var t,i=e.sheet,n=e.index,o=i.sheetAngle,s=!(i.isHard||0===this.flexibility);i.leftFlexibility=s?this.leftFlexibility:0,i.rightFlexibility=s?this.rightFlexibility:0,i.leftPos=this.midPosition+(n-e.midPoint+1)*this.sheetDepth-this.sheetDepth/2,i.rightPos=this.midPosition-(n-e.midPoint)*this.sheetDepth-this.sheetDepth/2,t=i.targetSide===u.TURN_DIRECTION.LEFT?0:180,!1===i.isFlipping&&(e.needsFlip?(i.isFlipping=!0,i.isCover&&0===e.sheetNumber&&(this.isRTL?this.rightCover.isFlipping=!0:this.leftCover.isFlipping=!0),i.isCover&&this.totalSheets-e.sheetNumber==1&&(this.isRTL?this.leftCover.isFlipping=!0:this.rightCover.isFlipping=!0),i.element.position.z=Math.max(o<90?i.leftPos:i.rightPos,this.midPosition)+this.sheetDepth,i.flexibility=o<90?i.leftFlexibility:i.rightFlexibility,i.flip(o,t)):(i.skipFlip=!1,i.sheetAngle=i.curveAngle=t,i.flexibility=t<90?i.leftFlexibility:i.rightFlexibility,i.element.position.z=t<90?i.leftPos:i.rightPos,i.side=i.targetSide,i.height=t<90?this.leftSheetHeight:this.rightSheetHeight,i.width=t<90?this.leftSheetWidth:this.rightSheetWidth),i.updateAngle(),this.app.renderRequestStatus=u.REQUEST_STATUS.ON),i.element.visible=e.visible}},{key:"updateCenter",value:function(){var e=this,t=this.app,i="vertical"==this.orientation?e.wrapper.position.y:e.wrapper.position.x,n=("vertical"===this.orientation?-1:1)*e.centerShift*(this.isLeftPage()?"vertical"==this.orientation?this.leftSheetHeight:this.leftSheetWidth:"vertical"==this.orientation?this.rightSheetHeight:this.rightSheetWidth)/2;e.seamPosition=(-t.dimensions.offset.width+t.dimensions.containerWidth)/2+n,n!==e.centerEnd&&(e.centerTween&&e.centerTween.stop&&e.centerTween.stop(),e.onCenterStartAnimation(this),e.centerTween=new TWEEN.Tween({x:i}).delay(0).to({x:n},1===t.zoomValue&&!0!==e.skipCenterAnimation?e.app.options.duration:1).onStart(function(){}).onUpdate(function(){e.onCenterUpdateAnimation(this)}).onComplete(function(){e.onCenterCompleteAnimation(this)}).onStop(function(){e.onCenterStopAnimation(this)}).easing(TWEEN.Easing.Cubic.InOut).start(),this.updatePendingStatusClass(),e.skipCenterAnimation=!1,e.centerEnd=n),e.renderRequestStatus=u.REQUEST_STATUS.ON,this.zoomViewer.updateCenter()}},{key:"onCenterUpdateAnimation",value:function(e){"vertical"==this.orientation?(this.wrapper.position.y=e.x,this.stage&&this.stage.cssScene&&(this.stage.cssScene.position.y=e.x)):(this.wrapper.position.x=e.x,this.stage&&this.stage.cssScene&&(this.stage.cssScene.position.x=e.x))}},{key:"onCenterStartAnimation",value:function(e){}},{key:"onCenterStopAnimation",value:function(e){}},{key:"onCenterCompleteAnimation",value:function(e){}},{key:"flipCover",value:function(e){var t,i,n=null;0===e.pageNumber||this.isBooklet&&1===e.pageNumber?(n=this.isRTL?this.rightCover:this.leftCover,t=this.isRTL?1:-1):e.pageNumber===this.totalSheets-1&&(n=this.isRTL?this.leftCover:this.rightCover,t=this.isRTL?-1:1),null!==n&&(i=n.depth+e.depth+1,n.sheetAngle=e.sheetAngle,n.curveAngle=e.curveAngle,this.rightCover.height=this.leftCover.height=e.height+this.coverExtraHeight,this.rightCover.width=this.leftCover.width=e.width+this.coverExtraWidth,n.flexibility=e.flexibility,this.rightCover.updateAngle(),this.leftCover.updateAngle(),n.element.position.x=e.element.position.x+t*Math.sin(e.sheetAngle*Math.PI/180)*i,n.element.position.z=e.element.position.z+t*Math.cos(e.sheetAngle*Math.PI/180)*i)}},{key:"pagesReady",value:function(){if(!this.isAnimating()&&!0===this.refreshRequested){if(!1===this.app.options.flipbookFitPages){var e=this.app.viewer.getBasePage(),t=this.leftViewport=this.getViewPort(e+(this.isBooklet?0:this.isRTL?1:0)),i=this.rightViewPort=this.getViewPort(e+(this.isBooklet?0:this.isRTL?0:1));if(t){var n=eH.contain(t.width,t.height,this.availablePageWidth(),this.availablePageHeight());(this.leftSheetWidth!=Math.floor(n.width)||this.leftSheetHeight!=Math.floor(n.height))&&(this.cameraPositionDirty=!0),this.leftSheetWidth=Math.floor(n.width),this.leftSheetHeight=Math.floor(n.height)}if(i){var o=eH.contain(i.width,i.height,this.availablePageWidth(),this.availablePageHeight());(this.rightSheetWidth!=Math.floor(o.width)||this.rightSheetWidth!=Math.floor(o.height))&&(this.cameraPositionDirty=!0),this.rightSheetWidth=Math.floor(o.width),this.rightSheetHeight=Math.floor(o.height)}for(var s=0;s<this.sheets.length;s++){var a=this.sheets[s];a.side===u.TURN_DIRECTION.LEFT?(a.height=this.leftSheetHeight,a.width=this.leftSheetWidth):(a.height=this.rightSheetHeight,a.width=this.rightSheetWidth),a.updateAngle()}if(this.isClosedPage()){var r=this.isRTL&&this.isLastPage()||!this.isRTL&&this.isFirstPage();this.leftCover.width=this.rightCover.width=r?this.rightSheetWidth:this.leftSheetWidth,this.leftCover.height=this.rightCover.height=r?this.rightSheetHeight:this.leftSheetHeight}else this.leftCover.height=this.rightCover.height=this.coverExtraHeight+Math.max(this.leftSheetHeight,this.rightSheetHeight),this.leftCover.width=this.coverExtraWidth+this.leftSheetWidth,this.rightCover.width=this.coverExtraWidth+this.rightSheetWidth;this.leftSheets.width=this.leftSheetWidth,this.leftSheets.height=this.leftSheetHeight,this.rightSheets.height=this.rightSheetHeight,this.rightSheets.width=this.rightSheetWidth,this.leftCover.updateAngle(),this.leftSheets.updateAngle(),this.rightCover.updateAngle(),this.rightSheets.updateAngle(),this.updateSheets(!0)}this.updateCenter(),this.updateCSSLayer(),this.updatePendingStatusClass(),this.refreshSpiral(),!0===this.cameraPositionDirty&&this.updateCameraPosition()}}},{key:"updateSheets",value:function(e){if(!0!==this.isClosedPage()){var t=this.getPageByNumber(this.getRightPageNumber());if(!0!==this.rightCover.isFlipping&&t&&t.sheet.element.geometry.attributes){var i=this.rightSheets.element.geometry.attributes.position,n=e?t.sheet.element.geometry.boundingBox.max.x*t.sheet.element.scale.x:this.rightSheets.lastSlopeX;i.setX(21,n),i.setX(23,n),i.setX(4,n),i.setX(6,n),i.setX(10,n),i.setX(14,n),i.needsUpdate=!0,this.rightSheets.element.geometry.attributes.uv.needsUpdate=!0,this.rightSheets.element.geometry.computeVertexNormals(),e&&(this.rightSheets.lastSlopeX=n)}var o=this.getPageByNumber(this.getLeftPageNumber());if(!0!==this.leftCover.isFlipping&&o&&o.sheet.element.geometry.attributes){var s=this.leftSheets.element.geometry.attributes.position,a=e?o.sheet.element.geometry.boundingBox.min.x*o.sheet.element.scale.x:this.leftSheets.lastSlopeX;s.setX(16,a),s.setX(18,a),s.setX(5,a),s.setX(7,a),s.setX(8,a),s.setX(12,a),s.needsUpdate=!0,this.leftSheets.element.geometry.attributes.uv.needsUpdate=!0,this.leftSheets.element.geometry.computeVertexNormals(),e&&(this.leftSheets.lastSlopeX=a)}}}},{key:"updateCSSLayer",value:function(){}},{key:"mouseMove",value:function(e){if(e=eH.fixMouseEvent(e),this.app.renderRequestStatus=u.REQUEST_STATUS.ON,null!=e.touches&&2===e.touches.length){this.pinchMove(e);return}var t=this.eventToPoint(e);if(null!==this.dragSheet&&!1!==this.drag3D&&Math.abs(t.x-this.startPoint.x)>2){!0!==this.isDragging&&(this.updatePendingStatusClass(!0),this.isDragging=!0);var i=this.dragSheet.width,n=t.x-(this.app.dimensions.origin.x+this.centerEnd-i),o=eH.limitAt(1-n/i,-1,1),s=eH.toDeg(Math.acos(o)),a=this.dragSheet,r=this.drag===u.TURN_DIRECTION.LEFT;a.sheetAngle=s;var l=eH.getCurveAngle(r,s,45);a.isCover&&a.viewer.flipCover(a),a.curveAngle=a.isHard?s:l,a.updateAngle()}this.checkSwipe(t,e)}},{key:"mouseUp",value:function(e){if((e=eH.fixMouseEvent(e)).touches||0===e.button){if(null==this.dragSheet&&null!=e.touches&&0===e.touches.length){this.pinchUp(e);return}var t=this.eventToPoint(e);if(1===this.app.zoomValue){if(null!==this.dragSheet){var i=t.x-this.startPoint.x;Math.abs(i)>2*this.swipeThreshold&&(this.drag===u.TURN_DIRECTION.LEFT&&i>0?this.app.openLeft():this.drag===u.TURN_DIRECTION.RIGHT&&i<0&&this.app.openRight()),this.requestRefresh(),this.updatePendingStatusClass()}var n=e.target||e.originalTarget,o=this.startPoint&&t.x===this.startPoint.x&&t.y===this.startPoint.y&&"A"!==n.nodeName;!0===e.ctrlKey&&o?this.zoomOnPoint(t):o&&t.sheet&&this.clickAction===u.MOUSE_CLICK_ACTIONS.NAV&&(t.sheet.sheetAngle>90?this.app.openRight():this.app.openLeft())}this.dragSheet=null,this.drag=null,!0===this.isDragging&&(this.isDragging=!1),this.startPoint=null,this.canSwipe=!1,this.app.renderRequestStatus=u.REQUEST_STATUS.ON}}},{key:"raycastCLick",value:function(e){this.mouse=new THREE.Vector2,this.raycaster=new THREE.Raycaster,this.mouse.x=e.offsetX/this.app.dimensions.stage.width*2-1,this.mouse.y=1-e.offsetY/this.app.dimensions.stage.height*2,this.raycaster.setFromCamera(this.mouse,this.camera);var t=this.raycaster.intersectObjects(this.bookWrapper.children,!0);if(t.length>0){var i,n=0;do{if((i=null!=t[n]?t[n].object:null).sheet&&i.sheet.index&&!0!==i.sheet.isFlipping)return i;n++}while(n<t.length)}}},{key:"mouseDown",value:function(e){if((e=eH.fixMouseEvent(e)).touches||0===e.button){if(null!=e.touches&&2===e.touches.length)this.pinchDown(e);else{e=eH.fixMouseEvent(e);var t=this.eventToPoint(e);this.startPoint=t,this.lastPosX=t.x,this.lastPosY=t.y;var i=this.raycastCLick(e),n=t.sheet?t.sheet.width-Math.abs(t.x-(this.app.dimensions.origin.x+this.centerEnd)):0;t.sheet&&i&&t.isInsideSheet&&n<t.sheet.width/2?(this.dragSheet=i.sheet,this.drag=t.sheet.sheetAngle<90?u.TURN_DIRECTION.LEFT:u.TURN_DIRECTION.RIGHT):this.canSwipe=!0}}}},{key:"eventToPoint",value:function(e){var t=this.app.dimensions,i={x:(e=eH.fixMouseEvent(e)).clientX,y:e.clientY};i.x=i.x-this.parentElement[0].getBoundingClientRect().left,i.y=i.y-this.parentElement[0].getBoundingClientRect().top;var n=(-t.offset.width+t.containerWidth)/2-t.stage.width/2,o=(-t.offset.width+t.containerWidth)/2+t.stage.width/2,s=t.padding.top,a=t.padding.top+this.availablePageHeight(),r=i.x<this.seamPosition,l=this.getBasePage()+(r?0:1),h=this.getPageByNumber(l);h&&(h=h.sheet);var u=i.x>n&&i.x<o&&i.y>s&&i.y<a;return{isInsideSheet:u,isInsideDragZone:u&&i.x-n<this.foldSense||o-i.x<this.foldSense,x:i.x,y:i.y,left:n,top:s,right:o,bottom:a,raw:i,isLeftSheet:r,sheet:h}}},{key:"checkPageLoading",value:function(){for(var e=!0,t=this.getVisiblePages().main,i=0;i<(this.isBooklet?1:2);i++){var n=this.getPageByNumber(t[i]);n&&(e=n.textureLoaded&&e)}this.element.toggleClass("df-loading",!e)}},{key:"textureLoadedCallback",value:function(e){this.app.renderRequestStart(),this.pagesReady()}},{key:"getTextureSize",value:function(e){var t=eA(eM(i.prototype),"getTextureSize",this).call(this,e);if(1!==this.app.zoomValue||!0===e.isAnnotation)return t;var n=eH.nearestPowerOfTwo(t.height),o=t.width*n/t.height;return this.texturePowerOfTwo?{height:n,width:o}:t}},{key:"getPageByNumber",value:function(e){if(this.has3DCover){var t=!this.isBooklet&&e===this.app.pageCount&&e%2==0,n=1===e;if(!this.isRTL&&n||this.isRTL&&t)return this.leftCover.frontPage;if(!this.isRTL&&t||this.isRTL&&n)return this.rightCover.backPage}return eA(eM(i.prototype),"getPageByNumber",this).call(this,e)}},{key:"setPage",value:function(e){return eA(eM(i.prototype),"setPage",this).call(this,e)}},{key:"beforeFlip",value:function(){eA(eM(i.prototype),"beforeFlip",this).call(this)}}]),i}(K),ej=/*#__PURE__*/function(e){eD(i,e);var t=ez(i);function i(e){e_(this,i);var n,o=eL(n=t.call(this,e));return o.element=null,o.face=e.face,o.parent3D=e.sheet,o.sheet=e.sheet,o.cssPage=new THREE.CSS3DObject(o.contentLayer[0]),n}return eI(i,[{key:"setLoading",value:function(){this.sheet.viewer.checkPageLoading()}},{key:"clearMap",value:function(){this.sheet.element.material[this.face].map=null,this.sheet.element.material[this.face].needsUpdate=!0}},{key:"loadTexture",value:function(e){var t=this,i=e.texture,n=e.callback;function o(i,o){t.updateTextureLoadStatus(!0),t.sheet.resetMatColor(t.face,e.texture===t.textureLoadFallback),"function"==typeof n&&n(e)}t.textureSrc=i,"function"==typeof u.defaults.beforeLoadTexture&&u.defaults.beforeLoadTexture({texture:i,page:t}),4===this.face?this.sheet.backImage(i,o):this.sheet.frontImage(i,o)}}]),i}(_);function eW(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function eq(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function eG(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function eZ(e,t,i){return t&&eG(e.prototype,t),i&&eG(e,i),e}function eK(e){return(eK=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eX(e,t){return null!=t&&"undefined"!=typeof Symbol&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}function eQ(e,t){return(eQ=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function eY(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}u.defaults.maxTextureSize=2048,u.viewers={},u.viewers.flipbook=function e(t,i){return(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,e),!1==u.utils.canSupport3D()&&(t.is3D=!1),u.utils.isTrue(t.is3D))?new eV(t,i):new el(t,i)},u.viewers.default=u.viewers.reader=F,u.viewers.slider=eb;var eJ=u.jQuery,e$=u.utils,e0=/*#__PURE__*/function(){function e(){eq(this,e),this.baseUrl=null,this.pdfDocument=null,this.pdfApp=null,this.pdfHistory=null,this.externalLinkRel=null,this.externalLinkEnabled=!0,this._pagesRefCache=null}return eZ(e,[{key:"dispose",value:function(){this.baseUrl=null,this.pdfDocument=null,this.pdfApp=null,this.pdfHistory=null,this._pagesRefCache=null}},{key:"setDocument",value:function(e,t){this.baseUrl=t,this.pdfDocument=e,this._pagesRefCache=Object.create(null)}},{key:"setViewer",value:function(e){this.pdfApp=e,this.externalLinkTarget=e.options.linkTarget}},{key:"setHistory",value:function(e){this.pdfHistory=e}},{key:"pagesCount",get:function(){return this.pdfDocument.numPages}},{key:"page",get:function(){return this.pdfApp.currentPageNumber},set:function(e){this.pdfApp.gotoPage(e)}},{key:"navigateTo",value:function(e){this.goToDestination(e)}},{key:"addLinkAttributes",value:function(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2];var i=this.externalLinkTarget,n=this.externalLinkRel,o=this.externalLinkEnabled;if(!t||"string"!=typeof t)throw Error('A valid "url" parameter must provided.');var s=(0,e$.removeNullCharacters)(t);o?e.href=e.title=s:(e.href="",e.title="Disabled: ".concat(s),e.onclick=function(){return!1});var a="";switch(i){case u.LINK_TARGET.NONE:break;case u.LINK_TARGET.SELF:a="_self";break;case u.LINK_TARGET.BLANK:a="_blank";break;case u.LINK_TARGET.PARENT:a="_parent";break;case u.LINK_TARGET.TOP:a="_top"}e.target=a,e.rel="string"==typeof n?n:"noopener noreferrer nofollow"}},{key:"goToDestination",value:function(e){var t,i="",n=this,o=function(t){e$.log("Requested: ",t);var s=eX(t,Object)?n._pagesRefCache[t.num+" "+t.gen+" R"]:t+1;s?((s=n.pdfApp.viewer.getViewerPageNumber(s))>n.pdfApp.pageCount&&(s=n.pdfApp.pageCount),e$.log("Loading for:",t," at page ",s),n.pdfApp.requestDestRefKey===t.num+" "+t.gen+" R"?(n.pdfApp.gotoPage(s),n.pdfHistory&&n.pdfHistory.push({dest:e,hash:i,page:s})):e$.log("Expired Request for ",s," with ",t)):(n.pdfApp.container.addClass("df-fetch-pdf"),n.pdfDocument.getPageIndex(t).then(function(e){var i=t.num+" "+t.gen+" R";n._pagesRefCache[i]=e+1,o(t)}))};"string"==typeof e?(i=e,t=this.pdfDocument.getDestination(e)):t=Promise.resolve(e),t.then(function(t){e$.log("Started:",t),e=t,eX(t,Array)&&(n.pdfApp.requestDestRefKey=t[0].num+" "+t[0].gen+" R",o(t[0]))})}},{key:"customNavigateTo",value:function(e){if(""!==e&&null!=e&&"null"!==e){var t=null;if(isNaN(Math.floor(e))){if("string"==typeof e&&isNaN(t=parseInt(e.replace("#",""),10))){window.open(e,this.pdfApp.options.linkTarget===u.LINK_TARGET.SELF?"_self":"_blank");return}}else t=e;null!=t&&this.pdfApp.gotoPage(t)}}},{key:"getDestinationHash",value:function(e){if("string"==typeof e)return this.getAnchorUrl("#"+escape(e));if(eX(e,Array)){var t=e[0],i=eX(t,Object)?this._pagesRefCache[t.num+" "+t.gen+" R"]:t+1;if(i){var n=this.getAnchorUrl("#page="+i),o=e[1];if((void 0===o?"undefined":eY(o))==="object"&&"name"in o&&"XYZ"===o.name){var s=e[4]||this.pdfApp.pageScaleValue,a=parseFloat(s);a&&(s=100*a),n+="&zoom="+s,(e[2]||e[3])&&(n+=","+(e[2]||0)+","+(e[3]||0))}return n}}return this.getAnchorUrl("")}},{key:"getCustomDestinationHash",value:function(e){return"#"+escape(e)}},{key:"getAnchorUrl",value:function(e){return(this.baseUrl||"")+e}},{key:"executeNamedAction",value:function(e){switch(e){case"GoBack":this.pdfHistory&&this.pdfHistory.back();break;case"GoForward":this.pdfHistory&&this.pdfHistory.forward();break;case"NextPage":this.page++;break;case"PrevPage":this.page--;break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1}var t=document.createEvent("CustomEvent");t.initCustomEvent("namedaction",!0,!0,{action:e}),this.pdfApp.container.dispatchEvent(t)}},{key:"cachePageRef",value:function(e,t){var i=t.num+" "+t.gen+" R";this._pagesRefCache[i]=e}}]),e}(),e1=/*#__PURE__*/function(){function e(t,i){eq(this,e),this.props=t,this.app=i,this.textureCache=[],this.pageCount=0,this.numPages=0,this.outline=[],this.viewPorts=[],this.requestedPages="",this.requestIndex=0,this.pagesToClean=[],this.defaultPage=void 0,this.pageSize=this.app.options.pageSize,this._page1Pass=!1,this._page2Pass=!1,this.pageLabels=void 0,this.textSearchLength=0,this.textSearch="",this.textContentSearch=[],this.textContentJoinedSearch=[],this.textOffsetSearch=[],this.textContent=[],this.textContentJoined=[],this.textOffset=[],this.autoLinkItemsCache=[],this.autoLinkHitsCache=[],this.searchHitItemsCache=[],this.searchHits=[],this.PDFLinkItemsCache=[],this.canPrint=!0,this.textPostion=[]}return eZ(e,[{key:"finalize",value:function(){}},{key:"dispose",value:function(){}},{key:"softDispose",value:function(){}},{key:"setCache",value:function(e,t,i){i&&(void 0===this.textureCache[i]&&(this.textureCache[i]=[]),this.textureCache[i][e]=t)}},{key:"getCache",value:function(e,t){return void 0===this.textureCache[t]?void 0:this.textureCache[t][e]}},{key:"_isValidPage",value:function(e){return e>0&&e<=this.pageCount}},{key:"getLabelforPage",value:function(e){return this.pageLabels&&void 0!==this.pageLabels[e-1]?this.pageLabels[e-1]:e}},{key:"getThumbLabel",value:function(e){var t=this.getLabelforPage(e);return t!==e?t+" ("+e+")":e}},{key:"getPageNumberForLabel",value:function(e){if(!this.pageLabels)return e;var t=this.pageLabels.indexOf(e);return t<0?null:t+1}},{key:"processPage",value:function(e){}},{key:"cleanUpPages",value:function(){}},{key:"checkRequestQueue",value:function(){}},{key:"processAnnotations",value:function(){}},{key:"processTextContent",value:function(){}},{key:"loadDocument",value:function(){}},{key:"pagesLoaded",value:function(){this._page1Pass&&this._page2Pass&&(this.app.viewer.checkDocumentPageSizes(),this.finalize())}},{key:"_documentLoaded",value:function(){this.finalizeOutLine(),this.app&&this.app.dimensions&&void 0===this.app.dimensions.pageFit&&e$.log("Provider needs to initialize page properties for the app"),this.app._documentLoaded()}},{key:"finalizeOutLine",value:function(){if(null!==this.app&&null!==this.app.options){var e=this.app.options.outline;if(e)for(var t=0;t<e.length;t++)e[t].custom=!0,e[t].dest=e[t].dest.replace(/javascript:/g,""),this.outline.push(e[t])}}},{key:"search",value:function(){}}]),e}(),e2=/*#__PURE__*/function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&eQ(e,t)}(o,e);var t,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,i=eK(o);return e=t?Reflect.construct(i,arguments,eK(this).constructor):i.apply(this,arguments),e&&("object"===eY(e)||"function"==typeof e)?e:eW(this)});function o(e,t){eq(this,o);var s,a,r=function(e){l.updateInfo(l.options.text.loading+" PDF Worker ...");var t=document.createElement("a");t.href=l.options.pdfjsWorkerSrc+h.cacheBustParameters,t.hostname!==window.location.hostname&&!0===u.loadCorsPdfjsWorker?(l.updateInfo(l.options.text.loading+" PDF Worker CORS ..."),eJ.ajax({url:l.options.pdfjsWorkerSrc+h.cacheBustParameters,cache:!0,success:function(t){l.options.pdfjsWorkerSrc=e$.createObjectURL(t,"text/javascript"),"function"==typeof e&&e()}})):"function"==typeof e&&e()},l=(s=n.call(this,e,t)).app,h=eW(s);return h.pdfDocument=void 0,h._page2Ratio=void 0,h.cacheBustParameters="?ver="+u.version+"&pdfver="+l.options.pdfVersion,a=function(){pdfjsLib.GlobalWorkerOptions.workerSrc=l.options.pdfjsWorkerSrc+h.cacheBustParameters,pdfjsLib.canvasWillReadFrequently=u.defaults.canvasWillReadFrequently,h.loadDocument()},"undefined"==typeof pdfjsLib?(l.updateInfo(l.options.text.loading+" PDF Service ..."),e$.getScript(l.options.pdfjsSrc+h.cacheBustParameters,function(){"function"==typeof define&&i.amdO&&window.requirejs&&window.require&&window.require.config?(l.updateInfo(l.options.text.loading+" PDF Service (require) ..."),window.require.config({paths:{"pdfjs-dist/build/pdf.worker":l.options.pdfjsWorkerSrc.replace(".js","")}}),window.require(["pdfjs-dist/build/pdf"],function(e){window.pdfjsLib=e,r(a)})):r(a)},function(){l.updateInfo("Unable to load PDF service.."),h.dispose()},l.options.pdfjsSrc.indexOf("pdfjs-4")>1)):"function"==typeof a&&a(),s}return eZ(o,[{key:"dispose",value:function(){this.pdfDocument&&this.pdfDocument.destroy(),this.linkService=e$.disposeObject(this.linkService),this.pdfLoadProgress&&this.pdfLoadProgress.destroy(),this.pdfLoadProgress=null,this.pdfDocument=null}},{key:"loadDocument",value:function(){var e=this.app,t=this.app.options,i=this,n=t.pdfParameters||{};if(n.url=e$.httpsCorrection(n.url||t.source),n.rangeChunkSize=t.rangeChunkSize,n.cMapPacked=!0,n.disableAutoFetch=t.disableAutoFetch,n.disableStream=t.disableStream,n.disableRange=!0===t.disableRange,n.disableFontFace=t.disableFontFace,n.isEvalSupported=!1,n.cMapUrl=t.cMapUrl,n.imagesLocation=t.imagesLocation,n.imageResourcesPath=t.imageResourcesPath,!n.url&&!n.data&&!n.range){e.updateInfo("ERROR : No PDF File provided! ","df-error");return}var o=i.pdfLoadProgress=pdfjsLib.getDocument(n);o._worker.promise.then(function(t){e.updateInfo(e.options.text.loading+" PDF ...")}),o.onPassword=function(e,t){switch(t){case pdfjsLib.PasswordResponses.NEED_PASSWORD:var i=prompt("Enter the password to open the PDF file.");if(null===i)throw Error("No password givsen.");e(i);break;case pdfjsLib.PasswordResponses.INCORRECT_PASSWORD:var i=prompt("Invalid password. Please try again.");if(!i)throw Error("No password givaen.");e(i)}},o.promise.then(function(n){i.pdfDocument=n,n.getPage(1).then(function(o){i.defaultPage=o;var s,a=i.defaultPage.viewPort=o.getViewport({scale:1,rotation:o._pageInfo.rotate+e.options.pageRotation}),r=i.defaultPage.pageRatio=a.width/a.height;i.viewPorts[1]=a,e.dimensions.defaultPage={ratio:r,viewPort:a,width:a.width,height:a.height},e.dimensions.maxTextureHeight=(null!==(s=t.maxTextureSize)&&void 0!==s?s:3200)/(r>1?r:1),e.dimensions.maxTextureWidth=e.dimensions.maxTextureHeight*r,e.dimensions.autoHeightRatio=1/r,i.pageCount=n.numPages,i.numPages=n.numPages,i._page1Pass=!0,i.pagesLoaded()}),n.numPages>1&&!0===e.checkSecondPage?n.getPage(2).then(function(t){var n=t.getViewport({scale:1,rotation:t._pageInfo.rotate+e.options.pageRotation});i._page2Ratio=n.width/n.height,i.viewPorts[2]=n,i._page2Pass=!0,i.pagesLoaded()}):(i._page2Pass=!0,i.pagesLoaded())}).catch(function(t){if(null!==e&&null!=e.options){var n,o="",s=document.createElement("a");s.href=e.options.source,s.hostname===window.location.hostname||-1!==s.href.indexOf("file://")||e$.isChromeExtension()||-1!==s.href.indexOf("blob:")||(o="<strong>CROSS ORIGIN!! </strong>");var a=(null===(n=e.options)||void 0===n?void 0:n.fileName)||s.href;e.updateInfo(o+"<strong>Error: Cannot access file!  </strong>"+a+"<br><br>"+t.message,"df-error"),console.log(t),e.container.removeClass("df-loading").addClass("df-error"),i.dispose()}}),o.getTotalLength=function(){return i.pdfLoadProgress._transport._networkStream._fullRequestReader.contentLength},o.onProgress=function(t){if(null!==e){var i=100*t.loaded/o.getTotalLength();isNaN(i)?t&&t.loaded?(void 0===o.lastLoaded||o.lastLoaded+25e4<t.loaded)&&(o.lastLoaded=t.loaded,e.updateInfo(e.options.text.loading+" PDF "+(Math.ceil(t.loaded/1e4)/100).toFixed(2).toString()+"MB ...")):e.updateInfo(e.options.text.loading+" PDF ..."):e.updateInfo(e.options.text.loading+" PDF "+Math.ceil(Math.min(100,i)).toString().split(".")[0]+"% ...")}}}},{key:"pdfFetchStarted",value:function(){this.pdfFetchStatusCount=0,this.app.container.addClass("df-fetch-pdf"),this.pdfFetchStatus=u.REQUEST_STATUS.COUNT}},{key:"checkRequestQueue",value:function(){}},{key:"finalize",value:function(){var e=this.app,t=this;null!==e&&null!==e.options&&(t.linkService=new e0,t.linkService.setDocument(t.pdfDocument,null),t.linkService.setViewer(e),t.pdfDocument.getOutline().then(function(i){!0===e.options.overwritePDFOutline&&(i=[]),i=i||[],t.outline=i}).finally(function(){t._getLabels()}))}},{key:"_getLabels",value:function(){var e=this.app,t=this;t.pdfDocument.getPageLabels().then(function(i){if(i&&!0!==e.options.disablePageLabels){for(var n=i.length,o=0,s=0,a=0;a<n;a++){var r=i[a];if(r===(a+1).toString())o++;else if(""===r)s++;else break}o>=n||s>=n||(t.pageLabels=i)}}).finally(function(){t._getPermissions()})}},{key:"_getPermissions",value:function(){var e=this.app,t=this;t.pdfDocument.getPermissions().then(function(i){null!==i&&Array.isArray(i)&&(t.canPrint=i.indexOf(pdfjsLib.PermissionFlag.PRINT)>-1,!1==t.canPrint&&(console.log("PDF printing is disabled."),e.options.showPrintControl=e.options.showPrintControl&&t.canPrint))}).finally(function(){t._documentLoaded()})}},{key:"processPage",value:function(e){var t=this.app,i=this,n=e.pageNumber,o=performance.now(),s=t.viewer.getTextureSize(e);if(!0===DEARFLIP.defaults.cachePDFTexture&&void 0!==this.getCache(n,s.height)){t.applyTexture(this.getCache(n,s.height),e),e$.log("Texture loaded from cache for : "+n);return}var a=t.viewer.getDocumentPageNumber(n);e$.log("Requesting PDF Page:"+a),i.pdfDocument.getPage(a).then(function(r){i.viewPorts[n]||(e.isFreshPage=!0,i.viewPorts[n]=r.getViewport({scale:1,rotation:r._pageInfo.rotate+t.options.pageRotation}));var l,h=t.viewer.getRenderContext(r,e);e.isFreshPage&&(null===(l=t.viewer.getPageByNumber(e.pageNumber))||void 0===l||l.changeTexture(e.pageNumber,h.canvas.height)),e$.log("Page "+n+" rendering - "+h.canvas.width+"x"+h.canvas.height),e.trace=i.requestIndex++,i.requestedPages+=","+e.trace+"["+a+"|"+h.canvas.height+"]",r.cleanupAfterRender=!1,r.render(h).promise.then(function(){if(t.applyTexture(h.canvas,e),!0===DEARFLIP.defaults.cachePDFTexture&&i.setCache(e.pageNumber,h.canvas,s.height),!0===t.options.cleanupAfterRender){var l=","+e.trace+"["+a+"|"+h.canvas.height+"]";e$.log("CleanUp Requesting for ("+n+") actual "+a),i.requestedPages.indexOf(l)>-1&&(i.requestedPages=i.requestedPages.replace(l,""),-1==i.requestedPages.indexOf("["+a+"|")?(e$.log("CleanUp Passed for ("+n+") actual "+a),i.pagesToClean.push(r),i.pagesToClean.length>0&&i.cleanUpPages()):e$.log("CleanUp Cancelled waiting for ("+n+") actual "+a+" : "+i.requestedPages))}h=null,e$.log("Rendered "+n+" in "+(performance.now()-o)+" milliseconds")}).catch(function(e){console.log(e)})}).catch(function(e){console.log(e)})}},{key:"cleanUpPages",value:function(){for(;this.pagesToClean.length>0;){var e=this.pagesToClean.splice(-1)[0];e$.log("Cleanup Completed for PDF page: "+(e._pageIndex+1)),e.cleanup()}}},{key:"clearSearch",value:function(){this.searchHits=[],this.searchHitItemsCache=[],this.totalHits=0,this.app.searchResults.html(""),this.app.container.removeClass("df-search-open"),this.textSearch="",this.app.container.find(".df-search-hits").remove()}},{key:"search",value:function(e){if(this.textSearch!==e){if(this.clearSearch(),e.length<3&&""!=e){this.app.updateSearchInfo("Minimum 3 letters required.");return}this.textSearch=e,this.textSearchLength=e.length,this.app.searchContainer.addClass("df-searching"),this.app.container.addClass("df-fetch-pdf"),this._search(e,1)}}},{key:"_search",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,i=this;i.app.updateSearchInfo("Searching Page: "+t),i.searchPage(t).then(function(n){for(var o,s=RegExp(e,"gi"),a=[];o=s.exec(n);)a.push({index:o.index,length:i.textSearchLength});if(i.searchHits[t]=a,a.length>0){var r=i.app.viewer.searchPage(t);!0===r.include&&(i.totalHits+=a.length,i.app.searchResults.append('<div class="df-search-result '+(i.app.currentPageNumber===t?"df-active":"")+'" data-df-page="'+t+'"><span>Page '+r.label+"</span><span>"+a.length+" "+(a.length>1?"results":"result")+"</span></div>"))}i.app.viewer.isActivePage(t)&&(i.processTextContent(t,i.app.viewer.getTextElement(t,!0)),i.app.ui.update()),i._search(e,t+1)}).catch(function(){}).finally(function(){0==i.totalHits?i.app.updateSearchInfo("No results Found!"):i.app.updateSearchInfo(i.totalHits+" results found"),i.app.searchContainer.removeClass("df-searching"),i.app.container.removeClass("df-fetch-pdf")})}},{key:"prepareTextContent",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this;if(void 0==n.textContentJoinedSearch[t]||i){var o,n=this,s=0,a=0,r=0;n.textContentSearch[t]=[],n.textContent[t]=[],n.textOffsetSearch[t]=[],n.textOffset[t]=[],n.textContentJoinedSearch[t]=[],n.textContentJoined[t]=[];for(var l=0;l<e.items.length;l++)o=e.items[l],n.textContentSearch[t].push(!0===o.hasEOL?o.str+" ":o.str),n.textContent[t].push(o.str+" "),a+=r=(o.str.length||0)+(!0===o.hasEOL?1:0),n.textOffsetSearch[t].push({length:r,offset:a-r}),s+=r=(o.str.length||0)+1,n.textOffset[t].push({length:r,offset:s-r});n.textContentJoinedSearch[t]=n.textContentSearch[t].join(""),n.textContentJoined[t]=n.textContent[t].join("")}}},{key:"searchPage",value:function(e){var t=this;return new Promise(function(i,n){if(t._isValidPage(e))try{var o=t.app.viewer.getDocumentPageNumber(e);void 0==t.textContentJoinedSearch[o]?t.pdfDocument.getPage(o).then(function(e){e.getTextContent().then(function(e){t.prepareTextContent(e,o),i(t.textContentJoinedSearch[o])})}):i(t.textContentJoinedSearch[o])}catch(e){e$.log(e),n(e)}else n()})}}]),o}(e1);function e3(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e5(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function e8(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function e4(e,t,i){return t&&e8(e.prototype,t),i&&e8(e,i),e}function e9(e){return(e9=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function e7(e,t){return(e7=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}u.providers.pdf=e2;var e6=u.jQuery,te=u.utils,tt=/*#__PURE__*/function(){function e(t){e5(this,e),this._viewPort=new tn(0,0),this._pageInfo={rotate:0},this.src=t.src}return e4(e,[{key:"getViewport",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{scale:1};return new tn(this._viewPort.height*e.scale,this._viewPort.width*e.scale,e.scale)}}]),e}(),ti=/*#__PURE__*/function(){function e(t){e5(this,e),this.source=[],this.pages=[],this.numPages=t.length;for(var i=0;i<t.length;i++)this.source[i]=te.httpsCorrection(t[i].toString()),this.pages.push(new tt({src:this.source[i]}))}return e4(e,[{key:"getPage",value:function(e){var t=this;return new Promise(function(i,n){try{var o=e6("<img/>");o.attr("src",t.source[e-1]),o[0].crossOrigin="Anonymous",o.on("load",function(){e6(this).off();var e=new tt({src:this.src});e._viewPort.height=this.height,e._viewPort.width=this.width,e._viewPort.scale=1,e.image=this,i(e)})}catch(e){n(e)}})}}]),e}(),tn=/*#__PURE__*/function(){function e(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;e5(this,e),this.scale=n,this.height=t,this.width=i,this.scale=n,this.transform=[0,0,0,0,0,this.height]}return e4(e,[{key:"clone",value:function(){return new e(this.height,this.width,this.scale)}}]),e}(),to=/*#__PURE__*/function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&e7(e,t)}(n,e);var t,i=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,i=e9(n);return e=t?Reflect.construct(i,arguments,e9(this).constructor):i.apply(this,arguments),e&&("object"==(e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e)||"function"==typeof e)?e:e3(this)});function n(e,t){e5(this,n);var o,s=(o=i.call(this,e,t)).app,a=e3(o);return a.document=new ti(s.options.source),a.pageCount=a.document.numPages,a.numPages=a.document.numPages,a.loadDocument(),o}return e4(n,[{key:"dispose",value:function(){}},{key:"loadDocument",value:function(){var e=this.app,t=this.app.options,i=this;i.document.getPage(1).then(function(n){i.defaultPage=n;var o,s=i.defaultPage.viewPort=n._viewPort,a=i.defaultPage.pageRatio=s.width/s.height;i.viewPorts[1]=s,e.dimensions.defaultPage={ratio:a,viewPort:s,width:s.width,height:s.height},e.dimensions.maxTextureHeight=(null!==(o=t.maxTextureSize)&&void 0!==o?o:3200)/(a>1?a:1),e.dimensions.maxTextureWidth=e.dimensions.maxTextureHeight*a,e.dimensions.autoHeightRatio=1/a,i._page1Pass=!0,i.pagesLoaded()}),i.pageCount>1&&!0===e.checkSecondPage?i.document.getPage(2).then(function(e){var t=e._viewPort;i._page2Ratio=t.width/t.height,i.viewPorts[2]=t,i._page2Pass=!0,i.pagesLoaded()}):(i._page2Pass=!0,i.pagesLoaded())}},{key:"finalize",value:function(){var e=this.app;null!==e&&null!==e.options&&(this.linkService=new e0,this.linkService.setViewer(e),this._documentLoaded())}},{key:"processPage",value:function(e){var t=this.app,i=this,n=e.pageNumber,o=performance.now(),s=t.viewer.getDocumentPageNumber(n);te.log("Requesting PDF Page:"+s),i.document.getPage(s).then(function(s){i.viewPorts[n]||(e.isFreshPage=!0,i.viewPorts[n]=s._viewPort);var a,r,l=t.viewer.getRenderContext(s,e);(e.isFreshPage&&(null===(a=t.viewer.getPageByNumber(e.pageNumber))||void 0===a||a.changeTexture(e.pageNumber,l.canvas.height)),e.preferCanvas=!0,!0===e.preferCanvas)?(l.canvas.getContext("2d").drawImage(s.image,l.viewport.transform[4],0,l.canvas.width*(null!==(r=l.viewport.widthFix)&&void 0!==r?r:1),l.canvas.height),t.applyTexture(l.canvas,e)):t.applyTexture({src:s.src,height:l.canvas.height,width:l.canvas.width},e),te.log("Rendered "+n+" in "+(performance.now()-o)+" milliseconds")})}}]),n}(e1);function ts(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function ta(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function tr(e,t,i){return t&&ta(e.prototype,t),i&&ta(e,i),e}function tl(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}u.providers.image=to,i(101);var th=u.jQuery,tu=u.utils,tp=u.REQUEST_STATUS,tc=/*#__PURE__*/function(){function e(t,i){ts(this,e),this.options=t,this.app=i,this.parentElement=this.app.container,this.element=th("<div>",{class:"df-ui"}),this.leftElement=th("<div>",{class:"df-ui-left"}).appendTo(this.element),this.centerElement=th("<div>",{class:"df-ui-center"}).appendTo(this.element),this.rightElement=th("<div>",{class:"df-ui-right"}).appendTo(this.element),this.parentElement.append(this.element),this.events={},this.controls={}}return tr(e,[{key:"init",value:function(){var e=this,t="<div>",i=this.app,n=this.controls,o=i.options.text,s=i.options.icons;e.createLogo(),this.openRight=n.openRight=th(t,{class:"df-ui-nav df-ui-next",title:i.isRTL?o.previousPage:o.nextPage,html:'<div class="df-ui-btn '+s.next+'"></div>'}).on("click",function(){i.openRight()}),this.openLeft=n.openLeft=th(t,{class:"df-ui-nav df-ui-prev",title:i.isRTL?o.nextPage:o.previousPage,html:'<div class="df-ui-btn '+s.prev+'"></div>'}).on("click",function(){i.openLeft()}),!0==i.options.autoPlay&&(this.play=n.play=tu.createBtn("play",s.play,o.play).on("click",function(){var e=th(this);i.setAutoPlay(!e.hasClass(i.options.icons.pause))}),i.setAutoPlay(i.options.autoPlayStart)),this.pageNumber=n.pageNumber=tu.createBtn("page").on("change",function(){i.gotoPageLabel(n.pageInput.val())}).on("keyup",function(e){13===e.keyCode&&i.gotoPageLabel(n.pageInput.val())});var a="df_book_page_number_"+Math.ceil(performance.now()/10);this.pageInput=n.pageInput=th('<input id="'+a+'" type="text"/>').appendTo(n.pageNumber),this.pageLabel=n.pageLabel=th('<label for="'+a+'"></label>').appendTo(n.pageNumber),this.thumbnail=n.thumbnail=tu.createBtn("thumbnail",s.thumbnail,o.toggleThumbnails),n.thumbnail.on("click",function(){var t=th(this);null==i.thumblist&&i.initThumbs(),i.thumbContainer.toggleClass("df-sidemenu-visible"),t.toggleClass("df-active"),t.hasClass("df-active")&&(t.siblings(".df-active").trigger("click"),i.thumbRequestStatus=tp.ON),e.update(),!1===i.options.sideMenuOverlay&&i.resizeRequestStart()}).addClass("df-sidemenu-trigger"),i.hasOutline()&&(this.outline=n.outline=tu.createBtn("outline",s.outline,o.toggleOutline),n.outline.on("click",function(){var t=th(this);if(null==i.outlineViewer&&i.initOutline(),i.outlineContainer){var n=i.outlineContainer;t.toggleClass("df-active"),n.toggleClass("df-sidemenu-visible"),t.hasClass("df-active")&&t.siblings(".df-active").trigger("click"),e.update(),!1===i.options.sideMenuOverlay&&i.resizeRequestStart()}}).addClass("df-sidemenu-trigger")),!0===i.options.showSearchControl&&!0!==tu.isMobile&&"string"==typeof i.options.source&&(n.search=tu.createBtn("search",s.search,o.search),n.search.on("click",function(){var t=th(this);if(null==i.searchContainer&&i.initSearch(),i.searchContainer){var n=i.searchContainer;t.toggleClass("df-active"),n.toggleClass("df-sidemenu-visible"),t.hasClass("df-active")&&(t.siblings(".df-active").trigger("click"),i.searchBox[0].focus()),e.update(),!1===i.options.sideMenuOverlay&&i.resizeRequestStart()}}).addClass("df-sidemenu-trigger"));var r=e.element;if(this.zoomIn=n.zoomIn=tu.createBtn("zoomin",s.zoomin,o.zoomIn).on("click",function(){i.zoom(1),e.update()}),this.zoomOut=n.zoomOut=tu.createBtn("zoomout",s.zoomout,o.zoomOut).on("click",function(){i.zoom(-1),e.update()}),this.resetZoom=n.resetZoom=tu.createBtn("resetzoom",s.resetzoom,o.resetZoom).on("click",function(){i.resetZoom(-1),e.update()}),i.viewer.isFlipBook){if(i.pageCount>2){var l=i.viewer.pageMode===u.FLIPBOOK_PAGE_MODE.SINGLE;this.pageMode=n.pageMode=tu.createBtn("pagemode",s[l?"doublepage":"singlepage"],l?o.doublePageMode:o.singlePageMode).on("click",function(){var e=th(this);i.viewer.setPageMode({isSingle:!e.hasClass(s.doublepage)}),i.viewer.pageModeChangedManually=!0})}}else this.pageFit=n.pageFit=tu.createBtn("pagefit",s.pagefit,o.pageFit).on("click",function(){var e=n.pageFit;!0==!e.hasClass(s.widthfit)?(e.addClass(s.widthfit),e.html("<span>"+o.widthFit+"</span>"),e.attr("title",o.widthFit)):(e.removeClass(s.widthfit),e.html("<span>"+o.pageFit+"</span>"),e.attr("title",o.pageFit))});if(this.share=n.share=tu.createBtn("share",s.share,o.share).on("click",function(){null==e.shareBox&&(e.shareBox=new td(i.container,i.options)),!0===e.shareBox.isOpen?e.shareBox.close():(e.shareBox.update(i.getURLHash()),e.shareBox.show())}),this.more=n.more=tu.createBtn("more",s.more).on("click",function(t){!0!==e.moreContainerOpen&&(th(this).addClass("df-active"),e.moreContainerOpen=!0,t.stopPropagation())}),this.startPage=n.startPage=tu.createBtn("start",s.start,o.gotoFirstPage).on("click",function(){i.start()}),this.endPage=n.endPage=tu.createBtn("end",s.end,o.gotoLastPage).on("click",function(){i.end()}),!0===i.options.showPrintControl&&!0!==tu.isMobile&&"string"==typeof i.options.source&&(this.print=n.print=tu.createBtn("print",s.print,o.print).on("click",function(){u.printHandler=u.printHandler||new tg,u.printHandler.printPDF(i.options.source)})),!0===i.options.showDownloadControl&&"string"==typeof i.options.source){var h="df-ui-btn df-ui-download "+s.download;this.download=n.download=th('<a download target="_blank" class="'+h+'"><span>'+o.downloadPDFFile+"</span></a>"),n.download.attr("href",tu.httpsCorrection(i.options.source)).attr("title",o.downloadPDFFile)}e.moreContainer=th(t,{class:"df-more-container"}),n.more.append(e.moreContainer),!0===i.options.isLightBox&&!0!==i.fullscreenSupported||(this.fullScreen=n.fullScreen=tu.createBtn("fullscreen",s.fullscreen,o.toggleFullscreen).on("click",i.switchFullscreen.bind(i))),i.viewer.initCustomControls();var p=i.options.allControls.replace(/ /g,"").split(","),c=","+i.options.moreControls.replace(/ /g,"")+",",d=","+i.options.hideControls.replace(/ /g,"")+",";i.options.leftControls.replace(/ /g,""),i.options.rightControls.replace(/ /g,""),d+=",";for(var f=0;f<p.length;f++){var g=p[f];if(0>d.indexOf(","+g+",")){var v=n[g];null!=v&&(void 0===v?"undefined":tl(v))=="object"&&(c.indexOf(","+g+",")>-1&&"more"!==g&&"pageNumber"!==g?e.moreContainer.append(v):!0==i.options.controlsFloating?r.append(v):this.centerElement.append(v))}}0==e.moreContainer.children().length&&this.more.addClass("df-hidden"),i.container.append(r),i.container.append(n.openLeft),i.container.append(this.controls.openRight),window.addEventListener("click",e.events.closePanels=e.closePanels.bind(e),!1),window.addEventListener("keyup",e.events.keyup=e.keyUp.bind(e),!1),document.addEventListener("fullscreenchange",e.events.fullscreenChange=e.fullscreenChange.bind(e),!1),!0===i.options.autoOpenThumbnail&&e.controls.thumbnail.trigger("click"),i.hasOutline()&&!0===i.options.autoOpenOutline&&e.controls.outline.trigger("click"),i.executeCallback("onCreateUI")}},{key:"closePanels",value:function(e){if(!0===this.moreContainerOpen){var t;null===(t=this.controls.more)||void 0===t||t.removeClass("df-active"),this.moreContainerOpen=!1}}},{key:"fullscreenChange",value:function(e){void 0===tu.getFullscreenElement()&&!0===this.app.isFullscreen&&this.app.switchFullscreen()}},{key:"keyUp",value:function(e){var t=this.app;if("INPUT"!==e.target.nodeName){var i=!1;switch(t.options.arrowKeysAction===u.ARROW_KEYS_ACTIONS.NAV&&((!0===t.isFullscreen||!0===t.options.isLightBox)&&(i=!0),!0!=t.options.isLightBox&&u.activeEmbeds.length<2&&!1===th("body").hasClass("df-lightbox-open")&&(i=!0)),e.keyCode){case 27:u.activeLightBox&&u.activeLightBox.app&&!tu.isChromeExtension()&&u.activeLightBox.closeButton.trigger("click");break;case 37:i&&t.openLeft();break;case 39:i&&t.openRight()}}}},{key:"createLogo",value:function(){var e=this.app,t=null;e.options.logo.indexOf("<")>-1?t=th(e.options.logo).addClass("df-logo df-logo-html"):e.options.logo.trim().length>2&&(t=th('<a class="df-logo df-logo-img" target="_blank" href="'+e.options.logoUrl+'"><img alt="" src="'+e.options.logo+'"/>')),this.element.append(t)}},{key:"dispose",value:function(){for(var e in this.controls)if(this.controls.hasOwnProperty(e)){var t=this.controls[e];null!==t&&(void 0===t?"undefined":tl(t))=="object"&&t.off().remove()}this.element.remove(),this.shareBox=tu.disposeObject(this.shareBox),window.removeEventListener("click",this.events.closePanels,!1),window.removeEventListener("keyup",this.events.keyup,!1),document.removeEventListener("fullscreenchange",this.events.fullscreenChange,!1)}},{key:"update",value:function(){var e=this.app,t=this.controls;!0!==this._pageLabelWidthSet&&(this.pageLabel.width(""),e.provider.pageLabels?this.pageLabel.html("88888888888888888".substring(0,3*e.pageCount.toString().length+4)):this.pageLabel.html("88888888888".substring(0,2*e.pageCount.toString().length+3)),this.pageNumber.width(this.pageLabel.width()),this.pageLabel.width(this.pageLabel.width()),this.pageLabel.html(""),this._pageLabelWidthSet=!0);var i=e.getCurrentLabel();i.toString()!==e.currentPageNumber.toString()?t.pageLabel.html(i+"("+e.currentPageNumber+"/"+e.pageCount+")"):t.pageLabel.html(i+"/"+e.pageCount),t.pageInput.val(i),e.container.toggleClass("df-sidemenu-open",e.container.find(".df-sidemenu-visible").length>0);var n=e.provider.totalHits>0&&e.container.find(".df-sidemenu-visible.df-search-container").length>0;if(e.container.toggleClass("df-search-open",n),n){var o=e.searchContainer.find(".df-search-result[data-df-page="+e.currentPageNumber+"]");if(e.searchContainer.find(".df-search-result.df-active").removeClass("df-active"),o.length>0&&!o.hasClass(".df-active")){o.addClass("df-active");var s=e.searchResults[0],a=s.scrollTop;a+s.getBoundingClientRect().height<(o=o[0]).offsetTop+o.scrollHeight?tu.scrollIntoView(o,null,!1):a>o.offsetTop&&tu.scrollIntoView(o)}}t.zoomIn.toggleClass("disabled",e.zoomValue===e.viewer.maxZoom),t.zoomOut.toggleClass("disabled",e.zoomValue===e.viewer.minZoom);var r=e.isRTL,l=e.currentPageNumber===e.startPage,h=e.currentPageNumber===e.endPage,u=l&&!r||h&&r,p=h&&!r||l&&r;t.openRight.toggleClass("df-hidden",p),t.openLeft.toggleClass("df-hidden",u),e.viewer.afterControlUpdate()}}]),e}(),td=/*#__PURE__*/function(){function e(t,i){ts(this,e),this.isOpen=!1,this.shareUrl="",this.init(t,i)}return tr(e,[{key:"init",value:function(e,t){var i=this;for(var n in i.wrapper=th('<div class="df-share-wrapper" style="display: none;">').on("click",function(){i.close()}),i.box=th('<div class="df-share-box">'),i.box.on("click",function(e){e.preventDefault(),e.stopPropagation()}),i.box.appendTo(i.wrapper).html('<span class="df-share-title">'+t.text.share+"</span>"),i.urlInput=th('<textarea name="df-share-url" class="df-share-url">').on("click",function(){this.select()}),i.box.append(i.urlInput),t.share)!function(e){if(t.share.hasOwnProperty(e)&&0>t.hideShareControls.indexOf(e)){var n=t.share[e];null!==n&&(i[e]=th("<div>",{class:"df-share-button df-share-"+e+" "+t.icons[e]}).on("click",function(e){e.preventDefault(),window.open(n.replace("{{url}}",encodeURIComponent(i.shareUrl)).replace("{{mailsubject}}",t.text.mailSubject),"Sharer","width=500,height=400"),e.stopPropagation()}),i.box.append(i[e]))}}(n);th(e).append(i.wrapper)}},{key:"show",value:function(){this.wrapper.show(),this.urlInput.val(this.shareUrl),this.urlInput.trigger("click"),this.isOpen=!0}},{key:"dispose",value:function(){for(var e in this)this.hasOwnProperty(e)&&this[e]&&this[e].off&&this[e].off();this.wrapper.remove()}},{key:"close",value:function(){this.wrapper.hide(),this.isOpen=!1}},{key:"update",value:function(e){this.shareUrl=e}}]),e}(),tf=/*#__PURE__*/function(){function e(t){ts(this,e),this.duration=300;var i=this;return i.lightboxWrapper=th("<div>").addClass("df-lightbox-wrapper"),i.backGround=th("<div>").addClass("df-lightbox-bg").appendTo(i.lightboxWrapper),i.element=th("<div>").addClass("df-app").appendTo(i.lightboxWrapper),i.controls=th("<div>").addClass("df-lightbox-controls").appendTo(i.lightboxWrapper),i.closeButton=th("<div>").addClass("df-lightbox-close df-ui-btn "+u.defaults.icons.close).on("click",function(){i.close(t)}).appendTo(i.controls),i.lightboxWrapper.append(i.element),i}return tr(e,[{key:"show",value:function(e){return 0===this.lightboxWrapper.parent().length&&th("body").append(this.lightboxWrapper),th("html,body").addClass("df-lightbox-open"),this.lightboxWrapper.show(),"function"==typeof e&&e(),this}},{key:"close",value:function(e){return this.lightboxWrapper.hide(),Array.prototype.forEach.call(u.utils.getSharePrefixes(),function(e){0===window.location.hash.indexOf("#"+e)&&history.replaceState(void 0,void 0,"#_")}),"function"==typeof e&&setTimeout(e,this.duration),th("html,body").removeClass("df-lightbox-open"),this.element.attr("class","df-app").attr("style",""),this.lightboxWrapper.attr("class","df-lightbox-wrapper").attr("style","display:none"),this.backGround.attr("style",""),this}}]),e}(),tg=/*#__PURE__*/function(){function e(){ts(this,e);var t=this;return t.frame=th('<iframe id="df-print-frame" style="display:none">').appendTo(th("body")),t.frame.on("load",function(){try{t.frame[0].contentWindow.print()}catch(e){console.log(e)}}),t}return tr(e,[{key:"printPDF",value:function(e){this.frame[0].src=e}}]),e}(),tv=/*#__PURE__*/function(){function e(t,i){ts(this,e),this.options=t,this.app=i,this.parentElement=t.parentElement,this.element=th("<div>",{class:"df-sidemenu-wrapper"}),this.parentElement.append(this.element),this.buttons=th("<div>",{class:"df-sidemenu-buttons df-ui-wrapper"}).appendTo(this.element),this.close=tu.createBtn("close",i.options.icons.close,i.options.text.close),this.buttons.append(this.close)}return tr(e,[{key:"dispose",value:function(){this.element.remove()}}]),e}(),tm=/*#__PURE__*/function(){function e(t){ts(this,e),this.outline=null,this.lastToggleIsShow=!0,this.container=t.container,this.linkService=t.linkService,this.outlineItemClass=t.outlineItemClass||"outlineItem",this.outlineToggleClass=t.outlineToggleClass||"outlineItemToggler",this.outlineToggleHiddenClass=t.outlineToggleHiddenClass||"outlineItemsHidden"}return tr(e,[{key:"dispose",value:function(){this.container&&this.container.parentNode&&this.container.parentNode.removeChild(this.container),this.linkService=null}},{key:"reset",value:function(){this.outline=null,this.lastToggleIsShow=!0;for(var e=this.container;e.firstChild;)e.removeChild(e.firstChild)}},{key:"_dispatchEvent",value:function(e){var t=document.createEvent("CustomEvent");t.initCustomEvent("outlineloaded",!0,!0,{outlineCount:e}),this.container.dispatchEvent(t)}},{key:"_bindLink",value:function(e,t){var i=this.linkService;if(!0===t.custom)e.href=i.getCustomDestinationHash(t.dest),e.onclick=function(){return i.customNavigateTo(t.dest),!1};else{if(t.url){pdfjsLib.addLinkAttributes(e,{url:t.url});return}e.href=i.getDestinationHash(t.dest),e.onclick=function(){return i.navigateTo(t.dest),!1}}}},{key:"_addToggleButton",value:function(e){var t=this,i=document.createElement("div");i.className=this.outlineToggleClass+" "+this.outlineToggleHiddenClass,i.onclick=(function(n){if(n.stopPropagation(),i.classList.toggle(this.outlineToggleHiddenClass),n.shiftKey){var o=!i.classList.contains(this.outlineToggleHiddenClass);t._toggleOutlineItem(e,o)}}).bind(this),e.insertBefore(i,e.firstChild)}},{key:"_toggleOutlineItem",value:function(e,t){this.lastToggleIsShow=t;for(var i=e.querySelectorAll("."+this.outlineToggleClass),n=0,o=i.length;n<o;++n)i[n].classList[t?"remove":"add"](this.outlineToggleHiddenClass)}},{key:"render",value:function(e){var t=e&&e.outline||null,i=0;if(this.outline&&this.reset(),this.outline=t,t){for(var n=document.createDocumentFragment(),o=[{parent:n,items:this.outline,custom:!1}],s=!1;o.length>0;)for(var a=o.shift(),r=a.custom,l=0,h=a.items.length;l<h;l++){var u=a.items[l],p=document.createElement("div");p.className=this.outlineItemClass;var c=document.createElement("a");if(null==u.custom&&null!=r&&(u.custom=r),this._bindLink(c,u),c.textContent=u.title.replace(/\x00/g,""),p.appendChild(c),u.items&&u.items.length>0){s=!0,this._addToggleButton(p);var d=document.createElement("div");d.className=this.outlineItemClass+"s",p.appendChild(d),o.push({parent:d,custom:u.custom,items:u.items})}a.parent.appendChild(p),i++}s&&(null!=this.container.classList?this.container.classList.add(this.outlineItemClass+"s"):null!=this.container.className&&(this.container.className+=" picWindow")),this.container.appendChild(n),this._dispatchEvent(i)}}}]),e}(),ty=/*#__PURE__*/function(){function e(t){ts(this,e);var i=function(){s.thumbRequestCount=0,s.thumbRequestStatus=tp.COUNT},n=this.itemHeight=t.itemHeight,o=this.itemWidth=t.itemWidth,s=this.app=t.app;this.items=t.items,this.generatorFn=t.generatorFn,this.totalRows=t.totalRows||t.items&&t.items.length,this.addFn=t.addFn,this.scrollFn=t.scrollFn,this.container=document.createElement("div");for(var a=this,r=0;r<this.totalRows;r++){var l=document.createElement("div"),h=r+1;l.id="df-thumb"+h;var u=document.createElement("div"),p=document.createElement("div"),c=document.createElement("div");c.className="df-wrapper",p.className="df-thumb-number",l.className="df-thumb",u.className="df-bg-image",c.style.height=n+"px",c.style.width=o+"px",p.innerText=s.provider.getLabelforPage(h),l.appendChild(c),c.appendChild(p),c.appendChild(u),this.container.appendChild(l)}a.dispose=function(){a.container&&a.container.parentNode&&a.container.parentNode.removeChild(a.container),a.container.removeEventListener("scroll",i)},a.container.addEventListener("scroll",i)}return tr(e,[{key:"processThumbRequest",value:function(){tu.log("Thumb Request Initiated");var e=this.app;if(e.thumbRequestStatus=tp.OFF,e.activeThumb!==e.currentPageNumber&&null!=e.thumbContainer&&e.thumbContainer.hasClass("df-sidemenu-visible")){var t=e.thumblist.container,i=t.scrollTop,n=t.getBoundingClientRect().height,o=e.thumbContainer.find("#df-thumb"+e.currentPageNumber);o.length>0?(e.thumbContainer.find(".df-selected").removeClass("df-selected"),o.addClass("df-selected"),i+n<(o=o[0]).offsetTop+o.scrollHeight?tu.scrollIntoView(o,null,!1):i>o.offsetTop&&tu.scrollIntoView(o),e.activeThumb=e.currentPageNumber):(th(t).scrollTop(124*e.currentPageNumber),e.thumbRequestStatus=tp.ON)}if(0===e.thumblist.container.getElementsByClassName("df-thumb-requested").length){var s=tu.getVisibleElements({container:e.thumblist.container,elements:e.thumblist.container.children});-1===s.indexOf(e.activeThumb)&&s.unshift(e.activeThumb);for(var a=0;a<s.length;a++){var r=e.thumblist.container.children[s[a]-1];if(void 0!==r&&!1===r.classList.contains("df-thumb-loaded")&&!1===r.classList.contains("df-thumb-requested"))return r.classList.add("df-thumb-requested"),tu.log("Thumb Requested for "+s[a]),e.provider.processPage({pageNumber:s[a],textureTarget:u.TEXTURE_TARGET.THUMB}),!1}}}},{key:"setPage",value:function(e){var t=this.app,i=e.pageNumber,n=e.texture;if(e.textureTarget===u.TEXTURE_TARGET.THUMB){var o=t.container.find("#df-thumb"+i);o.find(".df-wrapper").css({height:e.height,width:e.width}),o.find(".df-bg-image").css({backgroundImage:tu.bgImage(n)}),o.addClass("df-thumb-loaded").removeClass("df-thumb-requested")}tu.log("Thumbnail set for "+e.pageNumber),t.thumbRequestStatus=tp.ON}}]),e}();function tb(){if(void 0===u.openLocalFileInput){var e=u.openLocalFileInput=th('<input type="file" accept=".pdf" style="display:none">').appendTo(th("body")).data("df-option",u.openFileOptions);e.change(function(){var t,i=e[0].files;i.length&&(t=i[0],e.val(""),u.openFile(t))})}}u.openLightBox=function(e){u.activeLightBox||(u.activeLightBox=new tf(function(){u.activeLightBox.app&&(u.activeLightBox.app.closeRequested=!0,u.activeLightBox.app.analytics({eventAction:u.activeLightBox.app.options.analyticsViewerClose,options:u.activeLightBox.app.options})),u.activeLightBox.app=tu.disposeObject(u.activeLightBox.app)})),u.activeLightBox.duration=300,(void 0===u.activeLightBox.app||null===u.activeLightBox.app||!0===u.activeLightBox.app.closeRequested||u.openLocalFileInput==e)&&(u.activeLightBox.app=tu.disposeObject(u.activeLightBox.app),null===u.activeLightBox.app&&u.activeLightBox.show(function(){u.activeLightBox.app=new u.Application({transparent:!1,isLightBox:!0,height:"100%",dataElement:e,element:u.activeLightBox.element}),!0!==u._isHashTriggered&&history.pushState(null,null,"#"),u.activeLightBox.lightboxWrapper.toggleClass("df-lightbox-padded",!1===u.activeLightBox.app.options.popupFullsize),u.activeLightBox.lightboxWrapper.toggleClass("df-rtl",u.activeLightBox.app.options.readDirection===u.READ_DIRECTION.RTL),u.activeLightBox.backGround.css({backgroundColor:"transparent"===u.activeLightBox.app.options.backgroundColor?u.defaults.popupBackGroundColor:u.activeLightBox.app.options.backgroundColor})}))},u.checkBrowserURLforDefaults=function(){if(!tu.isIEUnsupported){var e=new URL(location.href).searchParams.get("viewer-type")||new URL(location.href).searchParams.get("viewertype"),t=new URL(location.href).searchParams.get("is-3d")||new URL(location.href).searchParams.get("is3d");e&&(u.defaults.viewerType=e),("true"===t||"false"===t)&&(u.defaults.is3D="true"===t)}},u.fileDropHandler=function(e,t){var i=e[0];"application/pdf"===i.type&&(t.preventDefault(),t.stopPropagation(),u.openFile(i))},u.openFile=function(e){if(e){var t;u.oldLocalFileObjectURL&&window.URL.revokeObjectURL(u.oldLocalFileObjectURL),u.oldLocalFileObjectURL=window.URL.createObjectURL(e),null===(t=u.openFileSelected)||void 0===t||t.call(u,{url:u.oldLocalFileObjectURL,file:e}),u.openURL(u.oldLocalFileObjectURL)}else u.openURL()},u.openURL=function(e){tb(),e&&(u.openFileOptions.source=e,u.openFileOptions.pdfParameters=null),u.openLightBox(u.openLocalFileInput)},u.openBase64=function(e){u.openFileOptions.source=null,u.openFileOptions.pdfParameters={data:atob(e)},u.openURL()},u.openLocalFile=function(){tb(),u.openLocalFileInput.click()},u.initControls=function(){var e=th("body");if(!1!==u.defaults.autoPDFLinktoViewer&&e.on("click",'a[href$=".pdf"]',function(e){var t=th(this);void 0!==t.attr("download")||"_blank"===t.attr("target")||t.hasClass("df-ui-btn")||t.parents(".df-app").length>0||(e.preventDefault(),t.data("df-source",t.attr("href")),u.openLightBox(t))}),window.addEventListener("popstate",function(e){u.activeLightBox&&u.activeLightBox.app&&!tu.isChromeExtension()&&u.activeLightBox.closeButton.trigger("click")}),e.on("click",".df-open-local-file",function(e){u.openLocalFile()}),e.on("click",".df-sidemenu-buttons .df-ui-close",function(){th(this).closest(".df-app").find(".df-ui-btn.df-active").trigger("click")}),e.on("mouseout",".df-link-content section.squareAnnotation, .df-link-content section.textAnnotation, .df-link-content section.freeTextAnnotation",function(){var e=th(this);u.handlePopup(e,!1)}),e.on("mouseover",".df-link-content section.squareAnnotation, .df-link-content section.textAnnotation, .df-link-content section.freeTextAnnotation",function(){var e=th(this);u.handlePopup(e,!0)}),u.handlePopup=function(e){var t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=e.closest(".df-container"),n=i.find(".df-comment-popup");if(n.toggleClass("df-active",t),t){var o=e[0].getBoundingClientRect(),s=i[0].getBoundingClientRect(),a=e.find(".popupWrapper").first();if(e.hasClass("popupTriggerArea")){var r=e.data("annotation-id");void 0!==r&&(a=e.siblings("[data-annotation-id=popup_"+r+"]"))}n.html(a.html());var l=o.left-s.left;l+360>s.width?l=s.width-360-10:l<10&&(l=10);var h=o.top-s.top+o.height+5;h+n.height()>s.height?h=o.top-n.height()-o.height-10:h<10&&(h=10),n.css({left:l,top:h})}},void 0!=u.fileDropElement){var t=th(u.fileDropElement);t.length>0&&(t.on("dragover",function(e){e.preventDefault(),e.stopPropagation(),th(this).addClass("df-dragging")}),t.on("dragleave",function(e){e.preventDefault(),e.stopPropagation(),th(this).removeClass("df-dragging")}),t.on("drop",function(e){var t=e.originalEvent.dataTransfer.files;t.length&&u.fileDropHandler(t,e)}))}};var tw=u.jQuery,tP=u.REQUEST_STATUS,tS=u.utils,tE=/*#__PURE__*/function(){var e;function t(e){var i,n,o;(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,t),this.options=e,this.viewerType=this.options.viewerType,this.startPage=1,this.endPage=1,this.element=tw(this.options.element),e.maxTextureSize=null!==(i=e.maxTextureSize)&&void 0!==i?i:2048,tS.isMobile&&(e.maxTextureSize=4096===e.maxTextureSize?3200:e.maxTextureSize),this.dimensions={padding:{},offset:{},pageFit:{},stage:{},isAutoHeight:"auto"===e.height,maxTextureSize:e.maxTextureSize},this.is3D=e.is3D,this.options.pixelRatio=tS.limitAt(this.options.pixelRatio,1,this.options.maxDPI),this.options.fakeZoom=null!==(n=this.options.fakeZoom)&&void 0!==n?n:1,this.events={},this.links=e.links,this.thumbSize=128,this.pendingZoom=!0,this.currentPageNumber=this.options.openPage||this.startPage,this.hashNavigationEnabled=!0===this.options.hashNavigationEnabled,this.pendingZoom=!0,this.zoomValue=1,this.pageScaling=u.PAGE_SCALE.MANUAL,this.isRTL=e.readDirection===u.READ_DIRECTION.RTL,this.jumpStep=1,this.resizeRequestStatus=tP.OFF,this.refreshRequestStatus=tP.OFF,this.refreshRequestCount=0,this.resizeRequestCount=0,this.fullscreenSupported=tS.hasFullscreenEnabled(),this.thumbRequestCount=0,this.isExternalReady=null===(o=this.options.isExternalReady)||void 0===o||o,this.init(),!0===this.options.autoLightBoxFullscreen&&!0===this.options.isLightBox&&this.switchFullscreen(),this.executeCallback("onCreate"),this.target=this}return e=[{key:"init",value:function(){var e=this.options;if(this.initDOM(),this.initResourcesLocation(),this.initInfo(),(null==e.source||0===e.source.length)&&null==e.pdfParameters){this.updateInfo("ERROR: Set a Valid Document Source.",u.INFO_TYPE.ERROR),this.container.removeClass("df-loading").addClass("df-error");return}if(tS.isIEUnsupported){this.updateInfo("Your browser (Internet Explorer) is out of date! <br><a href='https://browsehappy.com/'>Upgrade to a new browser.</a>","df-old-browser"),this.container.removeClass("df-loading").addClass("df-error");return}this.commentPopup=tw('<div class="df-comment-popup">').appendTo(this.container),this.viewer=new this.viewerType(e,this),this.sideMenu=new tv({parentElement:this.container},this),this.provider=new u.providers[e.providerType](e,this),this.state="loading",this.checkRequestQueue()}},{key:"initDOM",value:function(){this.element.addClass("df-app").removeClass("df-container df-loading"),this.container=tw("<div>").appendTo(this.element),this.container.addClass("df-container df-loading df-init df-controls-"+this.options.controlsPosition+(!0===this.options.controlsFloating?" df-float":" df-float-off")+("transparent"===this.options.backgroundColor?" df-transparent":"")+(!0===this.isRTL?" df-rtl":"")+(!0===tS.isIOS||!0===tS.isIPad?" df-ios":"")),this._offsetParent=this.container[0].offsetParent,this.backGround=tw("<div class='df-bg'>").appendTo(this.container).css({backgroundColor:this.options.backgroundColor,backgroundImage:this.options.backgroundImage?"url('"+this.options.backgroundImage+"')":""}),this.viewerContainer=tw("<div>").appendTo(this.container),this.viewerContainer.addClass("df-viewer-container")}},{key:"initResourcesLocation",value:function(){var e=this.options;if(void 0!==window[u.locationVar]){if(e.pdfjsSrc=window[u.locationVar]+"js/libs/pdf.min.js",e.threejsSrc=window[u.locationVar]+"js/libs/three.min.js",e.pdfjsWorkerSrc=window[u.locationVar]+"js/libs/pdf.worker.min.js",e.soundFile=window[u.locationVar]+e.soundFile,e.imagesLocation=window[u.locationVar]+e.imagesLocation,e.imageResourcesPath=window[u.locationVar]+e.imageResourcesPath,e.cMapUrl=window[u.locationVar]+e.cMapUrl,void 0!==e.pdfVersion){var t="";"latest"==e.pdfVersion||"beta"==e.pdfVersion?t="latest":"stable"==e.pdfVersion&&(t="stable"),("latest"==e.pdfVersion||"default"==e.pdfVersion)&&(Array.prototype.at,void 0===Array.prototype.at&&(t="stable",console.log("Proper Support for Latest version PDF.js 3.7 not available. Switching to PDF.js 2.5!"))),"default"!==t&&""!==t&&(e.pdfjsSrc=window[u.locationVar]+"js/libs/pdfjs/"+t+"/pdf.min.js",e.pdfjsWorkerSrc=window[u.locationVar]+"js/libs/pdfjs/"+t+"/pdf.worker.min.js"),"stable"===t&&(this.options.fakeZoom=1)}}else console.warn("DEARVIEWER locationVar not found!");this.executeCallback("onInitResourcesLocation")}},{key:"initEvents",value:function(){var e=this.container[0];window.addEventListener("resize",this.events.resize=this.resetResizeRequest.bind(this),!1),e.addEventListener("mousemove",this.events.mousemove=this.mouseMove.bind(this),!1),e.addEventListener("mousedown",this.events.mousedown=this.mouseDown.bind(this),!1),window.addEventListener("mouseup",this.events.mouseup=this.mouseUp.bind(this),!1),e.addEventListener("touchmove",this.events.touchmove=this.mouseMove.bind(this),!1),e.addEventListener("touchstart",this.events.touchstart=this.mouseDown.bind(this),!1),window.addEventListener("touchend",this.events.touchend=this.mouseUp.bind(this),!1)}},{key:"mouseMove",value:function(e){e.touches&&e.touches.length>1&&e.preventDefault(),!0===this.viewer.acceptAppMouseEvents&&this.viewer.mouseMove(e)}},{key:"mouseDown",value:function(e){this.userHasInteracted=!0,!0===this.viewer.acceptAppMouseEvents&&0===tw(e.srcElement).closest(".df-sidemenu").length&&this.viewer.mouseDown(e)}},{key:"mouseUp",value:function(e){this.viewer&&!0===this.viewer.acceptAppMouseEvents&&this.viewer.mouseUp(e)}},{key:"softDispose",value:function(){this.softDisposed=!0,this.provider.dispose(),this.viewer.dispose()}},{key:"softInit",value:function(){this.viewer=new this.viewerType(this.options,this),this.provider=new u.providers[this.options.providerType](this.options,this),this.softDisposed=!1}},{key:"dispose",value:function(){var e,t,i,n,o,s=this.container[0];clearInterval(this.autoPlayTimer),this.autoPlayTimer=null,this.autoPlayFunction=null,this.provider=tS.disposeObject(this.provider),this.contentProvider=null,this.target=null,this.viewer=tS.disposeObject(this.viewer),this.sideMenu=tS.disposeObject(this.sideMenu),this.ui=tS.disposeObject(this.ui),this.thumblist=tS.disposeObject(this.thumblist),this.outlineViewer=tS.disposeObject(this.outlineViewer),this.events&&(window.removeEventListener("resize",this.events.resize,!1),s.removeEventListener("mousemove",this.events.mousemove,!1),s.removeEventListener("mousedown",this.events.mousedown,!1),window.removeEventListener("mouseup",this.events.mouseup,!1),s.removeEventListener("touchmove",this.events.touchmove,!1),s.removeEventListener("touchstart",this.events.touchstart,!1),window.removeEventListener("touchend",this.events.touchend,!1)),this.events=null,this.options=null,this.element.removeClass("df-app"),this.viewerType=null,this.checkRequestQueue=null,null===(e=this.info)||void 0===e||e.remove(),this.info=null,null===(t=this.loadingIcon)||void 0===t||t.remove(),this.loadingIcon=null,null===(i=this.backGround)||void 0===i||i.remove(),this.backGround=null,null===(n=this.outlineContainer)||void 0===n||n.remove(),this.outlineContainer=null,null===(o=this.commentPopup)||void 0===o||o.remove(),this.commentPopup=null,this.viewerContainer.off(),this.viewerContainer.remove(),this.viewerContainer=null,this.container.off(),this.container.remove(),this.container=null,this.element.off(),this.element.data("df-app",null),this.element=null,this._offsetParent=null,this.dimensions=null}},{key:"resetResizeRequest",value:function(){this.resizeRequestStatus=tP.COUNT,this.resizeRequestCount=0,this.container.addClass("df-pendingresize"),this.pendingResize=!0}},{key:"initInfo",value:function(){this.info=tw("<div>",{class:"df-loading-info"}),this.container.append(this.info),this.info.html(this.options.text.loading+"..."),this.loadingIcon=tw("<div>",{class:"df-loading-icon"}).appendTo(this.container)}},{key:"updateInfo",value:function(e,t){tS.log(e),void 0!==this.info&&this.info.html(e)}},{key:"_documentLoaded",value:function(){tS.log("Document Loaded"),this.isDocumentReady=!0,this.contentProvider=this.provider,this.executeCallback("onDocumentLoad"),this.endPage=this.pageCount=this.provider.pageCount,this.currentPageNumber=this.getValidPage(this.currentPageNumber)}},{key:"_viewerPrepared",value:function(){tS.log("Viewer Prepared"),this.isViewerPrepared=!0,this.executeCallback("onViewerLoad")}},{key:"requestFinalize",value:function(){!0===this.isDocumentReady&&!0===this.isViewerPrepared&&!0===this.isExternalReady&&!0!==this.finalizeRequested&&(this.finalizeRequested=!0,this.finalize())}},{key:"finalizeComponents",value:function(){this.ui=new tc({},this),this.ui.init(),this.calculateLayout(),this.viewer.init()}},{key:"finalize",value:function(){this.resize(),this.ui.update(),this.initEvents(),!0==this.options.isLightBox&&this.analytics({eventAction:this.options.analyticsViewerOpen,options:this.options}),this.container.removeClass("df-loading df-init"),this.viewer.onReady(),this.analytics({eventAction:this.options.analyticsViewerReady,options:this.options}),this.executeCallback("onReady"),!0===this.options.dataElement.hasClass("df-hash-focused")&&(tS.focusHash(this.options.dataElement),this.options.dataElement.removeClass("df-hash-focused")),!0===this.hashNavigationEnabled&&this.getURLHash(),tS.log("App Finalized")}},{key:"initOutline",value:function(){var e=tw("<div>").addClass("df-outline-container df-sidemenu");e.append('<div class="df-sidemenu-title">'+this.options.text.outlineTitle+"</div>");var t=tw("<div>").addClass("df-wrapper");e.append(t),this.sideMenu.element.append(e),this.outlineContainer=e,this.outlineViewer=new tm({container:t[0],linkService:this.provider.linkService,outlineItemClass:"df-outline-item",outlineToggleClass:"df-outline-toggle",outlineToggleHiddenClass:"df-outlines-hidden"}),this.outlineViewer.render({outline:this.provider.outline})}},{key:"initThumbs",value:function(){var e=this;e.thumblist=new ty({app:e,addFn:function(e){},scrollFn:function(){e.thumbRequestStatus=tP.ON},itemHeight:e.thumbSize,itemWidth:tS.limitAt(Math.floor(e.dimensions.defaultPage.ratio*e.thumbSize),32,180),totalRows:e.pageCount}),e.thumblist.lastScrolled=Date.now(),e.thumbRequestStatus=tP.ON;var t=tw("<div>").addClass("df-thumb-container df-sidemenu");t.append('<div class="df-sidemenu-title">'+this.options.text.thumbTitle+"</div>"),t.append(tw(e.thumblist.container).addClass("df-wrapper")),e.thumbContainer=t,e.sideMenu.element.append(t),e.container.on("click",".df-thumb-container .df-thumb",function(t){t.stopPropagation();var i=tw(this).attr("id").replace("df-thumb","");e.gotoPage(parseInt(i,10))})}},{key:"initSearch",value:function(){var e=this,t=tw("<div>").addClass("df-search-container df-sidemenu");t.append('<div class="df-sidemenu-title">'+this.options.text.searchTitle+"</div>"),e.searchForm=tw('<div class="df-search-form">').appendTo(t),e.searchBox=tw('<input type="text" class="df-search-text" placeholder="'+this.options.text.searchPlaceHolder+'">').on("keyup",function(t){13===t.keyCode&&e.search()}).appendTo(e.searchForm),e.searchButton=tw('<div class="df-ui-btn df-search-btn df-icon-search">').on("click",function(t){e.search()}).appendTo(e.searchForm),e.clearButton=tw('<a class="df-search-clear">Clear</a>').on("click",function(t){e.clearSearch()}).appendTo(e.searchForm),e.searchInfo=tw('<div class="df-search-info">').appendTo(t),e.searchResults=tw('<div class="df-wrapper df-search-results">').appendTo(t),e.searchContainer=t,e.sideMenu.element.append(t),e.container.on("click",".df-search-result",function(t){t.stopPropagation();var i=tw(this).data("df-page");e.gotoPage(parseInt(i,10))})}},{key:"search",value:function(e){void 0==e&&(e=this.searchBox.val()),this.provider.search(e.trim())}},{key:"clearSearch",value:function(){this.searchBox.val(""),this.searchInfo.html(""),this.provider.clearSearch()}},{key:"updateSearchInfo",value:function(e){tS.log(e),void 0!==this.searchInfo&&this.searchInfo.html(e)}},{key:"checkRequestQueue",value:function(){var e=this;if(e.checkRequestQueue&&requestAnimationFrame(function(){e&&e.checkRequestQueue&&e.checkRequestQueue()}),!e.softDisposed){if("ready"!=e.state){"loading"===e.state&&!0===this.isDocumentReady&&!0===this.isViewerPrepared&&!0===this.isExternalReady&&(e.state="finalizing",this.finalizeComponents()),"finalizing"===e.state&&(e.state="ready",e.finalize());return}e.container&&e.container[0]&&e._offsetParent!==e.container[0].offsetParent&&(e._offsetParent=e.container[0].offsetParent,null!==e._offsetParent&&(e.resize(),e.resizeRequestStatus=tP.OFF),tS.log("Visibility Resize Detected")),(null!==e._offsetParent||e.isFullscreen)&&(TWEEN.getAll().length>0&&(TWEEN.update(),e.renderRequestStatus=tP.ON),e.resizeRequestStatus===tP.ON?(e.resizeRequestStatus=tP.OFF,e.resize()):e.resizeRequestStatus===tP.COUNT&&(e.resizeRequestCount++,e.resizeRequestCount>10&&(e.resizeRequestCount=0,e.resizeRequestStatus=tP.ON)),e.refreshRequestStatus===tP.ON?(e.refreshRequestStatus=tP.OFF,e.pendingResize=!1,e.viewer.refresh(),this.container.removeClass("df-pendingresize")):e.refreshRequestStatus===tP.COUNT&&(e.refreshRequestCount++,e.refreshRequestCount>3&&(e.refreshRequestCount=0,e.refreshRequestStatus=tP.ON)),e.textureRequestStatus===tP.ON&&e.processTextureRequest(),e.thumbRequestStatus===tP.ON?e.processThumbRequest():e.thumbRequestStatus===tP.COUNT&&(e.thumbRequestCount++,e.thumbRequestCount>3&&(e.thumbRequestCount=0,e.thumbRequestStatus=tP.ON)),e.renderRequestStatus===tP.ON&&(e.viewer.render(),e.renderRequestStatus=tP.OFF),e.provider.checkRequestQueue(),e.viewer.checkRequestQueue())}}},{key:"processTextureRequest",value:function(){var e,t,i=this.viewer,n=this.provider,o=i.getVisiblePages().main,s=0,a=this.zoomValue>1;if(i.isAnimating()&&!0!==DEARFLIP.defaults.instantTextureProcess)this.textureRequestStatus=tP.ON;else{tS.log("Texture Request Working");for(var r=0;r<o.length;r++){s=0;var l=o[r];if(l>0&&l<=this.pageCount&&((e=a?i.zoomViewer.getPageByNumber(l):i.getPageByNumber(l))&&(t=i.getTextureSize({pageNumber:l}),e.changeTexture(l,Math.floor(t.height))&&(n.processPage({pageNumber:l,textureTarget:a?u.TEXTURE_TARGET.ZOOM:u.TEXTURE_TARGET.VIEWER}),s++,this.viewer.getAnnotationElement(l,!0))),s>0))break}0===s&&(this.textureRequestStatus=tP.OFF)}}},{key:"applyTexture",value:function(e,t){var i=void 0!==e.toDataURL;if(t.textureTarget===u.TEXTURE_TARGET.THUMB){if(t.height=e.height,t.width=e.width,i){var n=e.toDataURL("image/png");this.provider.setCache(t.pageNumber,n,this.thumbSize),t.texture=n}else t.texture=e.src;this.thumblist.setPage(t)}else t.texture=i?e:e.src,!0===this.viewer.setPage(t)&&(this.provider.processAnnotations(t.pageNumber,this.viewer.getAnnotationElement(t.pageNumber,!0)),this.provider.processTextContent(t.pageNumber,this.viewer.getTextElement(t.pageNumber,!0)))}},{key:"processThumbRequest",value:function(){null!==this.thumblist&&void 0!==this.thumblist&&this.thumblist.processThumbRequest()}},{key:"refreshRequestStart",value:function(){this.refreshRequestStatus=tP.COUNT,this.refreshRequestCount=0}},{key:"renderRequestStart",value:function(){this.renderRequestStatus=tP.ON}},{key:"resizeRequestStart",value:function(){this.resizeRequestStatus=tP.ON}},{key:"zoom",value:function(e){this.pendingZoom=!0,this.zoomDelta=e,this.resize()}},{key:"resetZoom",value:function(){1!==this.zoomValue&&(this.zoomValue=1.001,this.zoom(-1))}},{key:"calculateLayout",value:function(){var e,t,i=this.isSideMenuOpen=this.container.hasClass("df-sidemenu-open"),n=this.dimensions,o=this.dimensions.padding,s=tw(window).height();n.offset={top:0,left:this.options.sideMenuOverlay||!i||this.isRTL?0:220,right:!this.options.sideMenuOverlay&&i&&this.isRTL?220:0,bottom:0,width:!this.options.sideMenuOverlay&&i?220:0},this.viewerContainer.css({left:n.offset.left,right:n.offset.right});var a=n.controlsHeight=this.container.find(".df-ui").height();if(o.top=this.options.paddingTop+(this.options.controlsPosition===u.CONTROLS_POSITION.TOP?a:0),o.left=this.options.paddingLeft,o.right=this.options.paddingRight,o.bottom=this.options.paddingBottom+(this.options.controlsPosition===u.CONTROLS_POSITION.BOTTOM?a:0),o.height=o.top+o.bottom,o.width=o.left+o.right,o.heightDiff=o.top-o.bottom,o.widthDiff=o.left-o.right,n.isFullSize=!0===this.isFullscreen,n.isFixedHeight=n.isFullSize||!n.isAutoHeight,n.containerWidth=n.isFullSize?tw(window).width():this.element.width(),this.container.toggleClass("df-xs",n.containerWidth<400).toggleClass("df-xss",n.containerWidth<320),n.maxHeight=s-(n.containerWidth>600&&null!==(t=tw(null!==(e=this.options.headerElementSelector)&&void 0!==e?e:"#wpadminbar").height())&&void 0!==t?t:0),n.isFixedHeight){if(n.isFullSize)n.maxHeight=s;else{this.element.height(this.options.height);var r=this.element.height();n.maxHeight=Math.min(r,n.maxHeight)}}n.width,n.stage.innerWidth=this.viewer._getInnerWidth();var l=n.stage.innerHeight=this.viewer._getInnerHeight(),h=this.viewer._getOuterHeight(l+n.padding.height);n.containerHeight=n.isFullSize?s:h,this.element.height(n.containerHeight);var p=this.element.height();n.isFullSize||p==n.containerHeight||(n.containerHeight=p,n.stage.innerHeight=p-n.padding.height,n.stage.height=p),n.origin={x:(o.widthDiff+n.containerWidth-n.offset.left-n.offset.right)/2,y:(o.heightDiff+n.containerHeight)/2},this.viewer.determinePageMode()}},{key:"resize",value:function(){var e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];tS.log("Resize Request Initiated"),this.calculateLayout(),this.viewer.handleZoom(),this.viewer.resize(),!1!==e&&(this.pendingZoom?(this.viewer.refresh(),tS.log("Pending Zoom updated")):this.refreshRequestStart(),this.ui.update(),this.renderRequestStatus=tP.ON,this.zoomChanged=!1,this.pendingZoom=!1,this.executeCallback("afterResize"))}},{key:"hasOutline",value:function(){if(this.provider.outline.length>0)return!0}},{key:"switchFullscreen",value:function(){var e,t,i=this,n=i.container[0];if(i.container.toggleClass("df-fullscreen",!0!==i.isFullscreen),(null==i?void 0:null===(t=i.ui)||void 0===t?void 0:null===(e=t.controls)||void 0===e?void 0:e.fullscreen)&&i.ui.controls.fullScreen.toggleClass(i.options.icons["fullscreen-off"],!0!==i.isFullscreen),!0!==i.isFullscreen){var o=null;n.requestFullscreen?o=n.requestFullscreen():n.msRequestFullscreen?o=n.msRequestFullscreen():n.mozRequestFullScreen?o=n.mozRequestFullScreen():n.webkitRequestFullscreen&&(o=n.webkitRequestFullscreen()),o&&o.then&&o.then(function(){i.refreshRequestStatus,tP.ON,i.resize()}),i.isFullscreen=!0}else i.isFullscreen=!1,document.exitFullscreen?document.fullscreenElement&&document.exitFullscreen():document.msExitFullscreen?document.msExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen&&document.webkitExitFullscreen(),tS.hasFullscreenEnabled()||i.container[0].scrollIntoView();tS.hasFullscreenEnabled()||(i.resizeRequestStatus=tP.ON)}},{key:"next",value:function(){this.jumpBy(this.jumpStep)}},{key:"prev",value:function(){this.jumpBy(-this.jumpStep)}},{key:"jumpBy",value:function(e){var t=this.currentPageNumber+e;t=tS.limitAt(t,this.startPage,this.endPage),!0!=this.anyFirstPageChanged&&(this.analytics({eventAction:this.options.analyticsFirstPageChange,options:this.options}),this.anyFirstPageChanged=!0),this.gotoPage(t),this.ui.update()}},{key:"openRight",value:function(){this.isRTL?this.prev():this.next()}},{key:"openLeft",value:function(){this.isRTL?this.next():this.prev()}},{key:"start",value:function(){this.gotoPage(this.startPage)}},{key:"end",value:function(){this.gotoPage(this.endPage)}},{key:"gotoPage",value:function(e){e=this.getValidPage(parseInt(e,10)),null!==this.viewer&&!1!==this.viewer.validatePageChange(e)&&(this.executeCallback("beforePageChanged"),this.requestDestRefKey=void 0,this.container.removeClass("df-fetch-pdf"),this.oldPageNumber=this.currentPageNumber,this.currentPageNumber=e,this.thumbRequestStatus=tP.ON,this.viewer.gotoPageCallBack&&this.viewer.gotoPageCallBack(),this.ui.update(),!0==this.autoPlay&&this.setAutoPlay(this.autoPlay),!0===this.hashNavigationEnabled&&this.getURLHash(),this.executeCallback("onPageChanged"))}},{key:"gotoPageLabel",value:function(e){this.gotoPage(this.provider.getPageNumberForLabel(e.toString().trim()))}},{key:"getCurrentLabel",value:function(){return this.provider.getLabelforPage(this.currentPageNumber)}},{key:"autoPlayFunction",value:function(){this&&this.autoPlay&&(tS.limitAt(this.currentPageNumber+this.jumpStep,this.startPage,this.endPage)!==this.currentPageNumber?this.next():this.setAutoPlay(!1))}},{key:"setAutoPlay",value:function(e){if(this.options.autoPlay){var t=(e=!0==e)?this.options.text.pause:this.options.text.play;this.ui.controls.play.toggleClass(this.options.icons.pause,e),this.ui.controls.play.html("<span>"+t+"</span>"),this.ui.controls.play.attr("title",t),clearInterval(this.autoPlayTimer),e&&(this.autoPlayTimer=setInterval(this.autoPlayFunction.bind(this),this.options.autoPlayDuration)),this.autoPlay=e}}},{key:"isValidPage",value:function(e){return this.provider._isValidPage(e)}},{key:"getValidPage",value:function(e){return isNaN(e)?e=this.currentPageNumber:e<1?e=1:e>this.pageCount&&(e=this.pageCount),e}},{key:"getURLHash",value:function(){if(null!=this.options.id){var e=tS.getSharePrefix(this.options.sharePrefix)+(null!=this.options.slug?this.options.slug:this.options.id)+"/";null!=this.currentPageNumber&&(e+=this.currentPageNumber+"/"),history.replaceState(void 0,void 0,"#"+e)}return window.location.href}},{key:"executeCallback",value:function(e){}},{key:"analytics",value:function(e){}}],function(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,e),t}();u.prepareOptions=function(e){t=e.element,(null!=tw&&"undefined"!=typeof Symbol&&tw[Symbol.hasInstance]?tw[Symbol.hasInstance](t):t instanceof tw)||(e.element=tw(e.element));var t,i=e.element;null==e.dataElement&&(e.dataElement=i);var n=e.dataElement,o=u.utils.getOptions(n),s=tw.extend(!0,{},u.defaults,e,o);s=tS.fallbackOptions(s),tS.log(s);var a=tw.extend(!0,{},u._defaults,s);return tS.isMobile&&"function"==typeof u.viewers[a.mobileViewerType]&&(a.viewerType=a.mobileViewerType),"function"!=typeof u.viewers[a.viewerType]?(console.warn("Invalid Viewer Type! "+a.viewerType+" | Using default Viewer!"),a.viewerType=u.viewers.default):a.viewerType=u.viewers[a.viewerType],a=tS.finalizeOptions(tS.sanitizeOptions(a))},u.Application=function(e){var t=u.prepareOptions(e),i=new tE(t);return e.element.data("df-app",i),null!=t.id&&!0!==t.isLightBox&&(window[t.id.toString()]=i),i},void 0!==window.jQuery&&!1==u.fakejQuery&&tw.fn.extend({dearviewer_options:function(e){return null==e&&(e={}),e.element=tw(this),new u.prepareOptions(e)},dearviewer:function(e){return null==e&&(e={}),e.element=tw(this),new u.Application(e)}});var tx=u.jQuery,tC=window.DFLIP=window.DEARFLIP=u;tC.defaults.viewerType="flipbook",tC.slug="dflip",tC.locationVar="dFlipLocation",tC.locationFile="dflip",tC.PAGE_MODE={SINGLE:1,DOUBLE:2,AUTO:null},tC.SINGLE_PAGE_MODE={ZOOM:1,BOOKLET:2,AUTO:null},tC.CONTROLSPOSITION={HIDDEN:"hide",TOP:"top",BOTTOM:"bottom"},tC.DIRECTION={LTR:1,RTL:2},tC.PAGE_SIZE={AUTO:0,SINGLE:1,DOUBLEINTERNAL:2},tC.parseFallBack=function(){tx(".df-posts").addClass("dflip-books"),tx(".dflip-books").addClass("df-posts"),tx("._df_button, ._df_thumb, ._df_book").each(function(){var e=tx(this);"true"!==e.data("df-parsed")&&(e.addClass("df-element"),e.hasClass("_df_book")||(e.hasClass("_df_thumb")?(e.attr("data-df-lightbox","thumb"),void 0!==e.attr("thumb")&&e.data("df-thumb",e.attr("thumb"))):e.attr("data-df-lightbox","button")))})},tC.parseBooks=function(){tC.parseFallBack(),tC.parseElements()};var tT=function(e){var t,i;return null!=e.source&&(Array===e.source.constructor||Array.isArray(e.source)||(t=e.source,null!=(i=Array)&&"undefined"!=typeof Symbol&&i[Symbol.hasInstance]?!!i[Symbol.hasInstance](t):t instanceof i))&&(e.providerType="image"),null!=e.cover3DType&&(!0==e.cover3DType||"true"==e.cover3DType?e.cover3DType=tC.FLIPBOOK_COVER_TYPE.BASIC:(!1==e.cover3DType||"false"==e.cover3DType)&&(e.cover3DType=tC.FLIPBOOK_COVER_TYPE.NONE)),void 0!==e.pageSize&&("1"===e.pageSize||1===e.pageSize||e.pageSize===tC.FLIPBOOK_PAGE_SIZE.SINGLE?e.pageSize=tC.FLIPBOOK_PAGE_SIZE.SINGLE:"2"===e.pageSize||2===e.pageSize||e.pageSize===tC.FLIPBOOK_PAGE_SIZE.DOUBLE_INTERNAL?e.pageSize=tC.FLIPBOOK_PAGE_SIZE.DOUBLE_INTERNAL:e.pageSize===tC.FLIPBOOK_PAGE_SIZE.DOUBLE_COVER_BACK||(e.pageSize=tC.FLIPBOOK_PAGE_SIZE.AUTO)),void 0!==e.pageMode&&("1"===e.pageMode||1===e.pageMode||e.pageMode===tC.FLIPBOOK_PAGE_MODE.SINGLE?e.pageMode=tC.FLIPBOOK_PAGE_MODE.SINGLE:"2"===e.pageMode||2===e.pageMode||e.pageMode===tC.FLIPBOOK_PAGE_MODE.DOUBLE?e.pageMode=tC.FLIPBOOK_PAGE_MODE.DOUBLE:e.pageMode=tC.FLIPBOOK_PAGE_MODE.AUTO),void 0!==e.singlePageMode&&("1"===e.singlePageMode||1===e.singlePageMode||e.singlePageMode===tC.FLIPBOOK_SINGLE_PAGE_MODE.ZOOM?e.singlePageMode=tC.FLIPBOOK_SINGLE_PAGE_MODE.ZOOM:"2"===e.singlePageMode||2===e.singlePageMode||e.singlePageMode===tC.FLIPBOOK_SINGLE_PAGE_MODE.BOOKLET?e.singlePageMode=tC.FLIPBOOK_SINGLE_PAGE_MODE.BOOKLET:e.singlePageMode=tC.FLIPBOOK_SINGLE_PAGE_MODE.AUTO),void 0!==e.controlsPosition&&"hide"===e.controlsPosition&&(e.controlsPosition=tC.CONTROLS_POSITION.HIDDEN),void 0!==e.overwritePDFOutline&&(e.overwritePDFOutline=g.isTrue(e.overwritePDFOutline)),void 0!==e.webgl&&(e.is3D=e.webgl=e.webgl,delete e.webgl),void 0!==e.webglShadow&&(e.has3DShadow=g.isTrue(e.webglShadow),delete e.webglShadow),void 0!==e.scrollWheel&&(g.isTrue(e.scrollWheel)&&(e.mouseScrollAction=tC.MOUSE_SCROLL_ACTIONS.ZOOM),delete e.scrollWheel),void 0!==e.stiffness&&delete e.stiffness,void 0!==e.soundEnable&&(e.enableSound=g.isTrue(e.soundEnable),delete e.soundEnable),void 0!==e.enableDownload&&(e.showDownloadControl=g.isTrue(e.enableDownload),delete e.enableDownload),void 0!==e.autoEnableOutline&&(e.autoOpenOutline=g.isTrue(e.autoEnableOutline),delete e.autoEnableOutline),void 0!==e.autoEnableThumbnail&&(e.autoOpenThumbnail=g.isTrue(e.autoEnableThumbnail),delete e.autoEnableThumbnail),void 0!==e.direction&&("2"===e.direction||2===e.direction||e.direction===tC.READ_DIRECTION.RTL?e.readDirection=tC.READ_DIRECTION.RTL:e.readDirection=tC.READ_DIRECTION.LTR,delete e.direction),void 0!==e.hard&&(e.flipbookHardPages=e.hard,"hard"===e.flipbookHardPages&&(e.flipbookHardPages="all"),delete e.hard),void 0!==e.forceFit&&delete e.forceFit,g.sanitizeOptions(e)};tx.fn.extend({flipBook:function(e,t){return null==t&&(t={}),t.source=e,t.element=tx(this),new u.Application(t)}}),tx(document).ready(function(){var e=tx("body");tC.executeCallback("beforeDearFlipInit"),void 0!==window.dFlipWPGlobal&&tx.extend(!0,tC.defaults,tT(window.dFlipWPGlobal)),tC.initUtils(),tC.initControls(),e.on("click",".df-element[data-df-lightbox],.df-element[data-lightbox]",function(e){var t=tx(this);e.preventDefault(),e.stopPropagation(),tC.openLightBox(t)}),tC.checkBrowserURLforDefaults(),tC.parseCSSElements(),tC.parseFallBack(),g.detectHash(),tC.parseNormalElements(),tC.executeCallback("afterDearFlipInit")}),g.finalizeOptions=function(e){return tT(e)},tC.executeCallback("onDearFlipLoad")}()}();
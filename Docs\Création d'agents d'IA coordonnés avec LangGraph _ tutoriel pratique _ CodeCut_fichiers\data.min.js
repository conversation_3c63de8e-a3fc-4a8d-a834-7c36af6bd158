/*! This file is auto-generated */
(()=>{"use strict";var e={66:e=>{var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===r}(e)}(e)};var r="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?a((r=e,Array.isArray(r)?[]:{}),e,t):e;var r}function o(e,t,r){return e.concat(t).map((function(e){return n(e,r)}))}function s(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(e){return!1}}function c(e,t,r){var o={};return r.isMergeableObject(e)&&s(e).forEach((function(t){o[t]=n(e[t],r)})),s(t).forEach((function(s){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,s)||(i(e,s)&&r.isMergeableObject(t[s])?o[s]=function(e,t){if(!t.customMerge)return a;var r=t.customMerge(e);return"function"==typeof r?r:a}(s,r)(e[s],t[s],r):o[s]=n(t[s],r))})),o}function a(e,r,s){(s=s||{}).arrayMerge=s.arrayMerge||o,s.isMergeableObject=s.isMergeableObject||t,s.cloneUnlessOtherwiseSpecified=n;var i=Array.isArray(r);return i===Array.isArray(e)?i?s.arrayMerge(e,r,s):c(e,r,s):n(r,s)}a.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,r){return a(e,r,t)}),{})};var u=a;e.exports=u},3249:e=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function n(e,t){var r=e._map,n=e._arrayTreeMap,o=e._objectTreeMap;if(r.has(t))return r.get(t);for(var s=Object.keys(t).sort(),i=Array.isArray(t)?n:o,c=0;c<s.length;c++){var a=s[c];if(void 0===(i=i.get(a)))return;var u=t[a];if(void 0===(i=i.get(u)))return}var l=i.get("_ekm_value");return l?(r.delete(l[0]),l[0]=t,i.set("_ekm_value",l),r.set(t,l),l):void 0}var o=function(){function e(t){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.clear(),t instanceof e){var r=[];t.forEach((function(e,t){r.push([t,e])})),t=r}if(null!=t)for(var n=0;n<t.length;n++)this.set(t[n][0],t[n][1])}var o,s,i;return o=e,s=[{key:"set",value:function(r,n){if(null===r||"object"!==t(r))return this._map.set(r,n),this;for(var o=Object.keys(r).sort(),s=[r,n],i=Array.isArray(r)?this._arrayTreeMap:this._objectTreeMap,c=0;c<o.length;c++){var a=o[c];i.has(a)||i.set(a,new e),i=i.get(a);var u=r[a];i.has(u)||i.set(u,new e),i=i.get(u)}var l=i.get("_ekm_value");return l&&this._map.delete(l[0]),i.set("_ekm_value",s),this._map.set(r,s),this}},{key:"get",value:function(e){if(null===e||"object"!==t(e))return this._map.get(e);var r=n(this,e);return r?r[1]:void 0}},{key:"has",value:function(e){return null===e||"object"!==t(e)?this._map.has(e):void 0!==n(this,e)}},{key:"delete",value:function(e){return!!this.has(e)&&(this.set(e,void 0),!0)}},{key:"forEach",value:function(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this;this._map.forEach((function(o,s){null!==s&&"object"===t(s)&&(o=o[1]),e.call(n,o,s,r)}))}},{key:"clear",value:function(){this._map=new Map,this._arrayTreeMap=new Map,this._objectTreeMap=new Map}},{key:"size",get:function(){return this._map.size}}],s&&r(o.prototype,s),i&&r(o,i),e}();e.exports=o}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};r.r(n),r.d(n,{AsyncModeProvider:()=>Ke,RegistryConsumer:()=>De,RegistryProvider:()=>Ve,combineReducers:()=>st,controls:()=>N,createReduxStore:()=>ge,createRegistry:()=>be,createRegistryControl:()=>R,createRegistrySelector:()=>w,createSelector:()=>W,dispatch:()=>nt,plugins:()=>i,register:()=>pt,registerGenericStore:()=>ut,registerStore:()=>lt,resolveSelect:()=>it,select:()=>ot,subscribe:()=>at,suspendSelect:()=>ct,use:()=>ft,useDispatch:()=>rt,useRegistry:()=>Ge,useSelect:()=>qe,useSuspenseSelect:()=>Je,withDispatch:()=>et,withRegistry:()=>tt,withSelect:()=>Ye});var o={};r.r(o),r.d(o,{countSelectorsByStatus:()=>Z,getCachedResolvers:()=>Q,getIsResolving:()=>K,getResolutionError:()=>q,getResolutionState:()=>$,hasFinishedResolution:()=>B,hasResolutionFailed:()=>X,hasResolvingSelectors:()=>Y,hasStartedResolution:()=>z,isResolving:()=>J});var s={};r.r(s),r.d(s,{failResolution:()=>re,failResolutions:()=>se,finishResolution:()=>te,finishResolutions:()=>oe,invalidateResolution:()=>ie,invalidateResolutionForStore:()=>ce,invalidateResolutionForStoreSelector:()=>ae,startResolution:()=>ee,startResolutions:()=>ne});var i={};r.r(i),r.d(i,{persistence:()=>Le});const c=window.wp.deprecated;var a=r.n(c);function u(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var l=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),f=()=>Math.random().toString(36).substring(7).split("").join("."),p={INIT:`@@redux/INIT${f()}`,REPLACE:`@@redux/REPLACE${f()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${f()}`};function g(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function d(e,t,r){if("function"!=typeof e)throw new Error(u(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(u(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(u(1));return r(d)(e,t)}let n=e,o=t,s=new Map,i=s,c=0,a=!1;function f(){i===s&&(i=new Map,s.forEach(((e,t)=>{i.set(t,e)})))}function y(){if(a)throw new Error(u(3));return o}function h(e){if("function"!=typeof e)throw new Error(u(4));if(a)throw new Error(u(5));let t=!0;f();const r=c++;return i.set(r,e),function(){if(t){if(a)throw new Error(u(6));t=!1,f(),i.delete(r),s=null}}}function S(e){if(!g(e))throw new Error(u(7));if(void 0===e.type)throw new Error(u(8));if("string"!=typeof e.type)throw new Error(u(17));if(a)throw new Error(u(9));try{a=!0,o=n(o,e)}finally{a=!1}return(s=i).forEach((e=>{e()})),e}S({type:p.INIT});return{dispatch:S,subscribe:h,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw new Error(u(10));n=e,S({type:p.REPLACE})},[l]:function(){const e=h;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(u(11));function r(){const e=t;e.next&&e.next(y())}r();return{unsubscribe:e(r)}},[l](){return this}}}}}function y(...e){return t=>(r,n)=>{const o=t(r,n);let s=()=>{throw new Error(u(15))};const i={getState:o.getState,dispatch:(e,...t)=>s(e,...t)},c=e.map((e=>e(i)));return s=function(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce(((e,t)=>(...r)=>e(t(...r))))}(...c)(o.dispatch),{...o,dispatch:s}}}var h=r(3249),S=r.n(h);const b=window.wp.reduxRoutine;var v=r.n(b);const O=window.wp.compose;function m(e){const t=Object.keys(e);return function(r={},n){const o={};let s=!1;for(const i of t){const t=e[i],c=r[i],a=t(c,n);o[i]=a,s=s||a!==c}return s?o:r}}function w(e){const t=new WeakMap,r=(...n)=>{let o=t.get(r.registry);return o||(o=e(r.registry.select),t.set(r.registry,o)),o(...n)};return r.isRegistrySelector=!0,r}function R(e){return e.isRegistryControl=!0,e}const E="@@data/SELECT",_="@@data/RESOLVE_SELECT",I="@@data/DISPATCH";function T(e){return null!==e&&"object"==typeof e}const N={select:function(e,t,...r){return{type:E,storeKey:T(e)?e.name:e,selectorName:t,args:r}},resolveSelect:function(e,t,...r){return{type:_,storeKey:T(e)?e.name:e,selectorName:t,args:r}},dispatch:function(e,t,...r){return{type:I,storeKey:T(e)?e.name:e,actionName:t,args:r}}},A={[E]:R((e=>({storeKey:t,selectorName:r,args:n})=>e.select(t)[r](...n))),[_]:R((e=>({storeKey:t,selectorName:r,args:n})=>{const o=e.select(t)[r].hasResolver?"resolveSelect":"select";return e[o](t)[r](...n)})),[I]:R((e=>({storeKey:t,actionName:r,args:n})=>e.dispatch(t)[r](...n)))},j=window.wp.privateApis,{lock:L,unlock:M}=(0,j.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/data");const P=()=>e=>t=>{return!(r=t)||"object"!=typeof r&&"function"!=typeof r||"function"!=typeof r.then?e(t):t.then((t=>{if(t)return e(t)}));var r},x=(e,t)=>()=>r=>n=>{const o=e.select(t).getCachedResolvers();return Object.entries(o).forEach((([r,o])=>{const s=e.stores[t]?.resolvers?.[r];s&&s.shouldInvalidate&&o.forEach(((o,i)=>{void 0!==o&&("finished"!==o.status&&"error"!==o.status||s.shouldInvalidate(n,...i)&&e.dispatch(t).invalidateResolution(r,i))}))})),r(n)};function F(e){return()=>t=>r=>"function"==typeof r?r(e):t(r)}function U(e){if(null==e)return[];const t=e.length;let r=t;for(;r>0&&void 0===e[r-1];)r--;return r===t?e:e.slice(0,r)}const C=(k="selectorName",e=>(t={},r)=>{const n=r[k];if(void 0===n)return t;const o=e(t[n],r);return o===t[n]?t:{...t,[n]:o}})(((e=new(S()),t)=>{switch(t.type){case"START_RESOLUTION":{const r=new(S())(e);return r.set(U(t.args),{status:"resolving"}),r}case"FINISH_RESOLUTION":{const r=new(S())(e);return r.set(U(t.args),{status:"finished"}),r}case"FAIL_RESOLUTION":{const r=new(S())(e);return r.set(U(t.args),{status:"error",error:t.error}),r}case"START_RESOLUTIONS":{const r=new(S())(e);for(const e of t.args)r.set(U(e),{status:"resolving"});return r}case"FINISH_RESOLUTIONS":{const r=new(S())(e);for(const e of t.args)r.set(U(e),{status:"finished"});return r}case"FAIL_RESOLUTIONS":{const r=new(S())(e);return t.args.forEach(((e,n)=>{const o={status:"error",error:void 0},s=t.errors[n];s&&(o.error=s),r.set(U(e),o)})),r}case"INVALIDATE_RESOLUTION":{const r=new(S())(e);return r.delete(U(t.args)),r}}return e}));var k;const D=(e={},t)=>{switch(t.type){case"INVALIDATE_RESOLUTION_FOR_STORE":return{};case"INVALIDATE_RESOLUTION_FOR_STORE_SELECTOR":if(t.selectorName in e){const{[t.selectorName]:r,...n}=e;return n}return e;case"START_RESOLUTION":case"FINISH_RESOLUTION":case"FAIL_RESOLUTION":case"START_RESOLUTIONS":case"FINISH_RESOLUTIONS":case"FAIL_RESOLUTIONS":case"INVALIDATE_RESOLUTION":return C(e,t)}return e};var V={};function G(e){return[e]}function H(e,t,r){var n;if(e.length!==t.length)return!1;for(n=r;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function W(e,t){var r,n=t||G;function o(){r=new WeakMap}function s(){var t,o,s,i,c,a=arguments.length;for(i=new Array(a),s=0;s<a;s++)i[s]=arguments[s];for(t=function(e){var t,n,o,s,i,c=r,a=!0;for(t=0;t<e.length;t++){if(!(i=n=e[t])||"object"!=typeof i){a=!1;break}c.has(n)?c=c.get(n):(o=new WeakMap,c.set(n,o),c=o)}return c.has(V)||((s=function(){var e={clear:function(){e.head=null}};return e}()).isUniqueByDependants=a,c.set(V,s)),c.get(V)}(c=n.apply(null,i)),t.isUniqueByDependants||(t.lastDependants&&!H(c,t.lastDependants,0)&&t.clear(),t.lastDependants=c),o=t.head;o;){if(H(o.args,i,1))return o!==t.head&&(o.prev.next=o.next,o.next&&(o.next.prev=o.prev),o.next=t.head,o.prev=null,t.head.prev=o,t.head=o),o.val;o=o.next}return o={val:e.apply(null,i)},i[0]=null,o.args=i,t.head&&(t.head.prev=o,o.next=t.head),t.head=o,o.val}return s.getDependants=n,s.clear=o,o(),s}function $(e,t,r){const n=e[t];if(n)return n.get(U(r))}function K(e,t,r){a()("wp.data.select( store ).getIsResolving",{since:"6.6",version:"6.8",alternative:"wp.data.select( store ).getResolutionState"});const n=$(e,t,r);return n&&"resolving"===n.status}function z(e,t,r){return void 0!==$(e,t,r)}function B(e,t,r){const n=$(e,t,r)?.status;return"finished"===n||"error"===n}function X(e,t,r){return"error"===$(e,t,r)?.status}function q(e,t,r){const n=$(e,t,r);return"error"===n?.status?n.error:null}function J(e,t,r){return"resolving"===$(e,t,r)?.status}function Q(e){return e}function Y(e){return Object.values(e).some((e=>Array.from(e._map.values()).some((e=>"resolving"===e[1]?.status))))}const Z=W((e=>{const t={};return Object.values(e).forEach((e=>Array.from(e._map.values()).forEach((e=>{var r;const n=null!==(r=e[1]?.status)&&void 0!==r?r:"error";t[n]||(t[n]=0),t[n]++})))),t}),(e=>[e]));function ee(e,t){return{type:"START_RESOLUTION",selectorName:e,args:t}}function te(e,t){return{type:"FINISH_RESOLUTION",selectorName:e,args:t}}function re(e,t,r){return{type:"FAIL_RESOLUTION",selectorName:e,args:t,error:r}}function ne(e,t){return{type:"START_RESOLUTIONS",selectorName:e,args:t}}function oe(e,t){return{type:"FINISH_RESOLUTIONS",selectorName:e,args:t}}function se(e,t,r){return{type:"FAIL_RESOLUTIONS",selectorName:e,args:t,errors:r}}function ie(e,t){return{type:"INVALIDATE_RESOLUTION",selectorName:e,args:t}}function ce(){return{type:"INVALIDATE_RESOLUTION_FOR_STORE"}}function ae(e){return{type:"INVALIDATE_RESOLUTION_FOR_STORE_SELECTOR",selectorName:e}}const ue=e=>{const t=[...e];for(let e=t.length-1;e>=0;e--)void 0===t[e]&&t.splice(e,1);return t},le=(e,t)=>Object.fromEntries(Object.entries(null!=e?e:{}).map((([e,r])=>[e,t(r,e)]))),fe=(e,t)=>t instanceof Map?Object.fromEntries(t):t instanceof window.HTMLElement?null:t;function pe(e){const t=new WeakMap;return{get(r,n){let o=t.get(r);return o||(o=e(r,n),t.set(r,o)),o}}}function ge(e,t){const r={},n={},i={privateActions:r,registerPrivateActions:e=>{Object.assign(r,e)},privateSelectors:n,registerPrivateSelectors:e=>{Object.assign(n,e)}},c={name:e,instantiate:c=>{const a=new Set,u=t.reducer,l=function(e,t,r,n){const o={...t.controls,...A},s=le(o,(e=>e.isRegistryControl?e(r):e)),i=[x(r,e),P,v()(s),F(n)],c=[y(...i)];"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&c.push(window.__REDUX_DEVTOOLS_EXTENSION__({name:e,instanceId:e,serialize:{replacer:fe}}));const{reducer:a,initialState:u}=t,l=m({metadata:D,root:a});return d(l,{root:u},(0,O.compose)(c))}(e,t,c,{registry:c,get dispatch(){return w},get select(){return N},get resolveSelect(){return U()}});L(l,i);const f=function(){const e={};return{isRunning:(t,r)=>e[t]&&e[t].get(ue(r)),clear(t,r){e[t]&&e[t].delete(ue(r))},markAsRunning(t,r){e[t]||(e[t]=new(S())),e[t].set(ue(r),!0)}}}();function p(e){return(...t)=>Promise.resolve(l.dispatch(e(...t)))}const g={...le(s,p),...le(t.actions,p)},h=pe(p),b=new Proxy((()=>{}),{get:(e,t)=>{const n=r[t];return n?h.get(n,t):g[t]}}),w=new Proxy(b,{apply:(e,t,[r])=>l.dispatch(r)});L(g,b);const R=t.resolvers?function(e){return le(e,(e=>e.fulfill?e:{...e,fulfill:e}))}(t.resolvers):{};function E(e,t){e.isRegistrySelector&&(e.registry=c);const r=(...t)=>{t=de(e,t);const r=l.__unstableOriginalGetState();return e.isRegistrySelector&&(e.registry=c),e(r.root,...t)};r.__unstableNormalizeArgs=e.__unstableNormalizeArgs;const n=R[t];return n?function(e,t,r,n,o){function s(e){const s=n.getState();if(o.isRunning(t,e)||"function"==typeof r.isFulfilled&&r.isFulfilled(s,...e))return;const{metadata:i}=n.__unstableOriginalGetState();z(i,t,e)||(o.markAsRunning(t,e),setTimeout((async()=>{o.clear(t,e),n.dispatch(ee(t,e));try{const o=r.fulfill(...e);o&&await n.dispatch(o),n.dispatch(te(t,e))}catch(r){n.dispatch(re(t,e,r))}}),0))}const i=(...t)=>(s(t=de(e,t)),e(...t));return i.hasResolver=!0,i}(r,t,n,l,f):(r.hasResolver=!1,r)}const _={...le(o,(function(e){const r=(...r)=>{const n=l.__unstableOriginalGetState(),o=r&&r[0],s=r&&r[1],i=t?.selectors?.[o];return o&&i&&(r[1]=de(i,s)),e(n.metadata,...r)};return r.hasResolver=!1,r})),...le(t.selectors,E)},I=pe(E);for(const[e,t]of Object.entries(n))I.get(t,e);const T=new Proxy((()=>{}),{get:(e,t)=>{const r=n[t];return r?I.get(r,t):_[t]}}),N=new Proxy(T,{apply:(e,t,[r])=>r(l.__unstableOriginalGetState())});L(_,T);const j=function(e,t){const{getIsResolving:r,hasStartedResolution:n,hasFinishedResolution:o,hasResolutionFailed:s,isResolving:i,getCachedResolvers:c,getResolutionState:a,getResolutionError:u,hasResolvingSelectors:l,countSelectorsByStatus:f,...p}=e;return le(p,((r,n)=>r.hasResolver?(...o)=>new Promise(((s,i)=>{const c=()=>e.hasFinishedResolution(n,o),a=t=>{if(e.hasResolutionFailed(n,o)){const t=e.getResolutionError(n,o);i(t)}else s(t)},u=()=>r.apply(null,o),l=u();if(c())return a(l);const f=t.subscribe((()=>{c()&&(f(),a(u()))}))})):async(...e)=>r.apply(null,e)))}(_,l),M=function(e,t){return le(e,((r,n)=>r.hasResolver?(...o)=>{const s=r.apply(null,o);if(e.hasFinishedResolution(n,o)){if(e.hasResolutionFailed(n,o))throw e.getResolutionError(n,o);return s}throw new Promise((r=>{const s=t.subscribe((()=>{e.hasFinishedResolution(n,o)&&(r(),s())}))}))}:r))}(_,l),U=()=>j;l.__unstableOriginalGetState=l.getState,l.getState=()=>l.__unstableOriginalGetState().root;const C=l&&(e=>(a.add(e),()=>a.delete(e)));let k=l.__unstableOriginalGetState();return l.subscribe((()=>{const e=l.__unstableOriginalGetState(),t=e!==k;if(k=e,t)for(const e of a)e()})),{reducer:u,store:l,actions:g,selectors:_,resolvers:R,getSelectors:()=>_,getResolveSelectors:U,getSuspendSelectors:()=>M,getActions:()=>g,subscribe:C}}};return L(c,i),c}function de(e,t){return e.__unstableNormalizeArgs&&"function"==typeof e.__unstableNormalizeArgs&&t?.length?e.__unstableNormalizeArgs(t):t}const ye={name:"core/data",instantiate(e){const t=t=>(r,...n)=>e.select(r)[t](...n),r=t=>(r,...n)=>e.dispatch(r)[t](...n);return{getSelectors:()=>Object.fromEntries(["getIsResolving","hasStartedResolution","hasFinishedResolution","isResolving","getCachedResolvers"].map((e=>[e,t(e)]))),getActions:()=>Object.fromEntries(["startResolution","finishResolution","invalidateResolution","invalidateResolutionForStore","invalidateResolutionForStoreSelector"].map((e=>[e,r(e)]))),subscribe:()=>()=>()=>{}}}};function he(){let e=!1,t=!1;const r=new Set,n=()=>Array.from(r).forEach((e=>e()));return{get isPaused(){return e},subscribe:e=>(r.add(e),()=>r.delete(e)),pause(){e=!0},resume(){e=!1,t&&(t=!1,n())},emit(){e?t=!0:n()}}}function Se(e){return"string"==typeof e?e:e.name}function be(e={},t=null){const r={},n=he();let o=null;function s(){n.emit()}function i(e,n){if(r[e])return console.error('Store "'+e+'" is already registered.'),r[e];const o=n();if("function"!=typeof o.getSelectors)throw new TypeError("store.getSelectors must be a function");if("function"!=typeof o.getActions)throw new TypeError("store.getActions must be a function");if("function"!=typeof o.subscribe)throw new TypeError("store.subscribe must be a function");o.emitter=he();const i=o.subscribe;if(o.subscribe=e=>{const t=o.emitter.subscribe(e),r=i((()=>{o.emitter.isPaused?o.emitter.emit():e()}));return()=>{r?.(),t?.()}},r[e]=o,o.subscribe(s),t)try{M(o.store).registerPrivateActions(M(t).privateActionsOf(e)),M(o.store).registerPrivateSelectors(M(t).privateSelectorsOf(e))}catch(e){}return o}let c={batch:function(e){if(n.isPaused)e();else{n.pause(),Object.values(r).forEach((e=>e.emitter.pause()));try{e()}finally{n.resume(),Object.values(r).forEach((e=>e.emitter.resume()))}}},stores:r,namespaces:r,subscribe:(e,o)=>{if(!o)return n.subscribe(e);const s=Se(o),i=r[s];return i?i.subscribe(e):t?t.subscribe(e,o):n.subscribe(e)},select:function(e){const n=Se(e);o?.add(n);const s=r[n];return s?s.getSelectors():t?.select(n)},resolveSelect:function(e){const n=Se(e);o?.add(n);const s=r[n];return s?s.getResolveSelectors():t&&t.resolveSelect(n)},suspendSelect:function(e){const n=Se(e);o?.add(n);const s=r[n];return s?s.getSuspendSelectors():t&&t.suspendSelect(n)},dispatch:function(e){const n=Se(e),o=r[n];return o?o.getActions():t&&t.dispatch(n)},use:function(e,t){if(!e)return;return c={...c,...e(c,t)},c},register:function(e){i(e.name,(()=>e.instantiate(c)))},registerGenericStore:function(e,t){a()("wp.data.registerGenericStore",{since:"5.9",alternative:"wp.data.register( storeDescriptor )"}),i(e,(()=>t))},registerStore:function(e,t){if(!t.reducer)throw new TypeError("Must specify store reducer");return i(e,(()=>ge(e,t).instantiate(c))).store},__unstableMarkListeningStores:function(e,t){o=new Set;try{return e.call(this)}finally{t.current=Array.from(o),o=null}}};c.register(ye);for(const[t,r]of Object.entries(e))c.register(ge(t,r));t&&t.subscribe(s);const u=(l=c,Object.fromEntries(Object.entries(l).map((([e,t])=>"function"!=typeof t?[e,t]:[e,function(){return c[e].apply(null,arguments)}]))));var l;return L(u,{privateActionsOf:e=>{try{return M(r[e].store).privateActions}catch(e){return{}}},privateSelectorsOf:e=>{try{return M(r[e].store).privateSelectors}catch(e){return{}}}}),u}const ve=be();
/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */
function Oe(e){return"[object Object]"===Object.prototype.toString.call(e)}function me(e){var t,r;return!1!==Oe(e)&&(void 0===(t=e.constructor)||!1!==Oe(r=t.prototype)&&!1!==r.hasOwnProperty("isPrototypeOf"))}var we=r(66),Re=r.n(we);let Ee;const _e={getItem:e=>Ee&&Ee[e]?Ee[e]:null,setItem(e,t){Ee||_e.clear(),Ee[e]=String(t)},clear(){Ee=Object.create(null)}},Ie=_e;let Te;try{Te=window.localStorage,Te.setItem("__wpDataTestLocalStorage",""),Te.removeItem("__wpDataTestLocalStorage")}catch(e){Te=Ie}const Ne=Te,Ae="WP_DATA";function je(e,t){const r=function(e){const{storage:t=Ne,storageKey:r=Ae}=e;let n;return{get:function(){if(void 0===n){const e=t.getItem(r);if(null===e)n={};else try{n=JSON.parse(e)}catch(e){n={}}}return n},set:function(e,o){n={...n,[e]:o},t.setItem(r,JSON.stringify(n))}}}(t);return{registerStore(t,n){if(!n.persist)return e.registerStore(t,n);const o=r.get()[t];if(void 0!==o){let e=n.reducer(n.initialState,{type:"@@WP/PERSISTENCE_RESTORE"});e=me(e)&&me(o)?Re()(e,o,{isMergeableObject:me}):o,n={...n,initialState:e}}const s=e.registerStore(t,n);return s.subscribe(function(e,t,n){let o;if(Array.isArray(n)){const e=n.reduce(((e,t)=>Object.assign(e,{[t]:(e,r)=>r.nextState[t]})),{});s=st(e),o=(e,t)=>t.nextState===e?e:s(e,t)}else o=(e,t)=>t.nextState;var s;let i=o(void 0,{nextState:e()});return()=>{const n=o(i,{nextState:e()});n!==i&&(r.set(t,n),i=n)}}(s.getState,t,n.persist)),s}}}je.__unstableMigrate=()=>{};const Le=je,Me=window.wp.priorityQueue,Pe=window.wp.element,xe=window.wp.isShallowEqual;var Fe=r.n(xe);const Ue=(0,Pe.createContext)(ve),{Consumer:Ce,Provider:ke}=Ue,De=Ce,Ve=ke;function Ge(){return(0,Pe.useContext)(Ue)}const He=(0,Pe.createContext)(!1),{Consumer:We,Provider:$e}=He,Ke=$e;const ze=(0,Me.createQueue)();function Be(e,t){const r=t?e.suspendSelect:e.select,n={};let o,s,i,c,a=!1;const u=new Map;function l(t){var r;return null!==(r=e.stores[t]?.store?.getState?.())&&void 0!==r?r:{}}return(t,f)=>{function p(){if(a&&t===o)return s;const f={current:null},p=e.__unstableMarkListeningStores((()=>t(r,e)),f);if(c)c.updateStores(f.current);else{for(const e of f.current)u.set(e,l(e));c=(t=>{const r=[...t],o=new Set;return{subscribe:function(t){if(a)for(const e of r)u.get(e)!==l(e)&&(a=!1);u.clear();const s=()=>{a=!1,t()},c=()=>{i?ze.add(n,s):s()},f=[];function p(t){f.push(e.subscribe(c,t))}for(const e of r)p(e);return o.add(p),()=>{o.delete(p);for(const e of f.values())e?.();ze.cancel(n)}},updateStores:function(e){for(const t of e)if(!r.includes(t)){r.push(t);for(const e of o)e(t)}}}})(f.current)}Fe()(s,p)||(s=p),o=t,a=!0}return i&&!f&&(a=!1,ze.cancel(n)),p(),i=f,{subscribe:c.subscribe,getValue:function(){return p(),s}}}}function Xe(e,t,r){const n=Ge(),o=(0,Pe.useContext)(He),s=(0,Pe.useMemo)((()=>Be(n,e)),[n,e]),i=(0,Pe.useCallback)(t,r),{subscribe:c,getValue:a}=s(i,o),u=(0,Pe.useSyncExternalStore)(c,a,a);return(0,Pe.useDebugValue)(u),u}function qe(e,t){const r="function"!=typeof e,n=(0,Pe.useRef)(r);if(r!==n.current){const e=n.current?"static":"mapping";throw new Error(`Switching useSelect from ${e} to ${r?"static":"mapping"} is not allowed`)}return r?(o=e,Ge().select(o)):Xe(!1,e,t);var o}function Je(e,t){return Xe(!0,e,t)}const Qe=window.ReactJSXRuntime,Ye=e=>(0,O.createHigherOrderComponent)((t=>(0,O.pure)((r=>{const n=qe(((t,n)=>e(t,r,n)));return(0,Qe.jsx)(t,{...r,...n})}))),"withSelect"),Ze=(e,t)=>{const r=Ge(),n=(0,Pe.useRef)(e);return(0,O.useIsomorphicLayoutEffect)((()=>{n.current=e})),(0,Pe.useMemo)((()=>{const e=n.current(r.dispatch,r);return Object.fromEntries(Object.entries(e).map((([e,t])=>("function"!=typeof t&&console.warn(`Property ${e} returned from dispatchMap in useDispatchWithMap must be a function.`),[e,(...t)=>n.current(r.dispatch,r)[e](...t)]))))}),[r,...t])},et=e=>(0,O.createHigherOrderComponent)((t=>r=>{const n=Ze(((t,n)=>e(t,r,n)),[]);return(0,Qe.jsx)(t,{...r,...n})}),"withDispatch"),tt=(0,O.createHigherOrderComponent)((e=>t=>(0,Qe.jsx)(De,{children:r=>(0,Qe.jsx)(e,{...t,registry:r})})),"withRegistry"),rt=e=>{const{dispatch:t}=Ge();return void 0===e?t:t(e)};function nt(e){return ve.dispatch(e)}function ot(e){return ve.select(e)}const st=m,it=ve.resolveSelect,ct=ve.suspendSelect,at=ve.subscribe,ut=ve.registerGenericStore,lt=ve.registerStore,ft=ve.use,pt=ve.register;(window.wp=window.wp||{}).data=n})();
#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Script PowerShell pour exécuter les tests du projet SEO AI Agents.

.DESCRIPTION
    Ce script permet d'exécuter les tests Python en utilisant le script run_tests.py.
    Par défaut, il exécute le fichier test_web_scrape.py.

.PARAMETER TestFile
    Nom du fichier de test spécifique à exécuter (optionnel)

.PARAMETER Unit
    Exécuter seulement les tests unitaires

.PARAMETER Integration
    Exécuter seulement les tests d'intégration

.PARAMETER Coverage
    Générer un rapport de couverture

.PARAMETER Html
    Générer un rapport HTML

.PARAMETER VerboseMode
    Mode verbeux

.PARAMETER All
    Exécuter tous les tests (ignore TestFile)

.EXAMPLE
    .\run_tests.ps1
    Exécute test_web_scrape.py (comportement par défaut)

.EXAMPLE
    .\run_tests.ps1 -TestFile "test_keyword_research.py"
    Exécute un fichier de test spécifique

.EXAMPLE
    .\run_tests.ps1 -All -Coverage
    Exécute tous les tests avec rapport de couverture

.EXAMPLE
    .\run_tests.ps1 -Unit -VerboseMode
    Exécute les tests unitaires en mode verbeux
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory=$false)]
    [string]$TestFile = "test_web_scrape.py",

    [Parameter(Mandatory=$false)]
    [switch]$Unit,

    [Parameter(Mandatory=$false)]
    [switch]$Integration,

    [Parameter(Mandatory=$false)]
    [switch]$Coverage,

    [Parameter(Mandatory=$false)]
    [switch]$Html,

    [Parameter(Mandatory=$false)]
    [switch]$VerboseMode,

    [Parameter(Mandatory=$false)]
    [switch]$All
)

# Fonction pour afficher des messages colorés
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Effacer l'écran
Clear-Host

# Afficher l'en-tête
Write-ColorOutput "🧪 SEO AI Agents - Exécution des tests" "Cyan"
Write-ColorOutput "=" * 50 "Cyan"

# Construire la commande Python
$pythonArgs = @("run_tests.py")

# Ajouter les paramètres selon les options
if ($Unit) {
    $pythonArgs += "--unit"
    Write-ColorOutput "📋 Mode: Tests unitaires seulement" "Yellow"
}

if ($Integration) {
    $pythonArgs += "--integration"
    Write-ColorOutput "📋 Mode: Tests d'intégration seulement" "Yellow"
}

if ($Coverage) {
    $pythonArgs += "--coverage"
    Write-ColorOutput "📊 Génération du rapport de couverture activée" "Yellow"
}

if ($Html) {
    $pythonArgs += "--html"
    Write-ColorOutput "📄 Génération du rapport HTML activée" "Yellow"
}

if ($VerboseMode) {
    $pythonArgs += "--verbose"
    Write-ColorOutput "🔍 Mode verbeux activé" "Yellow"
}

if ($All) {
    Write-ColorOutput "🎯 Exécution de tous les tests" "Yellow"
} else {
    $pythonArgs += "--file"
    $pythonArgs += $TestFile
    Write-ColorOutput "🎯 Fichier de test: $TestFile" "Yellow"
}

Write-ColorOutput "" "White"

# Vérifier que Python est disponible
try {
    $pythonVersion = python --version 2>&1
    Write-ColorOutput "🐍 Python détecté: $pythonVersion" "Green"
} catch {
    Write-ColorOutput "❌ Erreur: Python n'est pas installé ou n'est pas dans le PATH" "Red"
    exit 1
}

Write-ColorOutput "" "White"

# Exécuter la commande Python
try {
    Write-ColorOutput "🚀 Lancement des tests..." "Green"
    Write-ColorOutput "Commande: python $($pythonArgs -join ' ')" "Gray"
    Write-ColorOutput "" "White"

    $process = Start-Process -FilePath "python" -ArgumentList $pythonArgs -Wait -PassThru -NoNewWindow

    Write-ColorOutput "" "White"

    if ($process.ExitCode -eq 0) {
        Write-ColorOutput "✅ Tests terminés avec succès!" "Green"
    } else {
        Write-ColorOutput "❌ Certains tests ont échoué (code de sortie: $($process.ExitCode))" "Red"
    }

    exit $process.ExitCode

} catch {
    Write-ColorOutput "❌ Erreur lors de l'exécution: $($_.Exception.Message)" "Red"
    exit 1
}

{var Favorites=Favorites||{};Favorites.Utilities=function(){var t=this,e=jQuery;t.isFavorite=function(o,t){var a=!1;return e.each(t,function(t,e){e.post_id===parseInt(o)&&(a=!0),parseInt(e.post_id)===o&&(a=!0)}),a},t.objectLength=function(t){var e,o=0;for(e in t)t.hasOwnProperty(e)&&o++;return o},t.siteIndex=function(t){for(var e=0;e<Favorites.userFavorites.length;e++)if(Favorites.userFavorites[e].site_id===parseInt(t))return e},t.getThumbnail=function(t,e){var t=t.thumbnails;return void 0!==t&&0!=t.length&&void 0!==(t=t[e])&&t||!1}}}function favorites_after_button_submit(t,e,o,a){}function favorites_after_initial_load(t){}(Favorites=Favorites||{}).Formatter=function(){var s=jQuery;this.addFavoriteCount=function(t,e){return Favorites.jsData.button_options.include_count&&(t+=' <span class="simplefavorite-button-count">'+(e=e<=0?0:e)+"</span>"),t},this.decrementAllCounts=function(){for(var t=s(".simplefavorite-button.active.has-count"),e=0;e<t.length;e++){var o=s(t)[e],a=s(o).find(".simplefavorite-button-count"),a=s(a).text()-1;s(o).attr("data-favoritecount",a)}}},(Favorites=Favorites||{}).ButtonOptionsFormatter=function(){var i=this,n=jQuery;i.options=Favorites.jsData.button_options,i.formatter=new Favorites.Formatter,i.format=function(t,e){i.options.custom_colors&&i.colors(t,e),i.html(t,e)},i.html=function(t,e){var o=n(t).attr("data-favoritecount"),a=i.options.button_type,s="";"custom"===i.options.button_type?(e&&n(t).html(i.formatter.addFavoriteCount(Favorites.jsData.favorited,o)),e||n(t).html(i.formatter.addFavoriteCount(Favorites.jsData.favorite,o)),i.applyIconColor(t,e),i.applyCountColor(t,e)):e?(s=(s+='<i class="'+a.icon_class+'"></i> ')+a.state_active,n(t).html(i.formatter.addFavoriteCount(s,o))):(s=(s+='<i class="'+a.icon_class+'"></i> ')+a.state_default,n(t).html(i.formatter.addFavoriteCount(s,o)),i.applyIconColor(t,e),i.applyCountColor(t,e))},i.colors=function(t,e){var o;i.options.custom_colors&&(e?((o=i.options.active).background_active&&n(t).css("background-color",o.background_active),o.border_active&&n(t).css("border-color",o.border_active),o.text_active&&n(t).css("color",o.text_active)):((o=i.options.default).background_default&&n(t).css("background-color",o.background_default),o.border_default&&n(t).css("border-color",o.border_default),o.text_default&&n(t).css("color",o.text_default),i.boxShadow(t)))},i.boxShadow=function(t){i.options.box_shadow||(n(t).css("box-shadow","none"),n(t).css("-webkit-box-shadow","none"),n(t).css("-moz-box-shadow","none"))},i.applyIconColor=function(t,e){i.options.custom_colors&&(e&&i.options.active.icon_active&&n(t).find("i").css("color",i.options.active.icon_active),!e)&&i.options.default.icon_default&&n(t).find("i").css("color",i.options.default.icon_default)},i.applyCountColor=function(t,e){i.options.custom_colors&&(e&&i.options.active.count_active?n(t).find(Favorites.selectors.count).css("color",i.options.active.count_active):!e&&i.options.default.count_default&&n(t).find(Favorites.selectors.count).css("color",i.options.default.count_default))}},(Favorites=Favorites||{}).UserFavorites=function(){var e=this,o=jQuery;return e.initialLoad=!1,e.bindEvents=function(){o(window).on("load",function(){e.initialLoad=!0,e.getFavorites()})},e.getFavorites=function(){o.ajax({url:Favorites.jsData.ajaxurl,type:"POST",datatype:"json",data:{action:Favorites.formActions.favoritesarray},success:function(t){Favorites.jsData.dev_mode&&(console.log("The current user favorites were successfully loaded."),console.log(t)),Favorites.userFavorites=t.favorites,o(document).trigger("favorites-user-favorites-loaded",[t.favorites,e.initialLoad]),o(document).trigger("favorites-update-all-buttons"),e.initialLoad&&favorites_after_initial_load(Favorites.userFavorites)},error:function(t){Favorites.jsData.dev_mode&&(console.log("The was an error loading the user favorites."),console.log(t))}})},e.bindEvents()},(Favorites=Favorites||{}).Clear=function(){var s=this,i=jQuery;return s.activeButton,s.utilities=new Favorites.Utilities,s.formatter=new Favorites.Formatter,s.bindEvents=function(){i(document).on("click",Favorites.selectors.clear_button,function(t){t.preventDefault(),s.activeButton=i(this),s.clearFavorites()}),i(document).on("favorites-updated-single",function(){s.updateClearButtons()}),i(document).on("favorites-user-favorites-loaded",function(){s.updateClearButtons()})},s.clearFavorites=function(){s.loading(!0);var e=i(s.activeButton).attr("data-siteid");i.ajax({url:Favorites.jsData.ajaxurl,type:"post",datatype:"json",data:{action:Favorites.formActions.clearall,siteid:e},success:function(t){Favorites.jsData.dev_mode&&(console.log("Favorites list successfully cleared."),console.log(t)),Favorites.userFavorites=t.favorites,s.formatter.decrementAllCounts(),s.loading(!1),s.clearSiteFavorites(e),i(document).trigger("favorites-cleared",[s.activeButton,t.old_favorites]),i(document).trigger("favorites-update-all-buttons")},error:function(t){Favorites.jsData.dev_mode&&(console.log("There was an error clearing the favorites list."),console.log(t))}})},s.loading=function(t){t?(i(s.activeButton).addClass(Favorites.cssClasses.loading),i(s.activeButton).attr("disabled","disabled")):i(s.activeButton).removeClass(Favorites.cssClasses.loading)},s.updateClearButtons=function(){for(var t=0;t<i(Favorites.selectors.clear_button).length;t++)for(var e=i(Favorites.selectors.clear_button)[t],o=i(e).attr("data-siteid"),a=0;a<Favorites.userFavorites.length;a++)Favorites.userFavorites[a].site_id===parseInt(o)&&(0<s.utilities.objectLength(Favorites.userFavorites[a].posts)?i(e).attr("disabled",!1):i(e).attr("disabled","disabled"))},s.clearSiteFavorites=function(o){i.each(Favorites.userFavorites,function(t,e){this.site_id===parseInt(o)&&(Favorites.userFavorites[t].posts={})})},s.bindEvents()},(Favorites=Favorites||{}).Lists=function(){var u=this,v=jQuery;return u.utilities=new Favorites.Utilities,u.buttonFormatter=new Favorites.ButtonOptionsFormatter,u.bindEvents=function(){v(document).on("favorites-update-all-lists",function(){u.updateAllLists()}),v(document).on("favorites-updated-single",function(){u.updateAllLists()}),v(document).on("favorites-cleared",function(){u.updateAllLists()}),v(document).on("favorites-user-favorites-loaded",function(){u.updateAllLists()})},u.updateAllLists=function(){if(void 0!==Favorites.userFavorites)for(var t=0;t<Favorites.userFavorites.length;t++)for(var e=v(Favorites.selectors.list+'[data-siteid="'+Favorites.userFavorites[t].site_id+'"]'),o=0;o<v(e).length;o++){var a=v(e)[o];u.updateSingleList(a)}},u.updateSingleList=function(e){var t=v(e).attr("data-userid"),o=v(e).attr("data-siteid"),a=v(e).attr("data-includelinks"),s=v(e).attr("data-includebuttons"),i=v(e).attr("data-includethumbnails"),n=v(e).attr("data-thumbnailsize"),r=v(e).attr("data-includeexcerpts"),c=v(e).attr("data-posttypes"),d=v(e).attr("data-nofavoritestext");v.ajax({url:Favorites.jsData.ajaxurl,type:"post",dataType:"json",data:{action:Favorites.formActions.favoritelist,userid:t,siteid:o,include_links:a,include_buttons:s,include_thumbnails:i,thumbnail_size:n,include_excerpt:r,no_favorites:d,post_types:c},success:function(t){Favorites.jsData.dev_mode&&(console.log("Favorites list successfully retrieved."),console.log(v(e)),console.log(t));t=v(t.list);v(e).replaceWith(t),u.removeButtonLoading(t),v(document).trigger("favorites-list-updated",[t])},error:function(t){Favorites.jsData.dev_mode&&(console.log("There was an error updating the list."),console.log(e),console.log(t))}})},u.removeButtonLoading=function(t){t=v(t).find(Favorites.selectors.button);v.each(t,function(){u.buttonFormatter.format(v(this),!1),v(this).removeClass(Favorites.cssClasses.active),v(this).removeClass(Favorites.cssClasses.loading)})},u.removeInvalidListItems=function(t,a){t=v(t).find("li[data-postid]");v.each(t,function(t,e){var o=v(this).attr("data-postid");u.utilities.isFavorite(o,a)||v(this).remove()})},u.bindEvents()},(Favorites=Favorites||{}).Button=function(){var e=this,o=jQuery;return e.activeButton,e.allButtons,e.authenticated=!0,e.formatter=new Favorites.Formatter,e.data={},e.bindEvents=function(){o(document).on("click",Favorites.selectors.button,function(t){t.preventDefault(),e.activeButton=o(this),e.setAllButtons(),e.submitFavorite()})},e.setAllButtons=function(){var t=o(e.activeButton).attr("data-postid");e.allButtons=o('button[data-postid="'+t+'"]')},e.setData=function(){e.data.post_id=o(e.activeButton).attr("data-postid"),e.data.site_id=o(e.activeButton).attr("data-siteid"),e.data.status=o(e.activeButton).hasClass("active")?"inactive":"active";var t=o(e.activeButton).attr("data-user-consent-accepted");e.data.user_consent_accepted=void 0!==t&&""!==t},e.submitFavorite=function(){e.loading(!0),e.setData();var t={action:Favorites.formActions.favorite,postid:e.data.post_id,siteid:e.data.site_id,status:e.data.status,user_consent_accepted:e.data.user_consent_accepted};o.ajax({url:Favorites.jsData.ajaxurl,type:"post",dataType:"json",data:t,success:function(t){Favorites.jsData.dev_mode&&(console.log("The favorite was successfully saved."),console.log(t)),"unauthenticated"===t.status?(Favorites.authenticated=!1,e.loading(!1),e.data.status="inactive",o(document).trigger("favorites-update-all-buttons"),o(document).trigger("favorites-require-authentication",[e.data])):"consent_required"===t.status?(e.loading(!1),o(document).trigger("favorites-require-consent",[t,e.data,e.activeButton])):(Favorites.userFavorites=t.favorites,e.loading(!1),e.resetButtons(),o(document).trigger("favorites-updated-single",[t.favorites,e.data.post_id,e.data.site_id,e.data.status]),o(document).trigger("favorites-update-all-buttons"),favorites_after_button_submit(t.favorites,e.data.post_id,e.data.site_id,e.data.status))},error:function(t){Favorites.jsData.dev_mode&&(console.log("There was an error saving the favorite."),console.log(t))}})},e.resetButtons=function(){var t=parseInt(o(e.activeButton).attr("data-favoritecount"));o.each(e.allButtons,function(){"inactive"===e.data.status?(t<=0&&(t=1),o(this).removeClass(Favorites.cssClasses.active),o(this).attr("data-favoritecount",t-1),o(this).find(Favorites.selectors.count).text(t-1)):(o(this).addClass(Favorites.cssClasses.active),o(this).attr("data-favoritecount",t+1),o(this).find(Favorites.selectors.count).text(t+1))})},e.loading=function(t){t?o.each(e.allButtons,function(){o(this).attr("disabled","disabled"),o(this).addClass(Favorites.cssClasses.loading),o(this).html(e.addLoadingIndication())}):o.each(e.allButtons,function(){o(this).attr("disabled",!1),o(this).removeClass(Favorites.cssClasses.loading)})},e.addLoadingIndication=function(t){return"1"!==Favorites.jsData.indicate_loading?t:"active"===e.data.status?Favorites.jsData.loading_text+Favorites.jsData.loading_image_active:Favorites.jsData.loading_text+Favorites.jsData.loading_image},e.bindEvents()},(Favorites=Favorites||{}).ButtonUpdater=function(){var a=this,s=jQuery;return a.utilities=new Favorites.Utilities,a.formatter=new Favorites.Formatter,a.buttonFormatter=new Favorites.ButtonOptionsFormatter,a.activeButton,a.data={},a.bindEvents=function(){s(document).on("favorites-update-all-buttons",function(){a.updateAllButtons()}),s(document).on("favorites-list-updated",function(t,e){a.updateAllButtons(e)})},a.updateAllButtons=function(t){if(void 0!==Favorites.userFavorites)for(var e=s(Favorites.selectors.button),o=0;o<s(e).length;o++)a.activeButton=s(e)[o],Favorites.authenticated&&a.setButtonData(),Favorites.authenticated&&a.utilities.isFavorite(a.data.postid,a.data.site_favorites)?(a.buttonFormatter.format(s(a.activeButton),!0),s(a.activeButton).addClass(Favorites.cssClasses.active)):(a.buttonFormatter.format(s(a.activeButton),!1),s(a.activeButton).removeClass(Favorites.cssClasses.active)),s(a.activeButton).removeClass(Favorites.cssClasses.loading),s(a.activeButton).find(Favorites.selectors.count).text(a.data.favorite_count)},a.setButtonData=function(){a.data.postid=s(a.activeButton).attr("data-postid"),a.data.siteid=s(a.activeButton).attr("data-siteid"),a.data.favorite_count=s(a.activeButton).attr("data-favoritecount"),a.data.site_index=a.utilities.siteIndex(a.data.siteid),a.data.site_favorites=Favorites.userFavorites[a.data.site_index].posts,a.data.favorite_count<=0&&(a.data.favorite_count=0)},a.bindEvents()},(Favorites=Favorites||{}).TotalCount=function(){var t=this,r=jQuery;return t.bindEvents=function(){r(document).on("favorites-updated-single",function(){t.updateTotal()}),r(document).on("favorites-cleared",function(){t.updateTotal()}),r(document).on("favorites-user-favorites-loaded",function(){t.updateTotal()})},t.updateTotal=function(){for(var t=0;t<r(Favorites.selectors.total_favorites).length;t++){for(var e=r(Favorites.selectors.total_favorites)[t],o=parseInt(r(e).attr("data-siteid")),a=r(e).attr("data-posttypes").split(","),s=0,i=0;i<Favorites.userFavorites.length;i++){var n=Favorites.userFavorites[i];n.site_id===o&&r.each(n.posts,function(){("all"===r(e).attr("data-posttypes")||-1!==r.inArray(this.post_type,a))&&s++})}r(e).text(s)}},t.bindEvents()},(Favorites=Favorites||{}).PostFavoriteCount=function(){var i=this,c=jQuery;return i.bindEvents=function(){c(document).on("favorites-updated-single",function(t,e,o,a,s){if("active"===s)return i.updateCounts();i.decrementSingle(o,a)}),c(document).on("favorites-cleared",function(t,e,o){i.updateCounts(o,!0)})},i.updateCounts=function(t,e){void 0!==t&&""!==t||(t=Favorites.userFavorites),void 0!==e&&""!==e||(e=!1);for(var o=0;o<c("["+Favorites.selectors.post_favorite_count+"]").length;o++){var a=c("["+Favorites.selectors.post_favorite_count+"]")[o],s=parseInt(c(a).attr(Favorites.selectors.post_favorite_count)),i=c(a).attr("data-siteid");""===i&&(i="1");for(var n=0;n<t.length;n++){var r=t[n];r.site_id===parseInt(i)&&c.each(r.posts,function(){var t;this.post_id===s&&(e?(t=parseInt(this.total)-1,c(a).text(t)):c(a).text(this.total))})}}},i.decrementSingle=function(t,e){for(var o=0;o<c("["+Favorites.selectors.post_favorite_count+"]").length;o++){var a=c("["+Favorites.selectors.post_favorite_count+"]")[o],s=c(a).attr(Favorites.selectors.post_favorite_count),i=c(a).attr("data-siteid");(i=""===i?"1":i)===e&&s===t&&(i=parseInt(c(a).text())-1,c(a).text(i))}},i.bindEvents()},(Favorites=Favorites||{}).RequireAuthentication=function(){var e=this,o=jQuery;return e.bindEvents=function(){o(document).on("favorites-require-authentication",function(){Favorites.jsData.dev_mode&&console.log("Unauthenticated user was prevented from favoriting."),Favorites.jsData.authentication_redirect?e.redirect():e.openModal()}),o(document).on("click",".simplefavorites-modal-backdrop",function(t){e.closeModal()}),o(document).on("click","["+Favorites.selectors.close_modals+"]",function(t){t.preventDefault(),e.closeModal()})},e.redirect=function(){window.location=Favorites.jsData.authentication_redirect_url},e.openModal=function(){e.buildModal(),setTimeout(function(){o("["+Favorites.selectors.modals+"]").addClass("active")},10)},e.buildModal=function(){var t;0<o("["+Favorites.selectors.modals+"]").length||(t='<div class="simplefavorites-modal-backdrop" '+Favorites.selectors.modals+"></div>",t=(t+='<div class="simplefavorites-modal-content" '+Favorites.selectors.modals+">")+'<div class="simplefavorites-modal-content-body">'+Favorites.jsData.authentication_modal_content+"</div>\x3c!-- .simplefavorites-modal-content-body --\x3e</div>\x3c!-- .simplefavorites-modal-content --\x3e",o("body").prepend(t))},e.closeModal=function(){o("["+Favorites.selectors.modals+"]").removeClass("active"),o(document).trigger("favorites-modal-closed")},e.bindEvents()},(Favorites=Favorites||{}).RequireConsent=function(){var s=this,o=jQuery;return s.consentData,s.postData,s.activeButton,s.bindEvents=function(){o(document).on("favorites-require-consent",function(t,e,o,a){s.consentData=e,s.postData=o,s.activeButton=a,s.openModal()}),o(document).on("favorites-user-consent-approved",function(t,e){void 0!==e?(o(s.activeButton).attr("data-user-consent-accepted","true"),o(s.activeButton).click(),s.closeModal()):s.setConsent(!0)}),o(document).on("favorites-user-consent-denied",function(){s.setConsent(!1)}),o(document).on("click",".simplefavorites-modal-backdrop",function(t){s.closeModal()}),o(document).on("click","[data-favorites-consent-deny]",function(t){t.preventDefault(),s.closeModal(),o(document).trigger("favorites-user-consent-denied")}),o(document).on("click","[data-favorites-consent-accept]",function(t){t.preventDefault(),o(document).trigger("favorites-user-consent-approved",[o(this)])})},s.openModal=function(){s.buildModal(),setTimeout(function(){o("["+Favorites.selectors.consentModal+"]").addClass("active")},10)},s.buildModal=function(){var t;0<o("["+Favorites.selectors.consentModal+"]").length||(t=(t=(t=(t=(t='<div class="simplefavorites-modal-backdrop" '+Favorites.selectors.consentModal+"></div>")+('<div class="simplefavorites-modal-content" '+Favorites.selectors.consentModal)+'><div class="simplefavorites-modal-content-body no-padding"><div class="simplefavorites-modal-content-interior">')+s.consentData.message+"</div>")+'<div class="simplefavorites-modal-content-footer"><button class="simplefavorites-button-consent-deny" data-favorites-consent-deny>'+s.consentData.deny_text+"</button>")+'<button class="simplefavorites-button-consent-accept" data-favorites-consent-accept>'+s.consentData.accept_text+"</button></div>\x3c!-- .simplefavorites-modal-footer --\x3e</div>\x3c!-- .simplefavorites-modal-content-body --\x3e</div>\x3c!-- .simplefavorites-modal-content --\x3e",o("body").prepend(t))},s.closeModal=function(){o("["+Favorites.selectors.consentModal+"]").removeClass("active")},s.setConsent=function(t){o.ajax({url:Favorites.jsData.ajaxurl,type:"post",dataType:"json",data:{action:Favorites.formActions.cookieConsent,consent:t}})},s.bindEvents()},jQuery(document).ready(function(){new Favorites.Factory}),(Favorites=Favorites||{}).selectors={button:".simplefavorite-button",list:".favorites-list",clear_button:".simplefavorites-clear",total_favorites:".simplefavorites-user-count",modals:"data-favorites-modal",consentModal:"data-favorites-consent-modal",close_modals:"data-favorites-modal-close",count:".simplefavorite-button-count",post_favorite_count:"data-favorites-post-count-id"},Favorites.cssClasses={loading:"loading",active:"active"},Favorites.jsData={ajaxurl:favorites_data.ajaxurl,favorite:favorites_data.favorite,favorited:favorites_data.favorited,include_count:favorites_data.includecount,indicate_loading:favorites_data.indicate_loading,loading_text:favorites_data.loading_text,loading_image_active:favorites_data.loading_image_active,loading_image:favorites_data.loading_image,cache_enabled:favorites_data.cache_enabled,authentication_modal_content:favorites_data.authentication_modal_content,authentication_redirect:favorites_data.authentication_redirect,authentication_redirect_url:favorites_data.authentication_redirect_url,button_options:favorites_data.button_options,dev_mode:favorites_data.dev_mode,logged_in:favorites_data.logged_in,user_id:favorites_data.user_id},Favorites.userFavorites=null,Favorites.authenticated=!0,Favorites.formActions={favoritesarray:"favorites_array",favorite:"favorites_favorite",clearall:"favorites_clear",favoritelist:"favorites_list",cookieConsent:"favorites_cookie_consent"},Favorites.Factory=function(){jQuery;return this.build=function(){new Favorites.UserFavorites,new Favorites.Lists,new Favorites.Clear,new Favorites.Button,new Favorites.ButtonUpdater,new Favorites.TotalCount,new Favorites.PostFavoriteCount,new Favorites.RequireAuthentication,new Favorites.RequireConsent},this.build()};
#adminmenu a.toplevel_page_chart-builder.wp-menu-open div.wp-menu-image img {
    -webkit-animation-name: heartBeat;
    animation-name: heartBeat;
    -webkit-animation-duration: 1.3s;
    animation-duration: 1.3s;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
}
#adminmenu a.toplevel_page_chart-builder div.wp-menu-name {    
    transition: .3s ease-in-out;
}
#adminmenu a.toplevel_page_chart-builder div.wp-menu-image img {
    width: 25px;
    padding: 5px 0 0;
    transition: .3s ease-in-out;
}
#adminmenu li.toplevel_page_chart-builder ul.wp-submenu.wp-submenu-wrap li a[href*="features"] {
    color: #68A615;
    font-weight: bold;
}

.chart-elementor-widget-logo-wrap .chart-elementor-widget-logo:before,
.chart-elementor-widget-logo:before {
    content: "";
    background-image: url('../images/ays_chart_logo.png');
    height: 30px;
    display: block;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
}

.chart-elementor-widget-logo-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 700;
}

.chart-elementor-widget-logo-wrap .chart-elementor-widget-logo {
    display: inline-block;
    margin-right: 5px;
    width: 30px;
    height: 30px;
}

.chart-elementor-widget-logo-wrap .chart-elementor-widget-logo:before {
    text-align: left;
}

.swal2-popup.swal2-modal.ays-chart-deactivate-popup.swal2-show {
    font-size: initial !important;
}

.swal2-popup .ays-chart-upgrade-button,
.swal2-popup .ays-chart-cancel-button {
    position: relative;
}

.swal2-popup .ays-chart-upgrade-button.swal2-styled.swal2-confirm:after {
    content: "(Save Your Data)";
    position: absolute;
    color: #878787;
    bottom: -18px;
    left: 25px;
    font-size: 12px;
}

.swal2-popup .ays-chart-cancel-button.swal2-styled.swal2-cancel:after {
    content: "(Delete All Data)";
    position: absolute;
    color: #878787;
    bottom: -18px;
    left: 30px;
    font-size: 12px;
}

.swal2-popup .ays-chart-upgrade-button.swal2-styled.swal2-confirm {
    background-color: #fff !important;
    color: #3085d6 !important;
    border: 1px solid #3085d6 !important;
}

#ays-chart-plugins-buy-now-button {
    color: #01A32A;
    font-weight: bold;
}

#ays-chart-plugins-buy-now-button:hover {
    color: #3B8D3F;
}

# https://github.com/tavily-ai/tavily-python
# https://www.tavily.com/
# https://app.tavily.com/playground
from tavily import TavilyClient

# https://docs.tavily.com/documentation/api-reference/endpoint/search#tavily-search
client = TavilyClient("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")
response = client.search(
    query="zonetuto banana",
    max_results=10,
    time_range="year",
    country="france"
)
print(response)
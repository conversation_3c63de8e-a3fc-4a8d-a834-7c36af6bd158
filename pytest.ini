[tool:pytest]
# Configuration pytest pour SEO AI Agents

# Répertoires de tests
testpaths = tests

# Patterns de fichiers de test
python_files = test_*.py *_test.py

# Patterns de classes de test
python_classes = Test*

# Patterns de fonctions de test
python_functions = test_*

# Options par défaut
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# Marqueurs personnalisés
markers =
    slow: marque les tests comme lents
    integration: tests d'intégration
    unit: tests unitaires
    web: tests nécessitant une connexion internet

# Filtres d'avertissements
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

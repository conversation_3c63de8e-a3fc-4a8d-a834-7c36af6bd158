!function(){"use strict";var e={n:function(t){var o=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(o,{a:o}),o},d:function(t,o){for(var u in o)e.o(o,u)&&!e.o(t,u)&&Object.defineProperty(t,u,{enumerable:!0,get:o[u]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}};!function(e,t,o){var u={};o.r(u),o.d(u,{category:function(){return g},metadata:function(){return y},name:function(){return h},settings:function(){return p}});var n=window.wp.blocks,a=window.wp.element,l=window.wp.i18n,r=window.wp.components,i=window.wp.blockEditor;class b extends a.Component{render(){const{attributes:e,setAttributes:t,isSelected:o}=this.props;return(0,a.createElement)(i.InspectorControls,null,(0,a.createElement)(r.PanelBody,{title:(0,l.__)("YouTube Player Configuration","ht-youtube-embed")},(0,a.createElement)(r.TextControl,{label:(0,l.__)("YouTube URL or YouTube ID:","ht-youtube-embed"),value:e.htYoutubeId,onChange:e=>{t({htYoutubeId:e})}}),(0,a.createElement)(r.ToggleControl,{id:"switch-btn-enable",label:(0,l.__)("YouTube Player Controller","ht-youtube-embed"),checked:e.htYoutubeController,onChange:()=>{const o=!0!==e.htYoutubeController;t({htYoutubeController:o})}}),(0,a.createElement)(r.TextControl,{label:(0,l.__)("Video Start Time ( in seconds ):","ht-youtube-embed"),type:(0,l.__)("number","ht-youtube-embed"),value:e.htYoutubevideoStartTime,onChange:e=>{t({htYoutubevideoStartTime:e})},style:{width:"100%"}}),(0,a.createElement)(r.TextControl,{label:(0,l.__)("Video End Time ( in seconds ):","ht-youtube-embed"),type:(0,l.__)("number","ht-youtube-embed"),value:e.htYoutubevideoEndTime,onChange:e=>{t({htYoutubevideoEndTime:e})},style:{width:"100%"}}),(0,a.createElement)("b",{style:{marginBottom:10}},(0,l.__)("YouTube Thumbnail Image:","ht-youtube-embed")),e.youtubeThumbnailImage.img_ID?(0,a.createElement)("div",{className:"image-ctr",style:{marginBottom:20}},(0,a.createElement)("img",{src:e.youtubeThumbnailImage.img_url,alt:e.youtubeThumbnailImage.img_alt,style:{height:150,width:250}}),o?(0,a.createElement)(r.Button,{className:"button button-large",onClick:()=>(()=>{const o={...e.youtubeThumbnailImage};o.img_ID=null,o.img_url=null,o.img_alt=null,t({youtubeThumbnailImage:o})})()},(0,l.__)("Image Remove","ht-youtube-embed")):null):(0,a.createElement)(i.MediaUploadCheck,null,(0,a.createElement)(i.MediaUpload,{allowedType:["image"],value:void 0!==e.youtubeThumbnailImage.img_ID?e.youtubeThumbnailImage.img_ID:"",onSelect:o=>{const u={...e.youtubeThumbnailImage};u.img_ID=o.id,u.img_url=o.url,u.img_alt=o.alt,t({youtubeThumbnailImage:u})},render:e=>{let{open:t}=e;return(0,a.createElement)(r.Button,{className:"button button-large",onClick:t,style:{marginBottom:20}},(0,l.__)("Add Image","ht-youtube-embed"))}})),(0,a.createElement)(r.SelectControl,{label:(0,l.__)("Select YouTube Thumbnail Resolution:","ht-youtube-embed"),value:e.thumbnailresolution,options:[{label:(0,l.__)("Lowest Quality Dimensions: 120px × 90px","ht-youtube-embed"),value:"lowest"},{label:(0,l.__)("Medium Quality Dimensions: 320px × 180px","ht-youtube-embed"),value:"medium"},{label:(0,l.__)("High Quality Dimensions: 480px × 360px","ht-youtube-embed"),value:"high"},{label:(0,l.__)("Standard Quality Dimensions: 640px × 480px","ht-youtube-embed"),value:"standard"},{label:(0,l.__)("Recommended Thumbnail Dimension for SEO: 1280px × 720px","ht-youtube-embed"),value:"recommended"}],onChange:e=>{t({thumbnailresolution:e})}})))}}var m=b,d=window.wp.serverSideRender,s=o.n(d);class c extends a.Component{render(){return(0,a.createElement)("div",{className:"App"},(0,a.createElement)(m,this.props),(0,a.createElement)("b",null,(0,l.__)("Faster YouTube Embed","faster-youtube-embed")),(0,a.createElement)(s(),{block:this.props.name,attributes:this.props.attributes}))}}var y=JSON.parse('{"name":"htyoutube/block-faster-youtube-embed","category":"common","attributes":{"htYoutubeId":{"type":"string","default":""},"htYoutubeController":{"type":"boolean","default":true},"htYoutubevideoStartTime":{"type":"string","default":"0"},"htYoutubevideoEndTime":{"type":"string","default":"0"},"youtubeThumbnailImage":{"type":"object","default":{}},"thumbnailresolution":{"type":"string","default":"recommended"}}}');const{name:h,category:g,attributes:_}=y,p={title:(0,l.__)("Faster YouTube Embed New","faster-youtube-embed"),description:(0,l.__)("Faster YouTube Embed enables you to insert YouTube videos to any page and post quickly and efficiently & you’ll have no hassle of slow YouTube video load time anymore!","ht-qrcode"),icon:"youtube",keywords:[(0,l.__)("Faster YouTube Embed","faster-youtube-embed"),(0,l.__)("YouTube Embed","faster-youtube-embed")],example:{attributes:{value:(0,l.__)("Faster YouTube Embed","faster-youtube-embed"),thumbnailresolution:"recommended"}},attributes:_,edit:c,save:()=>null};[u].forEach((e=>{if(!e)return;const{name:t,category:o,settings:u}=e;(0,n.registerBlockType)(t,{category:o,...u})}))}(0,0,e)}();
import urllib.parse
import requests
import datetime
from typing import Dict, List, Optional

def search_french_city(city: str = None, zipcode: str = None, dept: str = None) -> List[Dict]:
    """
    Recherche une commune française via l'API du gouvernement français.
    Cette API connaît TOUTES les communes françaises.

    :param city: Nom de la ville
    :param zipcode: Code postal (5 chiffres)
    :param dept: Nom du département
    :return: Liste de résultats avec coordonnées
    """
    base_url = "https://geo.api.gouv.fr/communes"
    params = {
        "fields": "nom,code,codesPostaux,centre,departement,region",
        "format": "json",
        "geometry": "centre"
    }

    results = []

    try:
        # Recherche par code postal (la plus précise)
        if zipcode:
            params["codePostal"] = zipcode
            if city:
                params["nom"] = city

            response = requests.get(base_url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data:
                    results.extend(data)

        # Si pas de résultat ou pas de code postal, rechercher par nom
        if not results and city:
            params = {
                "nom": city,
                "fields": "nom,code,codesPostaux,centre,departement,region",
                "format": "json",
                "geometry": "centre"
            }

            response = requests.get(base_url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()

                # Filtrer par département si spécifié
                if dept and data:
                    filtered = []
                    for commune in data:
                        dept_name = commune.get("departement", {}).get("nom", "")
                        if dept.lower() in dept_name.lower():
                            filtered.append(commune)
                    if filtered:
                        results = filtered
                    else:
                        results = data
                else:
                    results = data

        # Formater les résultats
        formatted_results = []
        for commune in results:
            centre = commune.get("centre", {}).get("coordinates", [None, None])
            postcodes = commune.get("codesPostaux", [])

            formatted_results.append({
                "name": commune.get("nom", "N/A"),
                "country": "France",
                "admin1": commune.get("region", {}).get("nom", "N/A"),
                "admin2": commune.get("departement", {}).get("nom", "N/A"),
                "latitude": centre[1] if len(centre) > 1 else None,
                "longitude": centre[0] if len(centre) > 0 else None,
                "timezone": "Europe/Paris",
                "postcode": postcodes[0] if postcodes else "N/A",
                "all_postcodes": postcodes,
                "error": ""
            })

        return formatted_results

    except requests.RequestException as e:
        return [{"error": f"Erreur lors de la recherche: {str(e)}"}]


def get_city_info_enhanced(city: str = None, zipcode: str = None, dept: str = None) -> List[Dict]:
    """
    Recherche des informations de ville avec support multi-critères.
    Essaie d'abord l'API française (plus complète), puis Open-Meteo en fallback.

    :param city: Nom de la ville
    :param zipcode: Code postal
    :param dept: Nom du département
    :return: Liste de dictionnaires avec informations de localisation
    """

    # Tentative 1: API française (pour la France uniquement)
    if zipcode or dept or city:
        french_results = search_french_city(city=city, zipcode=zipcode, dept=dept)
        if french_results and not french_results[0].get("error"):
            return french_results

    # Tentative 2: Open-Meteo (fallback pour villes internationales ou si API française échoue)
    if city:
        search_terms = [city]
        if dept:
            search_terms.insert(0, f"{city}, {dept}")

        for search_term in search_terms:
            url = f"https://geocoding-api.open-meteo.com/v1/search?language=fr&format=json&name={urllib.parse.quote(search_term)}&count=5"

            try:
                response = requests.get(url=url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    results = data.get("results", [])

                    if results:
                        formatted = []
                        for city_data in results:
                            formatted.append({
                                "name": city_data.get("name", "N/A"),
                                "country": city_data.get("country", "N/A"),
                                "admin1": city_data.get("admin1", "N/A"),
                                "admin2": city_data.get("admin2", "N/A"),
                                "latitude": city_data.get("latitude"),
                                "longitude": city_data.get("longitude"),
                                "timezone": city_data.get("timezone", "UTC"),
                                "postcode": city_data.get("postcodes", ["N/A"])[0] if city_data.get("postcodes") else "N/A",
                                "error": ""
                            })
                        return formatted
            except:
                continue

    # Aucun résultat trouvé
    error_parts = []
    if city:
        error_parts.append(f"ville '{city}'")
    if zipcode:
        error_parts.append(f"code postal '{zipcode}'")
    if dept:
        error_parts.append(f"département '{dept}'")

    error_msg = "Localisation non trouvée"
    if error_parts:
        error_msg += f" ({', '.join(error_parts)})"

    return [{"error": error_msg + ". Veuillez vérifier les informations fournies."}]


def fetch_weather_data(base_url: str, params: Dict) -> Dict:
    """Récupère les données météo depuis l'API Open-Meteo."""
    try:
        response = requests.get(url=base_url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if "error" in data:
            return {"error": f"Erreur API météo: {data.get('message', 'Inconnue')}"}
        return data
    except requests.RequestException as e:
        return {"error": f"Erreur lors de la récupération météo: {str(e)}"}


# Dictionnaire WMO des codes météo
WMO_WEATHER_CODES = {
    0:  {"fr": "Ciel dégagé", "en": "Clear sky", "icon": "☀️"},
    1:  {"fr": "Principalement clair", "en": "Mainly clear", "icon": "🌤️"},
    2:  {"fr": "Partiellement nuageux", "en": "Partly cloudy", "icon": "⛅"},
    3:  {"fr": "Couvert", "en": "Overcast", "icon": "☁️"},
    45: {"fr": "Brouillard", "en": "Fog", "icon": "🌫️"},
    48: {"fr": "Brouillard givrant", "en": "Depositing rime fog", "icon": "🌫️❄️"},
    51: {"fr": "Bruine faible", "en": "Light drizzle", "icon": "🌦️"},
    53: {"fr": "Bruine modérée", "en": "Moderate drizzle", "icon": "🌧️"},
    55: {"fr": "Bruine forte", "en": "Dense drizzle", "icon": "🌧️"},
    56: {"fr": "Bruine verglaçante faible", "en": "Light freezing drizzle", "icon": "🌧️❄️"},
    57: {"fr": "Bruine verglaçante modérée ou forte", "en": "Dense freezing drizzle", "icon": "🌧️❄️"},
    61: {"fr": "Pluie faible", "en": "Slight rain", "icon": "🌦️"},
    63: {"fr": "Pluie modérée", "en": "Moderate rain", "icon": "🌧️"},
    65: {"fr": "Pluie forte", "en": "Heavy rain", "icon": "🌧️💧"},
    66: {"fr": "Pluie verglaçante faible", "en": "Light freezing rain", "icon": "🌧️❄️"},
    67: {"fr": "Pluie verglaçante modérée ou forte", "en": "Heavy freezing rain", "icon": "🌧️❄️"},
    71: {"fr": "Chute de neige faible", "en": "Slight snow fall", "icon": "🌨️"},
    73: {"fr": "Chute de neige modérée", "en": "Moderate snow fall", "icon": "❄️🌨️"},
    75: {"fr": "Chute de neige forte", "en": "Heavy snow fall", "icon": "❄️❄️"},
    77: {"fr": "Grains de neige", "en": "Snow grains", "icon": "🌨️"},
    80: {"fr": "Averses de pluie faibles", "en": "Slight rain showers", "icon": "🌦️"},
    81: {"fr": "Averses de pluie modérées", "en": "Moderate rain showers", "icon": "🌧️"},
    82: {"fr": "Averses de pluie violentes", "en": "Violent rain showers", "icon": "🌧️💦"},
    85: {"fr": "Averses de neige faibles", "en": "Slight snow showers", "icon": "🌨️"},
    86: {"fr": "Averses de neige modérées ou fortes", "en": "Heavy snow showers", "icon": "❄️🌨️"},
    95: {"fr": "Orage faible ou modéré", "en": "Thunderstorm", "icon": "⛈️"},
    96: {"fr": "Orage avec grêle légère", "en": "Thunderstorm with slight hail", "icon": "⛈️🌨️"},
    99: {"fr": "Orage avec grêle forte", "en": "Thunderstorm with heavy hail", "icon": "🌩️❄️"}
}


# Pour intégration avec LangChain
from langchain.agents import tool

@tool
def get_current_weather(city: str = None, zipcode: str = None, department: str = None) -> str:
    """
    Récupère la météo actuelle pour une localisation française.

    Le LLM doit extraire et fournir ces paramètres depuis la question de l'utilisateur:

    :param city: Nom de la ville (ex: "Bolleville", "Paris", "Lyon")
    :param zipcode: Code postal à 5 chiffres (ex: "50260", "75001") - optionnel
    :param department: Nom du département (ex: "Manche", "Paris", "Rhône") - optionnel
    :return: Informations météo détaillées ou message d'erreur

    Exemples d'utilisation par le LLM:
    - Question: "Météo à Bolleville dans la Manche"
      Appel: get_current_weather(city="Bolleville", department="Manche")

    - Question: "Quel temps fait-il au 75001?"
      Appel: get_current_weather(zipcode="75001")

    - Question: "Météo à Paris 75001"
      Appel: get_current_weather(city="Paris", zipcode="75001")
    """

    # Validation: au moins un paramètre doit être fourni
    if not city and not zipcode and not department:
        return (
            "❌ Aucune localisation fournie.\n"
            "Veuillez fournir au moins:\n"
            "- Un nom de ville (ex: city='Paris')\n"
            "- Un code postal (ex: zipcode='75001')\n"
            "- Un département (ex: department='Manche')"
        )

    # Récupérer les informations de localisation
    city_info = get_city_info_enhanced(
        city=city,
        zipcode=zipcode,
        dept=department
    )

    if not city_info or city_info[0].get("error"):
        error = city_info[0].get("error") if city_info else "Erreur inconnue"

        # Message d'erreur détaillé
        provided = []
        if city:
            provided.append(f"ville='{city}'")
        if zipcode:
            provided.append(f"code postal='{zipcode}'")
        if department:
            provided.append(f"département='{department}'")

        return f"❌ {error}\n\nParamètres fournis: {', '.join(provided)}"

    # Préparer les données météo
    formatted_data = ""

    for city_data in city_info[:3]:  # Limiter à 3 résultats max
        if city_data.get("error"):
            continue

        lat = city_data.get("latitude")
        lon = city_data.get("longitude")

        if not lat or not lon:
            continue

        # Paramètres API Open-Meteo
        openmeteo_base_url = "https://api.open-meteo.com/v1/forecast"
        openmeteo_params = {
            "models": "meteofrance_seamless",
            "latitude": lat,
            "longitude": lon,
            "timezone": city_data.get("timezone", "Europe/Paris"),
            "temperature_unit": "celsius",
            "wind_speed_unit": "kmh",
            "precipitation_unit": "mm",
            "forecast_days": 1,
            "current": [
                "temperature_2m",
                "relative_humidity_2m",
                "apparent_temperature",
                "wind_speed_10m",
                "wind_direction_10m",
                "weather_code",
            ],
        }

        weather_data = fetch_weather_data(openmeteo_base_url, openmeteo_params)

        if weather_data.get("error"):
            continue

        # Formater la réponse
        dt = datetime.datetime.strptime(weather_data['current']['time'], "%Y-%m-%dT%H:%M")
        formatted_timestamp = dt.strftime("%d/%m/%Y à %H:%M")

        weather_code = weather_data['current']['weather_code']
        condition = WMO_WEATHER_CODES.get(weather_code, {"fr": "Non défini", "icon": ""})

        location_str = f"{city_data['name']}"
        if city_data.get('postcode') != "N/A":
            location_str += f" ({city_data['postcode']})"
        location_str += f" - {city_data['admin2']}, {city_data['admin1']}"

        formatted_data += f"""🌍 Localisation: {location_str}
📅 Date/Heure: {formatted_timestamp} ({weather_data.get('timezone_abbreviation', 'CET')})

🌡️ Température: {weather_data['current']['temperature_2m']}°C
💨 Température ressentie: {weather_data['current']['apparent_temperature']}°C
💧 Humidité: {weather_data['current']['relative_humidity_2m']}%
🌬️ Vent: {weather_data['current']['wind_speed_10m']} km/h (direction: {weather_data['current']['wind_direction_10m']}°)
{condition['icon']} Condition: {condition['fr']}

"""

    if not formatted_data:
        return "❌ Impossible de récupérer les données météo pour cette localisation."

    return formatted_data.strip()


# Exemple d'utilisation standalone (pour tests)
if __name__ == "__main__":
    print("=" * 70)
    print("Test 1: Ville + Département")
    print("=" * 70)
    result = get_current_weather(city="Bolleville", department="Manche")
    print(result)

    print("\n" + "=" * 70)
    print("Test 2: Code postal seul")
    print("=" * 70)
    result = get_current_weather(zipcode="50260")
    print(result)

    print("\n" + "=" * 70)
    print("Test 3: Ville + Code postal")
    print("=" * 70)
    result = get_current_weather(city="Paris", zipcode="75001")
    print(result)

    print("\n" + "=" * 70)
    print("Test 4: Ville seule")
    print("=" * 70)
    result = get_current_weather(city="Lyon")
    print(result)

    print("\n" + "=" * 70)
    print("Test 5: Aucun paramètre (erreur attendue)")
    print("=" * 70)
    result = get_current_weather()
    print(result)

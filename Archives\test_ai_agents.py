from langchain.agents import <PERSON><PERSON><PERSON>cut<PERSON>, tool
from langchain_ollama import ChatOllama
from langchain_core.tools import render_text_description
from langchain.agents import create_tool_calling_agent
from langchain_core.prompts import ChatPromptTemplate
from langchain.chat_models import init_chat_model
from langchain import hub
from langgraph.prebuilt import create_react_agent
# pip install --upgrade --force-reinstall langgraph
from langgraph_supervisor import create_supervisor


from html_clean import clean_html


LLM_MODEL_NAME = "gpt-oss:20b-cloud"

@tool
def fetch_content_from_url(url:str) -> str:
    """Fetch content from a URL"""
    return ("TODO: fetch_content_from_url: " + url)


@tool
def weather_forecast(location: str):
    """Get a weather forecast for a given location.

    Args:
        location (str): The name of the location (city, region, etc.) to get the weather for.

    Returns:
        str: A formatted string containing the weather forecast for the specified location.
    """
    print(f"Weather for  {location}")
    return f""""Weather forecast for {location}
- Temperature: 18°C
- Condition: Partly cloudy
- Wind: 10 km/h from the northwest
- Humidity: 60%
- Chance of rain: 20%
"""


def main():
    llm_model = ChatOllama(
        #base_url="http://************:11434",
        #model="phi4:latest",
        ###model="gpt-oss:20b-cloud",

        base_url="https://ollama.com",
        model="gpt-oss:20b",

        temperature=0.1,
    )

    tools = [weather_forecast,fetch_content_from_url]

    agent_tools = render_text_description(tools)
    agent_tool_names = ", ".join([t.name for t in tools])

    prompt = ChatPromptTemplate.from_messages(
                [
                    ("system", f"""Answer the following questions as best you can. You have access to the following tools:

                    {agent_tools}

                    Use the following format:

                    Question: the input question you must answer
                    Thought: you should always think about what to do
                    Action: the action to take, should be one of [{agent_tool_names}]
                    Action Input: the input to the action
                    Observation: the result of the action
                    ... (this Thought/Action/Action Input/Observation can repeat N times)
                    Thought: I now know the final answer
                    Final Answer: the final answer to the original input question

                    Begin!"""),
                    ("human", "{input}"),
                    ("placeholder", "{agent_scratchpad}"),
                ]
    )

    # https://python.langchain.com/api_reference/langchain/agents/langchain.agents.tool_calling_agent.base.create_tool_calling_agent.html
    prompt = hub.pull("hwchase17/react")
    agent = create_tool_calling_agent(llm_model, tools, prompt)

    """agent_executor = AgentExecutor(
        agent=agent,
        tools=tools,
        max_iterations=5,
        handle_parsing_errors=False,
        verbose=True
    )
    print(agent_executor.invoke({"input":"Quel temps fait-il à Paris ?"}))"""

    # https://python.langchain.com/api_reference/langchain/agents/langchain.agents.react.agent.create_react_agent.html
    agent_graph = create_react_agent(
        llm_model,
        tools,
        prompt=prompt,
        name="agent_graph",
    )

    supervisor = create_supervisor(
        model=llm_model, #init_chat_model("gpt-oss:20b"),
        agents=[agent_graph],
        prompt=prompt,
        add_handoff_back_messages=True,
        output_mode="full_history",
    ).compile()

    # A VOIR : https://github.com/langchain-ai/langgraph-supervisor-py

if __name__ == "__main__":
    import os
    # TODO Utiliser loadenv
    os.environ["OLLAMA_API_KEY"] = '447d9b4a99e745478aa557a1421b8448.sw7ANALKpswZmgEltzRctsGX'
    os.environ["HTTP_PROXY"] = "proxy-internet.crip.cnamts.fr:3128"
    os.environ["HTTPS_PROXY"] = "proxy-internet.crip.cnamts.fr:3128"

    main()

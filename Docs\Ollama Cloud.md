# Ollama Cloud

Ollama's cloud is free to try with a free [Ollama account](https://ollama.com/Lemoussel)!

Ollama’s cloud models also work via [Ollama’s OpenAI-compatible API](https://docs.ollama.com/openai).

## Available models

- `qwen3-coder:480b-cloud`
- `gpt-oss:120b-cloud`
- `gpt-oss:20b-cloud`
- `deepseek-v3.1:671b-cloud`

## Get started

Download Ollama v0.12, then open a terminal and run a cloud model:

`ollama run qwen3-coder:480b-cloud`

## Usage

Cloud models behave like regular models. For example, you can `ls`, `run`, `pull`, and `cp` them as needed:

```bash
ollama ls
ollama run qwen3-coder:480b-cloud
ollama pull qwen3-coder:480b-cloud
ollama cp qwen3-coder:480b-cloud mymodel
```

![Ollama Cloud Models](./Ollama%20Cloud.png)

Learn more about Ollama's cloud

- [Blog](https://ollama.com/blog/cloud-models) on cloud models
- [Documentation](https://docs.ollama.com/cloud)
- [Ollama's Cloud](https://ollama.com/cloud) features

## Divers

[Ollama's releases on GitHub](https://github.com/ollama/ollama/releases)

[run-ollama-in-colab.ipynb](https://colab.research.google.com/drive/1rKCD1olwXXfdJLXyt2wFf42Uv-mkr53s)

[ChatOllama](https://colab.research.google.com/github/langchain-ai/langchain/blob/master/docs/docs/integrations/chat/ollama.ipynb#scrollTo=e49f1e0d)
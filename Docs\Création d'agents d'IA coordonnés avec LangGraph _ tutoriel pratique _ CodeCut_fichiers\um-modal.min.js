jQuery(document).ready(function(){jQuery(document).on("click",".um-popup-overlay",function(){remove_Modal()}),jQuery(document).on("click",'.um-modal-overlay, a[data-action="um_remove_modal"]',function(){um_remove_modal()}),jQuery(document).on("click",'a[data-modal^="um_"], span[data-modal^="um_"], .um-modal:not(:has(.um-form)) a',function(e){return e.preventDefault(),!1}),jQuery(document).on("click",".um-finish-upload.file:not(.disabled)",function(){var e=jQuery(this).attr("data-key"),a=jQuery(this).parents(".um-modal-body").find(".um-single-file-preview").html(),a=(um_remove_modal(),jQuery(".um-single-file-preview[data-key="+e+"]").fadeIn().html(a),jQuery(".um-field[data-key="+e+"]").find(".um-single-fileinfo a").data("file"));jQuery(".um-single-file-preview[data-key="+e+"]").parents(".um-field").find(".um-btn-auto-width").html(jQuery(this).attr("data-change")),jQuery(".um-single-file-preview[data-key="+e+"]").parents(".um-field").find('input[type="hidden"]').val(a)}),jQuery(document).on("click",".um-finish-upload.image:not(.disabled)",function(){var r,e,u=jQuery(this),d=jQuery(this).attr("data-key"),a=jQuery(this).parents(".um-modal-body").find(".um-single-image-preview"),t=a.find("img").attr("src"),a=a.find("img").data("file"),i=0,m=(jQuery(this).parents("#um_upload_single").data("user_id")&&(i=jQuery(this).parents("#um_upload_single").data("user_id")),0),n="";1!==jQuery('div.um-field-image[data-key="'+d+'"]').length?console.warn(wp.i18n.__("UM Warning: No field associated with image uploader.","ultimate-member")):(m=(e=jQuery('div.um-field-image[data-key="'+d+'"]').closest(".um-form")).data("form_id")||e.find('input[name="form_id"]').val(),n=e.data("mode"),0<jQuery(".cropper-hidden").length&&UM.frontend.cropper.obj?(e=UM.frontend.cropper.obj.getData(),e=Math.round(e.x)+","+Math.round(e.y)+","+Math.round(e.width)+","+Math.round(e.height),jQuery(this).html(jQuery(this).attr("data-processing")).addClass("disabled"),jQuery.ajax({url:wp.ajax.settings.url,type:"POST",dataType:"json",data:{action:"um_resize_image",src:t,coord:e,user_id:i,key:d,set_id:m,set_mode:n,nonce:um_scripts.nonce},success:function(e){var a,t,i;e.success&&(r=new Date,"profile_photo"===d?(jQuery(".um-profile-photo-img img").attr("src",e.data.image.source_url+"?"+r.getTime()),(i=jQuery(".um-profile-photo .um-dropdown")).find(".um-reset-profile-photo").hasClass("um-is-visible")||(t=(a=i.find('.um-manual-trigger[data-parent=".um-profile-photo"]')).data("alt_text"),a.data("alt_text",a.text()).text(t),i.find(".um-reset-profile-photo").addClass("um-is-visible").show()),jQuery(".um").hasClass("um-editing")||i.remove()):"cover_photo"===d&&(jQuery(".um-cover-e").empty().html('<img src="'+e.data.image.source_url+"?"+r.getTime()+'" alt="" />'),(a=jQuery(".um-cover .um-dropdown")).find(".um-reset-cover-photo").hasClass("um-is-visible")||(i=(t=a.find('.um-manual-trigger[data-parent=".um-cover"]')).data("alt_text"),t.data("alt_text",t.text()).text(i),a.find(".um-reset-cover-photo").addClass("um-is-visible").show()),jQuery(".um").hasClass("um-editing")?jQuery(".um-cover-overlay").show():a.remove(),um_responsive()),jQuery(".um-single-image-preview[data-key="+d+"]").fadeIn().find("img").attr("src",e.data.image.source_url+"?"+r.getTime()),um_remove_modal(),jQuery("img.cropper-invisible").remove(),jQuery(".um-single-image-preview[data-key="+d+"]").parents(".um-field").find(".um-btn-auto-width").html(u.attr("data-change")),jQuery(".um-single-image-preview[data-key="+d+"]").parents(".um-field").find('input[type="hidden"]').val(e.data.image.filename))}})):(r=new Date,jQuery(".um-single-image-preview[data-key="+d+"]").fadeIn().find("img").attr("src",t+"?"+r.getTime()),um_remove_modal(),jQuery(".um-single-image-preview[data-key="+d+"]").parents(".um-field").find(".um-btn-auto-width").html(u.attr("data-change")),jQuery(".um-single-image-preview[data-key="+d+"]").parents(".um-field").find("input[type=hidden]").val(a)))}),jQuery(document.body).on("click",'a[data-modal^="um_"], span[data-modal^="um_"]',function(e){var a=jQuery(this).attr("data-modal"),t="normal";jQuery(this).data("modal-size")&&(t=jQuery(this).data("modal-size")),jQuery(this).data("modal-copy")&&(jQuery("#"+a).html(jQuery(this).parents(".um-field").find(".um-modal-hidden-content").html()),jQuery(this).parents(".um-profile-photo").attr("data-user_id")&&jQuery("#"+a).attr("data-user_id",jQuery(this).parents(".um-profile-photo").attr("data-user_id")),jQuery(this).parents(".um-cover").attr("data-ratio")&&jQuery("#"+a).attr("data-ratio",jQuery(this).parents(".um-cover").attr("data-ratio")),jQuery(this).parents(".um-cover").attr("data-user_id")&&jQuery("#"+a).attr("data-user_id",jQuery(this).parents(".um-cover").attr("data-user_id")),0<jQuery('input[type="hidden"][name="user_id"]').length)&&jQuery("#"+a).attr("data-user_id",jQuery('input[type="hidden"][name="user_id"]').val()),um_new_modal(a,t)})});
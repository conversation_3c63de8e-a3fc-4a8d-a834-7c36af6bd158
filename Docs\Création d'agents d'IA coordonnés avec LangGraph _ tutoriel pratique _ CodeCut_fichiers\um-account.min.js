jQuery(document).ready(function(){var t=jQuery(".um-account-main").attr("data-current_tab");t&&(jQuery('.um-account-tab[data-tab="'+t+'"]').show(),jQuery(".um-account-tab:not(:visible)").find("input, select, textarea").not(":disabled").addClass("um_account_inactive").prop("disabled",!0).attr("disabled",!0),wp.hooks.doAction("um_account_active_tab_inited",t)),jQuery(document.body).on("click",".um-account-side li a",function(t){t.preventDefault();var t=jQuery(this),t=(t.parents("ul").find("li a").removeClass("current"),t.addClass("current"),jQuery(this).attr("href")),a=jQuery(this).attr("data-tab");return jQuery('input[id="_um_account_tab"]:hidden').val(a),window.history.pushState("","",t),jQuery(".um-account-tab").hide(),jQuery('.um-account-tab[data-tab="'+a+'"]').fadeIn(),jQuery(".um-account-tab:visible").find("input, select, textarea").filter(".um_account_inactive:disabled").removeClass("um_account_inactive").prop("disabled",!1).attr("disabled",!1),jQuery(".um-account-tab:not(:visible)").find("input, select, textarea").not(":disabled").addClass("um_account_inactive").prop("disabled",!0).attr("disabled",!0),jQuery(".um-account-nav a").removeClass("current"),jQuery('.um-account-nav a[data-tab="'+a+'"]').addClass("current"),jQuery(this).parents(".um-account").find(".um-account-main .um-notice").fadeOut(),wp.hooks.doAction("um_after_account_tab_changed",a),!1}),jQuery(document.body).on("click",".um-account-nav a",function(t){t.preventDefault();var t=jQuery(this).attr("data-tab"),a=jQuery(this).parents("div"),e=jQuery(this);return jQuery('input[id="_um_account_tab"]:hidden').val(t),jQuery(".um-account-tab").hide(),e.hasClass("current")?(a.next(".um-account-tab").slideUp(),e.removeClass("current")):(a.next(".um-account-tab").slideDown(),e.parents("div").find("a").removeClass("current"),e.addClass("current")),jQuery(".um-account-tab:visible").find("input, select, textarea").filter(".um_account_inactive:disabled").removeClass("um_account_inactive").prop("disabled",!1).attr("disabled",!1),jQuery(".um-account-tab:not(:visible)").find("input, select, textarea").not(":disabled").addClass("um_account_inactive").prop("disabled",!0).attr("disabled",!0),jQuery(".um-account-side li a").removeClass("current"),jQuery('.um-account-side li a[data-tab="'+t+'"]').addClass("current"),wp.hooks.doAction("um_after_account_tab_changed",t),!1}),jQuery(document.body).on("click",".um-request-button",function(t){t.preventDefault();var a,e=jQuery(this).data("action"),t=jQuery("#"+e).val();jQuery(".um-field-area-response."+e).hide(),jQuery("#"+e).length&&""===t?jQuery(".um-field-error."+e).show():(jQuery(".um-field-error."+e).hide(),a={request_action:e,nonce:um_scripts.nonce},jQuery("#"+e).length&&(a.password=t),wp.ajax.send("um_request_user_data",{data:a,success:function(t){jQuery(".um-field-area-response."+e).text(t.answer).show()},error:function(t){console.log(t)}}))})});
from os.path import join, dirname


# https://github.com/langchain-ai/langchain
from langchain.agents import tool

# https://github.com/langchain-ai/langchain/tree/master/libs/partners/ollama
from langchain_ollama import ChatOllama

# https://github.com/langchain-ai/langgraph
from langgraph.prebuilt import create_react_agent

# https://github.com/langchain-ai/langgraph-supervisor-py
from langgraph_supervisor import create_supervisor

# https://github.com/theskumar/python-dotenv
from dotenv import load_dotenv

# https://requests.readthedocs.io/en/latest/
import requests

# https://urllib3.readthedocs.io/
import urllib3

# https://github.com/adbar/trafilatura
import trafilatura

# from html_clean import clean_html


class SEOConfig:
    """Configuration centralisée pour le système SEO."""

    # Configuration Ollama
    OLLAMA_BASE_URL: str = "https://ollama.com"
    OLLAMA_MODEL: str = "gpt-oss:20b"
    OLLAMA_TEMPERATURE: float = 0.1

    # Fichiers de sortie
    OUTPUT_DIR: str = "audits"
    OUTPUT_FORMAT: str = "md"

# https://openwebui.com/t/cooksleep/infinite_search


@tool
def web_scrape(url: str) -> str:
    """
    Extrait le contenu textuel principal d'une page web en utilisant la bibliothèque trafilatura.

    Cette fonction prend une URL, télécharge son contenu, et utilise trafilatura
    pour en extraire le texte principal et le titre. Elle est conçue pour récupérer
    le contenu de type article, en ignorant les commentaires et les tableaux.
    La fonction gère les erreurs de téléchargement et d'extraction.

    Args:
        url (str): L'URL de la page web à scraper.

    Returns:
        str: Une chaîne de caractères formatée contenant le titre et le contenu
             principal de la page, ou un message d'erreur en cas d'échec.
    """
    try:
        response = trafilatura.fetch_response(url, decode=True, with_headers=True)
        if response is None:
            return f"❌ Erreur: Impossible de récupérer la page : {url}."

        main_text = trafilatura.extract(
            response.html,
            include_comments=False,
            include_tables=True
        )
        if not main_text:
            return f"Aucun contenu principal n'a été trouvé pour la page : {url}."
        meta = trafilatura.extract_metadata(response.html) if response.html else None
        title = meta.title if meta and hasattr(meta, "title") else ""
        return (
            f"## {f'Site Name : {meta.sitename} - ' if meta and meta.sitename else ''}Title : {title} ({url})\n\n{main_text}"
        )
    except (urllib3.exceptions.HTTPError, requests.exceptions.RequestException, ValueError) as e:
        return f"❌ Erreur réseau lors de la récupération de {url}: {e}"
    except (AttributeError, TypeError, UnicodeDecodeError) as e:
        return f"❌ Erreur lors du traitement du contenu pour {url}: {e}"


@tool
def analyze_seo_metrics(content: str) -> str:
    """
    Analyse les métriques SEO basiques du contenu.

    Args:
        content (str): Contenu de la page au format Markdown

    Returns:
        str: Rapport des métriques SEO
    """
    lines = content.split("\n")
    word_count = len(content.split())

    # Extraction des titres
    h1_count = sum(1 for line in lines if line.startswith("# "))
    h2_count = sum(1 for line in lines if line.startswith("## "))
    h3_count = sum(1 for line in lines if line.startswith("### "))

    # Extraction des liens
    link_count = content.count("[")

    metrics = f"""
📊 **Métriques SEO basiques:**
- Nombre de mots: {word_count}
- Titres H1: {h1_count}
- Titres H2: {h2_count}
- Titres H3: {h3_count}
- Liens détectés: {link_count}
"""
    return metrics


def main():
    """
    Fonction principale pour exécuter l'audit SEO.
    """

    llm_model = ChatOllama(
        # base_url="http://************:11434",
        # model="phi4:latest",
        ###model="gpt-oss:20b-cloud",
        base_url="https://ollama.com",
        model="gpt-oss:20b",
        temperature=0.1,
    )

    web_scrape_agent = create_react_agent(
        model=llm_model,
        tools=[web_scrape],
        name="web_scrape_expert",
        prompt=(
            "Vous êtes un expert en SEO. "
            "Lorsqu'un utilisateur demande un audit ou une abalyse SEO d'une page web, vous devez extraire de sa question l'URL de la page web.\n"
            "Appelez ensuite l'outil `web_scrape` avec l'URL de la page web en paramètre.\n"
        ),
    )

    workflow = create_supervisor(
        agents=[web_scrape_agent],
        model=llm_model,
        output_mode="full_history",
        add_handoff_back_messages=True,
        prompt=(
            "Vous êtes responsable d'équipe et gérez des experts en SEO."
            "Pour extraire le contenu d'une page web, utilisez l'expert `web_scrape_expert`."
            "Sélectionnez toujours l'expert le plus approprié pour chaque demande utilisateur et coordonnez les réponses si nécessaire."
        ),
    )

    app = workflow.compile()
    result = app.invoke(
        {
            "messages": [
                {
                    "role": "user",
                    "content": "Réalise un audit SEO de cette page web : https://zonetuto.fr/intelligence-artificielle/generation-gratuit-image-openrouter-nano-banana/",
                }
            ]
        }
    )
    with open("resultat.md", "w", encoding="utf-8") as f:
        f.write(f"{result['messages'][-1].content}\n")
        print("Réponse sauvegardée dans le fichier 'résultat.md'")


if __name__ == "__main__":
    dotenv_path = join(dirname(__file__), ".env")
    load_dotenv(dotenv_path)

    main()

# Tests SEO AI Agents

Ce répertoire contient tous les tests pour le projet SEO AI Agents.

## 📁 Structure des tests

```
tests/
├── __init__.py              # Package de tests
├── conftest.py              # Configuration pytest et fixtures
├── test_web_scrape.py       # Tests pour web scraping
├── test_seo_metrics.py      # Tests pour analyse SEO
├── test_config.py           # Tests pour configuration
└── README.md               # Cette documentation
```

## 🚀 Exécution des tests

### Méthode 1: pytest directement

```bash
# Tous les tests
pytest tests/

# Tests avec couverture
pytest tests/ --cov=. --cov-report=term-missing

# Test spécifique
pytest tests/test_web_scrape.py

# Tests avec rapport HTML
pytest tests/ --html=reports/test_report.html
```

### Méthode 2: Script Python (Recommandé)

```bash
# Tous les tests
python run_tests.py

# Avec couverture de code
python run_tests.py --coverage

# Fichier spécifique
python run_tests.py --file test_web_scrape.py

# Rapport HTML
python run_tests.py --html

# Mode verbeux
python run_tests.py --verbose
```

## 📊 Types de tests

### Tests unitaires
- `test_config.py` : Tests de configuration
- `test_seo_metrics.py` : Tests d'analyse SEO
- `test_web_scrape.py` : Tests de web scraping

### Fixtures disponibles
- `sample_url` : URL de test pour web scraping
- `sample_content` : Contenu de test pour analyse SEO

## 📈 Rapports

### Couverture de code
Les rapports de couverture sont générés dans `htmlcov/` avec l'option `--coverage`.

### Rapports HTML
Les rapports HTML des tests sont générés dans `reports/test_report.html` avec l'option `--html`.

## 🔧 Configuration

La configuration pytest se trouve dans `pytest.ini` à la racine du projet.

### Marqueurs disponibles
- `unit` : Tests unitaires
- `integration` : Tests d'intégration
- `slow` : Tests lents
- `web` : Tests nécessitant une connexion internet

## 📝 Écriture de nouveaux tests

### Exemple de test unitaire

```python
import unittest
from seo_ai_agents import ma_fonction

class TestMaFonction(unittest.TestCase):
    def test_cas_normal(self):
        result = ma_fonction.invoke("test")
        self.assertEqual(result, "expected")
```

### Exemple de test pytest

```python
import pytest
from seo_ai_agents import ma_fonction

def test_ma_fonction():
    result = ma_fonction.invoke("test")
    assert result == "expected"

def test_avec_fixture(sample_url):
    result = ma_fonction.invoke(sample_url)
    assert "expected" in result
```

## 🐛 Débogage

Pour déboguer un test spécifique :

```bash
# Mode verbeux avec détails
python run_tests.py --file test_web_scrape.py --verbose

# Avec pytest directement
pytest tests/test_web_scrape.py -v -s
```

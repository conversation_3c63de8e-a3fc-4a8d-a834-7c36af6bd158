/*! This file is auto-generated */
(()=>{var e={1933:(e,t,n)=>{var r;!function(o,i){if(o){for(var u,c={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},s={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},a={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},l={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},f=1;f<20;++f)c[111+f]="f"+f;for(f=0;f<=9;++f)c[f+96]=f.toString();g.prototype.bind=function(e,t,n){var r=this;return e=e instanceof Array?e:[e],r._bindMultiple.call(r,e,t,n),r},g.prototype.unbind=function(e,t){return this.bind.call(this,e,(function(){}),t)},g.prototype.trigger=function(e,t){var n=this;return n._directMap[e+":"+t]&&n._directMap[e+":"+t]({},e),n},g.prototype.reset=function(){var e=this;return e._callbacks={},e._directMap={},e},g.prototype.stopCallback=function(e,t){if((" "+t.className+" ").indexOf(" mousetrap ")>-1)return!1;if(y(t,this.target))return!1;if("composedPath"in e&&"function"==typeof e.composedPath){var n=e.composedPath()[0];n!==e.target&&(t=n)}return"INPUT"==t.tagName||"SELECT"==t.tagName||"TEXTAREA"==t.tagName||t.isContentEditable},g.prototype.handleKey=function(){return this._handleKey.apply(this,arguments)},g.addKeycodes=function(e){for(var t in e)e.hasOwnProperty(t)&&(c[t]=e[t]);u=null},g.init=function(){var e=g(i);for(var t in e)"_"!==t.charAt(0)&&(g[t]=function(t){return function(){return e[t].apply(e,arguments)}}(t))},g.init(),o.Mousetrap=g,e.exports&&(e.exports=g),void 0===(r=function(){return g}.call(t,n,t,e))||(e.exports=r)}function d(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent("on"+t,n)}function p(e){if("keypress"==e.type){var t=String.fromCharCode(e.which);return e.shiftKey||(t=t.toLowerCase()),t}return c[e.which]?c[e.which]:s[e.which]?s[e.which]:String.fromCharCode(e.which).toLowerCase()}function h(e){return"shift"==e||"ctrl"==e||"alt"==e||"meta"==e}function v(e,t,n){return n||(n=function(){if(!u)for(var e in u={},c)e>95&&e<112||c.hasOwnProperty(e)&&(u[c[e]]=e);return u}()[e]?"keydown":"keypress"),"keypress"==n&&t.length&&(n="keydown"),n}function m(e,t){var n,r,o,i=[];for(n=function(e){return"+"===e?["+"]:(e=e.replace(/\+{2}/g,"+plus")).split("+")}(e),o=0;o<n.length;++o)r=n[o],l[r]&&(r=l[r]),t&&"keypress"!=t&&a[r]&&(r=a[r],i.push("shift")),h(r)&&i.push(r);return{key:r,modifiers:i,action:t=v(r,i,t)}}function y(e,t){return null!==e&&e!==i&&(e===t||y(e.parentNode,t))}function g(e){var t=this;if(e=e||i,!(t instanceof g))return new g(e);t.target=e,t._callbacks={},t._directMap={};var n,r={},o=!1,u=!1,c=!1;function s(e){e=e||{};var t,n=!1;for(t in r)e[t]?n=!0:r[t]=0;n||(c=!1)}function a(e,n,o,i,u,c){var s,a,l,f,d=[],p=o.type;if(!t._callbacks[e])return[];for("keyup"==p&&h(e)&&(n=[e]),s=0;s<t._callbacks[e].length;++s)if(a=t._callbacks[e][s],(i||!a.seq||r[a.seq]==a.level)&&p==a.action&&("keypress"==p&&!o.metaKey&&!o.ctrlKey||(l=n,f=a.modifiers,l.sort().join(",")===f.sort().join(",")))){var v=!i&&a.combo==u,m=i&&a.seq==i&&a.level==c;(v||m)&&t._callbacks[e].splice(s,1),d.push(a)}return d}function l(e,n,r,o){t.stopCallback(n,n.target||n.srcElement,r,o)||!1===e(n,r)&&(function(e){e.preventDefault?e.preventDefault():e.returnValue=!1}(n),function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}(n))}function f(e){"number"!=typeof e.which&&(e.which=e.keyCode);var n=p(e);n&&("keyup"!=e.type||o!==n?t.handleKey(n,function(e){var t=[];return e.shiftKey&&t.push("shift"),e.altKey&&t.push("alt"),e.ctrlKey&&t.push("ctrl"),e.metaKey&&t.push("meta"),t}(e),e):o=!1)}function v(e,t,i,u){function a(t){return function(){c=t,++r[e],clearTimeout(n),n=setTimeout(s,1e3)}}function f(t){l(i,t,e),"keyup"!==u&&(o=p(t)),setTimeout(s,10)}r[e]=0;for(var d=0;d<t.length;++d){var h=d+1===t.length?f:a(u||m(t[d+1]).action);y(t[d],h,u,e,d)}}function y(e,n,r,o,i){t._directMap[e+":"+r]=n;var u,c=(e=e.replace(/\s+/g," ")).split(" ");c.length>1?v(e,c,n,r):(u=m(e,r),t._callbacks[u.key]=t._callbacks[u.key]||[],a(u.key,u.modifiers,{type:u.action},o,e,i),t._callbacks[u.key][o?"unshift":"push"]({callback:n,modifiers:u.modifiers,action:u.action,seq:o,level:i,combo:e}))}t._handleKey=function(e,t,n){var r,o=a(e,t,n),i={},f=0,d=!1;for(r=0;r<o.length;++r)o[r].seq&&(f=Math.max(f,o[r].level));for(r=0;r<o.length;++r)if(o[r].seq){if(o[r].level!=f)continue;d=!0,i[o[r].seq]=1,l(o[r].callback,n,o[r].combo,o[r].seq)}else d||l(o[r].callback,n,o[r].combo);var p="keypress"==n.type&&u;n.type!=c||h(e)||p||s(i),u=d&&"keydown"==n.type},t._bindMultiple=function(e,t,n){for(var r=0;r<e.length;++r)y(e[r],t,n)},d(e,"keypress",f),d(e,"keydown",f),d(e,"keyup",f)}}("undefined"!=typeof window?window:null,"undefined"!=typeof window?document:null)},3758:function(e){
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
var t;t=function(){return function(){var e={686:function(e,t,n){"use strict";n.d(t,{default:function(){return S}});var r=n(279),o=n.n(r),i=n(370),u=n.n(i),c=n(817),s=n.n(c);function a(e){try{return document.execCommand(e)}catch(e){return!1}}var l=function(e){var t=s()(e);return a("cut"),t},f=function(e,t){var n=function(e){var t="rtl"===document.documentElement.getAttribute("dir"),n=document.createElement("textarea");n.style.fontSize="12pt",n.style.border="0",n.style.padding="0",n.style.margin="0",n.style.position="absolute",n.style[t?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return n.style.top="".concat(r,"px"),n.setAttribute("readonly",""),n.value=e,n}(e);t.container.appendChild(n);var r=s()(n);return a("copy"),n.remove(),r},d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},n="";return"string"==typeof e?n=f(e,t):e instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null==e?void 0:e.type)?n=f(e.value,t):(n=s()(e),a("copy")),n};function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}var h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.action,n=void 0===t?"copy":t,r=e.container,o=e.target,i=e.text;if("copy"!==n&&"cut"!==n)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==o){if(!o||"object"!==p(o)||1!==o.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===n&&o.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===n&&(o.hasAttribute("readonly")||o.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return i?d(i,{container:r}):o?"cut"===n?l(o):d(o,{container:r}):void 0};function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function m(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function y(e,t){return y=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},y(e,t)}function g(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r,o,i=b(e);if(t){var u=b(this).constructor;n=Reflect.construct(i,arguments,u)}else n=i.apply(this,arguments);return r=this,!(o=n)||"object"!==v(o)&&"function"!=typeof o?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r):o}}function b(e){return b=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},b(e)}function w(e,t){var n="data-clipboard-".concat(e);if(t.hasAttribute(n))return t.getAttribute(n)}var E=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&y(e,t)}(i,e);var t,n,r,o=g(i);function i(e,t){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),(n=o.call(this)).resolveOptions(t),n.listenClick(e),n}return t=i,n=[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"==typeof e.action?e.action:this.defaultAction,this.target="function"==typeof e.target?e.target:this.defaultTarget,this.text="function"==typeof e.text?e.text:this.defaultText,this.container="object"===v(e.container)?e.container:document.body}},{key:"listenClick",value:function(e){var t=this;this.listener=u()(e,"click",(function(e){return t.onClick(e)}))}},{key:"onClick",value:function(e){var t=e.delegateTarget||e.currentTarget,n=this.action(t)||"copy",r=h({action:n,container:this.container,target:this.target(t),text:this.text(t)});this.emit(r?"success":"error",{action:n,text:r,trigger:t,clearSelection:function(){t&&t.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(e){return w("action",e)}},{key:"defaultTarget",value:function(e){var t=w("target",e);if(t)return document.querySelector(t)}},{key:"defaultText",value:function(e){return w("text",e)}},{key:"destroy",value:function(){this.listener.destroy()}}],r=[{key:"copy",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return d(e,t)}},{key:"cut",value:function(e){return l(e)}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],t="string"==typeof e?[e]:e,n=!!document.queryCommandSupported;return t.forEach((function(e){n=n&&!!document.queryCommandSupported(e)})),n}}],n&&m(t.prototype,n),r&&m(t,r),i}(o()),S=E},828:function(e){if("undefined"!=typeof Element&&!Element.prototype.matches){var t=Element.prototype;t.matches=t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector}e.exports=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}}},438:function(e,t,n){var r=n(828);function o(e,t,n,r,o){var u=i.apply(this,arguments);return e.addEventListener(n,u,o),{destroy:function(){e.removeEventListener(n,u,o)}}}function i(e,t,n,o){return function(n){n.delegateTarget=r(n.target,t),n.delegateTarget&&o.call(e,n)}}e.exports=function(e,t,n,r,i){return"function"==typeof e.addEventListener?o.apply(null,arguments):"function"==typeof n?o.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return o(e,t,n,r,i)})))}},879:function(e,t){t.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},t.nodeList=function(e){var n=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===n||"[object HTMLCollection]"===n)&&"length"in e&&(0===e.length||t.node(e[0]))},t.string=function(e){return"string"==typeof e||e instanceof String},t.fn=function(e){return"[object Function]"===Object.prototype.toString.call(e)}},370:function(e,t,n){var r=n(879),o=n(438);e.exports=function(e,t,n){if(!e&&!t&&!n)throw new Error("Missing required arguments");if(!r.string(t))throw new TypeError("Second argument must be a String");if(!r.fn(n))throw new TypeError("Third argument must be a Function");if(r.node(e))return function(e,t,n){return e.addEventListener(t,n),{destroy:function(){e.removeEventListener(t,n)}}}(e,t,n);if(r.nodeList(e))return function(e,t,n){return Array.prototype.forEach.call(e,(function(e){e.addEventListener(t,n)})),{destroy:function(){Array.prototype.forEach.call(e,(function(e){e.removeEventListener(t,n)}))}}}(e,t,n);if(r.string(e))return function(e,t,n){return o(document.body,e,t,n)}(e,t,n);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},817:function(e){e.exports=function(e){var t;if("SELECT"===e.nodeName)e.focus(),t=e.value;else if("INPUT"===e.nodeName||"TEXTAREA"===e.nodeName){var n=e.hasAttribute("readonly");n||e.setAttribute("readonly",""),e.select(),e.setSelectionRange(0,e.value.length),n||e.removeAttribute("readonly"),t=e.value}else{e.hasAttribute("contenteditable")&&e.focus();var r=window.getSelection(),o=document.createRange();o.selectNodeContents(e),r.removeAllRanges(),r.addRange(o),t=r.toString()}return t}},279:function(e){function t(){}t.prototype={on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var r=this;function o(){r.off(e,o),t.apply(n,arguments)}return o._=t,this.on(e,o,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,o=n.length;r<o;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],o=[];if(r&&t)for(var i=0,u=r.length;i<u;i++)r[i].fn!==t&&r[i].fn._!==t&&o.push(r[i]);return o.length?n[e]=o:delete n[e],this}},e.exports=t,e.exports.TinyEmitter=t}},t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}return n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n(686)}().default},e.exports=t()},5760:()=>{!function(e){if(e){var t={},n=e.prototype.stopCallback;e.prototype.stopCallback=function(e,r,o,i){return!!this.paused||!t[o]&&!t[i]&&n.call(this,e,r,o)},e.prototype.bindGlobal=function(e,n,r){if(this.bind(e,n,r),e instanceof Array)for(var o=0;o<e.length;o++)t[e[o]]=!0;else t[e]=!0},e.init()}}("undefined"!=typeof Mousetrap?Mousetrap:void 0)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";n.r(r),n.d(r,{__experimentalUseDialog:()=>G,__experimentalUseDragging:()=>Y,__experimentalUseDropZone:()=>_e,__experimentalUseFixedWindowList:()=>Pe,__experimentalUseFocusOutside:()=>$,compose:()=>m,createHigherOrderComponent:()=>a,debounce:()=>f,ifCondition:()=>g,observableMap:()=>p,pipe:()=>v,pure:()=>S,throttle:()=>d,useAsyncList:()=>Te,useConstrainedTabbing:()=>j,useCopyOnClick:()=>z,useCopyToClipboard:()=>U,useDebounce:()=>De,useDebouncedInput:()=>Oe,useDisabled:()=>Z,useEvent:()=>Q,useFocusOnMount:()=>q,useFocusReturn:()=>W,useFocusableIframe:()=>Ae,useInstanceId:()=>R,useIsomorphicLayoutEffect:()=>X,useKeyboardShortcut:()=>te,useMediaQuery:()=>re,useMergeRefs:()=>B,useObservableValue:()=>Ie,usePrevious:()=>oe,useReducedMotion:()=>ie,useRefEffect:()=>A,useResizeObserver:()=>xe,useStateWithHistory:()=>fe,useThrottle:()=>Me,useViewportMatch:()=>ye,useWarnOnChange:()=>Ce,withGlobalEvents:()=>C,withInstanceId:()=>D,withSafeTimeout:()=>O,withState:()=>M});var e=function(){return e=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},e.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function t(e){return e.toLowerCase()}var o=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],i=/[^A-Z0-9]+/gi;function u(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}function c(e,t){var n=e.charAt(0),r=e.substr(1).toLowerCase();return t>0&&n>="0"&&n<="9"?"_"+n+r:""+n.toUpperCase()+r}function s(n,r){return void 0===r&&(r={}),function(e,n){void 0===n&&(n={});for(var r=n.splitRegexp,c=void 0===r?o:r,s=n.stripRegexp,a=void 0===s?i:s,l=n.transform,f=void 0===l?t:l,d=n.delimiter,p=void 0===d?" ":d,h=u(u(e,c,"$1\0$2"),a,"\0"),v=0,m=h.length;"\0"===h.charAt(v);)v++;for(;"\0"===h.charAt(m-1);)m--;return h.slice(v,m).split("\0").map(f).join(p)}(n,e({delimiter:"",transform:c},r))}function a(e,t){return n=>{const r=e(n);return r.displayName=l(t,n),r}}const l=(e,t)=>{const n=t.displayName||t.name||"Component";return`${s(null!=e?e:"")}(${n})`},f=(e,t,n)=>{let r,o,i,u,c,s=0,a=0,l=!1,f=!1,d=!0;function p(t){const n=r,u=o;return r=void 0,o=void 0,a=t,i=e.apply(u,n),i}function h(e,t){u=setTimeout(e,t)}function v(e){return e-(c||0)}function m(e){const n=v(e);return void 0===c||n>=t||n<0||f&&e-a>=s}function y(){const e=Date.now();if(m(e))return b(e);h(y,function(e){const n=v(e),r=e-a,o=t-n;return f?Math.min(o,s-r):o}(e))}function g(){u=void 0}function b(e){return g(),d&&r?p(e):(r=o=void 0,i)}function w(){return void 0!==u}function E(...e){const n=Date.now(),u=m(n);if(r=e,o=this,c=n,u){if(!w())return function(e){return a=e,h(y,t),l?p(e):i}(c);if(f)return h(y,t),p(c)}return w()||h(y,t),i}return n&&(l=!!n.leading,f="maxWait"in n,void 0!==n.maxWait&&(s=Math.max(n.maxWait,t)),d="trailing"in n?!!n.trailing:d),E.cancel=function(){void 0!==u&&clearTimeout(u),a=0,g(),r=c=o=void 0},E.flush=function(){return w()?b(Date.now()):i},E.pending=w,E},d=(e,t,n)=>{let r=!0,o=!0;return n&&(r="leading"in n?!!n.leading:r,o="trailing"in n?!!n.trailing:o),f(e,t,{leading:r,trailing:o,maxWait:t})};function p(){const e=new Map,t=new Map;function n(e){const n=t.get(e);if(n)for(const e of n)e()}return{get:t=>e.get(t),set(t,r){e.set(t,r),n(t)},delete(t){e.delete(t),n(t)},subscribe(e,n){let r=t.get(e);return r||(r=new Set,t.set(e,r)),r.add(n),()=>{r.delete(n),0===r.size&&t.delete(e)}}}}const h=(e=!1)=>(...t)=>(...n)=>{const r=t.flat();return e&&r.reverse(),r.reduce(((e,t)=>[t(...e)]),n)[0]},v=h(),m=h(!0),y=window.ReactJSXRuntime;const g=function(e){return a((t=>n=>e(n)?(0,y.jsx)(t,{...n}):null),"ifCondition")},b=window.wp.isShallowEqual;var w=n.n(b);const E=window.wp.element,S=a((function(e){return e.prototype instanceof E.Component?class extends e{shouldComponentUpdate(e,t){return!w()(e,this.props)||!w()(t,this.state)}}:class extends E.Component{shouldComponentUpdate(e){return!w()(e,this.props)}render(){return(0,y.jsx)(e,{...this.props})}}}),"pure"),x=window.wp.deprecated;var k=n.n(x);const T=new class{constructor(){this.listeners={},this.handleEvent=this.handleEvent.bind(this)}add(e,t){this.listeners[e]||(window.addEventListener(e,this.handleEvent),this.listeners[e]=[]),this.listeners[e].push(t)}remove(e,t){this.listeners[e]&&(this.listeners[e]=this.listeners[e].filter((e=>e!==t)),this.listeners[e].length||(window.removeEventListener(e,this.handleEvent),delete this.listeners[e]))}handleEvent(e){this.listeners[e.type]?.forEach((t=>{t.handleEvent(e)}))}};function C(e){return k()("wp.compose.withGlobalEvents",{since:"5.7",alternative:"useEffect"}),a((t=>{class n extends E.Component{constructor(e){super(e),this.handleEvent=this.handleEvent.bind(this),this.handleRef=this.handleRef.bind(this)}componentDidMount(){Object.keys(e).forEach((e=>{T.add(e,this)}))}componentWillUnmount(){Object.keys(e).forEach((e=>{T.remove(e,this)}))}handleEvent(t){const n=e[t.type];"function"==typeof this.wrappedRef[n]&&this.wrappedRef[n](t)}handleRef(e){this.wrappedRef=e,this.props.forwardedRef&&this.props.forwardedRef(e)}render(){return(0,y.jsx)(t,{...this.props.ownProps,ref:this.handleRef})}}return(0,E.forwardRef)(((e,t)=>(0,y.jsx)(n,{ownProps:e,forwardedRef:t})))}),"withGlobalEvents")}const L=new WeakMap;const R=function(e,t,n){return(0,E.useMemo)((()=>{if(n)return n;const r=function(e){const t=L.get(e)||0;return L.set(e,t+1),t}(e);return t?`${t}-${r}`:r}),[e,n,t])},D=a((e=>t=>{const n=R(e);return(0,y.jsx)(e,{...t,instanceId:n})}),"instanceId"),O=a((e=>class extends E.Component{constructor(e){super(e),this.timeouts=[],this.setTimeout=this.setTimeout.bind(this),this.clearTimeout=this.clearTimeout.bind(this)}componentWillUnmount(){this.timeouts.forEach(clearTimeout)}setTimeout(e,t){const n=setTimeout((()=>{e(),this.clearTimeout(n)}),t);return this.timeouts.push(n),n}clearTimeout(e){clearTimeout(e),this.timeouts=this.timeouts.filter((t=>t!==e))}render(){return(0,y.jsx)(e,{...this.props,setTimeout:this.setTimeout,clearTimeout:this.clearTimeout})}}),"withSafeTimeout");function M(e={}){return k()("wp.compose.withState",{since:"5.8",alternative:"wp.element.useState"}),a((t=>class extends E.Component{constructor(t){super(t),this.setState=this.setState.bind(this),this.state=e}render(){return(0,y.jsx)(t,{...this.props,...this.state,setState:this.setState})}}),"withState")}const _=window.wp.dom;function A(e,t){const n=(0,E.useRef)();return(0,E.useCallback)((t=>{t?n.current=e(t):n.current&&n.current()}),t)}const j=function(){return A((e=>{function t(t){const{key:n,shiftKey:r,target:o}=t;if("Tab"!==n)return;const i=r?"findPrevious":"findNext",u=_.focus.tabbable[i](o)||null;if(o.contains(u))return t.preventDefault(),void u?.focus();if(e.contains(u))return;const c=r?"append":"prepend",{ownerDocument:s}=e,a=s.createElement("div");a.tabIndex=-1,e[c](a),a.addEventListener("blur",(()=>e.removeChild(a))),a.focus()}return e.addEventListener("keydown",t),()=>{e.removeEventListener("keydown",t)}}),[])};var P=n(3758),I=n.n(P);function z(e,t,n=4e3){k()("wp.compose.useCopyOnClick",{since:"5.8",alternative:"wp.compose.useCopyToClipboard"});const r=(0,E.useRef)(),[o,i]=(0,E.useState)(!1);return(0,E.useEffect)((()=>{let o;if(e.current)return r.current=new(I())(e.current,{text:()=>"function"==typeof t?t():t}),r.current.on("success",(({clearSelection:e,trigger:t})=>{e(),t&&t.focus(),n&&(i(!0),clearTimeout(o),o=setTimeout((()=>i(!1)),n))})),()=>{r.current&&r.current.destroy(),clearTimeout(o)}}),[t,n,i]),o}function N(e){const t=(0,E.useRef)(e);return(0,E.useLayoutEffect)((()=>{t.current=e}),[e]),t}function U(e,t){const n=N(e),r=N(t);return A((e=>{const t=new(I())(e,{text:()=>"function"==typeof n.current?n.current():n.current||""});return t.on("success",(({clearSelection:e})=>{e(),r.current&&r.current()})),()=>{t.destroy()}}),[])}const V=window.wp.keycodes;function q(e="firstElement"){const t=(0,E.useRef)(e),n=e=>{e.focus({preventScroll:!0})},r=(0,E.useRef)();return(0,E.useEffect)((()=>{t.current=e}),[e]),A((e=>{var o;if(e&&!1!==t.current&&!e.contains(null!==(o=e.ownerDocument?.activeElement)&&void 0!==o?o:null)){if("firstElement"===t.current)return r.current=setTimeout((()=>{const t=_.focus.tabbable.find(e)[0];t&&n(t)}),0),()=>{r.current&&clearTimeout(r.current)};n(e)}}),[])}let K=null;const W=function(e){const t=(0,E.useRef)(null),n=(0,E.useRef)(null),r=(0,E.useRef)(e);return(0,E.useEffect)((()=>{r.current=e}),[e]),(0,E.useCallback)((e=>{if(e){var o;if(t.current=e,n.current)return;const r=e.ownerDocument.activeElement instanceof window.HTMLIFrameElement?e.ownerDocument.activeElement.contentDocument:e.ownerDocument;n.current=null!==(o=r?.activeElement)&&void 0!==o?o:null}else if(n.current){const e=t.current?.contains(t.current?.ownerDocument.activeElement);var i;if(t.current?.isConnected&&!e)return void(null!==(i=K)&&void 0!==i||(K=n.current));r.current?r.current():(n.current.isConnected?n.current:K)?.focus(),K=null}}),[])},H=["button","submit"];function $(e){const t=(0,E.useRef)(e);(0,E.useEffect)((()=>{t.current=e}),[e]);const n=(0,E.useRef)(!1),r=(0,E.useRef)(),o=(0,E.useCallback)((()=>{clearTimeout(r.current)}),[]);(0,E.useEffect)((()=>()=>o()),[]),(0,E.useEffect)((()=>{e||o()}),[e,o]);const i=(0,E.useCallback)((e=>{const{type:t,target:r}=e;["mouseup","touchend"].includes(t)?n.current=!1:function(e){if(!(e instanceof window.HTMLElement))return!1;switch(e.nodeName){case"A":case"BUTTON":return!0;case"INPUT":return H.includes(e.type)}return!1}(r)&&(n.current=!0)}),[]),u=(0,E.useCallback)((e=>{if(e.persist(),n.current)return;const o=e.target.getAttribute("data-unstable-ignore-focus-outside-for-relatedtarget");o&&e.relatedTarget?.closest(o)||(r.current=setTimeout((()=>{document.hasFocus()?"function"==typeof t.current&&t.current(e):e.preventDefault()}),0))}),[]);return{onFocus:o,onMouseDown:i,onMouseUp:i,onTouchStart:i,onTouchEnd:i,onBlur:u}}function F(e,t){"function"==typeof e?e(t):e&&e.hasOwnProperty("current")&&(e.current=t)}function B(e){const t=(0,E.useRef)(),n=(0,E.useRef)(!1),r=(0,E.useRef)(!1),o=(0,E.useRef)([]),i=(0,E.useRef)(e);return i.current=e,(0,E.useLayoutEffect)((()=>{!1===r.current&&!0===n.current&&e.forEach(((e,n)=>{const r=o.current[n];e!==r&&(F(r,null),F(e,t.current))})),o.current=e}),e),(0,E.useLayoutEffect)((()=>{r.current=!1})),(0,E.useCallback)((e=>{F(t,e),r.current=!0,n.current=null!==e;const u=e?i.current:o.current;for(const t of u)F(t,e)}),[])}const G=function(e){const t=(0,E.useRef)(),{constrainTabbing:n=!1!==e.focusOnMount}=e;(0,E.useEffect)((()=>{t.current=e}),Object.values(e));const r=j(),o=q(e.focusOnMount),i=W(),u=$((e=>{t.current?.__unstableOnClose?t.current.__unstableOnClose("focus-outside",e):t.current?.onClose&&t.current.onClose()})),c=(0,E.useCallback)((e=>{e&&e.addEventListener("keydown",(e=>{e.keyCode===V.ESCAPE&&!e.defaultPrevented&&t.current?.onClose&&(e.preventDefault(),t.current.onClose())}))}),[]);return[B([n?r:null,!1!==e.focusOnMount?i:null,!1!==e.focusOnMount?o:null,c]),{...u,tabIndex:-1}]};function Z({isDisabled:e=!1}={}){return A((t=>{if(e)return;const n=t?.ownerDocument?.defaultView;if(!n)return;const r=[],o=()=>{t.childNodes.forEach((e=>{e instanceof n.HTMLElement&&(e.getAttribute("inert")||(e.setAttribute("inert","true"),r.push((()=>{e.removeAttribute("inert")}))))}))},i=f(o,0,{leading:!0});o();const u=new window.MutationObserver(i);return u.observe(t,{childList:!0}),()=>{u&&u.disconnect(),i.cancel(),r.forEach((e=>e()))}}),[e])}function Q(e){const t=(0,E.useRef)((()=>{throw new Error("Callbacks created with `useEvent` cannot be called during rendering.")}));return(0,E.useInsertionEffect)((()=>{t.current=e})),(0,E.useCallback)(((...e)=>t.current?.(...e)),[])}const X="undefined"!=typeof window?E.useLayoutEffect:E.useEffect;function Y({onDragStart:e,onDragMove:t,onDragEnd:n}){const[r,o]=(0,E.useState)(!1),i=(0,E.useRef)({onDragStart:e,onDragMove:t,onDragEnd:n});X((()=>{i.current.onDragStart=e,i.current.onDragMove=t,i.current.onDragEnd=n}),[e,t,n]);const u=(0,E.useCallback)((e=>i.current.onDragMove&&i.current.onDragMove(e)),[]),c=(0,E.useCallback)((e=>{i.current.onDragEnd&&i.current.onDragEnd(e),document.removeEventListener("mousemove",u),document.removeEventListener("mouseup",c),o(!1)}),[]),s=(0,E.useCallback)((e=>{i.current.onDragStart&&i.current.onDragStart(e),document.addEventListener("mousemove",u),document.addEventListener("mouseup",c),o(!0)}),[]);return(0,E.useEffect)((()=>()=>{r&&(document.removeEventListener("mousemove",u),document.removeEventListener("mouseup",c))}),[r]),{startDrag:s,endDrag:c,isDragging:r}}var J=n(1933),ee=n.n(J);n(5760);const te=function(e,t,{bindGlobal:n=!1,eventName:r="keydown",isDisabled:o=!1,target:i}={}){const u=(0,E.useRef)(t);(0,E.useEffect)((()=>{u.current=t}),[t]),(0,E.useEffect)((()=>{if(o)return;const t=new(ee())(i&&i.current?i.current:document);return(Array.isArray(e)?e:[e]).forEach((e=>{const o=e.split("+"),i=new Set(o.filter((e=>e.length>1))),c=i.has("alt"),s=i.has("shift");if((0,V.isAppleOS)()&&(1===i.size&&c||2===i.size&&c&&s))throw new Error(`Cannot bind ${e}. Alt and Shift+Alt modifiers are reserved for character input.`);t[n?"bindGlobal":"bind"](e,((...e)=>u.current(...e)),r)})),()=>{t.reset()}}),[e,n,r,i,o])},ne=new Map;function re(e){const t=(0,E.useMemo)((()=>{const t=function(e){if(!e)return null;let t=ne.get(e);return t||("undefined"!=typeof window&&"function"==typeof window.matchMedia?(t=window.matchMedia(e),ne.set(e,t),t):null)}(e);return{subscribe:e=>t?(t.addEventListener?.("change",e),()=>{t.removeEventListener?.("change",e)}):()=>{},getValue(){var e;return null!==(e=t?.matches)&&void 0!==e&&e}}}),[e]);return(0,E.useSyncExternalStore)(t.subscribe,t.getValue,(()=>!1))}function oe(e){const t=(0,E.useRef)();return(0,E.useEffect)((()=>{t.current=e}),[e]),t.current}const ie=()=>re("(prefers-reduced-motion: reduce)");function ue(e,t){const n={...e};return Object.entries(t).forEach((([e,t])=>{n[e]?n[e]={...n[e],to:t.to}:n[e]=t})),n}const ce=(e,t)=>{const n=e?.findIndex((({id:e})=>"string"==typeof e?e===t.id:w()(e,t.id))),r=[...e];return-1!==n?r[n]={id:t.id,changes:ue(r[n].changes,t.changes)}:r.push(t),r};function se(){let e=[],t=[],n=0;const r=()=>{e=e.slice(0,n||void 0),n=0},o=()=>{var n;const r=0===e.length?0:e.length-1;let o=null!==(n=e[r])&&void 0!==n?n:[];t.forEach((e=>{o=ce(o,e)})),t=[],e[r]=o};return{addRecord(n,i=!1){const u=!n||(e=>!e.filter((({changes:e})=>Object.values(e).some((({from:e,to:t})=>"function"!=typeof e&&"function"!=typeof t&&!w()(e,t))))).length)(n);if(i){if(u)return;n.forEach((e=>{t=ce(t,e)}))}else{if(r(),t.length&&o(),u)return;e.push(n)}},undo(){t.length&&(r(),o());const i=e[e.length-1+n];if(i)return n-=1,i},redo(){const t=e[e.length+n];if(t)return n+=1,t},hasUndo:()=>!!e[e.length-1+n],hasRedo:()=>!!e[e.length+n]}}function ae(e,t){switch(t.type){case"UNDO":{const t=e.manager.undo();return t?{...e,value:t[0].changes.prop.from}:e}case"REDO":{const t=e.manager.redo();return t?{...e,value:t[0].changes.prop.to}:e}case"RECORD":return e.manager.addRecord([{id:"object",changes:{prop:{from:e.value,to:t.value}}}],t.isStaged),{...e,value:t.value}}return e}function le(e){return{manager:se(),value:e}}function fe(e){const[t,n]=(0,E.useReducer)(ae,e,le);return{value:t.value,setValue:(0,E.useCallback)(((e,t)=>{n({type:"RECORD",value:e,isStaged:t})}),[]),hasUndo:t.manager.hasUndo(),hasRedo:t.manager.hasRedo(),undo:(0,E.useCallback)((()=>{n({type:"UNDO"})}),[]),redo:(0,E.useCallback)((()=>{n({type:"REDO"})}),[])}}const de={xhuge:1920,huge:1440,wide:1280,xlarge:1080,large:960,medium:782,small:600,mobile:480},pe={">=":"min-width","<":"max-width"},he={">=":(e,t)=>t>=e,"<":(e,t)=>t<e},ve=(0,E.createContext)(null),me=(e,t=">=")=>{const n=(0,E.useContext)(ve),r=re(!n&&`(${pe[t]}: ${de[e]}px)`||void 0);return n?he[t](de[e],n):r};me.__experimentalWidthProvider=ve.Provider;const ye=me;function ge(e,t={}){const n=Q(e),r=(0,E.useRef)(),o=(0,E.useRef)();return Q((e=>{var i;if(e===r.current)return;null!==(i=o.current)&&void 0!==i||(o.current=new ResizeObserver(n));const{current:u}=o;r.current&&u.unobserve(r.current),r.current=e,e&&u.observe(e,t)}))}const be=e=>{let t;if(e.contentBoxSize)if(e.contentBoxSize[0]){const n=e.contentBoxSize[0];t=[n.inlineSize,n.blockSize]}else{const n=e.contentBoxSize;t=[n.inlineSize,n.blockSize]}else t=[e.contentRect.width,e.contentRect.height];const[n,r]=t.map((e=>Math.round(e)));return{width:n,height:r}},we={position:"absolute",top:0,left:0,right:0,bottom:0,pointerEvents:"none",opacity:0,overflow:"hidden",zIndex:-1};function Ee({onResize:e}){const t=ge((t=>{const n=be(t.at(-1));e(n)}));return(0,y.jsx)("div",{ref:t,style:we,"aria-hidden":"true"})}const Se={width:null,height:null};function xe(e,t={}){return e?ge(e,t):function(){const[e,t]=(0,E.useState)(Se),n=(0,E.useRef)(Se),r=(0,E.useCallback)((e=>{var r,o;o=e,((r=n.current).width!==o.width||r.height!==o.height)&&(n.current=e,t(e))}),[]);return[(0,y.jsx)(Ee,{onResize:r}),e]}()}const ke=window.wp.priorityQueue;const Te=function(e,t={step:1}){const{step:n=1}=t,[r,o]=(0,E.useState)([]);return(0,E.useEffect)((()=>{let t=function(e,t){const n=[];for(let r=0;r<e.length;r++){const o=e[r];if(!t.includes(o))break;n.push(o)}return n}(e,r);t.length<n&&(t=t.concat(e.slice(t.length,n))),o(t);const i=(0,ke.createQueue)();for(let r=t.length;r<e.length;r+=n)i.add({},(()=>{(0,E.flushSync)((()=>{o((t=>[...t,...e.slice(r,r+n)]))}))}));return()=>i.reset()}),[e]),r};const Ce=function(e,t="Change detection"){const n=oe(e);Object.entries(null!=n?n:[]).forEach((([n,r])=>{r!==e[n]&&console.warn(`${t}: ${n} key changed:`,r,e[n])}))},Le=window.React;function Re(e,t){var n=(0,Le.useState)((function(){return{inputs:t,result:e()}}))[0],r=(0,Le.useRef)(!0),o=(0,Le.useRef)(n),i=r.current||Boolean(t&&o.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,o.current.inputs))?o.current:{inputs:t,result:e()};return(0,Le.useEffect)((function(){r.current=!1,o.current=i}),[i]),i.result}function De(e,t,n){const r=Re((()=>f(e,null!=t?t:0,n)),[e,t,n]);return(0,E.useEffect)((()=>()=>r.cancel()),[r]),r}function Oe(e=""){const[t,n]=(0,E.useState)(e),[r,o]=(0,E.useState)(e),i=De(o,250);return(0,E.useEffect)((()=>{i(t)}),[t,i]),[t,n,r]}function Me(e,t,n){const r=Re((()=>d(e,null!=t?t:0,n)),[e,t,n]);return(0,E.useEffect)((()=>()=>r.cancel()),[r]),r}function _e({dropZoneElement:e,isDisabled:t,onDrop:n,onDragStart:r,onDragEnter:o,onDragLeave:i,onDragEnd:u,onDragOver:c}){const s=Q(n),a=Q(r),l=Q(o),f=Q(i),d=Q(u),p=Q(c);return A((h=>{if(t)return;const v=null!=e?e:h;let m=!1;const{ownerDocument:y}=v;function g(e){m||(m=!0,y.addEventListener("dragend",x),y.addEventListener("mousemove",x),r&&a(e))}function b(e){e.preventDefault(),v.contains(e.relatedTarget)||o&&l(e)}function w(e){!e.defaultPrevented&&c&&p(e),e.preventDefault()}function E(e){(function(e){const{defaultView:t}=y;if(!(e&&t&&e instanceof t.HTMLElement&&v.contains(e)))return!1;let n=e;do{if(n.dataset.isDropZone)return n===v}while(n=n.parentElement);return!1})(e.relatedTarget)||i&&f(e)}function S(e){e.defaultPrevented||(e.preventDefault(),e.dataTransfer&&e.dataTransfer.files.length,n&&s(e),x(e))}function x(e){m&&(m=!1,y.removeEventListener("dragend",x),y.removeEventListener("mousemove",x),u&&d(e))}return v.setAttribute("data-is-drop-zone","true"),v.addEventListener("drop",S),v.addEventListener("dragenter",b),v.addEventListener("dragover",w),v.addEventListener("dragleave",E),y.addEventListener("dragenter",g),()=>{v.removeAttribute("data-is-drop-zone"),v.removeEventListener("drop",S),v.removeEventListener("dragenter",b),v.removeEventListener("dragover",w),v.removeEventListener("dragleave",E),y.removeEventListener("dragend",x),y.removeEventListener("mousemove",x),y.removeEventListener("dragenter",g)}}),[t,e])}function Ae(){return A((e=>{const{ownerDocument:t}=e;if(!t)return;const{defaultView:n}=t;if(n)return n.addEventListener("blur",r),()=>{n.removeEventListener("blur",r)};function r(){t&&t.activeElement===e&&e.focus()}}),[])}const je=30;function Pe(e,t,n,r){var o,i;const u=null!==(o=r?.initWindowSize)&&void 0!==o?o:je,c=null===(i=r?.useWindowing)||void 0===i||i,[s,a]=(0,E.useState)({visibleItems:u,start:0,end:u,itemInView:e=>e>=0&&e<=u});return(0,E.useLayoutEffect)((()=>{if(!c)return;const o=(0,_.getScrollContainer)(e.current),i=e=>{var i;if(!o)return;const u=Math.ceil(o.clientHeight/t),c=e?u:null!==(i=r?.windowOverscan)&&void 0!==i?i:u,s=Math.floor(o.scrollTop/t),l=Math.max(0,s-c),f=Math.min(n-1,s+u+c);a((e=>{const t={visibleItems:u,start:l,end:f,itemInView:e=>l<=e&&e<=f};return e.start!==t.start||e.end!==t.end||e.visibleItems!==t.visibleItems?t:e}))};i(!0);const u=f((()=>{i()}),16);return o?.addEventListener("scroll",u),o?.ownerDocument?.defaultView?.addEventListener("resize",u),o?.ownerDocument?.defaultView?.addEventListener("resize",u),()=>{o?.removeEventListener("scroll",u),o?.ownerDocument?.defaultView?.removeEventListener("resize",u)}}),[t,e,n,r?.expandedState,r?.windowOverscan,c]),(0,E.useLayoutEffect)((()=>{if(!c)return;const r=(0,_.getScrollContainer)(e.current),o=e=>{switch(e.keyCode){case V.HOME:return r?.scrollTo({top:0});case V.END:return r?.scrollTo({top:n*t});case V.PAGEUP:return r?.scrollTo({top:r.scrollTop-s.visibleItems*t});case V.PAGEDOWN:return r?.scrollTo({top:r.scrollTop+s.visibleItems*t})}};return r?.ownerDocument?.defaultView?.addEventListener("keydown",o),()=>{r?.ownerDocument?.defaultView?.removeEventListener("keydown",o)}}),[n,t,e,s.visibleItems,c,r?.expandedState]),[s,a]}function Ie(e,t){const[n,r]=(0,E.useMemo)((()=>[n=>e.subscribe(t,n),()=>e.get(t)]),[e,t]);return(0,E.useSyncExternalStore)(n,r,r)}})(),(window.wp=window.wp||{}).compose=r})();
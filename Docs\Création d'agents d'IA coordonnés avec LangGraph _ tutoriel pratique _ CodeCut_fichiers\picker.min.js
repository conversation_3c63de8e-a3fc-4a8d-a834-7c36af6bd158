!function(e){"function"==typeof define&&define.amd?define("picker",["jquery"],e):"object"==typeof exports?module.exports=e(require("jquery")):this.Picker=e(jQuery)}(function(h){var o=h(window),m=h(document),r=h(document.documentElement),g=null!=document.documentElement.style.transition;function v(r,t,n,e){var o,d,s,i,c,u;return r?(o=!1,d={id:r.id||"P"+Math.abs(~~(Math.random()*new Date))},s=n?h.extend(!0,{},n.defaults,e):e||{},i=h.extend({},v.klasses(),s.klass),c=h(r),u=(e=function(){return this.start()}).prototype={constructor:e,$node:c,start:function(){var e;return d&&d.start?u:(d.methods={},d.start=!0,d.open=!1,d.type=r.type,r.autofocus=r==b(),r.readOnly=!s.editable,r.id=r.id||d.id,"text"!=r.type&&(r.type="text"),u.component=new n(u,s),u.$root=h('<div class="'+i.picker+'" id="'+r.id+'_root" />'),$(u.$root[0],"hidden",!0),u.$holder=h(a()).appendTo(u.$root),l(),s.formatSubmit&&(!0===s.hiddenName?(e=r.name,r.name=""):e=(e=["string"==typeof s.hiddenPrefix?s.hiddenPrefix:"","string"==typeof s.hiddenSuffix?s.hiddenSuffix:"_submit"])[0]+r.name+e[1],u._hidden=h('<input type=hidden name="'+e+'"'+(c.data("value")||r.value?' value="'+u.get("select",s.formatSubmit)+'"':"")+">")[0],c.on("change."+d.id,function(){u._hidden.value=r.value?u.get("select",s.formatSubmit):""})),c.data(t,u).addClass(i.input).val(c.data("value")?u.get("select",s.format):r.value).on("focus."+d.id+" click."+d.id,function(o,r,i){var a;return function(){var e=this,t=arguments,n=i&&!a;clearTimeout(a),a=setTimeout(function(){a=null,i||o.apply(e,t)},r),n&&o.apply(e,t)}}(function(e){e.preventDefault(),u.open()},100)),s.editable||c.on("keydown."+d.id,p),$(r,{haspopup:!0,expanded:!1,readonly:!1,owns:r.id+"_root"}),s.containerHidden?h(s.containerHidden).append(u._hidden):c.after(u._hidden),s.container?h(s.container).append(u.$root):c.after(u.$root),u.on({start:u.component.onStart,render:u.component.onRender,stop:u.component.onStop,open:u.component.onOpen,close:u.component.onClose,set:u.component.onSet}).on({start:s.onStart,render:s.onRender,stop:s.onStop,open:s.onOpen,close:s.onClose,set:s.onSet}),o=function(e){var t,n="position";e.currentStyle?t=e.currentStyle[n]:window.getComputedStyle&&(t=getComputedStyle(e)[n]);return"fixed"==t}(u.$holder[0]),r.autofocus&&u.open(),u.trigger("start").trigger("render"))},render:function(e){return e?(u.$holder=h(a()),l(),u.$root.html(u.$holder)):u.$root.find("."+i.box).html(u.component.nodes(d.open)),u.trigger("render")},stop:function(){return d.start&&(u.close(),u._hidden&&u._hidden.parentNode.removeChild(u._hidden),u.$root.remove(),c.removeClass(i.input).removeData(t),setTimeout(function(){c.off("."+d.id)},0),r.type=d.type,r.readOnly=!1,u.trigger("stop"),d.methods={},d.start=!1),u},open:function(e){return d.open?u:(c.addClass(i.active),$(r,"expanded",!0),setTimeout(function(){u.$root.addClass(i.opened),$(u.$root[0],"hidden",!1)},0),!1!==e&&(d.open=!0,o&&h("body").css("overflow","hidden").css("padding-right","+="+y()),o&&g?u.$holder.find("."+i.frame).one("transitionend",function(){u.$holder.eq(0).focus()}):setTimeout(function(){u.$holder.eq(0).focus()},0),m.on("click."+d.id+" focusin."+d.id,function(e){var t=_(e,r);e.isSimulated||t==r||t==document||3==e.which||u.close(t===u.$holder[0])}).on("keydown."+d.id,function(e){var t=e.keyCode,n=u.component.key[t],o=_(e,r);27==t?u.close(!0):o!=u.$holder[0]||!n&&13!=t?h.contains(u.$root[0],o)&&13==t&&(e.preventDefault(),o.click()):(e.preventDefault(),n?v._.trigger(u.component.key.go,u,[v._.trigger(n)]):u.$root.find("."+i.highlighted).hasClass(i.disabled)||(u.set("select",u.component.item.highlight),s.closeOnSelect&&u.close(!0)))})),u.trigger("open"))},close:function(e){return e&&(s.editable?r.focus():(u.$holder.off("focus.toOpen").focus(),setTimeout(function(){u.$holder.on("focus.toOpen",f)},0))),c.removeClass(i.active),$(r,"expanded",!1),setTimeout(function(){u.$root.removeClass(i.opened+" "+i.focused),$(u.$root[0],"hidden",!0)},0),d.open?(d.open=!1,o&&h("body").css("overflow","").css("padding-right","-="+y()),m.off("."+d.id),u.trigger("close")):u},clear:function(e){return u.set("clear",null,e)},set:function(e,t,n){var o,r,i=h.isPlainObject(e),a=i?e:{};if(n=i&&h.isPlainObject(t)?t:n||{},e){for(o in i||(a[e]=t),a)r=a[o],o in u.component.item&&(void 0===r&&(r=null),u.component.set(o,r,n)),"select"!=o&&"clear"!=o||!s.updateInput||c.val("clear"==o?"":u.get(o,s.format)).trigger("change");u.render()}return n.muted?u:u.trigger("set",a)},get:function(e,t){if(null!=d[e=e||"value"])return d[e];if("valueSubmit"==e){if(u._hidden)return u._hidden.value;e="value"}var n;return"value"==e?r.value:e in u.component.item?"string"==typeof t?(n=u.component.get(e))?v._.trigger(u.component.formats.toString,u.component,[t,n]):"":u.component.get(e):void 0},on:function(e,t,n){var o,r,i=h.isPlainObject(e),a=i?e:{};if(e)for(o in i||(a[e]=t),a)r=a[o],d.methods[o=n?"_"+o:o]=d.methods[o]||[],d.methods[o].push(r);return u},off:function(){var e,t=arguments,n=0;for(namesCount=t.length;n<namesCount;n+=1)(e=t[n])in d.methods&&delete d.methods[e];return u},trigger:function(e,t){function n(e){(e=d.methods[e])&&e.map(function(e){v._.trigger(e,u,[t])})}return n("_"+e),n(e),u}},new e):v;function a(){return v._.node("div",v._.node("div",v._.node("div",v._.node("div",u.component.nodes(d.open),i.box),i.wrap),i.frame),i.holder,'tabindex="-1"')}function l(){u.$holder.on({keydown:p,"focus.toOpen":f,blur:function(){c.removeClass(i.target)},focusin:function(e){u.$root.removeClass(i.focused),e.stopPropagation()},"mousedown click":function(e){var t=_(e,r);t!=u.$holder[0]&&(e.stopPropagation(),"mousedown"!=e.type||h(t).is("input, select, textarea, button, option")||(e.preventDefault(),u.$holder.eq(0).focus()))}}).on("click","[data-pick], [data-nav], [data-clear], [data-close]",function(){var e=h(this),t=e.data(),e=e.hasClass(i.navDisabled)||e.hasClass(i.disabled),n=(n=b())&&(n.type||n.href?n:null);(e||n&&!h.contains(u.$root[0],n))&&u.$holder.eq(0).focus(),!e&&t.nav?u.set("highlight",u.component.item.highlight,{nav:t.nav}):!e&&"pick"in t?(u.set("select",t.pick),s.closeOnSelect&&u.close(!0)):t.clear?(u.clear(),s.closeOnClear&&u.close(!0)):t.close&&u.close(!0)})}function f(e){e.stopPropagation(),c.addClass(i.target),u.$root.addClass(i.focused),u.open()}function p(e){var t=e.keyCode,n=/^(8|46)$/.test(t);if(27==t)return u.close(!0),!1;(32==t||n||!d.open&&u.component.key[t])&&(e.preventDefault(),e.stopPropagation(),n?u.clear().close():u.open())}}function y(){if(r.height()<=o.height())return 0;var e=h('<div style="visibility:hidden;width:100px" />').appendTo("body"),t=e[0].offsetWidth;e.css("overflow","scroll");var n=h('<div style="width:100%" />').appendTo(e)[0].offsetWidth;return e.remove(),t-n}function _(e,t){var n=[];return e.path&&(n=e.path),(n=e.originalEvent&&e.originalEvent.path?e.originalEvent.path:n)&&0<n.length?t&&0<=n.indexOf(t)?t:n[0]:e.target}function $(e,t,n){if(h.isPlainObject(t))for(var o in t)i(e,o,t[o]);else i(e,t,n)}function i(e,t,n){e.setAttribute(("role"==t?"":"aria-")+t,n)}function b(){try{return document.activeElement}catch(e){}}return v.klasses=function(e){return{picker:e=e||"picker",opened:e+"--opened",focused:e+"--focused",input:e+"__input",active:e+"__input--active",target:e+"__input--target",holder:e+"__holder",frame:e+"__frame",wrap:e+"__wrap",box:e+"__box"}},v._={group:function(e){for(var t,n="",o=v._.trigger(e.min,e);o<=v._.trigger(e.max,e,[o]);o+=e.i)t=v._.trigger(e.item,e,[o]),n+=v._.node(e.node,t[0],t[1],t[2]);return n},node:function(e,t,n,o){return t?"<"+e+(n=n?' class="'+n+'"':"")+(o=o?" "+o:"")+">"+(t=h.isArray(t)?t.join(""):t)+"</"+e+">":""},lead:function(e){return(e<10?"0":"")+e},trigger:function(e,t,n){return"function"==typeof e?e.apply(t,n||[]):e},digits:function(e){return/\d/.test(e[1])?2:1},isDate:function(e){return-1<{}.toString.call(e).indexOf("Date")&&this.isInteger(e.getDate())},isInteger:function(e){return-1<{}.toString.call(e).indexOf("Number")&&e%1==0},ariaAttr:function(e,t){h.isPlainObject(e)||(e={attribute:t});for(var n in t="",e){var o=("role"==n?"":"aria-")+n,r=e[n];t+=null==r?"":o+'="'+e[n]+'"'}return t}},v.extend=function(o,r){h.fn[o]=function(e,t){var n=this.data(o);return"picker"==e?n:n&&"string"==typeof e?v._.trigger(n[e],n,[t]):this.each(function(){h(this).data(o)||new v(this,o,r,e)})},h.fn[o].defaults=r.defaults},v});
"""
Tests pour la configuration SEO.
"""

import unittest
from seo_ai_agents import SEOConfig


class TestSEOConfig(unittest.TestCase):
    """Tests unitaires pour la classe SEOConfig."""

    def test_config_default_values(self):
        """Test des valeurs par défaut de la configuration."""
        self.assertEqual(SEOConfig.OLLAMA_BASE_URL, "https://ollama.com")
        self.assertEqual(SEOConfig.OLLAMA_MODEL, "gpt-oss:20b")
        self.assertEqual(SEOConfig.OLLAMA_TEMPERATURE, 0.1)
        self.assertEqual(SEOConfig.OUTPUT_DIR, "audits")
        self.assertEqual(SEOConfig.OUTPUT_FORMAT, "md")

    def test_config_types(self):
        """Test des types des attributs de configuration."""
        self.assertIsInstance(SEOConfig.OLLAMA_BASE_URL, str)
        self.assertIsInstance(SEOConfig.OLLAMA_MODEL, str)
        self.assertIsInstance(SEOConfig.OLLAMA_TEMPERATURE, float)
        self.assertIsInstance(SEOConfig.OUTPUT_DIR, str)
        self.assertIsInstance(SEOConfig.OUTPUT_FORMAT, str)

    def test_config_temperature_range(self):
        """Test que la température est dans une plage valide."""
        self.assertGreaterEqual(SEOConfig.OLLAMA_TEMPERATURE, 0.0)
        self.assertLessEqual(SEOConfig.OLLAMA_TEMPERATURE, 1.0)


if __name__ == "__main__":
    unittest.main()

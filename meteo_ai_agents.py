import urllib.parse
import datetime
from os.path import join, dirname

# https://requests.readthedocs.io/en/latest/
import requests

# https://github.com/langchain-ai/langchain
from langchain.agents import tool

# https://github.com/langchain-ai/langchain/tree/master/libs/partners/ollama
from langchain_ollama import ChatOllama

# https://github.com/langchain-ai/langgraph
from langgraph.prebuilt import create_react_agent

# https://github.com/langchain-ai/langgraph-supervisor-py
from langgraph_supervisor import create_supervisor

# https://github.com/theskumar/python-dotenv
from dotenv import load_dotenv


# Dictionnaire des codes météo World Meteorological Organization (WMO)
WMO_WEATHER_CODES = {
    0: {"fr": "Ciel dégagé", "en": "Clear sky", "icon": "☀️"},
    1: {"fr": "Principalement clair", "en": "Mainly clear", "icon": "🌤️"},
    2: {"fr": "Partiellement nuageux", "en": "Partly cloudy", "icon": "⛅"},
    3: {"fr": "Couvert", "en": "Overcast", "icon": "☁️"},
    45: {"fr": "Brouillard", "en": "Fog", "icon": "🌫️"},
    48: {"fr": "Brouillard givrant", "en": "Depositing rime fog", "icon": "🌫️❄️"},
    51: {"fr": "Bruine faible", "en": "Light drizzle", "icon": "🌦️"},
    53: {"fr": "Bruine modérée", "en": "Moderate drizzle", "icon": "🌧️"},
    55: {"fr": "Bruine forte", "en": "Dense drizzle", "icon": "🌧️"},
    56: {
        "fr": "Bruine verglaçante faible",
        "en": "Light freezing drizzle",
        "icon": "🌧️❄️",
    },
    57: {
        "fr": "Bruine verglaçante modérée ou forte",
        "en": "Dense freezing drizzle",
        "icon": "🌧️❄️",
    },
    61: {"fr": "Pluie faible", "en": "Slight rain", "icon": "🌦️"},
    63: {"fr": "Pluie modérée", "en": "Moderate rain", "icon": "🌧️"},
    65: {"fr": "Pluie forte", "en": "Heavy rain", "icon": "🌧️💧"},
    66: {"fr": "Pluie verglaçante faible", "en": "Light freezing rain", "icon": "🌧️❄️"},
    67: {
        "fr": "Pluie verglaçante modérée ou forte",
        "en": "Heavy freezing rain",
        "icon": "🌧️❄️",
    },
    71: {"fr": "Chute de neige faible", "en": "Slight snow fall", "icon": "🌨️"},
    73: {"fr": "Chute de neige modérée", "en": "Moderate snow fall", "icon": "❄️🌨️"},
    75: {"fr": "Chute de neige forte", "en": "Heavy snow fall", "icon": "❄️❄️"},
    77: {"fr": "Grains de neige", "en": "Snow grains", "icon": "🌨️"},
    80: {"fr": "Averses de pluie faibles", "en": "Slight rain showers", "icon": "🌦️"},
    81: {"fr": "Averses de pluie modérées", "en": "Moderate rain showers", "icon": "🌧️"},
    82: {
        "fr": "Averses de pluie violentes",
        "en": "Violent rain showers",
        "icon": "🌧️💦",
    },
    85: {"fr": "Averses de neige faibles", "en": "Slight snow showers", "icon": "🌨️"},
    86: {
        "fr": "Averses de neige modérées ou fortes",
        "en": "Heavy snow showers",
        "icon": "❄️🌨️",
    },
    95: {"fr": "Orage faible ou modéré", "en": "Thunderstorm", "icon": "⛈️"},
    96: {
        "fr": "Orage avec grêle légère",
        "en": "Thunderstorm with slight hail",
        "icon": "⛈️🌨️",
    },
    99: {
        "fr": "Orage avec grêle forte",
        "en": "Thunderstorm with heavy hail",
        "icon": "🌩️❄️",
    },
}


def get_city_info(search: str, country_code: str) -> list[dict]:
    """
    Fetches geographical information for a given city using the Open-Meteo Geocoding API.
    :param search: The search string can be a location name or a postal code.
    :param country_code: ISO-3166-1 alpha2 country code (https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2), which the results will be filtered for.

    Returns a list of dictionaries, each containing detailed information for a matching city.
    """
    # https://open-meteo.com/en/docs/geocoding-api
    url = f"https://geocoding-api.open-meteo.com/v1/search?language=fr&format=json&name={urllib.parse.quote(search)}&countryCode={urllib.parse.quote(country_code)}"
    response = requests.get(url=url, timeout=10)

    formatted_results = []
    if response.status_code == 200:
        try:
            results = response.json().get("results", [])
            if not results:
                return [{"error": f"Aucune information trouvée pour '{search}'."}]
            for city_data in results:
                formatted_results.append(
                    {
                        "name": city_data.get("name", "N/A"),
                        "country": city_data.get("country", "N/A"),
                        "admin1": city_data.get("admin1", "N/A"),
                        "admin2": city_data.get("admin2", "N/A"),
                        "latitude": city_data.get("latitude", "N/A"),
                        "longitude": city_data.get("longitude", "N/A"),
                        "timezone": city_data.get("timezone", "N/A"),
                        "postcode": city_data.get("postcodes", ["N/A"])[0],
                        "error": "",
                    }
                )
        except (KeyError, IndexError):
            return [
                {"error": f"Erreur lors du traitement des données pour '{search}'."}
            ]
    else:
        return [
            {
                "error": f"Échec de la récupération des données pour '{search}': {response.status_code}"
            }
        ]

    return formatted_results


def fetch_weather_data(base_url, params):
    try:
        response = requests.get(url=base_url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if "error" in data:
            return f"Erreur lors de la récupération des données météorologiques: {data['message']}"
        return data
    except requests.RequestException as e:
        return f"Erreur lors de la récupération des données météorologiques: {str(e)}"


def direction_vent(degres: float) -> dict:
    """
    Retourne la direction du vent (français + anglais)
    à partir d’un angle en degrés (0–360°).
    """

    # Directions abrégées et complètes (16 points cardinaux)
    abregees_fr = [
        "N",
        "NNE",
        "NE",
        "ENE",
        "E",
        "ESE",
        "SE",
        "SSE",
        "S",
        "SSO",
        "SO",
        "OSO",
        "O",
        "ONO",
        "NO",
        "NNO",
    ]

    toutes_lettres_fr = [
        "nord",
        "nord-nord-est",
        "nord-est",
        "est-nord-est",
        "est",
        "est-sud-est",
        "sud-est",
        "sud-sud-est",
        "sud",
        "sud-sud-ouest",
        "sud-ouest",
        "ouest-sud-ouest",
        "ouest",
        "ouest-nord-ouest",
        "nord-ouest",
        "nord-nord-ouest",
    ]

    abregees_en = [
        "N",
        "NNE",
        "NE",
        "ENE",
        "E",
        "ESE",
        "SE",
        "SSE",
        "S",
        "SSW",
        "SW",
        "WSW",
        "W",
        "WNW",
        "NW",
        "NNW",
    ]

    toutes_lettres_en = [
        "north",
        "north-northeast",
        "northeast",
        "east-northeast",
        "east",
        "east-southeast",
        "southeast",
        "south-southeast",
        "south",
        "south-southwest",
        "southwest",
        "west-southwest",
        "west",
        "west-northwest",
        "northwest",
        "north-northwest",
    ]

    # Calcul du secteur (22,5° chacun)
    secteur = int((degres + 11.25) % 360 // 22.5)

    return {
        "degres": degres,
        "francais": {
            "complet": toutes_lettres_fr[secteur],
            "abrege": abregees_fr[secteur],
        },
        "anglais": {
            "complet": toutes_lettres_en[secteur],
            "abrege": abregees_en[secteur],
        },
    }


@tool
def get_current_weather(
    city: str = "", zipcode: str = "", department: str = "", country_code="FR"
) -> str:
    """
    Récupère la météo actuelle pour une localisation française.

    Le LLM doit extraire et fournir ces paramètres depuis la question de l'utilisateur:

    :param city: Nom de la ville (ex: "Bolleville", "Paris", "Lyon")
    :param zipcode: Code postal à 5 chiffres (ex: "50260", "75001") - optionnel
    :param department: Nom du département (ex: "Manche", "Paris", "Rhône") - optionnel
    :param country_code: Code pays ISO 3166-1 alpha-2 (par défaut "FR" pour France, https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) - optionnel
    :return: Informations météo détaillées ou message d'erreur

    Exemples d'utilisation par le LLM:
    - Question: "Météo à Bolleville dans la Manche"
        -> Appel: get_current_weather(city="Bolleville", department="Manche")

    - Question: "Quel temps fait-il au 75001?"
        -> Appel: get_current_weather(zipcode="75001")

    - Question: "Météo à Paris 75001"
        -> Appel: get_current_weather(city="Paris", zipcode="75001")
    """

    if not city and not zipcode and not department:
        return (
            "❌ Aucune localisation fournie.\n"
            "Veuillez fournir au moins:\n"
            "- Un nom de ville (ex: city='Paris')\n"
            "- Un code postal (ex: zipcode='75001')"
        )

    search_for = city if city else zipcode if zipcode else ""
    if not search_for:
        return (
            "❌ Veuillez fournir au moins:\n"
            "- Un nom de ville (ex: city='Paris')\n"
            "- Un code postal (ex: zipcode='75001')"
        )

    city_info = get_city_info(search_for, country_code.upper())
    if not city_info:
        return "❌ Aucune information trouvée pour la localisation fournie."

    if search_for == zipcode:
        city_info = [city_info[0]]

    formatted_data = ""
    for city_data in city_info:
        if city_data["error"]:
            return city_data["error"]

        # https://open-meteo.com/en/docs?hourly=#api_documentation
        # https://open-meteo.com/en/docs/meteofrance-api
        openmeteo_base_url = "https://api.open-meteo.com/v1/forecast"
        openmeteo_params = {
            # Modèle de prévision météorologique à utiliser (ex. ECMWF, GFS, ICON, etc.). Par défaut, le modèle global GFS est utilisé.
            "models": "meteofrance_seamless",
            # Coordonnées WGS84 (latitude, longitude en degrés décimaux) du lieu pour lequel on veut les données.
            "latitude": city_data["latitude"],
            "longitude": city_data["longitude"],
            # Fuseau horaire pour les données temporelles. Si auto, le système détecte automatiquement le fuseau horaire du lieu donné. Pour les variables
            "timezone": city_data["timezone"],
            # Unité pour la température : celsius ou fahrenheit.
            "temperature_unit": "celsius",
            # Unité pour la vitesse du vent : kmh, ms, mph ou kn.
            "wind_speed_unit": "kmh",
            # Unité pour les précipitations : mm ou inch.
            "precipitation_unit": "mm",
            # Nombre de jours de prévisions à retourner (1-16). Par défaut 7.
            "forecast_days": 1,
            # Conditions “actuelles”
            "current": [
                # Température de l’air à 2 mètres au-dessus du sol
                "temperature_2m",
                # % Humidité relative à 2 mètres au-dessus du sol
                "relative_humidity_2m",
                # Température ressenti
                "apparent_temperature",
                # Vitesse du vent à 10 mètres au-dessus du sol
                "wind_speed_10m",
                # Direction du vent à 10 mètres au-dessus du sol (degrés)
                "wind_direction_10m",
                # Code météo World Meteorological Organization (WMO)
                "weather_code",
            ],
        }

        openmeteo_data = fetch_weather_data(openmeteo_base_url, openmeteo_params)
        if isinstance(openmeteo_data, str):
            return openmeteo_data

        dt = datetime.datetime.strptime(
            openmeteo_data["current"]["time"], "%Y-%m-%dT%H:%M"
        )
        formatted_timestamp = dt.strftime("%d/%m/%Y - %H:%M:%S")
        info_direction_vent = direction_vent(
            openmeteo_data["current"]["wind_direction_10m"]
        )
        formatted_data += f"""{formatted_timestamp} ({openmeteo_data['timezone_abbreviation']}) Météo actuelle pour {city_data['postcode']} - {city_data['name']} ({city_data['country']}, {city_data['admin1']}, {city_data['admin2']}) :
        - Température : {openmeteo_data['current']['temperature_2m']} °C
        - Humidité : {openmeteo_data['current']['relative_humidity_2m']} %
        - Température ressentie : {openmeteo_data['current']['apparent_temperature']} °C
        - Vitesse du vent : {openmeteo_data['current']['wind_speed_10m']} km/h
        - Direction du vent : {info_direction_vent['francais']['complet']} ({info_direction_vent['francais']['abrege']} - {openmeteo_data['current']['wind_direction_10m']}°)
        - Direction du vent243° (ouest-sud‑ouest) °
        - Condition : {WMO_WEATHER_CODES[openmeteo_data['current']['weather_code']]['fr']} {WMO_WEATHER_CODES[openmeteo_data['current']['weather_code']]['icon']}
        \n\n"""

    prompt = f"""Donnez une description détaillée des conditions météorologiques, en indiquant l'heure des données et le fuseau horaire.
Utilisez un langage simple et clair, adapté à un public général.
Évitez le jargon technique et les termes complexes.
Répondez en français et structurer la réponse ainsi : conditions actuelles → conseils ou suggestions utiles.

Inclure ces données :

{formatted_data}

"""
    # n'incluez aucun autre texte que la description météorologique.

    return prompt


# https://openwebui.com/t/jamesjmj621/duckduckgo_websearch
# https://github.com/open-webui/open-webui/blob/main/backend/open_webui/retrieval/web/duckduckgo.py#L37
# Exemple : https://discord.com/channels/1110598183144399058/1111687693919846421/1424870464869634202


@tool
def add(a: float, b: float) -> float:
    """Add two numbers."""
    return a + b


@tool
def multiply(a: float, b: float) -> float:
    """Multiply two numbers."""
    return a * b


@tool
def web_search(query: str) -> str:
    """Search the web for information."""
    return (
        f"Query :\n{query}\n\n"
        "Here are the headcounts for each of the FAANG companies in 2024:\n"
        "1. **Facebook (Meta)**: 67,317 employees.\n"
        "2. **Apple**: 164,000 employees.\n"
        "3. **Amazon**: 1,551,000 employees.\n"
        "4. **Netflix**: 14,000 employees.\n"
        "5. **Google (Alphabet)**: 181,269 employees."
    )


def main():
    llm_model = ChatOllama(
        # base_url="http://************:11434",
        # model="phi4:latest",
        ###model="gpt-oss:20b-cloud",
        base_url="https://ollama.com",
        model="gpt-oss:20b",
        temperature=0.1,
    )

    math_agent = create_react_agent(
        model=llm_model,
        tools=[add, multiply],
        name="math_expert",
        prompt="Vous êtes un expert en mathématiques.",
    )

    research_agent = create_react_agent(
        model=llm_model,
        tools=[web_search],
        name="research_expert",
        prompt="Vous êtes un chercheur de renommée mondiale et avez accès à la recherche web. Ne faites pas de calculs.",
    )

    weather_agent = create_react_agent(
        model=llm_model,
        tools=[get_current_weather],
        name="weather_expert",
        prompt=(
            "Vous êtes un expert en météorologie. "
            "Lorsqu'un utilisateur demande la météo, vous devez extraire de sa question:\n"
            "- Le nom de la ville (paramètre 'city')\n"
            "- Le code postal si mentionné (paramètre 'zipcode')\n"
            "- Le département si mentionné (paramètre 'department')\n"
            "Appelez ensuite l'outil get_current_weather avec ces paramètres.\n"
            "Au moins un paramètre doit être fourni."
        ),
    )

    workflow = create_supervisor(
        agents=[research_agent, math_agent, weather_agent],
        model=llm_model,
        output_mode="full_history",  # Include full message history from an agent
        # output_mode="last_message", # Include only the final agent response
        add_handoff_back_messages=True,
        prompt=(
            "Vous êtes responsable d'équipe et gérez trois experts : un expert en recherche, un expert en mathématiques et un expert en météo."
            "Pour l'actualité ou les informations générales, utilisez l'outil `research_expert`."
            "Pour les calculs mathématiques, utilisez l'outil `math_expert`."
            "Si la demande concerne la météo → utiliser l’outil `get_current_weather`"
            "Sélectionnez toujours l'expert le plus approprié pour chaque demande utilisateur et coordonnez ses réponses si nécessaire."
        ),
    )

    app = workflow.compile()
    result = app.invoke(
        {
            "messages": [
                {
                    "role": "user",
                    "content": "Quelles sont les prévisions météo pour bolleville dans la manche ?",
                    # "content": "Quelles sont les prévisions météo pour le code postal 50190 ?"
                    # "content": "what's the combined headcount of the FAANG companies in 2024?"
                }
            ]
        }
    )
    with open("resultat.md", "w", encoding="utf-8") as f:
        f.write(f"{result['messages'][-1].content}\n")
        print("Réponse sauvegardée dans le fichier 'résultat.md'")


if __name__ == "__main__":
    dotenv_path = join(dirname(__file__), ".env")
    load_dotenv(dotenv_path)

    main()


# ===================================================================================

"""
https://serper.dev/dashboard
<EMAIL>
<EMAIL>+?39PJ.n
Api Key: bf313bea56f88e41c09b7f550f2ed0ae1699491f
"""


"""
import requests
import json

url = "https://google.serper.dev/search"

payload = json.dumps({
  "q": "apple inc",
  "location": "France",
  "gl": "fr",
  "hl": "fr"
})
headers = {
  'X-API-KEY': 'bf313bea56f88e41c09b7f550f2ed0ae1699491f',
  'Content-Type': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
"""

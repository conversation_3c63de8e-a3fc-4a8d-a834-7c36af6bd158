function um_sanitize_value(e,t){var a=document.createElement("div"),e=(a.innerText=e,a.innerHTML);return t&&jQuery(t).val(e),e}function um_unsanitize_value(e){var t=document.createElement("textarea");return t.innerHTML=e,0===t.childNodes.length?"":t.childNodes[0].nodeValue}jQuery(document).ready(function(){function n(e){var a=jQuery(e.currentTarget),e=a.find(":selected");1<e.length&&e.each(function(e,t){""===t.value&&(t.selected=!1,a.trigger("change"))})}jQuery(document.body).on("click",".um-dropdown a.real_url",function(){window.location=jQuery(this).attr("href")}),jQuery(document.body).on("click",".um-trigger-menu-on-click",function(){var e=jQuery(this).find(".um-dropdown");return UM.dropdown.show(e),!1}),jQuery(document.body).on("click",".um-dropdown-hide",function(){return UM.dropdown.hideAll(),!1}),jQuery(document.body).on("click","a.um-manual-trigger",function(){var e=jQuery(this).attr("data-child"),t=jQuery(this).attr("data-parent");return jQuery(this).parents(t).find(e).trigger("click"),UM.dropdown.hideAll(),!1}),jQuery(".um-s1,.um-s2").css({display:"block"}),"function"==typeof jQuery.fn.select2&&(jQuery(".um-s1").each(function(e){var t=jQuery(this);t.select2({allowClear:!0,dropdownParent:t.parent()}).on("change",n)}),jQuery(".um-s2").each(function(e){var t=jQuery(this),a={},a=t.parents(".um-custom-shortcode-tab").length?{allowClear:!1}:{allowClear:!1,minimumResultsForSearch:10,dropdownParent:t.parent()};t.select2(a).on("change",n)}),jQuery(".um-s3").each(function(e){var t=jQuery(this);t.select2({allowClear:!1,minimumResultsForSearch:-1,dropdownParent:t.parent()}).on("change",n)})),"function"==typeof jQuery.fn.um_raty&&(jQuery(".um-rating").um_raty({half:!1,starType:"i",number:function(){return jQuery(this).attr("data-number")},score:function(){return jQuery(this).attr("data-score")},scoreName:function(){return jQuery(this).attr("data-key")},hints:!1,click:function(e,t){um_live_field=this.id,um_live_value=e,um_apply_conditions(jQuery(this),!1)}}),jQuery(".um-rating-readonly").um_raty({half:!1,starType:"i",number:function(){return jQuery(this).attr("data-number")},score:function(){return jQuery(this).attr("data-score")},scoreName:function(){return jQuery(this).attr("data-key")},hints:!1,readOnly:!0})),jQuery(document).on("change",'.um-field-area input[type="radio"]',function(){var e=jQuery(this).parents(".um-field-area"),t=jQuery(this).parents("label");e.find(".um-field-radio").removeClass("active"),e.find(".um-field-radio").find("i").removeAttr("class").addClass("um-icon-android-radio-button-off"),t.addClass("active"),t.find("i").removeAttr("class").addClass("um-icon-android-radio-button-on")}),jQuery(document).on("change",'.um-field-area input[type="checkbox"]',function(){var e=jQuery(this).parents("label");e.hasClass("active")?(e.removeClass("active"),e.find("i").removeAttr("class").addClass("um-icon-android-checkbox-outline-blank")):(e.addClass("active"),e.find("i").removeAttr("class").addClass("um-icon-android-checkbox-outline"))}),jQuery(document.body).on("click",".um-single-image-preview a.cancel",function(e){e.preventDefault();let t=!1;0<jQuery(this).parents(".um-modal-body").length&&(t=!0);let a,n,i,r;t?(a=jQuery(this).parents(".um-modal-body"),n=a.find(".um-single-image-upload").data("set_mode"),i=a.find(".um-single-image-preview img").attr("src"),r={data:{src:i,mode:n,nonce:um_scripts.nonce},success:function(){wp.hooks.doAction("um_after_removing_preview"),a.find(".um-single-image-preview img").replaceWith('<img src="" alt="" />'),a.find(".um-single-image-preview").removeAttr("style").hide(),a.find(".ajax-upload-dragdrop").show(),a.find(".um-modal-btn.um-finish-upload").addClass("disabled"),um_modal_responsive()}}):(a=jQuery(this).parents(".um-field"),n=a.data("mode"),i=a.find(".um-single-image-preview img").attr("src"),e=a.find('input[type="hidden"]#'+a.data("key")+"-"+jQuery(this).parents("form").find('input[type="hidden"][name="form_id"]').val()).val(),r={data:{mode:n,filename:e,src:i,nonce:um_scripts.nonce},success:function(){a.find(".um-single-image-preview img").replaceWith('<img src="" alt="" />'),a.find(".um-single-image-preview").removeAttr("style").hide(),a.find(".um-btn-auto-width").html(a.data("upload-label")),a.find('input[type="hidden"]').val("empty_file")}},"register"!==n&&(r.data.user_id=jQuery(this).parents("form").find("#user_id").val())),wp.ajax.send("um_remove_file",r)}),jQuery(document.body).on("click",".um-single-file-preview a.cancel",function(e){e.preventDefault();let t=!1;0<jQuery(this).parents(".um-modal-body").length&&(t=!0);let a,n,i,r;t?(a=jQuery(this).parents(".um-modal-body"),i=a.find(".um-single-fileinfo a").attr("href"),n=a.find(".um-single-file-upload").data("set_mode"),r={data:{src:i,mode:n,nonce:um_scripts.nonce},success:function(){a.find(".um-single-file-preview").hide(),a.find(".ajax-upload-dragdrop").show(),a.find(".um-modal-btn.um-finish-upload").addClass("disabled"),um_modal_responsive()}}):(a=jQuery(this).parents(".um-field"),i=a.find(".um-single-fileinfo a").attr("href"),n=a.data("mode"),e=a.find('input[type="hidden"]#'+a.data("key")+"-"+jQuery(this).parents("form").find('input[type="hidden"][name="form_id"]').val()).val(),r={data:{mode:n,filename:e,src:i,nonce:um_scripts.nonce},success:function(){a.find(".um-single-file-preview").hide(),a.find(".um-btn-auto-width").html(a.data("upload-label")),a.find("input[type=hidden]").val("empty_file")}},"register"!==n&&(r.data.user_id=jQuery(this).parents("form").find("#user_id").val())),wp.ajax.send("um_remove_file",r)}),jQuery(document).on("click",".um-field-group-head:not(.disabled)",function(){var e=jQuery(this).parents(".um-field-group"),t=e.data("max_entries"),a=(e.find(".um-field-group-body").is(":hidden")?e.find(".um-field-group-body").show():e.find(".um-field-group-body:first").clone().appendTo(e),0);e.find(".um-field-group-body").each(function(){a++,jQuery(this).find("input").each(function(){var e=jQuery(this);e.attr("id",e.data("key")+"-"+a),e.attr("name",e.data("key")+"-"+a),e.parent().parent().find("label").attr("for",e.data("key")+"-"+a)})}),0<t&&e.find(".um-field-group-body").length==t&&jQuery(this).addClass("disabled")}),jQuery(document).on("click",".um-field-group-cancel",function(e){e.preventDefault();var e=jQuery(this).parents(".um-field-group"),t=e.data("max_entries");return 1<e.find(".um-field-group-body").length?jQuery(this).parents(".um-field-group-body").remove():jQuery(this).parents(".um-field-group-body").hide(),0<t&&e.find(".um-field-group-body").length<t&&e.find(".um-field-group-head").removeClass("disabled"),!1}),jQuery(document.body).on("click",".um-ajax-paginate",function(e){e.preventDefault();var t,a,n=jQuery(this),i=n.parent(),r=(i.addClass("loading"),+n.data("pages")),u=+n.data("page")+1,e=n.data("hook");"um_load_posts"===e?jQuery.ajax({url:wp.ajax.settings.url,type:"post",data:{action:"um_ajax_paginate_posts",author:jQuery(this).data("author"),page:u,nonce:um_scripts.nonce},complete:function(){i.removeClass("loading")},success:function(e){i.before(e),u==r?i.remove():n.data("page",u)}}):"um_load_comments"===e?jQuery.ajax({url:wp.ajax.settings.url,type:"post",data:{action:"um_ajax_paginate_comments",user_id:jQuery(this).data("user_id"),page:u,nonce:um_scripts.nonce},complete:function(){i.removeClass("loading")},success:function(e){i.before(e),u==r?i.remove():n.data("page",u)}}):(t=jQuery(this).data("args"),a=jQuery(this).parents(".um.um-profile.um-viewing").find(".um-ajax-items"),jQuery.ajax({url:wp.ajax.settings.url,type:"post",data:{action:"um_ajax_paginate",hook:e,args:t,nonce:um_scripts.nonce},complete:function(){i.removeClass("loading")},success:function(e){i.remove(),a.append(e)}}))}),jQuery(document).on("click",".um-ajax-action",function(e){e.preventDefault();var e=jQuery(this).data("hook"),t=jQuery(this).data("user_id"),a=jQuery(this).data("args");return jQuery(this).data("js-remove")&&jQuery(this).parents("."+jQuery(this).data("js-remove")).fadeOut("fast"),jQuery.ajax({url:wp.ajax.settings.url,type:"post",data:{action:"um_muted_action",hook:e,user_id:t,arguments:a,nonce:um_scripts.nonce},success:function(e){}}),!1}),jQuery(document.body).on("click","#um-search-button",function(){var e,t=jQuery(this).parents(".um-search-form").data("members_page"),a=[],n=(jQuery(this).parents(".um-search-form").find('input[name="um-search-keys[]"]').each(function(){a.push(jQuery(this).val())}),jQuery(this).parents(".um-search-form").find(".um-search-field").val());if(""===n)e=t;else{for(var i="?",r=0;r<a.length;r++)i+=a[r]+"="+n,r!==a.length-1&&(i+="&");e=t+i}window.location=e}),jQuery(document.body).on("keypress",".um-search-field",function(e){if(13===e.which){var t,e=jQuery(this).parents(".um-search-form").data("members_page"),a=[],n=(jQuery(this).parents(".um-search-form").find('input[name="um-search-keys[]"]').each(function(){a.push(jQuery(this).val())}),jQuery(this).val());if(""===n)t=e;else{for(var i="?",r=0;r<a.length;r++)i+=a[r]+"="+n,r!==a.length-1&&(i+="&");t=e+i}window.location=t}}),jQuery('.um-form input[class="um-button"][type="submit"]').prop("disabled",!1),jQuery(document).one("click",'.um:not(.um-account) .um-form input[class="um-button"][type="submit"]:not(.um-has-recaptcha)',function(){jQuery(this).attr("disabled","disabled"),jQuery(this).parents("form").trigger("submit")});var o={};function d(t,e,a){var n,i,r=t.parents(".um-directory"),u=t.attr("name"),o=(t.find('option[value!=""]').remove(),t.hasClass("um-child-option-disabled")||t.prop("disabled",!1),[]);"yes"===e.post.members_directory&&o.push({id:"",text:"",selected:1}),jQuery.each(e.items,function(e,t){o.push({id:e,text:t,selected:""===t})}),t.select2("destroy"),t.hasClass("um-s1")?t.select2({data:o,allowClear:!0,dropdownParent:t.parent()}):t.hasClass("um-s2")&&t.select2({data:o,allowClear:!0,minimumResultsForSearch:10,dropdownParent:t.parent()}),"yes"===e.post.members_directory&&(t.find("option").each(function(){""!==jQuery(this).html()&&jQuery(this).data("value_label",jQuery(this).html()).attr("data-value_label",jQuery(this).html())}),void 0!==(n=um_get_data_for_directory(r,"filter_"+u))&&(n=n.split("||"),i=[],jQuery.each(n,function(e){t.find('option[value="'+n[e]+'"]').length&&i.push(n[e]),t.find('option[value="'+n[e]+'"]').prop("disabled",!0).hide(),1===t.find("option:not(:disabled)").length&&t.prop("disabled",!0),t.select2("destroy").select2(),t.val("").trigger("change")}),i=i.join("||"),n!==i)&&(um_set_url_from_data(r,"filter_"+u,i),um_ajax_get_members(r)),um_change_tag(r)),"yes"!==e.post.members_directory&&(void 0===e.field.default||t.data("um-original-value")?""!==t.data("um-original-value")&&t.val(t.data("um-original-value")).trigger("change"):t.val(e.field.default).trigger("change"),0==e.field.editable)&&(t.addClass("um-child-option-disabled"),t.attr("disabled","disabled"))}jQuery("select[data-um-parent]").each(function(){var i=jQuery(this),r=i.data("um-parent"),u=i.data("um-ajax-source");i.attr("data-um-init-field",!0),jQuery(document).on("change",'select[name="'+r+'"]',function(){var e,t,a=jQuery(this),n=a.closest("form").find('input[type="hidden"][name="form_id"]').val();if(void 0!==(t="yes"===i.attr("data-member-directory")?(e=a.parents(".um-directory"),void 0!==(t=um_get_data_for_directory(e,"filter_"+r))?t.split("||"):""):a.val())&&""!==t&&"object"!=typeof o[t]){if(void 0!==i.um_wait&&!1!==i.um_wait)return;i.um_wait=!0,jQuery.ajax({url:wp.ajax.settings.url,type:"post",data:{action:"um_select_options",parent_option_name:r,parent_option:t,child_callback:u,child_name:i.attr("name"),members_directory:i.attr("data-member-directory"),form_id:n,nonce:um_scripts.nonce},success:function(e){"success"===e.status&&""!==t&&d(i,o[t]=e),void 0!==e.debug&&console.log(e),i.um_wait=!1},error:function(e){console.log(e),i.um_wait=!1}})}void 0!==t&&""!==t&&"object"==typeof o[t]&&setTimeout(d,10,i,o[t],t),void 0===t&&""!==t||(i.find('option[value!=""]').remove(),i.val("").trigger("change"))}),jQuery('select[name="'+r+'"]').trigger("change")}),jQuery(document.body).on("click",".um-toggle-password",function(){var e=jQuery(this).closest(".um-field-area-password"),t=e.find("input");"text"===t.attr("type")?t.attr("type","password"):t.attr("type","text"),e.find("i").toggleClass("um-icon-eye um-icon-eye-disabled")})}),jQuery(window).on("load",function(){var e=jQuery('input[name="um_request"]');e.length&&e.val("")});
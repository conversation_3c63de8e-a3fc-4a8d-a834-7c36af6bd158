/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{compileCSS:()=>w,getCSSRules:()=>R,getCSSValueFromRawStyle:()=>f});var n=function(){return n=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function r(e){return e.toLowerCase()}var o=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function i(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}function g(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,g=void 0===n?o:n,c=t.stripRegexp,u=void 0===c?a:c,d=t.transform,l=void 0===d?r:d,s=t.delimiter,p=void 0===s?" ":s,m=i(i(e,g,"$1\0$2"),u,"\0"),f=0,y=m.length;"\0"===m.charAt(f);)f++;for(;"\0"===m.charAt(y-1);)y--;return m.slice(f,y).split("\0").map(l).join(p)}(e,n({delimiter:"."},t))}function c(e,t){return void 0===t&&(t={}),g(e,n({delimiter:"-"},t))}const u="var:",d="|",l="--",s=(e,t)=>{let n=e;return t.forEach((e=>{n=n?.[e]})),n};function p(e,t,n,r){const o=s(e,n);return o?[{selector:t?.selector,key:r,value:f(o)}]:[]}function m(e,t,n,r,o=["top","right","bottom","left"]){const a=s(e,n);if(!a)return[];const i=[];if("string"==typeof a)i.push({selector:t?.selector,key:r.default,value:a});else{const e=o.reduce(((e,n)=>{const o=f(s(a,[n]));return o&&e.push({selector:t?.selector,key:r?.individual.replace("%s",y(n)),value:o}),e}),[]);i.push(...e)}return i}function f(e){if("string"==typeof e&&e.startsWith(u)){return`var(--wp--${e.slice(u.length).split(d).map((e=>c(e,{splitRegexp:[/([a-z0-9])([A-Z])/g,/([0-9])([a-z])/g,/([A-Za-z])([0-9])/g,/([A-Z])([A-Z][a-z])/g]}))).join(l)})`}return e}function y(e){const[t,...n]=e;return t.toUpperCase()+n.join("")}function b(e){try{return decodeURI(e)}catch(t){return e}}function h(e){return(t,n)=>p(t,n,e,function(e){const[t,...n]=e;return t.toLowerCase()+n.map(y).join("")}(e))}function k(e){return(t,n)=>["color","style","width"].flatMap((r=>h(["border",e,r])(t,n)))}const v={name:"radius",generate:(e,t)=>m(e,t,["border","radius"],{default:"borderRadius",individual:"border%sRadius"},["topLeft","topRight","bottomLeft","bottomRight"])},S=[...[{name:"color",generate:h(["border","color"])},{name:"style",generate:h(["border","style"])},{name:"width",generate:h(["border","width"])},v,{name:"borderTop",generate:k("top")},{name:"borderRight",generate:k("right")},{name:"borderBottom",generate:k("bottom")},{name:"borderLeft",generate:k("left")}],...[{name:"text",generate:(e,t)=>p(e,t,["color","text"],"color")},{name:"gradient",generate:(e,t)=>p(e,t,["color","gradient"],"background")},{name:"background",generate:(e,t)=>p(e,t,["color","background"],"backgroundColor")}],...[{name:"minHeight",generate:(e,t)=>p(e,t,["dimensions","minHeight"],"minHeight")},{name:"aspectRatio",generate:(e,t)=>p(e,t,["dimensions","aspectRatio"],"aspectRatio")}],...[{name:"color",generate:(e,t,n=["outline","color"],r="outlineColor")=>p(e,t,n,r)},{name:"style",generate:(e,t,n=["outline","style"],r="outlineStyle")=>p(e,t,n,r)},{name:"offset",generate:(e,t,n=["outline","offset"],r="outlineOffset")=>p(e,t,n,r)},{name:"width",generate:(e,t,n=["outline","width"],r="outlineWidth")=>p(e,t,n,r)}],...[{name:"margin",generate:(e,t)=>m(e,t,["spacing","margin"],{default:"margin",individual:"margin%s"})},{name:"padding",generate:(e,t)=>m(e,t,["spacing","padding"],{default:"padding",individual:"padding%s"})}],...[{name:"fontFamily",generate:(e,t)=>p(e,t,["typography","fontFamily"],"fontFamily")},{name:"fontSize",generate:(e,t)=>p(e,t,["typography","fontSize"],"fontSize")},{name:"fontStyle",generate:(e,t)=>p(e,t,["typography","fontStyle"],"fontStyle")},{name:"fontWeight",generate:(e,t)=>p(e,t,["typography","fontWeight"],"fontWeight")},{name:"letterSpacing",generate:(e,t)=>p(e,t,["typography","letterSpacing"],"letterSpacing")},{name:"lineHeight",generate:(e,t)=>p(e,t,["typography","lineHeight"],"lineHeight")},{name:"textColumns",generate:(e,t)=>p(e,t,["typography","textColumns"],"columnCount")},{name:"textDecoration",generate:(e,t)=>p(e,t,["typography","textDecoration"],"textDecoration")},{name:"textTransform",generate:(e,t)=>p(e,t,["typography","textTransform"],"textTransform")},{name:"writingMode",generate:(e,t)=>p(e,t,["typography","writingMode"],"writingMode")}],...[{name:"shadow",generate:(e,t)=>p(e,t,["shadow"],"boxShadow")}],...[{name:"backgroundImage",generate:(e,t)=>{const n=e?.background?.backgroundImage;return"object"==typeof n&&n?.url?[{selector:t.selector,key:"backgroundImage",value:`url( '${encodeURI(b(n.url))}' )`}]:p(e,t,["background","backgroundImage"],"backgroundImage")}},{name:"backgroundPosition",generate:(e,t)=>p(e,t,["background","backgroundPosition"],"backgroundPosition")},{name:"backgroundRepeat",generate:(e,t)=>p(e,t,["background","backgroundRepeat"],"backgroundRepeat")},{name:"backgroundSize",generate:(e,t)=>p(e,t,["background","backgroundSize"],"backgroundSize")},{name:"backgroundAttachment",generate:(e,t)=>p(e,t,["background","backgroundAttachment"],"backgroundAttachment")}]];function w(e,t={}){const n=R(e,t);if(!t?.selector){const e=[];return n.forEach((t=>{e.push(`${c(t.key)}: ${t.value};`)})),e.join(" ")}const r=n.reduce(((e,t)=>{const{selector:n}=t;return n?(e[n]||(e[n]=[]),e[n].push(t),e):e}),{});return Object.keys(r).reduce(((e,t)=>(e.push(`${t} { ${r[t].map((e=>`${c(e.key)}: ${e.value};`)).join(" ")} }`),e)),[]).join("\n")}function R(e,t={}){const n=[];return S.forEach((r=>{"function"==typeof r.generate&&n.push(...r.generate(e,t))})),n}(window.wp=window.wp||{}).styleEngine=t})();
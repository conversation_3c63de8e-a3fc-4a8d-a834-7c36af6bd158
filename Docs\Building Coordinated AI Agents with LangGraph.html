<!DOCTYPE html>
<html lang="en-US">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11"> 
	<meta name='robots' content='index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' />
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	
	<!-- This site is optimized with the Yoast SEO Premium plugin v25.4 (Yoast SEO v25.4) - https://yoast.com/wordpress/plugins/seo/ -->
	<title>Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial | CodeCut</title>
	<meta name="description" content="Learn how to orchestrate LangGraph agents with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> to build a debate‑style investment committee AI." />
	<link rel="canonical" href="https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/" />
	<meta property="og:locale" content="en_US" />
	<meta property="og:type" content="article" />
	<meta property="og:title" content="Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial" />
	<meta property="og:description" content="Learn how to orchestrate LangGraph agents with LangChain and Tavily to build a debate‑style investment committee AI." />
	<meta property="og:url" content="https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/" />
	<meta property="og:site_name" content="CodeCut" />
	<meta property="article:published_time" content="2025-06-11T00:19:14+00:00" />
	<meta property="article:modified_time" content="2025-09-27T13:27:25+00:00" />
	<meta property="og:image" content="https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png" />
	<meta property="og:image:width" content="931" />
	<meta property="og:image:height" content="488" />
	<meta property="og:image:type" content="image/png" />
	<meta name="author" content="Khuyen Tran, Bex Tuychiev" />
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:creator" content="@KhuyenTran16" />
	<meta name="twitter:site" content="@KhuyenTran16" />
	<meta name="twitter:label1" content="Written by" />
	<meta name="twitter:data1" content="Khuyen Tran, Bex Tuychiev" />
	<meta name="twitter:label2" content="Est. reading time" />
	<meta name="twitter:data2" content="14 minutes" />
	<script type="application/ld+json" class="yoast-schema-graph">{"@context":"https://schema.org","@graph":[{"@type":"Article","@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#article","isPartOf":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/"},"author":{"name":"Khuyen Tran","@id":"https://codecut.ai/#/schema/person/0c1c7b36aab1f535b34cecc99c946014"},"headline":"Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial","datePublished":"2025-06-11T00:19:14+00:00","dateModified":"2025-09-27T13:27:25+00:00","mainEntityOfPage":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/"},"wordCount":1478,"commentCount":0,"publisher":{"@id":"https://codecut.ai/#organization"},"image":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#primaryimage"},"thumbnailUrl":"https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png","articleSection":["Blog","LLM"],"inLanguage":"en-US","potentialAction":[{"@type":"CommentAction","name":"Comment","target":["https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#respond"]}]},{"@type":"WebPage","@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/","url":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/","name":"Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial | CodeCut","isPartOf":{"@id":"https://codecut.ai/#website"},"primaryImageOfPage":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#primaryimage"},"image":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#primaryimage"},"thumbnailUrl":"https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png","datePublished":"2025-06-11T00:19:14+00:00","dateModified":"2025-09-27T13:27:25+00:00","description":"Learn how to orchestrate LangGraph agents with LangChain and Tavily to build a debate‑style investment committee AI.","breadcrumb":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#breadcrumb"},"inLanguage":"en-US","potentialAction":[{"@type":"ReadAction","target":["https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/"]}]},{"@type":"ImageObject","inLanguage":"en-US","@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#primaryimage","url":"https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png","contentUrl":"https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png","width":931,"height":488},{"@type":"BreadcrumbList","@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#breadcrumb","itemListElement":[{"@type":"ListItem","position":1,"name":"Home","item":"https://codecut.ai/"},{"@type":"ListItem","position":2,"name":"Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial"}]},{"@type":"WebSite","@id":"https://codecut.ai/#website","url":"https://codecut.ai/","name":"CodeCut","description":"Productivity for Data Scientists","publisher":{"@id":"https://codecut.ai/#organization"},"potentialAction":[{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://codecut.ai/?s={search_term_string}"},"query-input":{"@type":"PropertyValueSpecification","valueRequired":true,"valueName":"search_term_string"}}],"inLanguage":"en-US"},{"@type":"Organization","@id":"https://codecut.ai/#organization","name":"CodeCut","url":"https://codecut.ai/","logo":{"@type":"ImageObject","inLanguage":"en-US","@id":"https://codecut.ai/#/schema/logo/image/","url":"https://codecut.ai/wp-content/uploads/2025/04/logo_1200x675_no_crop.png","contentUrl":"https://codecut.ai/wp-content/uploads/2025/04/logo_1200x675_no_crop.png","width":1200,"height":675,"caption":"CodeCut"},"image":{"@id":"https://codecut.ai/#/schema/logo/image/"},"sameAs":["https://x.com/KhuyenTran16","https://www.linkedin.com/in/khuyen-tran-1401/"],"description":"CodeCut is a platform dedicated to helping data professionals write cleaner, faster, and more efficient code. We provide hands-on tutorials, tool comparisons, and best practices across Python, data science, and modern workflow automation.","legalName":"Codecut Technologies LLC","foundingDate":"2024-08-06","numberOfEmployees":{"@type":"QuantitativeValue","minValue":"1","maxValue":"10"}},{"@type":"Person","@id":"https://codecut.ai/#/schema/person/0c1c7b36aab1f535b34cecc99c946014","name":"Khuyen Tran","description":"Khuyen Tran creates accessible data science content that reaches over 100,000 monthly readers through my 180+ Towards Data Science articles and 800+ daily CodeCut tips covering Python, data workflows, and ML engineering. Her mission is to make open-source tools approachable through clear tutorials, documentation, and videos while helping data scientists adopt engineering best practices from version control to deployment."}]}</script>
	<!-- / Yoast SEO Premium plugin. -->


<link rel='dns-prefetch' href='//www.googletagmanager.com' />
<link rel='dns-prefetch' href='//fonts.googleapis.com' />
<script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/codecut.ai\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.2"}};
/*! This file is auto-generated */
!function(s,n){var o,i,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),a=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===a[t]})}function u(e,t){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);for(var n=e.getImageData(16,16,1,1),a=0;a<n.data.length;a++)if(0!==n.data[a])return!1;return!0}function f(e,t,n,a){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\udde8\ud83c\uddf6","\ud83c\udde8\u200b\ud83c\uddf6")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!a(e,"\ud83e\udedf")}return!1}function g(e,t,n,a){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):s.createElement("canvas"),o=r.getContext("2d",{willReadFrequently:!0}),i=(o.textBaseline="top",o.font="600 32px Arial",{});return e.forEach(function(e){i[e]=t(o,e,n,a)}),i}function t(e){var t=s.createElement("script");t.src=e,t.defer=!0,s.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",i=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){s.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+g.toString()+"("+[JSON.stringify(i),f.toString(),p.toString(),u.toString()].join(",")+"));",a=new Blob([e],{type:"text/javascript"}),r=new Worker(URL.createObjectURL(a),{name:"wpTestEmojiSupports"});return void(r.onmessage=function(e){c(n=e.data),r.terminate(),t(n)})}catch(e){}c(n=g(i,f,p,u))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);
</script>
<link rel='stylesheet' id='woosb-blocks-css' href='https://codecut.ai/wp-content/plugins/woo-product-bundle/assets/css/blocks.css?ver=8.3.2' media='all' />
<link rel='stylesheet' id='astra-theme-css-css' href='https://codecut.ai/wp-content/themes/astra/assets/css/minified/style.min.css?ver=4.11.12' media='all' />
<style id='astra-theme-css-inline-css'>
:root{--ast-post-nav-space:0;--ast-container-default-xlg-padding:6.67em;--ast-container-default-lg-padding:5.67em;--ast-container-default-slg-padding:4.34em;--ast-container-default-md-padding:3.34em;--ast-container-default-sm-padding:6.67em;--ast-container-default-xs-padding:2.4em;--ast-container-default-xxs-padding:1.4em;--ast-code-block-background:#EEEEEE;--ast-comment-inputs-background:#FAFAFA;--ast-normal-container-width:1230px;--ast-narrow-container-width:750px;--ast-blog-title-font-weight:normal;--ast-blog-meta-weight:inherit;--ast-global-color-primary:var(--ast-global-color-5);--ast-global-color-secondary:var(--ast-global-color-4);--ast-global-color-alternate-background:var(--ast-global-color-7);--ast-global-color-subtle-background:var(--ast-global-color-6);--ast-bg-style-guide:#F8FAFC;--ast-shadow-style-guide:0px 0px 4px 0 #00000057;--ast-global-dark-bg-style:#fff;--ast-global-dark-lfs:#fbfbfb;--ast-widget-bg-color:#fafafa;--ast-wc-container-head-bg-color:#fbfbfb;--ast-title-layout-bg:#eeeeee;--ast-search-border-color:#e7e7e7;--ast-lifter-hover-bg:#e6e6e6;--ast-gallery-block-color:#000;--srfm-color-input-label:var(--ast-global-color-2);}html{font-size:112.5%;}a,.page-title{color:var(--ast-global-color-0);}a:hover,a:focus{color:var(--ast-global-color-1);}body,button,input,select,textarea,.ast-button,.ast-custom-button{font-family:'Comfortaa',display;font-weight:400;font-size:18px;font-size:1rem;line-height:var(--ast-body-line-height,2em);}blockquote{color:var(--ast-global-color-3);}p,.entry-content p{margin-bottom:1.4em;}h1,h2,h3,h4,h5,h6,.entry-content :where(h1,h2,h3,h4,h5,h6),.site-title,.site-title a{font-weight:500;line-height:2em;}.ast-site-identity .site-title a{color:var(--ast-global-color-2);}.site-title{font-size:35px;font-size:1.9444444444444rem;display:none;}.site-header .site-description{font-size:15px;font-size:0.83333333333333rem;display:none;}.entry-title{font-size:26px;font-size:1.4444444444444rem;}.archive .ast-article-post .ast-article-inner,.blog .ast-article-post .ast-article-inner,.archive .ast-article-post .ast-article-inner:hover,.blog .ast-article-post .ast-article-inner:hover{overflow:hidden;}h1,.entry-content :where(h1){font-size:35px;font-size:1.9444444444444rem;font-weight:700;font-family:'Comfortaa',display;line-height:2.5em;}h2,.entry-content :where(h2){font-size:32px;font-size:1.7777777777778rem;font-weight:500;font-family:'Comfortaa',display;line-height:2.5em;}h3,.entry-content :where(h3){font-size:22px;font-size:1.2222222222222rem;font-weight:400;font-family:'Comfortaa',display;line-height:2.5em;}h4,.entry-content :where(h4){font-size:20px;font-size:1.1111111111111rem;line-height:3.5em;font-weight:400;font-family:'Comfortaa',display;text-decoration:initial;}h5,.entry-content :where(h5){font-size:18px;font-size:1rem;line-height:1.2em;}h6,.entry-content :where(h6){font-size:16px;font-size:0.88888888888889rem;line-height:1.25em;}::selection{background-color:var(--ast-global-color-0);color:#000000;}body,h1,h2,h3,h4,h5,h6,.entry-title a,.entry-content :where(h1,h2,h3,h4,h5,h6){color:var(--ast-global-color-3);}.tagcloud a:hover,.tagcloud a:focus,.tagcloud a.current-item{color:#000000;border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);}input:focus,input[type="text"]:focus,input[type="email"]:focus,input[type="url"]:focus,input[type="password"]:focus,input[type="reset"]:focus,input[type="search"]:focus,textarea:focus{border-color:var(--ast-global-color-0);}input[type="radio"]:checked,input[type=reset],input[type="checkbox"]:checked,input[type="checkbox"]:hover:checked,input[type="checkbox"]:focus:checked,input[type=range]::-webkit-slider-thumb{border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);box-shadow:none;}.site-footer a:hover + .post-count,.site-footer a:focus + .post-count{background:var(--ast-global-color-0);border-color:var(--ast-global-color-0);}.single .nav-links .nav-previous,.single .nav-links .nav-next{color:var(--ast-global-color-0);}.entry-meta,.entry-meta *{line-height:1.45;color:var(--ast-global-color-0);}.entry-meta a:not(.ast-button):hover,.entry-meta a:not(.ast-button):hover *,.entry-meta a:not(.ast-button):focus,.entry-meta a:not(.ast-button):focus *,.page-links > .page-link,.page-links .page-link:hover,.post-navigation a:hover{color:var(--ast-global-color-1);}#cat option,.secondary .calendar_wrap thead a,.secondary .calendar_wrap thead a:visited{color:var(--ast-global-color-0);}.secondary .calendar_wrap #today,.ast-progress-val span{background:var(--ast-global-color-0);}.secondary a:hover + .post-count,.secondary a:focus + .post-count{background:var(--ast-global-color-0);border-color:var(--ast-global-color-0);}.calendar_wrap #today > a{color:#000000;}.page-links .page-link,.single .post-navigation a{color:var(--ast-global-color-0);}.ast-search-menu-icon .search-form button.search-submit{padding:0 4px;}.ast-search-menu-icon form.search-form{padding-right:0;}.ast-header-search .ast-search-menu-icon.ast-dropdown-active .search-form,.ast-header-search .ast-search-menu-icon.ast-dropdown-active .search-field:focus{transition:all 0.2s;}.search-form input.search-field:focus{outline:none;}.wp-block-latest-posts > li > a{color:var(--ast-global-color-5);}.widget-title,.widget .wp-block-heading{font-size:25px;font-size:1.3888888888889rem;color:var(--ast-global-color-3);}.ast-search-menu-icon.slide-search a:focus-visible:focus-visible,.astra-search-icon:focus-visible,#close:focus-visible,a:focus-visible,.ast-menu-toggle:focus-visible,.site .skip-link:focus-visible,.wp-block-loginout input:focus-visible,.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper,.ast-header-navigation-arrow:focus-visible,.woocommerce .wc-proceed-to-checkout > .checkout-button:focus-visible,.woocommerce .woocommerce-MyAccount-navigation ul li a:focus-visible,.ast-orders-table__row .ast-orders-table__cell:focus-visible,.woocommerce .woocommerce-order-details .order-again > .button:focus-visible,.woocommerce .woocommerce-message a.button.wc-forward:focus-visible,.woocommerce #minus_qty:focus-visible,.woocommerce #plus_qty:focus-visible,a#ast-apply-coupon:focus-visible,.woocommerce .woocommerce-info a:focus-visible,.woocommerce .astra-shop-summary-wrap a:focus-visible,.woocommerce a.wc-forward:focus-visible,#ast-apply-coupon:focus-visible,.woocommerce-js .woocommerce-mini-cart-item a.remove:focus-visible,#close:focus-visible,.button.search-submit:focus-visible,#search_submit:focus,.normal-search:focus-visible,.ast-header-account-wrap:focus-visible,.woocommerce .ast-on-card-button.ast-quick-view-trigger:focus,.astra-cart-drawer-close:focus,.ast-single-variation:focus,.ast-woocommerce-product-gallery__image:focus,.ast-button:focus,.woocommerce-product-gallery--with-images [data-controls="prev"]:focus-visible,.woocommerce-product-gallery--with-images [data-controls="next"]:focus-visible{outline-style:dotted;outline-color:inherit;outline-width:thin;}input:focus,input[type="text"]:focus,input[type="email"]:focus,input[type="url"]:focus,input[type="password"]:focus,input[type="reset"]:focus,input[type="search"]:focus,input[type="number"]:focus,textarea:focus,.wp-block-search__input:focus,[data-section="section-header-mobile-trigger"] .ast-button-wrap .ast-mobile-menu-trigger-minimal:focus,.ast-mobile-popup-drawer.active .menu-toggle-close:focus,.woocommerce-ordering select.orderby:focus,#ast-scroll-top:focus,#coupon_code:focus,.woocommerce-page #comment:focus,.woocommerce #reviews #respond input#submit:focus,.woocommerce a.add_to_cart_button:focus,.woocommerce .button.single_add_to_cart_button:focus,.woocommerce .woocommerce-cart-form button:focus,.woocommerce .woocommerce-cart-form__cart-item .quantity .qty:focus,.woocommerce .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper .woocommerce-input-wrapper > .input-text:focus,.woocommerce #order_comments:focus,.woocommerce #place_order:focus,.woocommerce .woocommerce-address-fields .woocommerce-address-fields__field-wrapper .woocommerce-input-wrapper > .input-text:focus,.woocommerce .woocommerce-MyAccount-content form button:focus,.woocommerce .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row .woocommerce-Input.input-text:focus,.woocommerce .ast-woocommerce-container .woocommerce-pagination ul.page-numbers li a:focus,body #content .woocommerce form .form-row .select2-container--default .select2-selection--single:focus,#ast-coupon-code:focus,.woocommerce.woocommerce-js .quantity input[type=number]:focus,.woocommerce-js .woocommerce-mini-cart-item .quantity input[type=number]:focus,.woocommerce p#ast-coupon-trigger:focus{border-style:dotted;border-color:inherit;border-width:thin;}input{outline:none;}.woocommerce-js input[type=text]:focus,.woocommerce-js input[type=email]:focus,.woocommerce-js textarea:focus,input[type=number]:focus,.comments-area textarea#comment:focus,.comments-area textarea#comment:active,.comments-area .ast-comment-formwrap input[type="text"]:focus,.comments-area .ast-comment-formwrap input[type="text"]:active{outline-style:unset;outline-color:inherit;outline-width:thin;}.main-header-menu .menu-link,.ast-header-custom-item a{color:var(--ast-global-color-3);}.main-header-menu .menu-item:hover > .menu-link,.main-header-menu .menu-item:hover > .ast-menu-toggle,.main-header-menu .ast-masthead-custom-menu-items a:hover,.main-header-menu .menu-item.focus > .menu-link,.main-header-menu .menu-item.focus > .ast-menu-toggle,.main-header-menu .current-menu-item > .menu-link,.main-header-menu .current-menu-ancestor > .menu-link,.main-header-menu .current-menu-item > .ast-menu-toggle,.main-header-menu .current-menu-ancestor > .ast-menu-toggle{color:var(--ast-global-color-0);}.header-main-layout-3 .ast-main-header-bar-alignment{margin-right:auto;}.header-main-layout-2 .site-header-section-left .ast-site-identity{text-align:left;}.ast-logo-title-inline .site-logo-img{padding-right:1em;}.site-logo-img img{ transition:all 0.2s linear;}body .ast-oembed-container *{position:absolute;top:0;width:100%;height:100%;left:0;}body .wp-block-embed-pocket-casts .ast-oembed-container *{position:unset;}.ast-header-break-point .ast-mobile-menu-buttons-minimal.menu-toggle{background:transparent;color:var(--ast-global-color-0);}.ast-header-break-point .ast-mobile-menu-buttons-outline.menu-toggle{background:transparent;border:1px solid var(--ast-global-color-0);color:var(--ast-global-color-0);}.ast-header-break-point .ast-mobile-menu-buttons-fill.menu-toggle{background:var(--ast-global-color-0);}.ast-single-post-featured-section + article {margin-top: 2em;}.site-content .ast-single-post-featured-section img {width: 100%;overflow: hidden;object-fit: cover;}.site > .ast-single-related-posts-container {margin-top: 0;}@media (min-width: 922px) {.ast-desktop .ast-container--narrow {max-width: var(--ast-narrow-container-width);margin: 0 auto;}}.ast-page-builder-template .hentry {margin: 0;}.ast-page-builder-template .site-content > .ast-container {max-width: 100%;padding: 0;}.ast-page-builder-template .site .site-content #primary {padding: 0;margin: 0;}.ast-page-builder-template .no-results {text-align: center;margin: 4em auto;}.ast-page-builder-template .ast-pagination {padding: 2em;}.ast-page-builder-template .entry-header.ast-no-title.ast-no-thumbnail {margin-top: 0;}.ast-page-builder-template .entry-header.ast-header-without-markup {margin-top: 0;margin-bottom: 0;}.ast-page-builder-template .entry-header.ast-no-title.ast-no-meta {margin-bottom: 0;}.ast-page-builder-template.single .post-navigation {padding-bottom: 2em;}.ast-page-builder-template.single-post .site-content > .ast-container {max-width: 100%;}.ast-page-builder-template .entry-header {margin-top: 4em;margin-left: auto;margin-right: auto;padding-left: 20px;padding-right: 20px;}.single.ast-page-builder-template .entry-header {padding-left: 20px;padding-right: 20px;}.ast-page-builder-template .ast-archive-description {margin: 4em auto 0;padding-left: 20px;padding-right: 20px;}.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide {margin-left: 0;margin-right: 0;}.footer-adv .footer-adv-overlay{border-top-style:solid;border-top-color:#7a7a7a;}@media( max-width: 420px ) {.single .nav-links .nav-previous,.single .nav-links .nav-next {width: 100%;text-align: center;}}.wp-block-buttons.aligncenter{justify-content:center;}@media (max-width:921px){.ast-theme-transparent-header #primary,.ast-theme-transparent-header #secondary{padding:0;}}@media (max-width:921px){.ast-plain-container.ast-no-sidebar #primary{padding:0;}}.ast-plain-container.ast-no-sidebar #primary{margin-top:0;margin-bottom:0;}.wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button,.ast-outline-button,.wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button{border-top-width:2px;border-right-width:2px;border-bottom-width:2px;border-left-width:2px;font-family:inherit;font-weight:inherit;line-height:1em;}.wp-block-button .wp-block-button__link.wp-element-button.is-style-outline:not(.has-background),.wp-block-button.is-style-outline>.wp-block-button__link.wp-element-button:not(.has-background),.ast-outline-button{background-color:transparent;}.entry-content[data-ast-blocks-layout] > figure{margin-bottom:1em;}.elementor-widget-container .elementor-loop-container .e-loop-item[data-elementor-type="loop-item"]{width:100%;}.review-rating{display:flex;align-items:center;order:2;}@media (max-width:921px){.ast-left-sidebar #content > .ast-container{display:flex;flex-direction:column-reverse;width:100%;}.ast-separate-container .ast-article-post,.ast-separate-container .ast-article-single{padding:1.5em 2.14em;}.ast-author-box img.avatar{margin:20px 0 0 0;}}@media (min-width:922px){.ast-separate-container.ast-right-sidebar #primary,.ast-separate-container.ast-left-sidebar #primary{border:0;}.search-no-results.ast-separate-container #primary{margin-bottom:4em;}}.elementor-widget-button .elementor-button{border-style:solid;text-decoration:none;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;}body .elementor-button.elementor-size-sm,body .elementor-button.elementor-size-xs,body .elementor-button.elementor-size-md,body .elementor-button.elementor-size-lg,body .elementor-button.elementor-size-xl,body .elementor-button{padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;}.elementor-widget-button .elementor-button{border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);}.elementor-widget-button .elementor-button:hover,.elementor-widget-button .elementor-button:focus{color:#000000;background-color:var(--ast-global-color-1);border-color:var(--ast-global-color-1);}.wp-block-button .wp-block-button__link ,.elementor-widget-button .elementor-button,.elementor-widget-button .elementor-button:visited{color:#000000;}.elementor-widget-button .elementor-button{line-height:1em;}.wp-block-button .wp-block-button__link:hover,.wp-block-button .wp-block-button__link:focus{color:#000000;background-color:var(--ast-global-color-1);border-color:var(--ast-global-color-1);}.elementor-widget-heading h1.elementor-heading-title{line-height:2.5em;}.elementor-widget-heading h2.elementor-heading-title{line-height:2.5em;}.elementor-widget-heading h3.elementor-heading-title{line-height:2.5em;}.elementor-widget-heading h4.elementor-heading-title{line-height:3.5em;}.elementor-widget-heading h5.elementor-heading-title{line-height:1.2em;}.elementor-widget-heading h6.elementor-heading-title{line-height:1.25em;}.wp-block-button .wp-block-button__link,.wp-block-search .wp-block-search__button,body .wp-block-file .wp-block-file__button{border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);color:#000000;font-family:inherit;font-weight:inherit;line-height:1em;padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;}.menu-toggle,button,.ast-button,.ast-custom-button,.button,input#submit,input[type="button"],input[type="submit"],input[type="reset"],form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button,body .wp-block-file .wp-block-file__button{border-style:solid;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;color:#000000;border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;font-family:inherit;font-weight:inherit;line-height:1em;}button:focus,.menu-toggle:hover,button:hover,.ast-button:hover,.ast-custom-button:hover .button:hover,.ast-custom-button:hover ,input[type=reset]:hover,input[type=reset]:focus,input#submit:hover,input#submit:focus,input[type="button"]:hover,input[type="button"]:focus,input[type="submit"]:hover,input[type="submit"]:focus,form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button:hover,form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button:focus,body .wp-block-file .wp-block-file__button:hover,body .wp-block-file .wp-block-file__button:focus{color:#000000;background-color:var(--ast-global-color-1);border-color:var(--ast-global-color-1);}@media (max-width:921px){.ast-mobile-header-stack .main-header-bar .ast-search-menu-icon{display:inline-block;}.ast-header-break-point.ast-header-custom-item-outside .ast-mobile-header-stack .main-header-bar .ast-search-icon{margin:0;}.ast-comment-avatar-wrap img{max-width:2.5em;}.ast-comment-meta{padding:0 1.8888em 1.3333em;}.ast-separate-container .ast-comment-list li.depth-1{padding:1.5em 2.14em;}.ast-separate-container .comment-respond{padding:2em 2.14em;}}@media (min-width:544px){.ast-container{max-width:100%;}}@media (max-width:544px){.ast-separate-container .ast-article-post,.ast-separate-container .ast-article-single,.ast-separate-container .comments-title,.ast-separate-container .ast-archive-description{padding:1.5em 1em;}.ast-separate-container #content .ast-container{padding-left:0.54em;padding-right:0.54em;}.ast-separate-container .ast-comment-list .bypostauthor{padding:.5em;}.ast-search-menu-icon.ast-dropdown-active .search-field{width:170px;}.site-branding img,.site-header .site-logo-img .custom-logo-link img{max-width:100%;}} #ast-mobile-header .ast-site-header-cart-li a{pointer-events:none;}body,.ast-separate-container{background-color:#2f2d2e;background-image:none;}@media (max-width:921px){.widget-title{font-size:25px;font-size:1.3888888888889rem;}body,button,input,select,textarea,.ast-button,.ast-custom-button{font-size:18px;font-size:1rem;}#secondary,#secondary button,#secondary input,#secondary select,#secondary textarea{font-size:18px;font-size:1rem;}.site-title{display:none;}.site-header .site-description{display:none;}h1,.entry-content :where(h1){font-size:30px;}h2,.entry-content :where(h2){font-size:25px;}h3,.entry-content :where(h3){font-size:20px;}}@media (max-width:544px){.site-title{display:none;}.site-header .site-description{display:none;}h1,.entry-content :where(h1){font-size:30px;}h2,.entry-content :where(h2){font-size:25px;}h3,.entry-content :where(h3){font-size:20px;}}@media (max-width:544px){html{font-size:102.6%;}}@media (min-width:922px){.ast-container{max-width:1270px;}}@font-face {font-family: "Astra";src: url(https://codecut.ai/wp-content/themes/astra/assets/fonts/astra.woff) format("woff"),url(https://codecut.ai/wp-content/themes/astra/assets/fonts/astra.ttf) format("truetype"),url(https://codecut.ai/wp-content/themes/astra/assets/fonts/astra.svg#astra) format("svg");font-weight: normal;font-style: normal;font-display: fallback;}@media (max-width:10px) {.main-header-bar .main-header-bar-navigation{display:none;}}.ast-desktop .main-header-menu.submenu-with-border .sub-menu,.ast-desktop .main-header-menu.submenu-with-border .astra-full-megamenu-wrapper{border-color:var(--ast-global-color-0);}.ast-desktop .main-header-menu.submenu-with-border .sub-menu{border-top-width:2px;border-right-width:2px;border-left-width:2px;border-bottom-width:2px;border-style:solid;}.ast-desktop .main-header-menu.submenu-with-border .sub-menu .sub-menu{top:-2px;}.ast-desktop .main-header-menu.submenu-with-border .sub-menu .menu-link,.ast-desktop .main-header-menu.submenu-with-border .children .menu-link{border-bottom-width:1px;border-style:solid;border-color:#eaeaea;}@media (min-width:922px){.main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu:hover > .sub-menu,.main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu.focus > .sub-menu{margin-left:-4px;}}.ast-small-footer{border-top-style:solid;border-top-width:1px;border-top-color:#7a7a7a;}.ast-small-footer-wrap{text-align:center;}.site .comments-area{padding-bottom:3em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .main-header-bar-navigation .ast-search-icon {display: none;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-form {padding: 0;display: block;overflow: hidden;}.ast-header-break-point .ast-header-custom-item .widget:last-child {margin-bottom: 1em;}.ast-header-custom-item .widget {margin: 0.5em;display: inline-block;vertical-align: middle;}.ast-header-custom-item .widget p {margin-bottom: 0;}.ast-header-custom-item .widget li {width: auto;}.ast-header-custom-item-inside .button-custom-menu-item .menu-link {display: none;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item .ast-custom-button-link {display: none;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item .menu-link {display: block;}.ast-header-break-point.ast-header-custom-item-outside .main-header-bar .ast-search-icon {margin-right: 1em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-field,.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon.ast-inline-search .search-field {width: 100%;padding-right: 5.5em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-submit {display: block;position: absolute;height: 100%;top: 0;right: 0;padding: 0 1em;border-radius: 0;}.ast-header-break-point .ast-header-custom-item .ast-masthead-custom-menu-items {padding-left: 20px;padding-right: 20px;margin-bottom: 1em;margin-top: 1em;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item {padding-left: 0;padding-right: 0;margin-top: 0;margin-bottom: 0;}.astra-icon-down_arrow::after {content: "\e900";font-family: Astra;}.astra-icon-close::after {content: "\e5cd";font-family: Astra;}.astra-icon-drag_handle::after {content: "\e25d";font-family: Astra;}.astra-icon-format_align_justify::after {content: "\e235";font-family: Astra;}.astra-icon-menu::after {content: "\e5d2";font-family: Astra;}.astra-icon-reorder::after {content: "\e8fe";font-family: Astra;}.astra-icon-search::after {content: "\e8b6";font-family: Astra;}.astra-icon-zoom_in::after {content: "\e56b";font-family: Astra;}.astra-icon-check-circle::after {content: "\e901";font-family: Astra;}.astra-icon-shopping-cart::after {content: "\f07a";font-family: Astra;}.astra-icon-shopping-bag::after {content: "\f290";font-family: Astra;}.astra-icon-shopping-basket::after {content: "\f291";font-family: Astra;}.astra-icon-circle-o::after {content: "\e903";font-family: Astra;}.astra-icon-certificate::after {content: "\e902";font-family: Astra;}.wp-block-file {display: flex;align-items: center;flex-wrap: wrap;justify-content: space-between;}.wp-block-pullquote {border: none;}.wp-block-pullquote blockquote::before {content: "\201D";font-family: "Helvetica",sans-serif;display: flex;transform: rotate( 180deg );font-size: 6rem;font-style: normal;line-height: 1;font-weight: bold;align-items: center;justify-content: center;}.has-text-align-right > blockquote::before {justify-content: flex-start;}.has-text-align-left > blockquote::before {justify-content: flex-end;}figure.wp-block-pullquote.is-style-solid-color blockquote {max-width: 100%;text-align: inherit;}:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 3em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 3em;--wp--custom--ast-container-width: 1230px;--wp--custom--ast-content-width-size: 910px;--wp--custom--ast-wide-width-size: 1230px;}.ast-narrow-container {--wp--custom--ast-content-width-size: 750px;--wp--custom--ast-wide-width-size: 750px;}@media(max-width: 921px) {:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 2em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 2em;}}@media(max-width: 544px) {:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 1.5em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 1.5em;}}.entry-content > .wp-block-group,.entry-content > .wp-block-cover,.entry-content > .wp-block-columns {padding-top: var(--wp--custom--ast-default-block-top-padding);padding-right: var(--wp--custom--ast-default-block-right-padding);padding-bottom: var(--wp--custom--ast-default-block-bottom-padding);padding-left: var(--wp--custom--ast-default-block-left-padding);}.ast-plain-container.ast-no-sidebar .entry-content .alignfull,.ast-page-builder-template .ast-no-sidebar .entry-content .alignfull {margin-left: calc( -50vw + 50%);margin-right: calc( -50vw + 50%);max-width: 100vw;width: 100vw;}.ast-plain-container.ast-no-sidebar .entry-content .alignfull .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .alignfull .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .alignfull .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .alignfull .alignwide,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide .alignwide,.ast-plain-container.ast-no-sidebar .entry-content .wp-block-column .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-column .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .wp-block-column .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-column .alignwide {margin-left: auto;margin-right: auto;width: 100%;}[data-ast-blocks-layout] .wp-block-separator:not(.is-style-dots) {height: 0;}[data-ast-blocks-layout] .wp-block-separator {margin: 20px auto;}[data-ast-blocks-layout] .wp-block-separator:not(.is-style-wide):not(.is-style-dots) {max-width: 100px;}[data-ast-blocks-layout] .wp-block-separator.has-background {padding: 0;}.entry-content[data-ast-blocks-layout] > * {max-width: var(--wp--custom--ast-content-width-size);margin-left: auto;margin-right: auto;}.entry-content[data-ast-blocks-layout] > .alignwide,.entry-content[data-ast-blocks-layout] .wp-block-cover__inner-container,.entry-content[data-ast-blocks-layout] > p {max-width: var(--wp--custom--ast-wide-width-size);}.entry-content[data-ast-blocks-layout] .alignfull {max-width: none;}.entry-content .wp-block-columns {margin-bottom: 0;}blockquote {margin: 1.5em;border-color: rgba(0,0,0,0.05);}.wp-block-quote:not(.has-text-align-right):not(.has-text-align-center) {border-left: 5px solid rgba(0,0,0,0.05);}.has-text-align-right > blockquote,blockquote.has-text-align-right {border-right: 5px solid rgba(0,0,0,0.05);}.has-text-align-left > blockquote,blockquote.has-text-align-left {border-left: 5px solid rgba(0,0,0,0.05);}.wp-block-site-tagline,.wp-block-latest-posts .read-more {margin-top: 15px;}.wp-block-loginout p label {display: block;}.wp-block-loginout p:not(.login-remember):not(.login-submit) input {width: 100%;}.wp-block-loginout input:focus {border-color: transparent;}.wp-block-loginout input:focus {outline: thin dotted;}.entry-content .wp-block-media-text .wp-block-media-text__content {padding: 0 0 0 8%;}.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {padding: 0 8% 0 0;}.entry-content .wp-block-media-text.has-background .wp-block-media-text__content {padding: 8%;}.entry-content .wp-block-cover:not([class*="background-color"]):not(.has-text-color.has-link-color) .wp-block-cover__inner-container,.entry-content .wp-block-cover:not([class*="background-color"]) .wp-block-cover-image-text,.entry-content .wp-block-cover:not([class*="background-color"]) .wp-block-cover-text,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover__inner-container,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover-image-text,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover-text {color: var(--ast-global-color-primary,var(--ast-global-color-5));}.wp-block-loginout .login-remember input {width: 1.1rem;height: 1.1rem;margin: 0 5px 4px 0;vertical-align: middle;}.wp-block-latest-posts > li > *:first-child,.wp-block-latest-posts:not(.is-grid) > li:first-child {margin-top: 0;}.entry-content > .wp-block-buttons,.entry-content > .wp-block-uagb-buttons {margin-bottom: 1.5em;}.wp-block-latest-posts > li > a {font-size: 28px;}.wp-block-latest-posts > li > *,.wp-block-latest-posts:not(.is-grid) > li {margin-top: 15px;margin-bottom: 15px;}.wp-block-latest-posts .wp-block-latest-posts__post-date,.wp-block-latest-posts .wp-block-latest-posts__post-author {font-size: 15px;}@media (max-width:544px){.wp-block-columns .wp-block-column:not(:last-child){margin-bottom:20px;}.wp-block-latest-posts{margin:0;}}@media( max-width: 600px ) {.entry-content .wp-block-media-text .wp-block-media-text__content,.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {padding: 8% 0 0;}.entry-content .wp-block-media-text.has-background .wp-block-media-text__content {padding: 8%;}}.ast-narrow-container .site-content .wp-block-uagb-image--align-full .wp-block-uagb-image__figure {max-width: 100%;margin-left: auto;margin-right: auto;}:root .has-ast-global-color-0-color{color:var(--ast-global-color-0);}:root .has-ast-global-color-0-background-color{background-color:var(--ast-global-color-0);}:root .wp-block-button .has-ast-global-color-0-color{color:var(--ast-global-color-0);}:root .wp-block-button .has-ast-global-color-0-background-color{background-color:var(--ast-global-color-0);}:root .has-ast-global-color-1-color{color:var(--ast-global-color-1);}:root .has-ast-global-color-1-background-color{background-color:var(--ast-global-color-1);}:root .wp-block-button .has-ast-global-color-1-color{color:var(--ast-global-color-1);}:root .wp-block-button .has-ast-global-color-1-background-color{background-color:var(--ast-global-color-1);}:root .has-ast-global-color-2-color{color:var(--ast-global-color-2);}:root .has-ast-global-color-2-background-color{background-color:var(--ast-global-color-2);}:root .wp-block-button .has-ast-global-color-2-color{color:var(--ast-global-color-2);}:root .wp-block-button .has-ast-global-color-2-background-color{background-color:var(--ast-global-color-2);}:root .has-ast-global-color-3-color{color:var(--ast-global-color-3);}:root .has-ast-global-color-3-background-color{background-color:var(--ast-global-color-3);}:root .wp-block-button .has-ast-global-color-3-color{color:var(--ast-global-color-3);}:root .wp-block-button .has-ast-global-color-3-background-color{background-color:var(--ast-global-color-3);}:root .has-ast-global-color-4-color{color:var(--ast-global-color-4);}:root .has-ast-global-color-4-background-color{background-color:var(--ast-global-color-4);}:root .wp-block-button .has-ast-global-color-4-color{color:var(--ast-global-color-4);}:root .wp-block-button .has-ast-global-color-4-background-color{background-color:var(--ast-global-color-4);}:root .has-ast-global-color-5-color{color:var(--ast-global-color-5);}:root .has-ast-global-color-5-background-color{background-color:var(--ast-global-color-5);}:root .wp-block-button .has-ast-global-color-5-color{color:var(--ast-global-color-5);}:root .wp-block-button .has-ast-global-color-5-background-color{background-color:var(--ast-global-color-5);}:root .has-ast-global-color-6-color{color:var(--ast-global-color-6);}:root .has-ast-global-color-6-background-color{background-color:var(--ast-global-color-6);}:root .wp-block-button .has-ast-global-color-6-color{color:var(--ast-global-color-6);}:root .wp-block-button .has-ast-global-color-6-background-color{background-color:var(--ast-global-color-6);}:root .has-ast-global-color-7-color{color:var(--ast-global-color-7);}:root .has-ast-global-color-7-background-color{background-color:var(--ast-global-color-7);}:root .wp-block-button .has-ast-global-color-7-color{color:var(--ast-global-color-7);}:root .wp-block-button .has-ast-global-color-7-background-color{background-color:var(--ast-global-color-7);}:root .has-ast-global-color-8-color{color:var(--ast-global-color-8);}:root .has-ast-global-color-8-background-color{background-color:var(--ast-global-color-8);}:root .wp-block-button .has-ast-global-color-8-color{color:var(--ast-global-color-8);}:root .wp-block-button .has-ast-global-color-8-background-color{background-color:var(--ast-global-color-8);}:root{--ast-global-color-0:#72befa;--ast-global-color-1:#e583b6;--ast-global-color-2:#1e293b;--ast-global-color-3:#c9c8c8;--ast-global-color-4:#ffffff;--ast-global-color-5:#ffffff;--ast-global-color-6:#2f2d2e;--ast-global-color-7:#2f2d2e;--ast-global-color-8:#94a3b8;}:root {--ast-border-color : #dddddd;}#masthead .ast-container,.ast-header-breadcrumb .ast-container{max-width:100%;padding-left:35px;padding-right:35px;}@media (max-width:10px){#masthead .ast-container,.ast-header-breadcrumb .ast-container{padding-left:20px;padding-right:20px;}}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .main-header-bar-navigation .ast-search-icon {display: none;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-form {padding: 0;display: block;overflow: hidden;}.ast-header-break-point .ast-header-custom-item .widget:last-child {margin-bottom: 1em;}.ast-header-custom-item .widget {margin: 0.5em;display: inline-block;vertical-align: middle;}.ast-header-custom-item .widget p {margin-bottom: 0;}.ast-header-custom-item .widget li {width: auto;}.ast-header-custom-item-inside .button-custom-menu-item .menu-link {display: none;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item .ast-custom-button-link {display: none;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item .menu-link {display: block;}.ast-header-break-point.ast-header-custom-item-outside .main-header-bar .ast-search-icon {margin-right: 1em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-field,.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon.ast-inline-search .search-field {width: 100%;padding-right: 5.5em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-submit {display: block;position: absolute;height: 100%;top: 0;right: 0;padding: 0 1em;border-radius: 0;}.ast-header-break-point .ast-header-custom-item .ast-masthead-custom-menu-items {padding-left: 20px;padding-right: 20px;margin-bottom: 1em;margin-top: 1em;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item {padding-left: 0;padding-right: 0;margin-top: 0;margin-bottom: 0;}.astra-icon-down_arrow::after {content: "\e900";font-family: Astra;}.astra-icon-close::after {content: "\e5cd";font-family: Astra;}.astra-icon-drag_handle::after {content: "\e25d";font-family: Astra;}.astra-icon-format_align_justify::after {content: "\e235";font-family: Astra;}.astra-icon-menu::after {content: "\e5d2";font-family: Astra;}.astra-icon-reorder::after {content: "\e8fe";font-family: Astra;}.astra-icon-search::after {content: "\e8b6";font-family: Astra;}.astra-icon-zoom_in::after {content: "\e56b";font-family: Astra;}.astra-icon-check-circle::after {content: "\e901";font-family: Astra;}.astra-icon-shopping-cart::after {content: "\f07a";font-family: Astra;}.astra-icon-shopping-bag::after {content: "\f290";font-family: Astra;}.astra-icon-shopping-basket::after {content: "\f291";font-family: Astra;}.astra-icon-circle-o::after {content: "\e903";font-family: Astra;}.astra-icon-certificate::after {content: "\e902";font-family: Astra;}.wp-block-file {display: flex;align-items: center;flex-wrap: wrap;justify-content: space-between;}.wp-block-pullquote {border: none;}.wp-block-pullquote blockquote::before {content: "\201D";font-family: "Helvetica",sans-serif;display: flex;transform: rotate( 180deg );font-size: 6rem;font-style: normal;line-height: 1;font-weight: bold;align-items: center;justify-content: center;}.has-text-align-right > blockquote::before {justify-content: flex-start;}.has-text-align-left > blockquote::before {justify-content: flex-end;}figure.wp-block-pullquote.is-style-solid-color blockquote {max-width: 100%;text-align: inherit;}:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 3em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 3em;--wp--custom--ast-container-width: 1230px;--wp--custom--ast-content-width-size: 910px;--wp--custom--ast-wide-width-size: 1230px;}.ast-narrow-container {--wp--custom--ast-content-width-size: 750px;--wp--custom--ast-wide-width-size: 750px;}@media(max-width: 921px) {:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 2em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 2em;}}@media(max-width: 544px) {:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 1.5em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 1.5em;}}.entry-content > .wp-block-group,.entry-content > .wp-block-cover,.entry-content > .wp-block-columns {padding-top: var(--wp--custom--ast-default-block-top-padding);padding-right: var(--wp--custom--ast-default-block-right-padding);padding-bottom: var(--wp--custom--ast-default-block-bottom-padding);padding-left: var(--wp--custom--ast-default-block-left-padding);}.ast-plain-container.ast-no-sidebar .entry-content .alignfull,.ast-page-builder-template .ast-no-sidebar .entry-content .alignfull {margin-left: calc( -50vw + 50%);margin-right: calc( -50vw + 50%);max-width: 100vw;width: 100vw;}.ast-plain-container.ast-no-sidebar .entry-content .alignfull .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .alignfull .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .alignfull .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .alignfull .alignwide,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide .alignwide,.ast-plain-container.ast-no-sidebar .entry-content .wp-block-column .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-column .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .wp-block-column .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-column .alignwide {margin-left: auto;margin-right: auto;width: 100%;}[data-ast-blocks-layout] .wp-block-separator:not(.is-style-dots) {height: 0;}[data-ast-blocks-layout] .wp-block-separator {margin: 20px auto;}[data-ast-blocks-layout] .wp-block-separator:not(.is-style-wide):not(.is-style-dots) {max-width: 100px;}[data-ast-blocks-layout] .wp-block-separator.has-background {padding: 0;}.entry-content[data-ast-blocks-layout] > * {max-width: var(--wp--custom--ast-content-width-size);margin-left: auto;margin-right: auto;}.entry-content[data-ast-blocks-layout] > .alignwide,.entry-content[data-ast-blocks-layout] .wp-block-cover__inner-container,.entry-content[data-ast-blocks-layout] > p {max-width: var(--wp--custom--ast-wide-width-size);}.entry-content[data-ast-blocks-layout] .alignfull {max-width: none;}.entry-content .wp-block-columns {margin-bottom: 0;}blockquote {margin: 1.5em;border-color: rgba(0,0,0,0.05);}.wp-block-quote:not(.has-text-align-right):not(.has-text-align-center) {border-left: 5px solid rgba(0,0,0,0.05);}.has-text-align-right > blockquote,blockquote.has-text-align-right {border-right: 5px solid rgba(0,0,0,0.05);}.has-text-align-left > blockquote,blockquote.has-text-align-left {border-left: 5px solid rgba(0,0,0,0.05);}.wp-block-site-tagline,.wp-block-latest-posts .read-more {margin-top: 15px;}.wp-block-loginout p label {display: block;}.wp-block-loginout p:not(.login-remember):not(.login-submit) input {width: 100%;}.wp-block-loginout input:focus {border-color: transparent;}.wp-block-loginout input:focus {outline: thin dotted;}.entry-content .wp-block-media-text .wp-block-media-text__content {padding: 0 0 0 8%;}.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {padding: 0 8% 0 0;}.entry-content .wp-block-media-text.has-background .wp-block-media-text__content {padding: 8%;}.entry-content .wp-block-cover:not([class*="background-color"]):not(.has-text-color.has-link-color) .wp-block-cover__inner-container,.entry-content .wp-block-cover:not([class*="background-color"]) .wp-block-cover-image-text,.entry-content .wp-block-cover:not([class*="background-color"]) .wp-block-cover-text,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover__inner-container,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover-image-text,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover-text {color: var(--ast-global-color-primary,var(--ast-global-color-5));}.wp-block-loginout .login-remember input {width: 1.1rem;height: 1.1rem;margin: 0 5px 4px 0;vertical-align: middle;}.wp-block-latest-posts > li > *:first-child,.wp-block-latest-posts:not(.is-grid) > li:first-child {margin-top: 0;}.entry-content > .wp-block-buttons,.entry-content > .wp-block-uagb-buttons {margin-bottom: 1.5em;}.wp-block-latest-posts > li > a {font-size: 28px;}.wp-block-latest-posts > li > *,.wp-block-latest-posts:not(.is-grid) > li {margin-top: 15px;margin-bottom: 15px;}.wp-block-latest-posts .wp-block-latest-posts__post-date,.wp-block-latest-posts .wp-block-latest-posts__post-author {font-size: 15px;}@media (max-width:544px){.wp-block-columns .wp-block-column:not(:last-child){margin-bottom:20px;}.wp-block-latest-posts{margin:0;}}@media( max-width: 600px ) {.entry-content .wp-block-media-text .wp-block-media-text__content,.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {padding: 8% 0 0;}.entry-content .wp-block-media-text.has-background .wp-block-media-text__content {padding: 8%;}}.ast-narrow-container .site-content .wp-block-uagb-image--align-full .wp-block-uagb-image__figure {max-width: 100%;margin-left: auto;margin-right: auto;}:root .has-ast-global-color-0-color{color:var(--ast-global-color-0);}:root .has-ast-global-color-0-background-color{background-color:var(--ast-global-color-0);}:root .wp-block-button .has-ast-global-color-0-color{color:var(--ast-global-color-0);}:root .wp-block-button .has-ast-global-color-0-background-color{background-color:var(--ast-global-color-0);}:root .has-ast-global-color-1-color{color:var(--ast-global-color-1);}:root .has-ast-global-color-1-background-color{background-color:var(--ast-global-color-1);}:root .wp-block-button .has-ast-global-color-1-color{color:var(--ast-global-color-1);}:root .wp-block-button .has-ast-global-color-1-background-color{background-color:var(--ast-global-color-1);}:root .has-ast-global-color-2-color{color:var(--ast-global-color-2);}:root .has-ast-global-color-2-background-color{background-color:var(--ast-global-color-2);}:root .wp-block-button .has-ast-global-color-2-color{color:var(--ast-global-color-2);}:root .wp-block-button .has-ast-global-color-2-background-color{background-color:var(--ast-global-color-2);}:root .has-ast-global-color-3-color{color:var(--ast-global-color-3);}:root .has-ast-global-color-3-background-color{background-color:var(--ast-global-color-3);}:root .wp-block-button .has-ast-global-color-3-color{color:var(--ast-global-color-3);}:root .wp-block-button .has-ast-global-color-3-background-color{background-color:var(--ast-global-color-3);}:root .has-ast-global-color-4-color{color:var(--ast-global-color-4);}:root .has-ast-global-color-4-background-color{background-color:var(--ast-global-color-4);}:root .wp-block-button .has-ast-global-color-4-color{color:var(--ast-global-color-4);}:root .wp-block-button .has-ast-global-color-4-background-color{background-color:var(--ast-global-color-4);}:root .has-ast-global-color-5-color{color:var(--ast-global-color-5);}:root .has-ast-global-color-5-background-color{background-color:var(--ast-global-color-5);}:root .wp-block-button .has-ast-global-color-5-color{color:var(--ast-global-color-5);}:root .wp-block-button .has-ast-global-color-5-background-color{background-color:var(--ast-global-color-5);}:root .has-ast-global-color-6-color{color:var(--ast-global-color-6);}:root .has-ast-global-color-6-background-color{background-color:var(--ast-global-color-6);}:root .wp-block-button .has-ast-global-color-6-color{color:var(--ast-global-color-6);}:root .wp-block-button .has-ast-global-color-6-background-color{background-color:var(--ast-global-color-6);}:root .has-ast-global-color-7-color{color:var(--ast-global-color-7);}:root .has-ast-global-color-7-background-color{background-color:var(--ast-global-color-7);}:root .wp-block-button .has-ast-global-color-7-color{color:var(--ast-global-color-7);}:root .wp-block-button .has-ast-global-color-7-background-color{background-color:var(--ast-global-color-7);}:root .has-ast-global-color-8-color{color:var(--ast-global-color-8);}:root .has-ast-global-color-8-background-color{background-color:var(--ast-global-color-8);}:root .wp-block-button .has-ast-global-color-8-color{color:var(--ast-global-color-8);}:root .wp-block-button .has-ast-global-color-8-background-color{background-color:var(--ast-global-color-8);}:root{--ast-global-color-0:#72befa;--ast-global-color-1:#e583b6;--ast-global-color-2:#1e293b;--ast-global-color-3:#c9c8c8;--ast-global-color-4:#ffffff;--ast-global-color-5:#ffffff;--ast-global-color-6:#2f2d2e;--ast-global-color-7:#2f2d2e;--ast-global-color-8:#94a3b8;}:root {--ast-border-color : #dddddd;}#masthead .ast-container,.ast-header-breadcrumb .ast-container{max-width:100%;padding-left:35px;padding-right:35px;}@media (max-width:10px){#masthead .ast-container,.ast-header-breadcrumb .ast-container{padding-left:20px;padding-right:20px;}}.ast-single-entry-banner {-js-display: flex;display: flex;flex-direction: column;justify-content: center;text-align: center;position: relative;background: var(--ast-title-layout-bg);}.ast-single-entry-banner[data-banner-layout="layout-1"] {max-width: 1230px;background: inherit;padding: 20px 0;}.ast-single-entry-banner[data-banner-width-type="custom"] {margin: 0 auto;width: 100%;}.ast-single-entry-banner + .site-content .entry-header {margin-bottom: 0;}.site .ast-author-avatar {--ast-author-avatar-size: ;}a.ast-underline-text {text-decoration: underline;}.ast-container > .ast-terms-link {position: relative;display: block;}a.ast-button.ast-badge-tax {padding: 4px 8px;border-radius: 3px;font-size: inherit;}header.entry-header{text-align:left;}header.entry-header > *:not(:last-child){margin-bottom:10px;}@media (max-width:921px){header.entry-header{text-align:left;}}@media (max-width:544px){header.entry-header{text-align:left;}}.ast-archive-entry-banner {-js-display: flex;display: flex;flex-direction: column;justify-content: center;text-align: center;position: relative;background: var(--ast-title-layout-bg);}.ast-archive-entry-banner[data-banner-width-type="custom"] {margin: 0 auto;width: 100%;}.ast-archive-entry-banner[data-banner-layout="layout-1"] {background: inherit;padding: 20px 0;text-align: left;}body.archive .ast-archive-description{max-width:1230px;width:100%;text-align:left;padding-top:3em;padding-right:3em;padding-bottom:3em;padding-left:3em;}body.archive .ast-archive-description .ast-archive-title,body.archive .ast-archive-description .ast-archive-title *{font-size:40px;font-size:2.2222222222222rem;}body.archive .ast-archive-description > *:not(:last-child){margin-bottom:10px;}@media (max-width:921px){body.archive .ast-archive-description{text-align:left;}}@media (max-width:544px){body.archive .ast-archive-description{text-align:left;}}.ast-breadcrumbs .trail-browse,.ast-breadcrumbs .trail-items,.ast-breadcrumbs .trail-items li{display:inline-block;margin:0;padding:0;border:none;background:inherit;text-indent:0;text-decoration:none;}.ast-breadcrumbs .trail-browse{font-size:inherit;font-style:inherit;font-weight:inherit;color:inherit;}.ast-breadcrumbs .trail-items{list-style:none;}.trail-items li::after{padding:0 0.3em;content:"\00bb";}.trail-items li:last-of-type::after{display:none;}h1,h2,h3,h4,h5,h6,.entry-content :where(h1,h2,h3,h4,h5,h6){color:var(--ast-global-color-5);}.elementor-posts-container [CLASS*="ast-width-"]{width:100%;}.elementor-template-full-width .ast-container{display:block;}.elementor-screen-only,.screen-reader-text,.screen-reader-text span,.ui-helper-hidden-accessible{top:0 !important;}@media (max-width:544px){.elementor-element .elementor-wc-products .woocommerce[class*="columns-"] ul.products li.product{width:auto;margin:0;}.elementor-element .woocommerce .woocommerce-result-count{float:none;}}.ast-header-break-point .main-header-bar{border-bottom-width:1px;}@media (min-width:922px){.main-header-bar{border-bottom-width:1px;}}.main-header-menu .menu-item, #astra-footer-menu .menu-item, .main-header-bar .ast-masthead-custom-menu-items{-js-display:flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;}.main-header-menu > .menu-item > .menu-link, #astra-footer-menu > .menu-item > .menu-link{height:100%;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-js-display:flex;display:flex;}.ast-primary-menu-disabled .main-header-bar .ast-masthead-custom-menu-items{flex:unset;}.main-header-menu .sub-menu .menu-item.menu-item-has-children > .menu-link:after{position:absolute;right:1em;top:50%;transform:translate(0,-50%) rotate(270deg);}.ast-header-break-point .main-header-bar .main-header-bar-navigation .page_item_has_children > .ast-menu-toggle::before, .ast-header-break-point .main-header-bar .main-header-bar-navigation .menu-item-has-children > .ast-menu-toggle::before, .ast-mobile-popup-drawer .main-header-bar-navigation .menu-item-has-children>.ast-menu-toggle::before, .ast-header-break-point .ast-mobile-header-wrap .main-header-bar-navigation .menu-item-has-children > .ast-menu-toggle::before{font-weight:bold;content:"\e900";font-family:Astra;text-decoration:inherit;display:inline-block;}.ast-header-break-point .main-navigation ul.sub-menu .menu-item .menu-link:before{content:"\e900";font-family:Astra;font-size:.65em;text-decoration:inherit;display:inline-block;transform:translate(0, -2px) rotateZ(270deg);margin-right:5px;}.widget_search .search-form:after{font-family:Astra;font-size:1.2em;font-weight:normal;content:"\e8b6";position:absolute;top:50%;right:15px;transform:translate(0, -50%);}.astra-search-icon::before{content:"\e8b6";font-family:Astra;font-style:normal;font-weight:normal;text-decoration:inherit;text-align:center;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;z-index:3;}.main-header-bar .main-header-bar-navigation .page_item_has_children > a:after, .main-header-bar .main-header-bar-navigation .menu-item-has-children > a:after, .menu-item-has-children .ast-header-navigation-arrow:after{content:"\e900";display:inline-block;font-family:Astra;font-size:.6rem;font-weight:bold;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;margin-left:10px;line-height:normal;}.menu-item-has-children .sub-menu .ast-header-navigation-arrow:after{margin-left:0;}.ast-mobile-popup-drawer .main-header-bar-navigation .ast-submenu-expanded>.ast-menu-toggle::before{transform:rotateX(180deg);}.ast-header-break-point .main-header-bar-navigation .menu-item-has-children > .menu-link:after{display:none;}@media (min-width:922px){.ast-builder-menu .main-navigation > ul > li:last-child a{margin-right:0;}}.ast-separate-container .ast-article-inner{background-color:transparent;background-image:none;}.ast-separate-container .ast-article-post{background-color:#2f2d2e;background-image:none;}@media (max-width:921px){.ast-separate-container .ast-article-post{background-color:var(--ast-global-color-5);background-image:none;}}@media (max-width:544px){.ast-separate-container .ast-article-post{background-color:var(--ast-global-color-5);background-image:none;}}.ast-separate-container .ast-article-single:not(.ast-related-post), .woocommerce.ast-separate-container .ast-woocommerce-container, .ast-separate-container .error-404, .ast-separate-container .no-results, .single.ast-separate-container  .ast-author-meta, .ast-separate-container .related-posts-title-wrapper, .ast-separate-container .comments-count-wrapper, .ast-box-layout.ast-plain-container .site-content, .ast-padded-layout.ast-plain-container .site-content, .ast-separate-container .ast-archive-description, .ast-separate-container .comments-area .comment-respond, .ast-separate-container .comments-area .ast-comment-list li, .ast-separate-container .comments-area .comments-title{background-color:#2f2d2e;background-image:none;}@media (max-width:921px){.ast-separate-container .ast-article-single:not(.ast-related-post), .woocommerce.ast-separate-container .ast-woocommerce-container, .ast-separate-container .error-404, .ast-separate-container .no-results, .single.ast-separate-container  .ast-author-meta, .ast-separate-container .related-posts-title-wrapper, .ast-separate-container .comments-count-wrapper, .ast-box-layout.ast-plain-container .site-content, .ast-padded-layout.ast-plain-container .site-content, .ast-separate-container .ast-archive-description{background-color:var(--ast-global-color-5);background-image:none;}}@media (max-width:544px){.ast-separate-container .ast-article-single:not(.ast-related-post), .woocommerce.ast-separate-container .ast-woocommerce-container, .ast-separate-container .error-404, .ast-separate-container .no-results, .single.ast-separate-container  .ast-author-meta, .ast-separate-container .related-posts-title-wrapper, .ast-separate-container .comments-count-wrapper, .ast-box-layout.ast-plain-container .site-content, .ast-padded-layout.ast-plain-container .site-content, .ast-separate-container .ast-archive-description{background-color:var(--ast-global-color-5);background-image:none;}}.ast-separate-container.ast-two-container #secondary .widget{background-color:#2f2d2e;background-image:none;}@media (max-width:921px){.ast-separate-container.ast-two-container #secondary .widget{background-color:var(--ast-global-color-5);background-image:none;}}@media (max-width:544px){.ast-separate-container.ast-two-container #secondary .widget{background-color:var(--ast-global-color-5);background-image:none;}}
		#ast-scroll-top {
			display: none;
			position: fixed;
			text-align: center;
			cursor: pointer;
			z-index: 99;
			width: 2.1em;
			height: 2.1em;
			line-height: 2.1;
			color: #ffffff;
			border-radius: 2px;
			content: "";
			outline: inherit;
		}
		@media (min-width: 769px) {
			#ast-scroll-top {
				content: "769";
			}
		}
		#ast-scroll-top .ast-icon.icon-arrow svg {
			margin-left: 0px;
			vertical-align: middle;
			transform: translate(0, -20%) rotate(180deg);
			width: 1.6em;
		}
		.ast-scroll-to-top-right {
			right: 30px;
			bottom: 30px;
		}
		.ast-scroll-to-top-left {
			left: 30px;
			bottom: 30px;
		}
	#ast-scroll-top{background-color:#e583b6;font-size:15px;}.ast-scroll-top-icon::before{content:"\e900";font-family:Astra;text-decoration:inherit;}.ast-scroll-top-icon{transform:rotate(180deg);}@media (max-width:921px){#ast-scroll-top .ast-icon.icon-arrow svg{width:1em;}}:root{--e-global-color-astglobalcolor0:#72befa;--e-global-color-astglobalcolor1:#e583b6;--e-global-color-astglobalcolor2:#1e293b;--e-global-color-astglobalcolor3:#c9c8c8;--e-global-color-astglobalcolor4:#ffffff;--e-global-color-astglobalcolor5:#ffffff;--e-global-color-astglobalcolor6:#2f2d2e;--e-global-color-astglobalcolor7:#2f2d2e;--e-global-color-astglobalcolor8:#94a3b8;}.comment-reply-title{font-size:29px;font-size:1.6111111111111rem;}.ast-comment-meta{line-height:1.666666667;color:var(--ast-global-color-0);font-size:15px;font-size:0.83333333333333rem;}.ast-comment-list #cancel-comment-reply-link{font-size:18px;font-size:1rem;}.comments-count-wrapper {padding: 2em 0;}.comments-count-wrapper .comments-title {font-weight: normal;word-wrap: break-word;}.ast-comment-list {margin: 0;word-wrap: break-word;padding-bottom: 0.5em;list-style: none;}.site-content article .comments-area {border-top: 1px solid var(--ast-single-post-border,var(--ast-border-color));}.ast-comment-list li {list-style: none;}.ast-comment-list li.depth-1 .ast-comment,.ast-comment-list li.depth-2 .ast-comment {border-bottom: 1px solid #eeeeee;}.ast-comment-list .comment-respond {padding: 1em 0;border-bottom: 1px solid #dddddd;}.ast-comment-list .comment-respond .comment-reply-title {margin-top: 0;padding-top: 0;}.ast-comment-list .comment-respond p {margin-bottom: .5em;}.ast-comment-list .ast-comment-edit-reply-wrap {-js-display: flex;display: flex;justify-content: flex-end;}.ast-comment-list .ast-edit-link {flex: 1;}.ast-comment-list .comment-awaiting-moderation {margin-bottom: 0;}.ast-comment {padding: 1em 0 ;}.ast-comment-avatar-wrap img {border-radius: 50%;}.ast-comment-content {clear: both;}.ast-comment-cite-wrap {text-align: left;}.ast-comment-cite-wrap cite {font-style: normal;}.comment-reply-title {padding-top: 1em;font-weight: normal;line-height: 1.65;}.ast-comment-meta {margin-bottom: 0.5em;}.comments-area {border-top: 1px solid #eeeeee;margin-top: 2em;}.comments-area .comment-form-comment {width: 100%;border: none;margin: 0;padding: 0;}.comments-area .comment-notes,.comments-area .comment-textarea,.comments-area .form-allowed-tags {margin-bottom: 1.5em;}.comments-area .form-submit {margin-bottom: 0;}.comments-area textarea#comment,.comments-area .ast-comment-formwrap input[type="text"] {width: 100%;border-radius: 0;vertical-align: middle;margin-bottom: 10px;}.comments-area .no-comments {margin-top: 0.5em;margin-bottom: 0.5em;}.comments-area p.logged-in-as {margin-bottom: 1em;}.ast-separate-container .comments-count-wrapper {background-color: #fff;padding: 2em 6.67em 0;}@media (max-width: 1200px) {.ast-separate-container .comments-count-wrapper {padding: 2em 3.34em;}}.ast-separate-container .comments-area {border-top: 0;}.ast-separate-container .ast-comment-list {padding-bottom: 0;}.ast-separate-container .ast-comment-list li {background-color: #fff;}.ast-separate-container .ast-comment-list li.depth-1 .children li {padding-bottom: 0;padding-top: 0;margin-bottom: 0;}.ast-separate-container .ast-comment-list li.depth-1 .ast-comment,.ast-separate-container .ast-comment-list li.depth-2 .ast-comment {border-bottom: 0;}.ast-separate-container .ast-comment-list .comment-respond {padding-top: 0;padding-bottom: 1em;background-color: transparent;}.ast-separate-container .ast-comment-list .pingback p {margin-bottom: 0;}.ast-separate-container .ast-comment-list .bypostauthor {padding: 2em;margin-bottom: 1em;}.ast-separate-container .ast-comment-list .bypostauthor li {background: transparent;margin-bottom: 0;padding: 0 0 0 2em;}.ast-separate-container .comment-reply-title {padding-top: 0;}.comment-content a {word-wrap: break-word;}.comment-form-legend {margin-bottom: unset;padding: 0 0.5em;}.ast-separate-container .ast-comment-list li.depth-1 {padding: 4em 6.67em;margin-bottom: 2em;}@media (max-width: 1200px) {.ast-separate-container .ast-comment-list li.depth-1 {padding: 3em 3.34em;}}.ast-separate-container .comment-respond {background-color: #fff;padding: 4em 6.67em;border-bottom: 0;}@media (max-width: 1200px) {.ast-separate-container .comment-respond {padding: 3em 2.34em;}}.ast-comment-list .children {margin-left: 2em;}@media (max-width: 992px) {.ast-comment-list .children {margin-left: 1em;}}.ast-comment-list #cancel-comment-reply-link {white-space: nowrap;font-size: 15px;font-size: 1rem;margin-left: 1em;}.ast-comment-avatar-wrap {float: left;clear: right;margin-right: 1.33333em;}.ast-comment-meta-wrap {float: left;clear: right;padding: 0 0 1.33333em;}.ast-comment-time .timendate,.ast-comment-time .reply {margin-right: 0.5em;}.comments-area #wp-comment-cookies-consent {margin-right: 10px;}.ast-page-builder-template .comments-area {padding-left: 20px;padding-right: 20px;margin-top: 0;margin-bottom: 2em;}.ast-separate-container .ast-comment-list .bypostauthor .bypostauthor {background: transparent;margin-bottom: 0;padding-right: 0;padding-bottom: 0;padding-top: 0;}@media (min-width:922px){.ast-separate-container .ast-comment-list li .comment-respond{padding-left:2.66666em;padding-right:2.66666em;}}@media (max-width:544px){.ast-separate-container .comments-count-wrapper{padding:1.5em 1em;}.ast-separate-container .ast-comment-list li.depth-1{padding:1.5em 1em;margin-bottom:1.5em;}.ast-separate-container .ast-comment-list .bypostauthor{padding:.5em;}.ast-separate-container .comment-respond{padding:1.5em 1em;}.ast-separate-container .ast-comment-list .bypostauthor li{padding:0 0 0 .5em;}.ast-comment-list .children{margin-left:0.66666em;}}@media (max-width:921px){.ast-comment-avatar-wrap img{max-width:2.5em;}.comments-area{margin-top:1.5em;}.ast-separate-container .comments-count-wrapper{padding:2em 2.14em;}.ast-separate-container .ast-comment-list li.depth-1{padding:1.5em 2.14em;}.ast-separate-container .comment-respond{padding:2em 2.14em;}.ast-comment-meta{font-size:15px;font-size:0.83333333333333rem;}.comment-reply-title{font-size:29px;font-size:1.6111111111111rem;}.ast-comment-list #cancel-comment-reply-link{font-size:18px;font-size:1rem;}}@media (max-width:921px){.ast-comment-avatar-wrap{margin-right:0.5em;}}
</style>
<link rel='stylesheet' id='astra-google-fonts-css' href='https://fonts.googleapis.com/css?family=Comfortaa%3A400%2C%2C700%2C500&#038;display=fallback&#038;ver=4.11.12' media='all' />
<link rel='stylesheet' id='astra-menu-animation-css' href='https://codecut.ai/wp-content/themes/astra/assets/css/minified/menu-animation.min.css?ver=4.11.12' media='all' />
<link rel='stylesheet' id='dashicons-css' href='https://codecut.ai/wp-includes/css/dashicons.min.css?ver=6.8.2' media='all' />
<link rel='stylesheet' id='menu-icons-extra-css' href='https://codecut.ai/wp-content/plugins/menu-icons/css/extra.min.css?ver=0.13.18' media='all' />
<style id='wp-emoji-styles-inline-css'>

	img.wp-smiley, img.emoji {
		display: inline !important;
		border: none !important;
		box-shadow: none !important;
		height: 1em !important;
		width: 1em !important;
		margin: 0 0.07em !important;
		vertical-align: -0.1em !important;
		background: none !important;
		padding: 0 !important;
	}
</style>
<link rel='stylesheet' id='wp-block-library-css' href='https://codecut.ai/wp-includes/css/dist/block-library/style.min.css?ver=6.8.2' media='all' />
<link rel='stylesheet' id='prismatic-blocks-css' href='https://codecut.ai/wp-content/plugins/prismatic/css/styles-blocks.css?ver=6.8.2' media='all' />
<link rel='stylesheet' id='ht-youtube-embed-css-css' href='https://codecut.ai/wp-content/plugins/faster-youtube-embed/assets/css/ht-youtube-embed.css?ver=b4c7955cb0da4b73ca514c131e8ec697' media='all' />
<link rel='stylesheet' id='cr-frontend-css-css' href='https://codecut.ai/wp-content/plugins/customer-reviews-woocommerce/css/frontend.css?ver=5.80.2' media='all' />
<link rel='stylesheet' id='cr-badges-css-css' href='https://codecut.ai/wp-content/plugins/customer-reviews-woocommerce/css/badges.css?ver=5.80.2' media='all' />
<link rel='stylesheet' id='ab_test_styles-css' href='https://codecut.ai/wp-content/plugins/bt-bb-ab/css/experiment-frontend.css?ver=6.8.2' media='all' />
<style id='global-styles-inline-css'>
:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--color--ast-global-color-0: var(--ast-global-color-0);--wp--preset--color--ast-global-color-1: var(--ast-global-color-1);--wp--preset--color--ast-global-color-2: var(--ast-global-color-2);--wp--preset--color--ast-global-color-3: var(--ast-global-color-3);--wp--preset--color--ast-global-color-4: var(--ast-global-color-4);--wp--preset--color--ast-global-color-5: var(--ast-global-color-5);--wp--preset--color--ast-global-color-6: var(--ast-global-color-6);--wp--preset--color--ast-global-color-7: var(--ast-global-color-7);--wp--preset--color--ast-global-color-8: var(--ast-global-color-8);--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:root { --wp--style--global--content-size: var(--wp--custom--ast-content-width-size);--wp--style--global--wide-size: var(--wp--custom--ast-wide-width-size); }:where(body) { margin: 0; }.wp-site-blocks > .alignleft { float: left; margin-right: 2em; }.wp-site-blocks > .alignright { float: right; margin-left: 2em; }.wp-site-blocks > .aligncenter { justify-content: center; margin-left: auto; margin-right: auto; }:where(.wp-site-blocks) > * { margin-block-start: 24px; margin-block-end: 0; }:where(.wp-site-blocks) > :first-child { margin-block-start: 0; }:where(.wp-site-blocks) > :last-child { margin-block-end: 0; }:root { --wp--style--block-gap: 24px; }:root :where(.is-layout-flow) > :first-child{margin-block-start: 0;}:root :where(.is-layout-flow) > :last-child{margin-block-end: 0;}:root :where(.is-layout-flow) > *{margin-block-start: 24px;margin-block-end: 0;}:root :where(.is-layout-constrained) > :first-child{margin-block-start: 0;}:root :where(.is-layout-constrained) > :last-child{margin-block-end: 0;}:root :where(.is-layout-constrained) > *{margin-block-start: 24px;margin-block-end: 0;}:root :where(.is-layout-flex){gap: 24px;}:root :where(.is-layout-grid){gap: 24px;}.is-layout-flow > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}.is-layout-flow > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}.is-layout-flow > .aligncenter{margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}.is-layout-constrained > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}.is-layout-constrained > .aligncenter{margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull)){max-width: var(--wp--style--global--content-size);margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > .alignwide{max-width: var(--wp--style--global--wide-size);}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}body{padding-top: 0px;padding-right: 0px;padding-bottom: 0px;padding-left: 0px;}a:where(:not(.wp-element-button)){text-decoration: none;}:root :where(.wp-element-button, .wp-block-button__link){background-color: #32373c;border-width: 0;color: #fff;font-family: inherit;font-size: inherit;line-height: inherit;padding: calc(0.667em + 2px) calc(1.333em + 2px);text-decoration: none;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-ast-global-color-0-color{color: var(--wp--preset--color--ast-global-color-0) !important;}.has-ast-global-color-1-color{color: var(--wp--preset--color--ast-global-color-1) !important;}.has-ast-global-color-2-color{color: var(--wp--preset--color--ast-global-color-2) !important;}.has-ast-global-color-3-color{color: var(--wp--preset--color--ast-global-color-3) !important;}.has-ast-global-color-4-color{color: var(--wp--preset--color--ast-global-color-4) !important;}.has-ast-global-color-5-color{color: var(--wp--preset--color--ast-global-color-5) !important;}.has-ast-global-color-6-color{color: var(--wp--preset--color--ast-global-color-6) !important;}.has-ast-global-color-7-color{color: var(--wp--preset--color--ast-global-color-7) !important;}.has-ast-global-color-8-color{color: var(--wp--preset--color--ast-global-color-8) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-ast-global-color-0-background-color{background-color: var(--wp--preset--color--ast-global-color-0) !important;}.has-ast-global-color-1-background-color{background-color: var(--wp--preset--color--ast-global-color-1) !important;}.has-ast-global-color-2-background-color{background-color: var(--wp--preset--color--ast-global-color-2) !important;}.has-ast-global-color-3-background-color{background-color: var(--wp--preset--color--ast-global-color-3) !important;}.has-ast-global-color-4-background-color{background-color: var(--wp--preset--color--ast-global-color-4) !important;}.has-ast-global-color-5-background-color{background-color: var(--wp--preset--color--ast-global-color-5) !important;}.has-ast-global-color-6-background-color{background-color: var(--wp--preset--color--ast-global-color-6) !important;}.has-ast-global-color-7-background-color{background-color: var(--wp--preset--color--ast-global-color-7) !important;}.has-ast-global-color-8-background-color{background-color: var(--wp--preset--color--ast-global-color-8) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-ast-global-color-0-border-color{border-color: var(--wp--preset--color--ast-global-color-0) !important;}.has-ast-global-color-1-border-color{border-color: var(--wp--preset--color--ast-global-color-1) !important;}.has-ast-global-color-2-border-color{border-color: var(--wp--preset--color--ast-global-color-2) !important;}.has-ast-global-color-3-border-color{border-color: var(--wp--preset--color--ast-global-color-3) !important;}.has-ast-global-color-4-border-color{border-color: var(--wp--preset--color--ast-global-color-4) !important;}.has-ast-global-color-5-border-color{border-color: var(--wp--preset--color--ast-global-color-5) !important;}.has-ast-global-color-6-border-color{border-color: var(--wp--preset--color--ast-global-color-6) !important;}.has-ast-global-color-7-border-color{border-color: var(--wp--preset--color--ast-global-color-7) !important;}.has-ast-global-color-8-border-color{border-color: var(--wp--preset--color--ast-global-color-8) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}
</style>
<link rel='stylesheet' id='prismatic-prism-css' href='https://codecut.ai/wp-content/plugins/prismatic/lib/prism/css/theme-okaidia.css?ver=3.5' media='all' />
<link rel='stylesheet' id='prismatic-plugin-styles-css' href='https://codecut.ai/wp-content/plugins/prismatic/lib/prism/css/plugin-styles.css?ver=3.5' media='all' />
<link rel='stylesheet' id='woocommerce-layout-css' href='https://codecut.ai/wp-content/themes/astra/assets/css/minified/compatibility/woocommerce/woocommerce-layout.min.css?ver=4.11.12' media='all' />
<link rel='stylesheet' id='woocommerce-smallscreen-css' href='https://codecut.ai/wp-content/themes/astra/assets/css/minified/compatibility/woocommerce/woocommerce-smallscreen.min.css?ver=4.11.12' media='only screen and (max-width: 921px)' />
<link rel='stylesheet' id='woocommerce-general-css' href='https://codecut.ai/wp-content/themes/astra/assets/css/minified/compatibility/woocommerce/woocommerce.min.css?ver=4.11.12' media='all' />
<style id='woocommerce-general-inline-css'>

					.woocommerce .woocommerce-result-count, .woocommerce-page .woocommerce-result-count {
						float: left;
					}

					.woocommerce .woocommerce-ordering {
						float: right;
						margin-bottom: 2.5em;
					}
				
					.woocommerce-js a.button, .woocommerce button.button, .woocommerce input.button, .woocommerce #respond input#submit {
						font-size: 100%;
						line-height: 1;
						text-decoration: none;
						overflow: visible;
						padding: 0.5em 0.75em;
						font-weight: 700;
						border-radius: 3px;
						color: $secondarytext;
						background-color: $secondary;
						border: 0;
					}
					.woocommerce-js a.button:hover, .woocommerce button.button:hover, .woocommerce input.button:hover, .woocommerce #respond input#submit:hover {
						background-color: #dad8da;
						background-image: none;
						color: #515151;
					}
				#customer_details h3:not(.elementor-widget-woocommerce-checkout-page h3){font-size:1.2rem;padding:20px 0 14px;margin:0 0 20px;border-bottom:1px solid var(--ast-border-color);font-weight:700;}form #order_review_heading:not(.elementor-widget-woocommerce-checkout-page #order_review_heading){border-width:2px 2px 0 2px;border-style:solid;font-size:1.2rem;margin:0;padding:1.5em 1.5em 1em;border-color:var(--ast-border-color);font-weight:700;}.woocommerce-Address h3, .cart-collaterals h2{font-size:1.2rem;padding:.7em 1em;}.woocommerce-cart .cart-collaterals .cart_totals>h2{font-weight:700;}form #order_review:not(.elementor-widget-woocommerce-checkout-page #order_review){padding:0 2em;border-width:0 2px 2px;border-style:solid;border-color:var(--ast-border-color);}ul#shipping_method li:not(.elementor-widget-woocommerce-cart #shipping_method li){margin:0;padding:0.25em 0 0.25em 22px;text-indent:-22px;list-style:none outside;}.woocommerce span.onsale, .wc-block-grid__product .wc-block-grid__product-onsale{background-color:var(--ast-global-color-0);color:#000000;}.woocommerce-message, .woocommerce-info{border-top-color:var(--ast-global-color-0);}.woocommerce-message::before,.woocommerce-info::before{color:var(--ast-global-color-0);}.woocommerce ul.products li.product .price, .woocommerce div.product p.price, .woocommerce div.product span.price, .widget_layered_nav_filters ul li.chosen a, .woocommerce-page ul.products li.product .ast-woo-product-category, .wc-layered-nav-rating a{color:var(--ast-global-color-3);}.woocommerce nav.woocommerce-pagination ul,.woocommerce nav.woocommerce-pagination ul li{border-color:var(--ast-global-color-0);}.woocommerce nav.woocommerce-pagination ul li a:focus, .woocommerce nav.woocommerce-pagination ul li a:hover, .woocommerce nav.woocommerce-pagination ul li span.current{background:var(--ast-global-color-0);color:#000000;}.woocommerce-MyAccount-navigation-link.is-active a{color:var(--ast-global-color-1);}.woocommerce .widget_price_filter .ui-slider .ui-slider-range, .woocommerce .widget_price_filter .ui-slider .ui-slider-handle{background-color:var(--ast-global-color-0);}.woocommerce .star-rating, .woocommerce .comment-form-rating .stars a, .woocommerce .star-rating::before{color:var(--ast-global-color-3);}.woocommerce div.product .woocommerce-tabs ul.tabs li.active:before,  .woocommerce div.ast-product-tabs-layout-vertical .woocommerce-tabs ul.tabs li:hover::before{background:var(--ast-global-color-0);}.ast-site-header-cart a{color:var(--ast-global-color-3);}.ast-site-header-cart a:focus, .ast-site-header-cart a:hover, .ast-site-header-cart .current-menu-item a{color:var(--ast-global-color-0);}.ast-cart-menu-wrap .count, .ast-cart-menu-wrap .count:after{border-color:var(--ast-global-color-0);color:var(--ast-global-color-0);}.ast-cart-menu-wrap:hover .count{color:#000000;background-color:var(--ast-global-color-0);}.ast-site-header-cart .widget_shopping_cart .total .woocommerce-Price-amount{color:var(--ast-global-color-0);}.woocommerce a.remove:hover, .ast-woocommerce-cart-menu .main-header-menu .woocommerce-custom-menu-item .menu-item:hover > .menu-link.remove:hover{color:var(--ast-global-color-0);border-color:var(--ast-global-color-0);background-color:#ffffff;}.ast-site-header-cart .widget_shopping_cart .buttons .button.checkout, .woocommerce .widget_shopping_cart .woocommerce-mini-cart__buttons .checkout.wc-forward{color:#000000;border-color:var(--ast-global-color-1);background-color:var(--ast-global-color-1);}.site-header .ast-site-header-cart-data .button.wc-forward, .site-header .ast-site-header-cart-data .button.wc-forward:hover{color:#000000;}.below-header-user-select .ast-site-header-cart .widget, .ast-above-header-section .ast-site-header-cart .widget a, .below-header-user-select .ast-site-header-cart .widget_shopping_cart a{color:var(--ast-global-color-3);}.below-header-user-select .ast-site-header-cart .widget_shopping_cart a:hover, .ast-above-header-section .ast-site-header-cart .widget_shopping_cart a:hover, .below-header-user-select .ast-site-header-cart .widget_shopping_cart a.remove:hover, .ast-above-header-section .ast-site-header-cart .widget_shopping_cart a.remove:hover{color:var(--ast-global-color-0);}.woocommerce .woocommerce-cart-form button[name="update_cart"]:disabled{color:#000000;}.woocommerce #content table.cart .button[name="apply_coupon"], .woocommerce-page #content table.cart .button[name="apply_coupon"]{padding:10px 40px;}.woocommerce table.cart td.actions .button, .woocommerce #content table.cart td.actions .button, .woocommerce-page table.cart td.actions .button, .woocommerce-page #content table.cart td.actions .button{line-height:1;border-width:1px;border-style:solid;}.woocommerce ul.products li.product .button, .woocommerce-page ul.products li.product .button{line-height:1.3;}.woocommerce-js a.button, .woocommerce button.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce-js a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled], .woocommerce input.button:disabled:hover, .woocommerce input.button:disabled[disabled]:hover, .woocommerce #respond input#submit, .woocommerce button.button.alt.disabled, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link, .wc-block-grid__product-onsale{color:#000000;border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);}.woocommerce-js a.button:hover, .woocommerce button.button:hover, .woocommerce .woocommerce-message a.button:hover,.woocommerce #respond input#submit:hover,.woocommerce #respond input#submit.alt:hover, .woocommerce-js a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover, .woocommerce input.button:hover, .woocommerce button.button.alt.disabled:hover, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link:hover{color:#000000;border-color:var(--ast-global-color-1);background-color:var(--ast-global-color-1);}.woocommerce-js a.button, .woocommerce button.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce-js a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce-cart table.cart td.actions .button, .woocommerce form.checkout_coupon .button, .woocommerce #respond input#submit, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link{padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;}.woocommerce ul.products li.product a, .woocommerce-js a.button:hover, .woocommerce button.button:hover, .woocommerce input.button:hover, .woocommerce #respond input#submit:hover{text-decoration:none;}.woocommerce .up-sells h2, .woocommerce .related.products h2, .woocommerce .woocommerce-tabs h2{font-size:1.5rem;}.woocommerce h2, .woocommerce-account h2{font-size:1.625rem;}.woocommerce ul.product-categories > li ul li:before{content:"\e900";padding:0 5px 0 5px;display:inline-block;font-family:Astra;transform:rotate(-90deg);font-size:0.7rem;}.ast-site-header-cart i.astra-icon:before{font-family:Astra;}.ast-icon-shopping-cart:before{content:"\f07a";}.ast-icon-shopping-bag:before{content:"\f290";}.ast-icon-shopping-basket:before{content:"\f291";}.ast-icon-shopping-cart svg{height:.82em;}.ast-icon-shopping-bag svg{height:1em;width:1em;}.ast-icon-shopping-basket svg{height:1.15em;width:1.2em;}.ast-site-header-cart.ast-menu-cart-outline .ast-addon-cart-wrap, .ast-site-header-cart.ast-menu-cart-fill .ast-addon-cart-wrap {line-height:1;}.ast-site-header-cart.ast-menu-cart-fill i.astra-icon{ font-size:1.1em;}li.woocommerce-custom-menu-item .ast-site-header-cart i.astra-icon:after{ padding-left:2px;}.ast-hfb-header .ast-addon-cart-wrap{ padding:0.4em;}.ast-header-break-point.ast-header-custom-item-outside .ast-woo-header-cart-info-wrap{ display:none;}.ast-site-header-cart i.astra-icon:after{ background:var(--ast-global-color-0);}@media (min-width:545px) and (max-width:921px){.woocommerce.tablet-columns-6 ul.products li.product, .woocommerce-page.tablet-columns-6 ul.products li.product{width:calc(16.66% - 16.66px);}.woocommerce.tablet-columns-5 ul.products li.product, .woocommerce-page.tablet-columns-5 ul.products li.product{width:calc(20% - 16px);}.woocommerce.tablet-columns-4 ul.products li.product, .woocommerce-page.tablet-columns-4 ul.products li.product{width:calc(25% - 15px);}.woocommerce.tablet-columns-3 ul.products li.product, .woocommerce-page.tablet-columns-3 ul.products li.product{width:calc(33.33% - 14px);}.woocommerce.tablet-columns-2 ul.products li.product, .woocommerce-page.tablet-columns-2 ul.products li.product{width:calc(50% - 10px);}.woocommerce.tablet-columns-1 ul.products li.product, .woocommerce-page.tablet-columns-1 ul.products li.product{width:100%;}.woocommerce div.product .related.products ul.products li.product{width:calc(33.33% - 14px);}}@media (min-width:545px) and (max-width:921px){.woocommerce[class*="columns-"].columns-3 > ul.products li.product, .woocommerce[class*="columns-"].columns-4 > ul.products li.product, .woocommerce[class*="columns-"].columns-5 > ul.products li.product, .woocommerce[class*="columns-"].columns-6 > ul.products li.product{width:calc(33.33% - 14px);margin-right:20px;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(3n){margin-right:0;clear:right;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(3n+1){clear:left;}.woocommerce[class*="columns-"] ul.products li.product:nth-child(n), .woocommerce-page[class*="columns-"] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce.tablet-columns-2 ul.products li.product:nth-child(2n), .woocommerce-page.tablet-columns-2 ul.products li.product:nth-child(2n), .woocommerce.tablet-columns-3 ul.products li.product:nth-child(3n), .woocommerce-page.tablet-columns-3 ul.products li.product:nth-child(3n), .woocommerce.tablet-columns-4 ul.products li.product:nth-child(4n), .woocommerce-page.tablet-columns-4 ul.products li.product:nth-child(4n), .woocommerce.tablet-columns-5 ul.products li.product:nth-child(5n), .woocommerce-page.tablet-columns-5 ul.products li.product:nth-child(5n), .woocommerce.tablet-columns-6 ul.products li.product:nth-child(6n), .woocommerce-page.tablet-columns-6 ul.products li.product:nth-child(6n){margin-right:0;clear:right;}.woocommerce.tablet-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce-page.tablet-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce.tablet-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce-page.tablet-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce.tablet-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce-page.tablet-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce.tablet-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce-page.tablet-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce.tablet-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce-page.tablet-columns-6 ul.products li.product:nth-child(6n+1){clear:left;}.woocommerce div.product .related.products ul.products li.product:nth-child(3n), .woocommerce-page.tablet-columns-1 .site-main ul.products li.product{margin-right:0;clear:right;}.woocommerce div.product .related.products ul.products li.product:nth-child(3n+1){clear:left;}}@media (min-width:922px){.woocommerce form.checkout_coupon{width:50%;}.woocommerce #reviews #comments{float:left;}.woocommerce #reviews #review_form_wrapper{float:right;}}@media (max-width:921px){.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack.ast-no-menu-items .ast-site-header-cart, .ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack.ast-no-menu-items .ast-site-header-cart{padding-right:0;padding-left:0;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .main-header-bar{text-align:center;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .ast-site-header-cart, .ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .ast-mobile-menu-buttons{display:inline-block;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-2.ast-mobile-header-inline .site-branding{flex:auto;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack .site-branding{flex:0 0 100%;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack .main-header-container{display:flex;justify-content:center;}.woocommerce-cart .woocommerce-shipping-calculator .button{width:100%;}.woocommerce div.product div.images, .woocommerce div.product div.summary, .woocommerce #content div.product div.images, .woocommerce #content div.product div.summary, .woocommerce-page div.product div.images, .woocommerce-page div.product div.summary, .woocommerce-page #content div.product div.images, .woocommerce-page #content div.product div.summary{float:none;width:100%;}.woocommerce-cart table.cart td.actions .ast-return-to-shop{display:block;text-align:center;margin-top:1em;}}@media (max-width:544px){.ast-separate-container .ast-woocommerce-container{padding:.54em 1em 1.33333em;}.woocommerce-message, .woocommerce-error, .woocommerce-info{display:flex;flex-wrap:wrap;}.woocommerce-message a.button, .woocommerce-error a.button, .woocommerce-info a.button{order:1;margin-top:.5em;}.woocommerce .woocommerce-ordering, .woocommerce-page .woocommerce-ordering{float:none;margin-bottom:2em;}.woocommerce table.cart td.actions .button, .woocommerce #content table.cart td.actions .button, .woocommerce-page table.cart td.actions .button, .woocommerce-page #content table.cart td.actions .button{padding-left:1em;padding-right:1em;}.woocommerce #content table.cart .button, .woocommerce-page #content table.cart .button{width:100%;}.woocommerce #content table.cart td.actions .coupon, .woocommerce-page #content table.cart td.actions .coupon{float:none;}.woocommerce #content table.cart td.actions .coupon .button, .woocommerce-page #content table.cart td.actions .coupon .button{flex:1;}.woocommerce #content div.product .woocommerce-tabs ul.tabs li a, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li a{display:block;}.woocommerce ul.products a.button, .woocommerce-page ul.products a.button{padding:0.5em 0.75em;}.woocommerce div.product .related.products ul.products li.product, .woocommerce.mobile-columns-2 ul.products li.product, .woocommerce-page.mobile-columns-2 ul.products li.product{width:calc(50% - 10px);}.woocommerce.mobile-columns-6 ul.products li.product, .woocommerce-page.mobile-columns-6 ul.products li.product{width:calc(16.66% - 16.66px);}.woocommerce.mobile-columns-5 ul.products li.product, .woocommerce-page.mobile-columns-5 ul.products li.product{width:calc(20% - 16px);}.woocommerce.mobile-columns-4 ul.products li.product, .woocommerce-page.mobile-columns-4 ul.products li.product{width:calc(25% - 15px);}.woocommerce.mobile-columns-3 ul.products li.product, .woocommerce-page.mobile-columns-3 ul.products li.product{width:calc(33.33% - 14px);}.woocommerce.mobile-columns-1 ul.products li.product, .woocommerce-page.mobile-columns-1 ul.products li.product{width:100%;}}@media (max-width:544px){.woocommerce ul.products a.button.loading::after, .woocommerce-page ul.products a.button.loading::after{display:inline-block;margin-left:5px;position:initial;}.woocommerce.mobile-columns-1 .site-main ul.products li.product:nth-child(n), .woocommerce-page.mobile-columns-1 .site-main ul.products li.product:nth-child(n){margin-right:0;}.woocommerce #content div.product .woocommerce-tabs ul.tabs li, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li{display:block;margin-right:0;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product, .woocommerce[class*="columns-"].columns-4 > ul.products li.product, .woocommerce[class*="columns-"].columns-5 > ul.products li.product, .woocommerce[class*="columns-"].columns-6 > ul.products li.product{width:calc(50% - 10px);margin-right:20px;}.woocommerce[class*="columns-"] ul.products li.product:nth-child(n), .woocommerce-page[class*="columns-"] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce-page[class*=columns-].columns-3>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-4>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-5>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-6>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-3>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-4>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-5>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-6>ul.products li.product:nth-child(2n){margin-right:0;clear:right;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(2n+1){clear:left;}.woocommerce-page[class*=columns-] ul.products li.product:nth-child(n), .woocommerce[class*=columns-] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce.mobile-columns-6 ul.products li.product:nth-child(6n), .woocommerce-page.mobile-columns-6 ul.products li.product:nth-child(6n), .woocommerce.mobile-columns-5 ul.products li.product:nth-child(5n), .woocommerce-page.mobile-columns-5 ul.products li.product:nth-child(5n), .woocommerce.mobile-columns-4 ul.products li.product:nth-child(4n), .woocommerce-page.mobile-columns-4 ul.products li.product:nth-child(4n), .woocommerce.mobile-columns-3 ul.products li.product:nth-child(3n), .woocommerce-page.mobile-columns-3 ul.products li.product:nth-child(3n), .woocommerce.mobile-columns-2 ul.products li.product:nth-child(2n), .woocommerce-page.mobile-columns-2 ul.products li.product:nth-child(2n), .woocommerce div.product .related.products ul.products li.product:nth-child(2n){margin-right:0;clear:right;}.woocommerce.mobile-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce-page.mobile-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce.mobile-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce-page.mobile-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce.mobile-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce-page.mobile-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce.mobile-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce-page.mobile-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce.mobile-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce-page.mobile-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce div.product .related.products ul.products li.product:nth-child(2n+1){clear:left;}}@media (min-width:922px){.woocommerce #content .ast-woocommerce-container div.product div.images, .woocommerce .ast-woocommerce-container div.product div.images, .woocommerce-page #content .ast-woocommerce-container div.product div.images, .woocommerce-page .ast-woocommerce-container div.product div.images{width:50%;}.woocommerce #content .ast-woocommerce-container div.product div.summary, .woocommerce .ast-woocommerce-container div.product div.summary, .woocommerce-page #content .ast-woocommerce-container div.product div.summary, .woocommerce-page .ast-woocommerce-container div.product div.summary{width:46%;}.woocommerce.woocommerce-checkout form #customer_details.col2-set .col-1, .woocommerce.woocommerce-checkout form #customer_details.col2-set .col-2, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set .col-1, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set .col-2{float:none;width:auto;}}.woocommerce-js a.button , .woocommerce button.button.alt ,.woocommerce-page table.cart td.actions .button, .woocommerce-page #content table.cart td.actions .button , .woocommerce-js a.button.alt ,.woocommerce .woocommerce-message a.button , .ast-site-header-cart .widget_shopping_cart .buttons .button.checkout, .woocommerce button.button.alt.disabled , .wc-block-grid__products .wc-block-grid__product .wp-block-button__link {border:solid;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;border-color:var(--ast-global-color-0);}.woocommerce-js a.button:hover , .woocommerce button.button.alt:hover , .woocommerce-page table.cart td.actions .button:hover, .woocommerce-page #content table.cart td.actions .button:hover, .woocommerce-js a.button.alt:hover ,.woocommerce .woocommerce-message a.button:hover , .ast-site-header-cart .widget_shopping_cart .buttons .button.checkout:hover , .woocommerce button.button.alt.disabled:hover , .wc-block-grid__products .wc-block-grid__product .wp-block-button__link:hover{border-color:var(--ast-global-color-1);}.widget_product_search button{flex:0 0 auto;padding:10px 20px;}@media (min-width:922px){.woocommerce.woocommerce-checkout form #customer_details.col2-set, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set{width:55%;float:left;margin-right:4.347826087%;}.woocommerce.woocommerce-checkout form #order_review, .woocommerce.woocommerce-checkout form #order_review_heading, .woocommerce-page.woocommerce-checkout form #order_review, .woocommerce-page.woocommerce-checkout form #order_review_heading{width:40%;float:right;margin-right:0;clear:right;}}select, .select2-container .select2-selection--single{background-image:url("data:image/svg+xml,%3Csvg class='ast-arrow-svg' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' x='0px' y='0px' width='26px' height='16.043px' fill='%23c9c8c8' viewBox='57 35.171 26 16.043' enable-background='new 57 35.171 26 16.043' xml:space='preserve' %3E%3Cpath d='M57.5,38.193l12.5,12.5l12.5-12.5l-2.5-2.5l-10,10l-10-10L57.5,38.193z'%3E%3C/path%3E%3C/svg%3E");background-size:.8em;background-repeat:no-repeat;background-position-x:calc( 100% - 10px );background-position-y:center;-webkit-appearance:none;-moz-appearance:none;padding-right:2em;}
						.woocommerce ul.products li.product.desktop-align-left, .woocommerce-page ul.products li.product.desktop-align-left {
							text-align: left;
						}
						.woocommerce ul.products li.product.desktop-align-left .star-rating,
						.woocommerce ul.products li.product.desktop-align-left .button,
						.woocommerce-page ul.products li.product.desktop-align-left .star-rating,
						.woocommerce-page ul.products li.product.desktop-align-left .button {
							margin-left: 0;
							margin-right: 0;
						}
					@media(max-width: 921px){
						.woocommerce ul.products li.product.tablet-align-left, .woocommerce-page ul.products li.product.tablet-align-left {
							text-align: left;
						}
						.woocommerce ul.products li.product.tablet-align-left .star-rating,
						.woocommerce ul.products li.product.tablet-align-left .button,
						.woocommerce-page ul.products li.product.tablet-align-left .star-rating,
						.woocommerce-page ul.products li.product.tablet-align-left .button {
							margin-left: 0;
							margin-right: 0;
						}
					}@media(max-width: 544px){
						.woocommerce ul.products li.product.mobile-align-left, .woocommerce-page ul.products li.product.mobile-align-left {
							text-align: left;
						}
						.woocommerce ul.products li.product.mobile-align-left .star-rating,
						.woocommerce ul.products li.product.mobile-align-left .button,
						.woocommerce-page ul.products li.product.mobile-align-left .star-rating,
						.woocommerce-page ul.products li.product.mobile-align-left .button {
							margin-left: 0;
							margin-right: 0;
						}
					}.ast-woo-active-filter-widget .wc-block-active-filters{display:flex;align-items:self-start;justify-content:space-between;}.ast-woo-active-filter-widget .wc-block-active-filters__clear-all{flex:none;margin-top:2px;}.woocommerce.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #customer_details.col2-set, .woocommerce-page.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #customer_details.col2-set{width:100%;}.woocommerce.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #order_review, .woocommerce.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #order_review_heading, .woocommerce-page.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #order_review, .woocommerce-page.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #order_review_heading{width:100%;float:inherit;}.elementor-widget-woocommerce-checkout-page .select2-container .select2-selection--single, .elementor-widget-woocommerce-cart .select2-container .select2-selection--single{padding:0;}.elementor-widget-woocommerce-checkout-page .woocommerce form .woocommerce-additional-fields, .elementor-widget-woocommerce-checkout-page .woocommerce form .shipping_address, .elementor-widget-woocommerce-my-account .woocommerce-MyAccount-navigation-link, .elementor-widget-woocommerce-cart .woocommerce a.remove{border:none;}.elementor-widget-woocommerce-cart .cart-collaterals .cart_totals > h2{background-color:inherit;border-bottom:0px;margin:0px;}.elementor-widget-woocommerce-cart .cart-collaterals .cart_totals{padding:0;border-color:inherit;border-radius:0;margin-bottom:0px;border-width:0px;}.elementor-widget-woocommerce-cart .woocommerce-cart-form .e-apply-coupon{line-height:initial;}.elementor-widget-woocommerce-my-account .woocommerce-MyAccount-content .woocommerce-Address-title h3{margin-bottom:var(--myaccount-section-title-spacing, 0px);}.elementor-widget-woocommerce-my-account .woocommerce-Addresses .woocommerce-Address-title, .elementor-widget-woocommerce-my-account table.shop_table thead, .elementor-widget-woocommerce-my-account .woocommerce-page table.shop_table thead, .elementor-widget-woocommerce-cart table.shop_table thead{background:inherit;}.elementor-widget-woocommerce-cart .e-apply-coupon, .elementor-widget-woocommerce-cart #coupon_code, .elementor-widget-woocommerce-checkout-page .e-apply-coupon, .elementor-widget-woocommerce-checkout-page #coupon_code{height:100%;}.elementor-widget-woocommerce-cart td.product-name dl.variation dt{font-weight:inherit;}.elementor-element.elementor-widget-woocommerce-checkout-page .e-checkout__container #customer_details .col-1{margin-bottom:0;}
</style>
<style id='woocommerce-inline-inline-css'>
.woocommerce form .form-row .required { visibility: visible; }
</style>
<link rel='stylesheet' id='ivory-search-styles-css' href='https://codecut.ai/wp-content/plugins/add-search-to-menu/public/css/ivory-search.min.css?ver=5.5.11' media='all' />
<link rel='stylesheet' id='woocommerce-pre-orders-main-css-css' href='https://codecut.ai/wp-content/plugins/pre-orders-for-woocommerce/media/css/main.css?ver=2.1' media='all' />
<link rel='stylesheet' id='brands-styles-css' href='https://codecut.ai/wp-content/plugins/woocommerce/assets/css/brands.css?ver=10.2.2' media='all' />
<link rel='stylesheet' id='woosb-frontend-css' href='https://codecut.ai/wp-content/plugins/woo-product-bundle/assets/css/frontend.css?ver=8.3.2' media='all' />
<link rel='stylesheet' id='xoo-wsc-fonts-css' href='https://codecut.ai/wp-content/plugins/side-cart-woocommerce/assets/css/xoo-wsc-fonts.css?ver=2.6.8' media='all' />
<link rel='stylesheet' id='xoo-wsc-style-css' href='https://codecut.ai/wp-content/plugins/side-cart-woocommerce/assets/css/xoo-wsc-style.css?ver=2.6.8' media='all' />
<style id='xoo-wsc-style-inline-css'>




.xoo-wsc-ft-buttons-cont a.xoo-wsc-ft-btn, .xoo-wsc-container .xoo-wsc-btn {
	background-color: #000000;
	color: #ffffff;
	border: 2px solid #ffffff;
	padding: 10px 20px;
}

.xoo-wsc-ft-buttons-cont a.xoo-wsc-ft-btn:hover, .xoo-wsc-container .xoo-wsc-btn:hover {
	background-color: #ffffff;
	color: #000000;
	border: 2px solid #000000;
}

 

.xoo-wsc-footer{
	background-color: #ffffff;
	color: #000000;
	padding: 10px 20px;
	box-shadow: 0 -1px 10px #0000001a;
}

.xoo-wsc-footer, .xoo-wsc-footer a, .xoo-wsc-footer .amount{
	font-size: 18px;
}

.xoo-wsc-btn .amount{
	color: #ffffff}

.xoo-wsc-btn:hover .amount{
	color: #000000;
}

.xoo-wsc-ft-buttons-cont{
	grid-template-columns: auto;
}

.xoo-wsc-basket{
	bottom: 12px;
	right: 1px;
	background-color: #ffffff;
	color: #000000;
	box-shadow: 0px 0px 15px 2px #0000001a;
	border-radius: 50%;
	display: none;
	width: 60px;
	height: 60px;
}


.xoo-wsc-bki{
	font-size: 30px}

.xoo-wsc-items-count{
	top: -12px;
	left: -12px;
}

.xoo-wsc-items-count{
	background-color: #000000;
	color: #ffffff;
}

.xoo-wsc-container, .xoo-wsc-slider{
	max-width: 365px;
	right: -365px;
	top: 0;bottom: 0;
	bottom: 0;
	font-family: }


.xoo-wsc-cart-active .xoo-wsc-container, .xoo-wsc-slider-active .xoo-wsc-slider{
	right: 0;
}


.xoo-wsc-cart-active .xoo-wsc-basket{
	right: 365px;
}

.xoo-wsc-slider{
	right: -365px;
}

span.xoo-wsch-close {
    font-size: 16px;
    right: 10px;
}

.xoo-wsch-top{
	justify-content: center;
}

.xoo-wsch-text{
	font-size: 20px;
}

.xoo-wsc-header{
	color: #000000;
	background-color: #ffffff;
	border-bottom: 2px solid #eee;
}


.xoo-wsc-body{
	background-color: #f8f9fa;
}

.xoo-wsc-products:not(.xoo-wsc-pattern-card), .xoo-wsc-products:not(.xoo-wsc-pattern-card) span.amount, .xoo-wsc-products:not(.xoo-wsc-pattern-card) a{
	font-size: 16px;
	color: #000000;
}

.xoo-wsc-products:not(.xoo-wsc-pattern-card) .xoo-wsc-product{
	padding: 10px 15px;
	margin: 10px 15px;
	border-radius: 5px;
	box-shadow: 0 2px 2px #00000005;
	background-color: #ffffff;
}

.xoo-wsc-sum-col{
	justify-content: center;
}


/** Shortcode **/
.xoo-wsc-sc-count{
	background-color: #000000;
	color: #ffffff;
}

.xoo-wsc-sc-bki{
	font-size: 28px;
	color: #000000;
}
.xoo-wsc-sc-cont{
	color: #000000;
}

.added_to_cart{
	display: none!important;
}

.xoo-wsc-product dl.variation {
	display: block;
}


.xoo-wsc-product-cont{
	padding: 10px 10px;
}

.xoo-wsc-products:not(.xoo-wsc-pattern-card) .xoo-wsc-img-col{
	width: 28%;
}

.xoo-wsc-pattern-card .xoo-wsc-img-col img{
	max-width: 100%;
	height: auto;
}

.xoo-wsc-products:not(.xoo-wsc-pattern-card) .xoo-wsc-sum-col{
	width: 72%;
}

.xoo-wsc-pattern-card .xoo-wsc-product-cont{
	width: 50% 
}

@media only screen and (max-width: 600px) {
	.xoo-wsc-pattern-card .xoo-wsc-product-cont  {
		width: 50%;
	}
}


.xoo-wsc-pattern-card .xoo-wsc-product{
	border: 0;
	box-shadow: 0px 10px 15px -12px #0000001a;
}


.xoo-wsc-sm-front{
	background-color: #eee;
}
.xoo-wsc-pattern-card, .xoo-wsc-sm-front{
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
}
.xoo-wsc-pattern-card, .xoo-wsc-img-col img, .xoo-wsc-img-col, .xoo-wsc-sm-back-cont{
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;
}
.xoo-wsc-sm-back{
	background-color: #fff;
}
.xoo-wsc-pattern-card, .xoo-wsc-pattern-card a, .xoo-wsc-pattern-card .amount{
	font-size: 16px;
}

.xoo-wsc-sm-front, .xoo-wsc-sm-front a, .xoo-wsc-sm-front .amount{
	color: #000;
}

.xoo-wsc-sm-back, .xoo-wsc-sm-back a, .xoo-wsc-sm-back .amount{
	color: #000;
}


.magictime {
    animation-duration: 0.5s;
}


</style>
<link rel='stylesheet' id='dflip-style-css' href='https://codecut.ai/wp-content/plugins/3d-flipbook-dflip-lite/assets/css/dflip.min.css?ver=2.3.75' media='all' />
<link rel='stylesheet' id='wpdreams-asl-basic-css' href='https://codecut.ai/wp-content/plugins/ajax-search-lite/css/style.basic.css?ver=4.13.1' media='all' />
<link rel='stylesheet' id='wpdreams-asl-instance-css' href='https://codecut.ai/wp-content/plugins/ajax-search-lite/css/style-curvy-black.css?ver=4.13.1' media='all' />
<link rel='stylesheet' id='elementor-frontend-css' href='https://codecut.ai/wp-content/uploads/elementor/css/custom-frontend.min.css?ver=1759201151' media='all' />
<link rel='stylesheet' id='widget-image-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.32.3' media='all' />
<link rel='stylesheet' id='widget-nav-menu-css' href='https://codecut.ai/wp-content/uploads/elementor/css/custom-pro-widget-nav-menu.min.css?ver=1759201151' media='all' />
<link rel='stylesheet' id='widget-heading-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.32.3' media='all' />
<link rel='stylesheet' id='widget-social-icons-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/css/widget-social-icons.min.css?ver=3.32.3' media='all' />
<link rel='stylesheet' id='e-apple-webkit-css' href='https://codecut.ai/wp-content/uploads/elementor/css/custom-apple-webkit.min.css?ver=1759201151' media='all' />
<link rel='stylesheet' id='widget-divider-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/css/widget-divider.min.css?ver=3.32.3' media='all' />
<link rel='stylesheet' id='widget-post-info-css' href='https://codecut.ai/wp-content/plugins/elementor-pro/assets/css/widget-post-info.min.css?ver=3.32.2' media='all' />
<link rel='stylesheet' id='widget-icon-list-css' href='https://codecut.ai/wp-content/uploads/elementor/css/custom-widget-icon-list.min.css?ver=1759201151' media='all' />
<link rel='stylesheet' id='elementor-icons-shared-0-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/lib/font-awesome/css/fontawesome.min.css?ver=5.15.3' media='all' />
<link rel='stylesheet' id='elementor-icons-fa-regular-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/lib/font-awesome/css/regular.min.css?ver=5.15.3' media='all' />
<link rel='stylesheet' id='elementor-icons-fa-solid-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/lib/font-awesome/css/solid.min.css?ver=5.15.3' media='all' />
<link rel='stylesheet' id='widget-posts-css' href='https://codecut.ai/wp-content/plugins/elementor-pro/assets/css/widget-posts.min.css?ver=3.32.2' media='all' />
<link rel='stylesheet' id='widget-form-css' href='https://codecut.ai/wp-content/plugins/elementor-pro/assets/css/widget-form.min.css?ver=3.32.2' media='all' />
<link rel='stylesheet' id='e-popup-css' href='https://codecut.ai/wp-content/plugins/elementor-pro/assets/css/conditionals/popup.min.css?ver=3.32.2' media='all' />
<link rel='stylesheet' id='elementor-icons-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/lib/eicons/css/elementor-icons.min.css?ver=5.44.0' media='all' />
<link rel='stylesheet' id='elementor-post-887-css' href='https://codecut.ai/wp-content/uploads/elementor/css/post-887.css?ver=1759201152' media='all' />
<link rel='stylesheet' id='elementor-post-7716-css' href='https://codecut.ai/wp-content/uploads/elementor/css/post-7716.css?ver=1759201154' media='all' />
<link rel='stylesheet' id='elementor-post-7720-css' href='https://codecut.ai/wp-content/uploads/elementor/css/post-7720.css?ver=1759201154' media='all' />
<link rel='stylesheet' id='elementor-post-7888-css' href='https://codecut.ai/wp-content/uploads/elementor/css/post-7888.css?ver=1759201855' media='all' />
<link rel='stylesheet' id='chart-builder-admin-css' href='https://codecut.ai/wp-content/plugins/chart-builder/admin/css/admin.css?ver=3.5.2' media='all' />
<link rel='stylesheet' id='elementor-post-11778-css' href='https://codecut.ai/wp-content/uploads/elementor/css/post-11778.css?ver=1759201154' media='all' />
<link rel='stylesheet' id='elementor-post-11767-css' href='https://codecut.ai/wp-content/uploads/elementor/css/post-11767.css?ver=1759201154' media='all' />
<link rel='stylesheet' id='simple-favorites-css' href='https://codecut.ai/wp-content/plugins/favorites/assets/css/favorites.css?ver=2.3.6' media='all' />
<link rel='stylesheet' id='astra-child-theme-css-css' href='https://codecut.ai/wp-content/themes/astra-child/style.css?ver=1.0.0' media='all' />
<link rel='stylesheet' id='ekit-widget-styles-css' href='https://codecut.ai/wp-content/plugins/elementskit-lite/widgets/init/assets/css/widget-styles.css?ver=3.5.4' media='all' />
<link rel='stylesheet' id='ekit-responsive-css' href='https://codecut.ai/wp-content/plugins/elementskit-lite/widgets/init/assets/css/responsive.css?ver=3.5.4' media='all' />
<link rel='stylesheet' id='um_modal-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-modal.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_ui-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/jquery-ui/jquery-ui.min.css?ver=1.13.2' media='all' />
<link rel='stylesheet' id='um_tipsy-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/tipsy/tipsy.min.css?ver=1.0.0a' media='all' />
<link rel='stylesheet' id='um_raty-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/raty/um-raty.min.css?ver=2.6.0' media='all' />
<link rel='stylesheet' id='select2-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/select2/select2.min.css?ver=4.0.13' media='all' />
<link rel='stylesheet' id='um_fileupload-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-fileupload.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_confirm-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/um-confirm/um-confirm.min.css?ver=1.0' media='all' />
<link rel='stylesheet' id='um_datetime-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/pickadate/default.min.css?ver=3.6.2' media='all' />
<link rel='stylesheet' id='um_datetime_date-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/pickadate/default.date.min.css?ver=3.6.2' media='all' />
<link rel='stylesheet' id='um_datetime_time-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/pickadate/default.time.min.css?ver=3.6.2' media='all' />
<link rel='stylesheet' id='um_fonticons_ii-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/legacy/fonticons/fonticons-ii.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_fonticons_fa-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/legacy/fonticons/fonticons-fa.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_fontawesome-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-fontawesome.min.css?ver=6.5.2' media='all' />
<link rel='stylesheet' id='um_common-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/common.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_responsive-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-responsive.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_styles-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-styles.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_crop-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/cropper/cropper.min.css?ver=1.6.1' media='all' />
<link rel='stylesheet' id='um_profile-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-profile.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_account-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-account.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_misc-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-misc.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='um_default_css-css' href='https://codecut.ai/wp-content/plugins/ultimate-member/assets/css/um-old-default.min.css?ver=2.10.5' media='all' />
<link rel='stylesheet' id='ecs-styles-css' href='https://codecut.ai/wp-content/plugins/ele-custom-skin/assets/css/ecs-style.css?ver=3.1.9' media='all' />
<link rel='stylesheet' id='elementor-icons-fa-brands-css' href='https://codecut.ai/wp-content/plugins/elementor/assets/lib/font-awesome/css/brands.min.css?ver=5.15.3' media='all' />
<!--[if IE]>
<script src="https://codecut.ai/wp-content/themes/astra/assets/js/minified/flexibility.min.js?ver=4.11.12" id="astra-flexibility-js"></script>
<script id="astra-flexibility-js-after">
flexibility(document.documentElement);
</script>
<![endif]-->
<script src="https://codecut.ai/wp-includes/js/jquery/jquery.min.js?ver=3.7.1" id="jquery-core-js"></script>
<script src="https://codecut.ai/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" id="jquery-migrate-js"></script>
<script src="https://codecut.ai/wp-content/plugins/woocommerce/assets/js/jquery-blockui/jquery.blockUI.min.js?ver=2.7.0-wc.10.2.2" id="jquery-blockui-js" defer data-wp-strategy="defer"></script>
<script src="https://codecut.ai/wp-content/plugins/woocommerce/assets/js/js-cookie/js.cookie.min.js?ver=2.1.4-wc.10.2.2" id="js-cookie-js" defer data-wp-strategy="defer"></script>
<script id="woocommerce-js-extra">
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","i18n_password_show":"Show password","i18n_password_hide":"Hide password"};
</script>
<script src="https://codecut.ai/wp-content/plugins/woocommerce/assets/js/frontend/woocommerce.min.js?ver=10.2.2" id="woocommerce-js" defer data-wp-strategy="defer"></script>
<script id="favorites-js-extra">
var favorites_data = {"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","nonce":"e6fb9b2a6a","favorite":"Favorite <i class=\"sf-icon-star-empty\"><\/i>","favorited":"Favorited <i class=\"sf-icon-star-full\"><\/i>","includecount":"","indicate_loading":"","loading_text":"Loading","loading_image":"","loading_image_active":"","loading_image_preload":"","cache_enabled":"1","button_options":{"button_type":"custom","custom_colors":false,"box_shadow":false,"include_count":false,"default":{"background_default":false,"border_default":false,"text_default":false,"icon_default":false,"count_default":false},"active":{"background_active":false,"border_active":false,"text_active":false,"icon_active":false,"count_active":false}},"authentication_modal_content":"<p>Please login to add favorites.<\/p>\n<p><a href=\"#\" data-favorites-modal-close>Dismiss this notice<\/a><\/p>\n","authentication_redirect":"1","dev_mode":"","logged_in":"","user_id":"0","authentication_redirect_url":"https:\/\/codecut.ai\/login\/"};
</script>
<script src="https://codecut.ai/wp-content/plugins/favorites/assets/js/favorites.min.js?ver=2.3.6" id="favorites-js"></script>

<!-- Google tag (gtag.js) snippet added by Site Kit -->
<!-- Google Analytics snippet added by Site Kit -->
<script src="https://www.googletagmanager.com/gtag/js?id=GT-T5MH98FN" id="google_gtagjs-js" async></script>
<script id="google_gtagjs-js-after">
window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}
gtag("set","linker",{"domains":["codecut.ai"]});
gtag("js", new Date());
gtag("set", "developer_id.dZTNiMT", true);
gtag("config", "GT-T5MH98FN", {"googlesitekit_post_type":"post"});
</script>
<script id="ecs_ajax_load-js-extra">
var ecs_ajax_params = {"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","posts":"{\"page\":0,\"name\":\"building-multi-agent-ai-langgraph-tutorial\",\"error\":\"\",\"m\":\"\",\"p\":0,\"post_parent\":\"\",\"subpost\":\"\",\"subpost_id\":\"\",\"attachment\":\"\",\"attachment_id\":0,\"pagename\":\"\",\"page_id\":0,\"second\":\"\",\"minute\":\"\",\"hour\":\"\",\"day\":0,\"monthnum\":0,\"year\":0,\"w\":0,\"category_name\":\"\",\"tag\":\"\",\"cat\":\"\",\"tag_id\":\"\",\"author\":\"\",\"author_name\":\"\",\"feed\":\"\",\"tb\":\"\",\"paged\":0,\"meta_key\":\"\",\"meta_value\":\"\",\"preview\":\"\",\"s\":\"\",\"sentence\":\"\",\"title\":\"\",\"fields\":\"all\",\"menu_order\":\"\",\"embed\":\"\",\"category__in\":[],\"category__not_in\":[],\"category__and\":[],\"post__in\":[],\"post__not_in\":[],\"post_name__in\":[],\"tag__in\":[],\"tag__not_in\":[],\"tag__and\":[],\"tag_slug__in\":[],\"tag_slug__and\":[],\"post_parent__in\":[],\"post_parent__not_in\":[],\"author__in\":[],\"author__not_in\":[],\"search_columns\":[],\"ignore_sticky_posts\":false,\"suppress_filters\":false,\"cache_results\":true,\"update_post_term_cache\":true,\"update_menu_item_cache\":false,\"lazy_load_term_meta\":true,\"update_post_meta_cache\":true,\"post_type\":\"\",\"posts_per_page\":10,\"nopaging\":false,\"comments_per_page\":\"20\",\"no_found_rows\":false,\"order\":\"DESC\"}"};
</script>
<script src="https://codecut.ai/wp-content/plugins/ele-custom-skin/assets/js/ecs_ajax_pagination.js?ver=3.1.9" id="ecs_ajax_load-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/um-gdpr.min.js?ver=2.10.5" id="um-gdpr-js"></script>
<script id="wc-cart-fragments-js-extra">
var wc_cart_fragments_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","cart_hash_key":"wc_cart_hash_1cfdc3c5b2ef0c45f5c31af69a18eca7","fragment_name":"wc_fragments_1cfdc3c5b2ef0c45f5c31af69a18eca7","request_timeout":"5000"};
</script>
<script src="https://codecut.ai/wp-content/plugins/woocommerce/assets/js/frontend/cart-fragments.min.js?ver=10.2.2" id="wc-cart-fragments-js" defer data-wp-strategy="defer"></script>
<script src="https://codecut.ai/wp-content/plugins/ele-custom-skin/assets/js/ecs.js?ver=3.1.9" id="ecs-script-js"></script>
<script data-cfasync='false' id='abst_ajax'>var bt_ajaxurl = 'https://codecut.ai/wp-admin/admin-ajax.php';var bt_adminurl = 'https://codecut.ai/wp-admin/';var bt_pluginurl = 'https://codecut.ai/wp-content/plugins/bt-bb-ab/';var bt_homeurl = 'https://codecut.ai';</script><script data-cfasync='false' id='abst_variables'>var bt_ajaxurl = 'https://codecut.ai/wp-admin/admin-ajax.php';var bt_experiments = {};
bt_experiments["16639"] = {"name":"Home Page Newsletter With Artcles","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["14112"] = {"name":"Test TextHero","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["13414"] = {"name":"Pricing Test","target_percentage":"100","url_query":"","conversion_page":"selector","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"#book-now","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["13229"] = {"name":"Header on Home Page","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["13221"] = {"name":"Daily Tips Menu","target_percentage":"100","url_query":"","conversion_page":"13137","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"Browse Tool","target_option_device_size":"all"};bt_experiments["12329"] = {"name":"Testimonial","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"full_page","is_current_user_track":true,"full_page_default_page":"11846","page_variations":{"12316":"https:\/\/codecut.ai\/"},"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["11840"] = {"name":"Form in home page","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"full_page","is_current_user_track":true,"full_page_default_page":"7749","page_variations":{"11846":"https:\/\/codecut.ai\/home-without-testimonial\/"},"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"}; bt_conversion_vars = [];</script><style>[bt-variation="Browse Tool"][bt-eid="13221"]{display:inherit !important;} .page-id-11846,.postid-11846{display:none;} .11846{display:none;} .page-id-7749,.postid-7749{display:none;} .7749{display:none;}</style><style id='absthide'>
/* Default hidden styles for all variations */
[bt-variation]:not(.bt-show-variation),
[data-bt-variation]:not(.bt-show-variation),
[class*='ab-var-'] {
    opacity: 0 !important;
    display: none !important;
}

/* First hidden element uses display: inherit */
[bt-variation]:not(.bt-show-variation):first-of-type,
[data-bt-variation]:not(.bt-show-variation):first-of-type,
[class*='ab-var-']:first-of-type {
    display: inherit !important; /* Ensure it still occupies layout space */
}

/* When the body has the ab-test-setup-complete class, revert to fully hidden */
body.ab-test-setup-complete [bt-variation]:not(.bt-show-variation),
body.ab-test-setup-complete [data-bt-variation]:not(.bt-show-variation),
body.ab-test-setup-complete [class*='ab-var-'] {
    display: none !important;
    opacity: 1 !important; /* Reset opacity just in case */
    visibility: visible !important; /* Reset visibility */
}

/* Don't apply variation hiding when Beaver Builder is active */
body.fl-builder-edit [bt-variation]:not(.bt-show-variation),
body.fl-builder-edit [data-bt-variation]:not(.bt-show-variation),
body.fl-builder-edit [class*='ab-var-'] {
    display: inherit !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure variations are visible in , Bakery  and Bricks builders */
#breakdance_canvas [bt-eid], #editor [data-bt-eid], body[data-builder-window='iframe'] .brx-body [bt-eid],  .vc_editor .vc_element [class*='ab-var-'] {
    display: inherit !important; /* Retain inherited display type */
    opacity: 1 !important; /* Fully visible */
    visibility: visible !important; /* Ensure it's interactable */
}
</style></script><style>[bt_hidden=true] { display: none !important; visibility: hidden !important; height: 0 !important; } </style><script data-cfasync='false'>
          var conversion_details = [];
          var current_page = [15293];
        </script><meta name="generator" content="Site Kit by Google 1.162.1" /><!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-*********-1"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-*********-1');
</script>

<script
type="text/javascript"
src="//static.klaviyo.com/onsite/js/klaviyo.js?company_id=SgpyU4"
></script>

	<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
					<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
				<link rel="preload" as="style" href="//fonts.googleapis.com/css?family=Open+Sans&display=swap" />
				<link rel="stylesheet" href="//fonts.googleapis.com/css?family=Open+Sans&display=swap" media="all" />
				<meta name="generator" content="Elementor 3.32.3; features: additional_custom_breakpoints; settings: css_print_method-external, google_font-enabled, font_display-auto">
			<style>
				.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
				.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
					background-image: none !important;
				}
				@media screen and (max-height: 1024px) {
					.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
					.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
						background-image: none !important;
					}
				}
				@media screen and (max-height: 640px) {
					.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
					.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
						background-image: none !important;
					}
				}
			</style>
			<script>document.addEventListener("DOMContentLoaded", function() { var enlacesConImagen = document.querySelectorAll('a img\[alt="Seraphinite Accelerator"\]'); enlacesConImagen.forEach(function(imagen) { var enlace = imagen.closest('a'); if (enlace) { enlace.style.display = "none"; } }); });</script>			            <style>
				            
					div[id*='ajaxsearchlitesettings'].searchsettings .asl_option_inner label {
						font-size: 0px !important;
						color: rgba(0, 0, 0, 0);
					}
					div[id*='ajaxsearchlitesettings'].searchsettings .asl_option_inner label:after {
						font-size: 11px !important;
						position: absolute;
						top: 0;
						left: 0;
						z-index: 1;
					}
					.asl_w_container {
						width: 100%;
						margin: 0px 0px 0px 0px;
						min-width: 200px;
					}
					div[id*='ajaxsearchlite'].asl_m {
						width: 100%;
					}
					div[id*='ajaxsearchliteres'].wpdreams_asl_results div.resdrg span.highlighted {
						font-weight: bold;
						color: rgba(217, 49, 43, 1);
						background-color: rgba(238, 238, 238, 1);
					}
					div[id*='ajaxsearchliteres'].wpdreams_asl_results .results img.asl_image {
						width: 70px;
						height: 70px;
						object-fit: cover;
					}
					div[id*='ajaxsearchlite'].asl_r .results {
						max-height: none;
					}
					div[id*='ajaxsearchlite'].asl_r {
						position: absolute;
					}
				
							.asl_w, .asl_w * {font-family:"Comforta" !important;}
							.asl_m input[type=search]::placeholder{font-family:"Comforta" !important;}
							.asl_m input[type=search]::-webkit-input-placeholder{font-family:"Comforta" !important;}
							.asl_m input[type=search]::-moz-placeholder{font-family:"Comforta" !important;}
							.asl_m input[type=search]:-ms-input-placeholder{font-family:"Comforta" !important;}
						
						div.asl_r.asl_w.vertical .results .item::after {
							display: block;
							position: absolute;
							bottom: 0;
							content: '';
							height: 1px;
							width: 100%;
							background: #D8D8D8;
						}
						div.asl_r.asl_w.vertical .results .item.asl_last_item::after {
							display: none;
						}
					
						@media only screen and (max-width: 640px) {
							.asl_w_container {
								width: 60% !important;
							}
						}
								            </style>
			            <link rel="icon" href="https://codecut.ai/wp-content/uploads/2023/12/cropped-icon-1_icon-1-1-32x32.png" sizes="32x32" />
<link rel="icon" href="https://codecut.ai/wp-content/uploads/2023/12/cropped-icon-1_icon-1-1-192x192.png" sizes="192x192" />
<link rel="apple-touch-icon" href="https://codecut.ai/wp-content/uploads/2023/12/cropped-icon-1_icon-1-1-180x180.png" />
<meta name="msapplication-TileImage" content="https://codecut.ai/wp-content/uploads/2023/12/cropped-icon-1_icon-1-1-270x270.png" />
		<style id="wp-custom-css">
			
.elementor-shortcode{
color: #ffffff;
font-family: 'Comfortaa', sans-serif;
font-size:25px;
font-weight: 600;
}
.comments-area p.logged-in-as {
    margin-bottom: 1em;
    color: #ffffff !important;
}

p.comment-form-mailpoet {
    color: #ffffff !important;
}

ul.wc-block-components-express-payment__event-buttons.wc-ppcp-sm__container {
    display: none;
}
.wc-block-components-express-payment-continue-rule.wc-block-components-express-payment-continue-rule--cart {
    display: none;
}
a.components-button.wc-block-components-button.wp-element-button.wc-block-cart__submit-button.contained {
    background-color: #72befa;
    color: black;
}
h2.woocommerce-order-details__title {
    color: #2F2D2E;
}

h2.woocommerce-column__title
{
	color: #2F2D2E;
}
div.wpforms-container-full button[type=submit]:focus:after
{
	border:none !important;
}
.wpforms-container .wpforms-field
{
	padding:8px 0px !important;
}
div.wpforms-container-full .wpforms-confirmation-container-full
{
	background-color:#72b7ee !important;
	border:none !important;
	text-align:center !important;
}
.wp-block-image img {
    margin-bottom: 20px;
    margin-bottom: 20px; 
}
/* Center images in single blog posts */
.single-post .entry-content img,
.single-post .elementor-widget-theme-post-content img {
    display: block;
    margin: 20px auto;
}
h1.product_title.entry-title {
    color: white !important;
    font-size: 40px;
}
ul.tabs.wc-tabs {
    display: none;
}
.ast-woocommerce-container
 {
    margin-top: 50px !important;
}
.woocommerce-js div.product form.cart .button.single_add_to_cart_button
{
    background: linear-gradient(271deg, rgba(114, 190, 250, 1) 0%, rgba(229, 131, 182, 1) 100%) !important;
	border-radius: 50px;
	font-weight:400;
	padding: 15px 20px 15px 20px;
}

.woocommerce-cart .cart-collaterals .cart_totals>h2
{
	color:#000;
}
.woocommerce-page.woocommerce-checkout #payment div.payment_box{
	background-color:#2a2a2a !important;
}
.woocommerce-checkout #payment div.payment_box::before
{
	display:none !important;
}
fieldset#wc-stripe-upe-form
{
	border:1px solid #6a6a6a1a !important;
}
p.form-row.woocommerce-SavedPaymentMethods-saveNew label
{
	color:#fff;
}		</style>
		</head>

<body data-rsssl=1 itemtype='https://schema.org/Blog' itemscope='itemscope' class="wp-singular post-template-default single single-post postid-15293 single-format-standard wp-custom-logo wp-theme-astra wp-child-theme-astra-child theme-astra bt-hidevars stk--is-astra-theme woocommerce-no-js astra ast-desktop ast-page-builder-template ast-no-sidebar astra-4.11.12 ast-header-custom-item-inside ast-full-width-primary-header group-blog ast-blog-single-style-1 ast-single-post ast-inherit-site-logo-transparent elementor-page-7888 ast-normal-title-enabled elementor-default elementor-kit-887">

<a
	class="skip-link screen-reader-text"
	href="#content"
	title="Skip to content">
		Skip to content</a>

<div
class="hfeed site" id="page">
			<header data-elementor-type="header" data-elementor-id="7716" class="elementor elementor-7716 elementor-location-header" data-elementor-post-type="elementor_library">
			<div class="elementor-element elementor-element-b75b880 e-flex e-con-boxed e-con e-parent" data-id="b75b880" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
		<div class="elementor-element elementor-element-42bc3e7 e-con-full e-flex e-con e-child" data-id="42bc3e7" data-element_type="container">
				<div class="elementor-element elementor-element-ea0461e elementor-widget elementor-widget-image" data-id="ea0461e" data-element_type="widget" data-widget_type="image.default">
				<div class="elementor-widget-container">
																<a href="https://codecut.ai/">
							<img fetchpriority="high" width="1052" height="394" src="https://codecut.ai/wp-content/uploads/2025/01/logo_compressed-1052x394.png" class="attachment-medium size-medium wp-image-12564" alt="" srcset="https://codecut.ai/wp-content/uploads/2025/01/logo_compressed-1052x394.png 1052w, https://codecut.ai/wp-content/uploads/2025/01/logo_compressed-768x288.png 768w, https://codecut.ai/wp-content/uploads/2025/01/logo_compressed-600x225.png 600w, https://codecut.ai/wp-content/uploads/2025/01/logo_compressed.png 1319w" sizes="(max-width: 1052px) 100vw, 1052px" />								</a>
															</div>
				</div>
				</div>
		<div class="elementor-element elementor-element-3c72f79 e-con-full e-flex e-con e-child" data-id="3c72f79" data-element_type="container">
				<div class="elementor-element elementor-element-9762762 elementor-nav-menu__align-center elementor-nav-menu--stretch elementor-widget-mobile__width-auto elementor-widget-tablet__width-inherit elementor-widget-widescreen__width-initial elementor-widget-laptop__width-inherit elementor-nav-menu--dropdown-tablet elementor-nav-menu__text-align-aside elementor-nav-menu--toggle elementor-nav-menu--burger elementor-widget elementor-widget-nav-menu" data-id="9762762" data-element_type="widget" data-settings="{&quot;full_width&quot;:&quot;stretch&quot;,&quot;submenu_icon&quot;:{&quot;value&quot;:&quot;&lt;i class=\&quot;fas fa-angle-down\&quot; aria-hidden=\&quot;true\&quot;&gt;&lt;\/i&gt;&quot;,&quot;library&quot;:&quot;fa-solid&quot;},&quot;layout&quot;:&quot;horizontal&quot;,&quot;toggle&quot;:&quot;burger&quot;}" data-widget_type="nav-menu.default">
				<div class="elementor-widget-container">
								<nav aria-label="Menu" class="elementor-nav-menu--main elementor-nav-menu__container elementor-nav-menu--layout-horizontal e--pointer-none">
				<ul id="menu-1-9762762" class="elementor-nav-menu"><li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-17593"><a href="https://codecut.ai/newsletter/" class="elementor-item menu-link">Newsletter Archive</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-14736"><a href="https://codecut.ai/blog/" class="elementor-item menu-link">Blog</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-16107"><a href="https://codecut.ai/production-ready-data-science/" class="elementor-item menu-link">Book</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-14322"><a href="https://codecut.ai/sponsor/" class="elementor-item menu-link">Sponsor</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-12010"><a aria-expanded="false" href="https://codecut.ai/user-2/" class="elementor-item menu-link"><i class="_mi _before dashicons dashicons-admin-users" aria-hidden="true" style="font-size:1.3em;"></i><span>Account</span></a>
<ul class="sub-menu elementor-nav-menu--dropdown">
	<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10295"><a href="https://codecut.ai/login-2/" class="elementor-sub-item menu-link">Login</a></li>
</ul>
</li>
</ul>			</nav>
					<div class="elementor-menu-toggle" role="button" tabindex="0" aria-label="Menu Toggle" aria-expanded="false">
			<i aria-hidden="true" role="presentation" class="elementor-menu-toggle__icon--open eicon-menu-bar"></i><i aria-hidden="true" role="presentation" class="elementor-menu-toggle__icon--close eicon-close"></i>		</div>
					<nav class="elementor-nav-menu--dropdown elementor-nav-menu__container" aria-hidden="true">
				<ul id="menu-2-9762762" class="elementor-nav-menu"><li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-17593"><a href="https://codecut.ai/newsletter/" class="elementor-item menu-link" tabindex="-1">Newsletter Archive</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-14736"><a href="https://codecut.ai/blog/" class="elementor-item menu-link" tabindex="-1">Blog</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-16107"><a href="https://codecut.ai/production-ready-data-science/" class="elementor-item menu-link" tabindex="-1">Book</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-14322"><a href="https://codecut.ai/sponsor/" class="elementor-item menu-link" tabindex="-1">Sponsor</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-12010"><a aria-expanded="false" href="https://codecut.ai/user-2/" class="elementor-item menu-link" tabindex="-1"><i class="_mi _before dashicons dashicons-admin-users" aria-hidden="true" style="font-size:1.3em;"></i><span>Account</span></a>
<ul class="sub-menu elementor-nav-menu--dropdown">
	<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10295"><a href="https://codecut.ai/login-2/" class="elementor-sub-item menu-link" tabindex="-1">Login</a></li>
</ul>
</li>
</ul>			</nav>
						</div>
				</div>
				</div>
		<div class="elementor-element elementor-element-863881f e-con-full e-flex e-con e-child" data-id="863881f" data-element_type="container">
				<div class="elementor-element elementor-element-2212832 elementor-widget elementor-widget-shortcode" data-id="2212832" data-element_type="widget" data-widget_type="shortcode.default">
				<div class="elementor-widget-container">
							<div class="elementor-shortcode"><div class="asl_w_container asl_w_container_1" data-id="1" data-instance="1">
	<div id='ajaxsearchlite1'
		 data-id="1"
		 data-instance="1"
		 class="asl_w asl_m asl_m_1 asl_m_1_1">
		<div class="probox">

	
	<div class='prosettings' style='display:none;' data-opened=0>
				<div class='innericon'>
			<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="22" height="22" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve">
					<polygon transform = "rotate(90 256 256)" points="142.332,104.886 197.48,50 402.5,256 197.48,462 142.332,407.113 292.727,256 "/>
				</svg>
		</div>
	</div>

	
	
	<div class='proinput'>
        <form role="search" action='#' autocomplete="off"
			  aria-label="Search form">
			<input aria-label="Search input"
				   type='search' class='orig'
				   tabindex="0"
				   name='phrase'
				   placeholder='Search here..'
				   value=''
				   autocomplete="off"/>
			<input aria-label="Search autocomplete"
				   type='text'
				   class='autocomplete'
				   tabindex="-1"
				   name='phrase'
				   value=''
				   autocomplete="off" disabled/>
			<input type='submit' value="Start search" style='width:0; height: 0; visibility: hidden;'>
		</form>
	</div>

	
	
	<button class='promagnifier' tabindex="0" aria-label="Search magnifier">
				<span class='innericon' style="display:block;">
			<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="22" height="22" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve">
					<path d="M460.355,421.59L353.844,315.078c20.041-27.553,31.885-61.437,31.885-98.037
						C385.729,124.934,310.793,50,218.686,50C126.58,50,51.645,124.934,51.645,217.041c0,92.106,74.936,167.041,167.041,167.041
						c34.912,0,67.352-10.773,94.184-29.158L419.945,462L460.355,421.59z M100.631,217.041c0-65.096,52.959-118.056,118.055-118.056
						c65.098,0,118.057,52.959,118.057,118.056c0,65.096-52.959,118.056-118.057,118.056C153.59,335.097,100.631,282.137,100.631,217.041
						z"/>
				</svg>
		</span>
	</button>

	
	
	<div class='proloading'>

		<div class="asl_loader"><div class="asl_loader-inner asl_simple-circle"></div></div>

			</div>

			<div class='proclose'>
			<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
				 y="0px"
				 width="12" height="12" viewBox="0 0 512 512" enable-background="new 0 0 512 512"
				 xml:space="preserve">
				<polygon points="438.393,374.595 319.757,255.977 438.378,137.348 374.595,73.607 255.995,192.225 137.375,73.622 73.607,137.352 192.246,255.983 73.622,374.625 137.352,438.393 256.002,319.734 374.652,438.378 "/>
			</svg>
		</div>
	
	
</div>	</div>
	<div class='asl_data_container' style="display:none !important;">
		<div class="asl_init_data wpdreams_asl_data_ct"
	 style="display:none !important;"
	 id="asl_init_id_1"
	 data-asl-id="1"
	 data-asl-instance="1"
	 data-asldata="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"></div>	<div id="asl_hidden_data">
		<svg style="position:absolute" height="0" width="0">
			<filter id="aslblur">
				<feGaussianBlur in="SourceGraphic" stdDeviation="4"/>
			</filter>
		</svg>
		<svg style="position:absolute" height="0" width="0">
			<filter id="no_aslblur"></filter>
		</svg>
	</div>
	</div>

	<div id='ajaxsearchliteres1'
	 class='vertical wpdreams_asl_results asl_w asl_r asl_r_1 asl_r_1_1'>

	
	<div class="results">

		
		<div class="resdrg">
		</div>

		
	</div>

	
	
</div>

	<div id='__original__ajaxsearchlitesettings1'
		 data-id="1"
		 class="searchsettings wpdreams_asl_settings asl_w asl_s asl_s_1">
		<form name='options'
	  aria-label="Search settings form"
	  autocomplete = 'off'>

	
	
	<input type="hidden" name="filters_changed" style="display:none;" value="0">
	<input type="hidden" name="filters_initial" style="display:none;" value="1">

	<div class="asl_option_inner hiddend">
		<input type='hidden' name='qtranslate_lang' id='qtranslate_lang1'
			   value='0'/>
	</div>

	
	
	<fieldset class="asl_sett_scroll">
		<legend style="display: none;">Generic selectors</legend>
		<div class="asl_option" tabindex="0">
			<div class="asl_option_inner">
				<input type="checkbox" value="exact"
					   aria-label="Exact matches only"
					   name="asl_gen[]" />
				<div class="asl_option_checkbox"></div>
			</div>
			<div class="asl_option_label">
				Exact matches only			</div>
		</div>
		<div class="asl_option" tabindex="0">
			<div class="asl_option_inner">
				<input type="checkbox" value="title"
					   aria-label="Search in title"
					   name="asl_gen[]"  checked="checked"/>
				<div class="asl_option_checkbox"></div>
			</div>
			<div class="asl_option_label">
				Search in title			</div>
		</div>
		<div class="asl_option" tabindex="0">
			<div class="asl_option_inner">
				<input type="checkbox" value="content"
					   aria-label="Search in content"
					   name="asl_gen[]"  checked="checked"/>
				<div class="asl_option_checkbox"></div>
			</div>
			<div class="asl_option_label">
				Search in content			</div>
		</div>
		<div class="asl_option_inner hiddend">
			<input type="checkbox" value="excerpt"
				   aria-label="Search in excerpt"
				   name="asl_gen[]"  checked="checked"/>
			<div class="asl_option_checkbox"></div>
		</div>
	</fieldset>
	<fieldset class="asl_sett_scroll">
		<legend style="display: none;">Post Type Selectors</legend>
					<div class="asl_option_inner hiddend">
				<input type="checkbox" value="post"
					   aria-label="Hidden option, ignore please"
					   name="customset[]" checked="checked"/>
			</div>
						<div class="asl_option_inner hiddend">
				<input type="checkbox" value="page"
					   aria-label="Hidden option, ignore please"
					   name="customset[]" checked="checked"/>
			</div>
				</fieldset>
	
		<fieldset>
							<legend>Filter by Categories</legend>
						<div class='categoryfilter asl_sett_scroll'>
									<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="362"
								   aria-label="About Article"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							About Article						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="448"
								   aria-label="Analyze Data"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Analyze Data						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="502"
								   aria-label="Archive"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Archive						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="505"
								   aria-label="Best Practices"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Best Practices						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="475"
								   aria-label="Better Outputs"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Better Outputs						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="235"
								   aria-label="Blog"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Blog						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="462"
								   aria-label="Code Optimization"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Code Optimization						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="460"
								   aria-label="Code Quality"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Code Quality						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="468"
								   aria-label="Command Line"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Command Line						</div>
					</div>
										<div class="asl_option hiddend" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="1"
								   aria-label="Daily tips"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Daily tips						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="472"
								   aria-label="Dashboard"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Dashboard						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="447"
								   aria-label="Data Analysis &amp; Manipulation"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Data Analysis &amp; Manipulation						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="501"
								   aria-label="Data Engineer"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Data Engineer						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="508"
								   aria-label="Data Visualization"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Data Visualization						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="499"
								   aria-label="DataFrame"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							DataFrame						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="492"
								   aria-label="Delta Lake"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Delta Lake						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="489"
								   aria-label="DevOps"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							DevOps						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="493"
								   aria-label="DuckDB"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							DuckDB						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="469"
								   aria-label="Environment Management"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Environment Management						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="457"
								   aria-label="Feature Engineer"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Feature Engineer						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="470"
								   aria-label="Git"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Git						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="467"
								   aria-label="Jupyter Notebook"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Jupyter Notebook						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="500"
								   aria-label="LLM"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							LLM						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="459"
								   aria-label="LLM Tools"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							LLM Tools						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="509"
								   aria-label="Machine Learning"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Machine Learning						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="455"
								   aria-label="Machine Learning &amp; AI"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Machine Learning &amp; AI						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="456"
								   aria-label="Machine Learning Tools"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Machine Learning Tools						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="449"
								   aria-label="Manage Data"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Manage Data						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="498"
								   aria-label="MLOps"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							MLOps						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="458"
								   aria-label="Natural Language Processing"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Natural Language Processing						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="636"
								   aria-label="Newsletter Archive"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Newsletter Archive						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="452"
								   aria-label="NumPy"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							NumPy						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="450"
								   aria-label="Pandas"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Pandas						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="491"
								   aria-label="Polars"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Polars						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="453"
								   aria-label="PySpark"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							PySpark						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="464"
								   aria-label="Python Helpers"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Python Helpers						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="463"
								   aria-label="Python Tips"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Python Tips						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="503"
								   aria-label="Python Utilities"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Python Utilities						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="488"
								   aria-label="Scrape Data"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Scrape Data						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="454"
								   aria-label="SQL"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							SQL						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="465"
								   aria-label="Testing"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Testing						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="461"
								   aria-label="Time Series"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Time Series						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="466"
								   aria-label="Tools"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Tools						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="473"
								   aria-label="Visualization"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Visualization						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="471"
								   aria-label="Visualization &amp; Reporting"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Visualization &amp; Reporting						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="474"
								   aria-label="Workflow &amp; Automation"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Workflow &amp; Automation						</div>
					</div>
										<div class="asl_option" tabindex="0">
						<div class="asl_option_inner">
							<input type="checkbox" value="476"
								   aria-label="Workflow Automation"
								   name="categoryset[]" checked="checked"/>
							<div class="asl_option_checkbox"></div>
						</div>
						<div class="asl_option_label">
							Workflow Automation						</div>
					</div>
					
			</div>
		</fieldset>
		</form>
	</div>
</div></div>
						</div>
				</div>
				</div>
					</div>
				</div>
				</header>
			<div id="content" class="site-content">
		<div class="ast-container">
		

	<div id="primary" class="content-area primary">

		
					<main id="main" class="site-main">
						<div data-elementor-type="single-post" data-elementor-id="7888" class="elementor elementor-7888 elementor-location-single post-15293 post type-post status-publish format-standard has-post-thumbnail hentry category-blog category-llm ast-article-single" data-elementor-post-type="elementor_library">
			<div class="elementor-element elementor-element-93cf45a e-flex e-con-boxed e-con e-parent" data-id="93cf45a" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-12dc192 elementor-widget elementor-widget-theme-post-title elementor-page-title elementor-widget-heading" data-id="12dc192" data-element_type="widget" data-widget_type="theme-post-title.default">
				<div class="elementor-widget-container">
					<h1 class="elementor-heading-title elementor-size-default">Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial</h1>				</div>
				</div>
				<div class="elementor-element elementor-element-31795fb elementor-widget elementor-widget-woocommerce-breadcrumb" data-id="31795fb" data-element_type="widget" data-widget_type="woocommerce-breadcrumb.default">
				<div class="elementor-widget-container">
					<nav class="woocommerce-breadcrumb" aria-label="Breadcrumb"><a href="https://codecut.ai">Home</a>&nbsp;&#47;&nbsp;<a href="https://codecut.ai/blog/">Blog</a>&nbsp;&#47;&nbsp;Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial</nav>				</div>
				</div>
				<div class="elementor-element elementor-element-405e543 elementor-align-center elementor-widget elementor-widget-post-info" data-id="405e543" data-element_type="widget" data-widget_type="post-info.default">
				<div class="elementor-widget-container">
							<ul class="elementor-inline-items elementor-icon-list-items elementor-post-info">
								<li class="elementor-icon-list-item elementor-repeater-item-54fd367 elementor-inline-item" itemprop="datePublished">
						<a href="https://codecut.ai/2025/06/10/">
											<span class="elementor-icon-list-icon">
								<i aria-hidden="true" class="fas fa-calendar"></i>							</span>
									<span class="elementor-icon-list-text elementor-post-info__item elementor-post-info__item--type-date">
										<time>June 10, 2025</time>					</span>
									</a>
				</li>
				</ul>
						</div>
				</div>
					</div>
				</div>
		<div class="elementor-element elementor-element-914b2e5 e-con-full e-flex e-con e-parent" data-id="914b2e5" data-element_type="container">
		<div class="elementor-element elementor-element-a18a40b e-con-full e-flex e-con e-child" data-id="a18a40b" data-element_type="container">
				<div class="elementor-element elementor-element-77a40af elementor-widget elementor-widget-theme-post-featured-image elementor-widget-image" data-id="77a40af" data-element_type="widget" data-widget_type="theme-post-featured-image.default">
				<div class="elementor-widget-container">
															<img width="931" height="488" src="https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png" class="attachment-full size-full wp-image-15294" alt="" srcset="https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png 931w, https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image-768x403.png 768w, https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image-600x315.png 600w" sizes="(max-width: 931px) 100vw, 931px" />															</div>
				</div>
				<div class="elementor-element elementor-element-a7637e2 elementor-widget elementor-widget-theme-post-title elementor-page-title elementor-widget-heading" data-id="a7637e2" data-element_type="widget" data-widget_type="theme-post-title.default">
				<div class="elementor-widget-container">
					<h1 class="elementor-heading-title elementor-size-large">Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial</h1>				</div>
				</div>
				<div class="elementor-element elementor-element-c2ad409 elementor-widget-widescreen__width-initial elementor-widget elementor-widget-theme-post-content" data-id="c2ad409" data-element_type="widget" data-widget_type="theme-post-content.default">
				<div class="elementor-widget-container">
					
                
                    <!--begin code -->

                    
                    <div class="pp-multiple-authors-boxes-wrapper pp-multiple-authors-wrapper pp-multiple-authors-layout-inline multiple-authors-target-the-content box-post-id-14892 box-instance-id-1 ppma_boxes_14892"
                    data-post_id="14892"
                    data-instance_id="1"
                    data-additional_class="pp-multiple-authors-layout-inline.multiple-authors-target-the-content"
                    data-original_class="pp-multiple-authors-boxes-wrapper pp-multiple-authors-wrapper box-post-id-14892 box-instance-id-1">
                                                <span class="ppma-layout-prefix"></span>
                        <div class="ppma-author-category-wrap">
                                                                                                                                    <span class="ppma-category-group ppma-category-group-1 category-index-0">
                                                                                                                        <ul class="pp-multiple-authors-boxes-ul author-ul-0">
                                                                                                                                                                                                                                                                                                                                                            
                                                                                                                    <li class="pp-multiple-authors-boxes-li author_index_0 author_khuyentran1476 has-avatar">
                                                                                                                                                                                    <div class="pp-author-boxes-avatar">
                                                                    <div class="avatar-image">
                                                                                                                                                                                                                <img alt='Khuyen Tran photo' src='https://codecut.ai/wp-content/uploads/2025/05/khuyen_headshot.png' srcset='https://codecut.ai/wp-content/uploads/2025/05/khuyen_headshot.png' class='multiple_authors_guest_author_avatar avatar' height='100' width='100'/>                                                                                                                                                                                                            </div>
                                                                    <h2 class="pp-author-boxes-name multiple-authors-name"><a href="https://codecut.ai/author/khuyentran1476/" rel="author" title="Khuyen Tran" class="author url fn">Khuyen Tran</a></h2>                                                                </div>
                                                            
                                                            <div class="pp-author-boxes-avatar-details">
                                                                                                                                                                                                                                                                
                                                                                                                                
                                                                                                                            </div>
                                                                                                                                                                                                                        </li>
                                                                                                                                                                                                                                                                            
                                                                                                                    <li class="pp-multiple-authors-boxes-li author_index_1 author_bexgboost has-avatar">
                                                                                                                                                                                    <div class="pp-author-boxes-avatar">
                                                                    <div class="avatar-image">
                                                                                                                                                                                                                <img alt='Bex Tuychiev Photo' src='https://codecut.ai/wp-content/uploads/2025/05/Bex-Tuychiev-1.png' srcset='https://codecut.ai/wp-content/uploads/2025/05/Bex-Tuychiev-1.png' class='multiple_authors_guest_author_avatar avatar' height='100' width='100'/>                                                                                                                                                                                                            </div>
                                                                    <h2 class="pp-author-boxes-name multiple-authors-name"><a href="https://codecut.ai/author/bexgboost/" rel="author" title="Bex Tuychiev" class="author url fn">Bex Tuychiev</a></h2>                                                                </div>
                                                            
                                                            <div class="pp-author-boxes-avatar-details">
                                                                                                                                                                                                                                                                
                                                                                                                                
                                                                                                                            </div>
                                                                                                                                                                                                                        </li>
                                                                                                                                                                                                                                    </ul>
                                                                            </span>
                                                                                                                                                                                    </div>
                        <span class="ppma-layout-suffix"></span>
                                            </div>
                    <!--end code -->
                    
                
                            
        <h2>Table of Contents</h2>
<ul>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#getting-started-with-langgraph">Getting Started with LangGraph</a>
<ul>
<li><a href="#environment-setup">Environment Setup</a></li>
<li><a href="#creating-agents-in-langgraph">Creating Agents in LangGraph</a></li>
<li><a href="#creating-a-supervisor-multi-agent-system">Creating a Supervisor Multi-Agent System</a></li>
</ul>
</li>
<li><a href="#building-the-investment-board-of-agents">Building the Investment Board of Agents</a>
<ul>
<li><a href="#high-level-application-overview">High-level Application Overview</a></li>
<li><a href="#setting-up-configuration">Setting up configuration</a></li>
<li><a href="#defining-tools">Defining Tools</a></li>
<li><a href="#creating-the-agents">Creating the Agents</a></li>
<li><a href="#formatting-the-output">Formatting the Output</a></li>
<li><a href="#adding-a-terminal-based-chatbot-interface">Adding a Terminal-based Chatbot Interface</a></li>
<li><a href="#testing-and-running-the-system">Testing and Running the System</a></li>
</ul>
</li>
<li><a href="#final-thoughts">Final Thoughts</a></li>
</ul>
<h2 id="introduction">Introduction</h2>
<p>Have you ever noticed how a single chatbot can only analyze problems through one perspective at a time instead of weighing multiple viewpoints like humans do?</p>
<p>To demonstrate this, let&#8217;s see how a single agent would analyze Apple&#8217;s financial health:</p>
<pre><code class="language-python">from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate

# Single agent approach
single_agent = ChatOpenAI(model=&quot;gpt-4&quot;)
prompt = ChatPromptTemplate.from_messages([
    (&quot;system&quot;, &quot;You are a financial analyst. Analyze the company&#039;s health from all angles.&quot;),
    (&quot;user&quot;, &quot;Analyze Apple&#039;s financial health considering growth, risks, and market position.&quot;)
])

# The agent tries to be both optimistic and pessimistic at once
response = single_agent.invoke(prompt.format_messages())
print(response.content)
</code></pre>
<p>The output might look like this:</p>
<pre><code class="language-text">Apple demonstrates a healthy financial status with consistent growth and a strong market position. Its innovative product pipeline, growing services segment, high-profit margins and robust balance sheets substantiate its stability.
</code></pre>
<p>There are several issues with this response:</p>
<ol>
<li><strong>Confirmation Bias</strong>: The response tends to be one-sided (in this case, overly positive) because there&#8217;s no counter-balancing perspective. It&#8217;s like having only one voice in a debate.</li>
<li><strong>No Decision Making</strong>: The single bot only provides analysis without making any concrete decisions or recommendations.</li>
</ol>
<p>This limited perspective makes it particularly challenging for complex tasks like investment analysis that require balancing different factors and perspectives.</p>
<p>LangGraph solves this by providing a framework for building coordinated multi-agent systems with specialized agents working together through structured communication.</p>
<p>Here&#8217;s how the same analysis would look with a multi-agent system:</p>
<pre><code class="language-text">Key bull arguments centered on Apple&#039;s strong financials (record Q1 2024 profits and cash, robust Services and ecosystem growth), operational resilience, and the potential for future innovation.

The bears countered with Apple&#039;s slowing annual growth, overreliance on iPhone, regulatory headwinds, China risks, and concerns about high valuation and lack of breakthrough innovation.

The chairman ultimately decided on a HOLD/RESEARCH MORE position, balancing Apple&#039;s financial strength and stability against meaningful macro, regulatory, and growth risks.
</code></pre>
<p>This example illustrates the structured debate process between specialized agents:</p>
<ul>
<li>The bull agent starts by making its investment case</li>
<li>The bear agent responds by highlighting key risks</li>
<li>The chairman carefully weighs both perspectives and makes a final decision</li>
</ul>
<p>This multi-agent approach produces a more thorough and balanced analysis compared to a single agent trying to consider all angles at once.</p>
<h2>Key Takeaways</h2>
<p>Here&#8217;s what you&#8217;ll learn:</p>
<ul>
<li>Build coordinated multi-agent systems that debate from different perspectives instead of single biased responses</li>
<li>Create specialized agents with distinct tools and prompts for bull/bear investment analysis</li>
<li>Implement supervisor workflows that route conversations between agents in structured sequences</li>
<li>Deploy production-ready investment committees with real-time market data integration</li>
<li>Scale beyond finance to any domain requiring multiple viewpoints and structured decision-making</li>
</ul>
<div class="klaviyo-form-VWXSdu" style="margin: 20px;"></div>
<h2 id="getting-started-with-langgraph">Getting Started with LangGraph</h2>
<h3 id="environment-setup">Environment Setup</h3>
<p>First, you should setup your environment with the following packages:</p>
<pre><code class="language-bash">pip install langgraph langgraph-supervisor langchain langchain-core langchain-tavily langchain-openai python-dotenv
</code></pre>
<p>These packages are necessary for our investment committee system:</p>
<ul>
<li><code>langgraph</code>: Core framework for building multi-agent systems with state management</li>
<li><code>langgraph-supervisor</code>: Provides the supervisor pattern for agent coordination</li>
<li><code>langchain</code> and <code>langchain-core</code>: Foundational components for LLM applications</li>
<li><code>langchain-tavily</code>: Integration with Tavily search API for market research</li>
<li><code>langchain-openai</code>: OpenAI model integrations</li>
<li><code>python-dotenv</code>: Environment variable management for API keys</li>
</ul>
<p>Next, create a <code>.env</code> file and populate it with your API keys from <a href="https://platform.openai.com/">OpenAI</a> and <a href="https://www.tavily.com/">Tavily</a> (a search engine API).</p>
<pre><code class="language-bash">TAVILY_API_KEY=&#039;your-api-key-goes-here&#039;
OPENAI_API_KEY=&#039;your-api-key-goes-here&#039;
</code></pre>
<p>Now, let&#8217;s see how to create your first agent in LangGraph.</p>
<blockquote>
<p>Note that going forward, <a href="https://codecut.ai/private-ai-workflows-langchain-ollama/">familiarity with LangChain basics</a> will be helpful.</p>
</blockquote>
<h3 id="creating-agents-in-langgraph">Creating Agents in LangGraph</h3>
<p>LangGraph makes it very easy to create your first agent. Let&#8217;s walk through how to create a general purpose assistant with web search functionality in a few lines of code.</p>
<p>First, we load our environment variables containing API keys using <code>load_dotenv()</code> and import necessary components:</p>
<pre><code class="language-python">from dotenv import load_dotenv

from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI
from langchain_tavily import TavilySearch

load_dotenv()
</code></pre>
<p>Next, initialize the search tool with <code>TavilySearch(max_results=3)</code>, which will return the top three search results for any query.</p>
<pre><code class="language-python">web_search = TavilySearch(max_results=3)
</code></pre>
<p>Create the agent using <code>create_react_agent()</code>, which implements the ReAct pattern, a framework that enables agents to reason about actions, execute them with tools, observe results, and plan next steps accordingly.</p>
<pre><code class="language-python">agent = create_react_agent(
    model=ChatOpenAI(model=&quot;gpt-4o&quot;),
    tools=[web_search],
    prompt=&quot;You are a helpful assistant that can search the web for information and summarize the results to enhance your output.&quot;,
)
</code></pre>
<p>We configure the agent with the three core components:</p>
<ul>
<li>A language model (GPT-4o in this case)</li>
<li>A list of tools that allows the agent to connect to external tools and APIs like a search engine</li>
<li>A system prompt that defines the agent&#8217;s role and behavior</li>
</ul>
<p>Finally, we invoke the agent with a question about today&#8217;s stock market activity:</p>
<pre><code class="language-python">response = agent.invoke(
    {
        &quot;messages&quot;: [
            {
                &quot;role&quot;: &quot;user&quot;,
                &quot;content&quot;: &quot;Find the open and close prices of Apple&#039;s stocks for June 1, 2025.&quot;
            }
        ]
    }
)

print(response[&#039;messages&#039;][-1].content)
</code></pre>
<pre><code class="language-text">June 1, 2025, was a Sunday, so financial markets were closed. The next trading day was June 2, 2025. On June 2, 2025, Apple&#039;s (AAPL) stock:

- Opened at: $200.28
- Closed at: $201.70

You can verify this information on trusted sources such as Yahoo Finance and Macrotrends:
- Source: Yahoo Finance (historical data page)
- Source: Macrotrends Apple stock history

If you need data for the trading day immediately before June 2, 2025, let me know!
</code></pre>
<p>The output demonstrates the ReAct pattern in action:</p>
<ul>
<li>The agent reasons about what information it needs (stock prices for a specific date)</li>
<li>Executes the task using the web search tool to find the information</li>
<li>Observes the results and reasons again about their validity</li>
<li>Plans next steps accordingly (in this case, providing the data for the next trading day)</li>
</ul>
<h3 id="creating-a-supervisor-multi-agent-system">Creating a Supervisor Multi-Agent System</h3>
<p>A supervisor is a special agent that manages the workflow between multiple agents. It is responsible for:</p>
<ul>
<li>Routing the workflow between agents</li>
<li>Managing the conversation history</li>
<li>Ensuring the agents are working together to achieve the goal</li>
</ul>
<p>Let&#8217;s create a supervisor multi-agent system with three agents:</p>
<pre><code class="language-python">from langgraph_supervisor import create_supervisor

# Define domain-specific agents with their own tools and prompts
agent1 = create_react_agent(...)
agent2 = create_react_agent(...)
agent3 = create_react_agent(...)

# Create a memory checkpointer to persist conversation history for the supervisor
memory = MemorySaver()

supervisor = create_supervisor(
    model=ChatOpenAI(model=&quot;o3&quot;),
    agents=[agent1, agent2, agent3],
    prompt=&quot;Detailed system prompt instructing the model how to route the workflow.&quot;,
).compile(checkpointer=memory)

# Call the supervisor with a user query
response = supervisor.invoke({
    &quot;messages&quot;: [
        {
            &quot;role&quot;: &quot;user&quot;,
            &quot;content&quot;: &quot;Your question here...&quot;
        }
    ]
})
</code></pre>
<p>The supervisor multi-agent system follows a structured workflow for processing user queries. Here&#8217;s how it works:</p>
<ul>
<li>User submits a query to the supervisor</li>
<li>Supervisor routes the query to appropriate agents based on the system prompt</li>
<li>Agents analyze the information independently and return their findings back to the supervisor</li>
<li>Supervisor aggregates the findings and generates a final response</li>
<li>The final response is returned to the user</li>
</ul>
<p><img decoding="async" alt="Multi-agent workflow diagram showing user query being routed through supervisor to multiple agents and back" src="https://codecut.ai/wp-content/uploads/2025/06/langraph_2.png" /></p>
<h2 id="building-the-investment-board-of-agents">Building the Investment Board of Agents</h2>
<p>Now that we&#8217;ve covered the basics of LangGraph, let&#8217;s move on to explore the investment board of agents application.</p>
<h3 id="high-level-application-overview">High-level Application Overview</h3>
<p>Here&#8217;s a high-level overview of the application:</p>
<pre><code class="language-text">investment-committee/
├── src/
│   ├── __init__.py          # Package marker
│   ├── config.py            # Prompts and model configuration
│   ├── tools.py             # Agent tools organized by function
│   ├── utils.py             # Utility functions for display
│   └── agents.py            # Agent and supervisor creation
├── main.py                  # Command-line interface
├── requirements.txt         # Python dependencies
├── .env.example            # Environment variables template
└── README.md               # Documentation
</code></pre>
<p>In this structure:</p>
<ul>
<li><code>src/config.py</code> contains the system configuration</li>
<li><code>src/tools.py</code> contains the tools for the agents</li>
<li><code>src/utils.py</code> contains the utility functions for the application</li>
<li><code>src/agents.py</code> contains the agent and supervisor creation</li>
<li><code>main.py</code> is the entry point for the command-line interface</li>
</ul>
<p>Let&#8217;s go through each of these files in more detail.</p>
<h3 id="setting-up-configuration">Setting up configuration</h3>
<p>The <code>config.py</code> file centralizes all system configuration in one location, making the application easy to customize and maintain. The file contains two main types of configuration:</p>
<ul>
<li><strong>Model configuration</strong>: Specifies which language model to use across all agents, which is currently set to <code>&quot;openai:gpt-4&quot;</code>.</li>
<li><strong>Agent prompts</strong>: Define the personality and behavior of each agent through detailed system prompts.</li>
</ul>
<p>Here is the supervisor prompt:</p>
<pre><code class="language-python">SUPERVISOR_PROMPT = (
    &quot;You are a SIMPLE ROUTER with one final summary task.\n\n&quot;
    &quot;MANDATORY WORKFLOW (follow exactly):\n&quot;
    &quot;1. bull_agent: Make initial bullish case\n&quot;
    &quot;2. bear_agent: Make initial bearish case\n&quot;
    &quot;3. bull_agent: Counter the bear&#039;s specific arguments\n&quot;
    &quot;4. bear_agent: Counter the bull&#039;s specific arguments\n&quot;
    &quot;5. chairman_agent: Make final investment decision\n&quot;
    &quot;6. YOU: Summarize the debate outcome\n\n&quot;
    &quot;RULES:\n&quot;
    &quot;- DO NOT summarize until AFTER chairman makes decision\n&quot;
    &quot;- ALWAYS end with chairman_agent making the decision first\n&quot;
    &quot;- Route agents in the exact order above\n&quot;
    &quot;- After chairman decides, provide a brief summary of:\n&quot;
    &quot;  • Key bull arguments\n&quot;
    &quot;  • Key bear arguments  \n&quot;
    &quot;  • Chairman&#039;s final decision and reasoning\n&quot;
    &quot;- Keep summary concise (3-4 sentences max)&quot;
)
</code></pre>
<h3 id="defining-tools">Defining Tools</h3>
<p>Tools are functions that agents can use to interact with external data sources and perform specific tasks. There are six tools used in this system:</p>
<ul>
<li><code>find_positive_news</code>: Searches for positive news and developments about a stock</li>
<li><code>calculate_growth_potential</code>: Calculates basic growth metrics and bullish indicators</li>
<li><code>find_negative_news</code>: Searches for negative news and risks about a stock</li>
<li><code>assess_market_risks</code>: Assesses overall market risks and bearish indicators</li>
<li><code>get_current_market_sentiment</code>: Gets overall market sentiment and recent performance</li>
<li><code>make_investment_decision</code>: Makes final investment recommendation based on bull and bear arguments</li>
</ul>
<p>Each tool uses the Tavily search API to gather real-time market information, ensuring agents base their arguments on current data rather than outdated training information.</p>
<p>Here is the code for the <code>find_positive_news</code> tool:</p>
<pre><code class="language-python">def find_positive_news(stock_symbol: str):
    &quot;&quot;&quot;Search for positive news and developments about a stock&quot;&quot;&quot;
    query = f&quot;{stock_symbol} stock positive news earnings growth revenue profit upgrade&quot;
    keywords = [&quot;profit&quot;, &quot;growth&quot;, &quot;upgrade&quot;, &quot;beat&quot;, &quot;strong&quot;, &quot;positive&quot;, &quot;bullish&quot;]
    prefix = &quot;🐂 POSITIVE SIGNALS&quot;
    default = &quot;Limited positive news found, but that could mean it&#039;s undervalued!&quot;
    return search_and_extract_signals(stock_symbol, query, keywords, prefix, default)
</code></pre>
<p>View the full code for the tools in the <a href="https://github.com/CodeCutTech/langraph-demo/blob/main/src/tools.py">tools.py</a> file.</p>
<h3 id="creating-the-agents">Creating the Agents</h3>
<p>Agents are the core components of the multi-agent system. They are responsible for analyzing the information and making decisions.</p>
<p>The <code>agents.py</code> file creates the agents and supervisor. There are three specialized agents:</p>
<ol>
<li><strong>Bull agent</strong>: An optimistic analyst who searches for positive indicators and growth potential</li>
<li><strong>Bear agent</strong>: A pessimistic analyst who identifies risks and negative signals</li>
<li><strong>Chairman agent</strong>: A neutral decision-maker who weighs both sides and makes final investment recommendations</li>
</ol>
<p><img decoding="async" alt="Investment committee diagram showing bull, bear, and chairman agents with their roles in the analysis process" src="https://codecut.ai/wp-content/uploads/2025/06/langraph_1.png" /></p>
<p>Each agent gets created with the <code>create_react_agent</code> function with the following parameters:</p>
<ul>
<li><code>model</code>: The language model to use</li>
<li><code>tools</code>: The tools to use</li>
<li><code>prompt</code>: The system prompt to use</li>
<li><code>name</code>: The name of the agent</li>
</ul>
<p>Here is the code for the three agents:</p>
<pre><code class="language-python">def create_bull_agent():
    &quot;&quot;&quot;Create the bull (optimistic) investment agent&quot;&quot;&quot;
    return create_react_agent(
        model=MODEL_NAME,
        tools=[find_positive_news, calculate_growth_potential],
        prompt=BULL_AGENT_PROMPT,
        name=&quot;bull_agent&quot;,
    )


def create_bear_agent():
    &quot;&quot;&quot;Create the bear (pessimistic) investment agent&quot;&quot;&quot;
    return create_react_agent(
        model=MODEL_NAME,
        tools=[find_negative_news, assess_market_risks],
        prompt=BEAR_AGENT_PROMPT,
        name=&quot;bear_agent&quot;,
    )


def create_chairman_agent():
    &quot;&quot;&quot;Create the chairman (decision maker) agent&quot;&quot;&quot;
    return create_react_agent(
        model=MODEL_NAME,
        tools=[get_current_market_sentiment, make_investment_decision],
        prompt=CHAIRMAN_AGENT_PROMPT,
        name=&quot;chairman_agent&quot;,
    )
</code></pre>
<p>The supervisor combines all agents under coordinated workflow management. Here is the code for the supervisor:</p>
<pre><code class="language-python">def create_investment_supervisor():
    &quot;&quot;&quot;Create the supervisor that manages the investment committee&quot;&quot;&quot;
    bull_agent = create_bull_agent()
    bear_agent = create_bear_agent()
    chairman_agent = create_chairman_agent()

    supervisor = create_supervisor(
        model=init_chat_model(MODEL_NAME),
        agents=[bull_agent, bear_agent, chairman_agent],
        prompt=SUPERVISOR_PROMPT,
        add_handoff_back_messages=True,
        output_mode=&quot;full_history&quot;,
    ).compile()

    return supervisor
</code></pre>
<p>In this code:</p>
<ul>
<li><code>add_handoff_back_messages=True</code> ensures agents can reference each other&#8217;s previous arguments, creating true conversational debate rather than isolated analysis.</li>
<li><code>output_mode=&quot;full_history&quot;</code> provides complete conversation context, allowing you to see the full reasoning process rather than just final decisions.</li>
</ul>
<h3 id="formatting-the-output">Formatting the Output</h3>
<p>To format the output, we use the <code>pretty_print_messages</code> function in <code>utils.py</code>. It takes the conversation stream updates and formats them into readable output by:</p>
<ul>
<li>Identifying which agent is speaking</li>
<li>Processing supervisor workflow updates and agent interactions</li>
<li>Converting message objects into a human-readable format</li>
</ul>
<p>This allows us to clearly see the back-and-forth debate between agents as they analyze investment opportunities.</p>
<pre><code class="language-python">while True:
    try:
        stock_input = input(&quot;Enter stock symbol (or &#039;quit&#039; to exit): &quot;).strip()

        if stock_input.lower() in [&quot;quit&quot;, &quot;exit&quot;, &quot;q&quot;]:
            print(&quot;\n👋 Goodbye! Happy investing!&quot;)
            break

        if not stock_input:
            print(&quot;Please enter a valid stock symbol.&quot;)
            continue

        analyze_stock(supervisor, stock_input)

    except KeyboardInterrupt:
        print(&quot;\n\n👋 Goodbye! Happy investing!&quot;)
        sys.exit(0)
    except Exception as e:
        print(f&quot;\n❌ Error: {e}&quot;)
        print(&quot;Please try again with a different stock symbol.&quot;)
</code></pre>
<h3 id="adding-a-terminal-based-chatbot-interface">Adding a Terminal-based Chatbot Interface</h3>
<p>Next, let&#8217;s set up an interactive command-line interface that makes the investment committee system accessible to users.</p>
<p>In the <code>main.py</code> file, we set up the welcome message that will be displayed when the application is initialized:</p>
<pre><code class="language-python">def print_welcome():
    &quot;&quot;&quot;Print welcome message and system description&quot;&quot;&quot;
    print(&quot;💼 INVESTMENT COMMITTEE SYSTEM&quot;)
    print(&quot;=&quot; * 50)
    print(&quot;🐂 Bull Agent: Finds reasons to BUY&quot;)
    print(&quot;🐻 Bear Agent: Finds reasons to AVOID&quot;)
    print(&quot;🎯 Chairman: Makes final decision&quot;)
    print(&quot;=&quot; * 50)
</code></pre>
<p>Next, create the <code>analyze_stock</code> function that handles the core interaction pattern:</p>
<pre><code class="language-python">def analyze_stock(supervisor, stock_symbol):
    &quot;&quot;&quot;Analyze a stock using the investment committee&quot;&quot;&quot;
    print(f&quot;\n📈 ANALYZING: {stock_symbol.upper()}&quot;)
    print(&quot;-&quot; * 30)

    user_query = f&quot;Should I invest in {stock_symbol} stock? I want to hear both bullish and bearish arguments before making a decision.&quot;

    for chunk in supervisor.stream(
        {
            &quot;messages&quot;: [
                {
                    &quot;role&quot;: &quot;user&quot;,
                    &quot;content&quot;: user_query,
                }
            ]
        },
    ):
        pretty_print_messages(chunk, last_message=True)
</code></pre>
<p>The function:</p>
<ul>
<li>Takes the stock symbol and constructs a natural language query asking for investment advice</li>
<li>Uses <code>supervisor.stream()</code> to get real-time updates from each agent as they analyze the stock</li>
<li>Displays agent responses incrementally using <code>pretty_print_messages()</code> to show the analysis process</li>
</ul>
<h3 id="testing-and-running-the-system">Testing and Running the System</h3>
<p>Now we are ready to test and run the system! Run the <code>main.py</code> script to start the application:</p>
<pre><code class="language-bash">python main.py
</code></pre>
<p>You will be prompted to enter a stock symbol. Let&#8217;s try it with <code>NVDA</code>:</p>
<pre><code class="language-text">💼 INVESTMENT COMMITTEE SYSTEM
==================================================
🐂 Bull Agent: Finds reasons to BUY
🐻 Bear Agent: Finds reasons to AVOID
🎯 Chairman: Makes final decision
==================================================
🔄 Initializing investment committee...
✅ Committee ready!

Enter stock symbol (or &#039;quit&#039; to exit): NVDA
</code></pre>
<p>The output will look like this:</p>
<pre><code class="language-text">📈 ANALYZING: NVDA
------------------------------
...

================================== Ai Message ==================================
Name: supervisor

**Summary:**
The bullish case for NVDA centers on historic revenue and profit growth, AI market dominance, ecosystem lock-in, and robust financial momentum. The bearish side warns of bubble-like valuation, heavy customer concentration, rising competition, geopolitical risks, and the potential for hardware cycle reversals. The chairman ultimately recommends a HOLD or further research, acknowledging both the powerful growth story and the elevated risks, making it prudent to wait for more clarity or a better entry point.
</code></pre>
<h2 id="final-thoughts">Final Thoughts</h2>
<p>The investment committee system demonstrates the power of coordinated AI agents in making complex decisions. By combining specialized agents with different perspectives, we&#8217;ve created a system that can:</p>
<ul>
<li>Provide balanced analysis through structured debate</li>
<li>Consider multiple viewpoints simultaneously</li>
<li>Make informed decisions based on comprehensive data</li>
<li>Adapt to new information through real-time updates</li>
</ul>
<p>This approach can be extended to other domains where multiple perspectives and structured decision-making are crucial, such as risk assessment, policy analysis, or strategic planning.</p>
<button class="simplefavorite-button" data-postid="15293" data-siteid="1" data-groupid="1" data-favoritecount="1" style="">Favorite <i class="sf-icon-star-empty"></i></button>				</div>
				</div>
				</div>
				</div>
		<div class="elementor-element elementor-element-6c9c2df rp-wrap e-flex e-con-boxed e-con e-parent" data-id="6c9c2df" data-element_type="container">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-1d0db22 elementor-widget elementor-widget-heading" data-id="1d0db22" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h2 class="elementor-heading-title elementor-size-default">Related Posts</h2>				</div>
				</div>
				<div class="elementor-element elementor-element-63f8595 elementor-widget-widescreen__width-initial elementor-grid-tablet-1 elementor-grid-widescreen-3 elementor-posts--align-center elementor-grid-3 elementor-grid-mobile-1 elementor-posts--thumbnail-top elementor-posts__hover-gradient elementor-widget elementor-widget-posts" data-id="63f8595" data-element_type="widget" data-settings="{&quot;cards_row_gap&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:30,&quot;sizes&quot;:[]},&quot;cards_columns_tablet&quot;:&quot;1&quot;,&quot;cards_row_gap_widescreen&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:59,&quot;sizes&quot;:[]},&quot;cards_row_gap_tablet&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:30,&quot;sizes&quot;:[]},&quot;cards_columns_widescreen&quot;:&quot;3&quot;,&quot;cards_masonry&quot;:&quot;yes&quot;,&quot;cards_columns&quot;:&quot;3&quot;,&quot;cards_columns_mobile&quot;:&quot;1&quot;,&quot;cards_row_gap_laptop&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:[]},&quot;cards_row_gap_mobile&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:[]}}" data-widget_type="posts.cards">
				<div class="elementor-widget-container">
							<div class="elementor-posts-container elementor-posts elementor-posts--skin-cards elementor-grid" role="list">
				<article class="elementor-post elementor-grid-item post-16860 post type-post status-publish format-standard has-post-thumbnail hentry category-blog category-data-engineer category-llm tag-cloudquery tag-embeddings tag-etl tag-pgvector tag-rag tag-tutorial tag-vector-database" role="listitem">
			<div class="elementor-post__card">
				<a class="elementor-post__thumbnail__link" href="https://codecut.ai/cloudquery-pgvector-rag-pipelines/" tabindex="-1" ><div class="elementor-post__thumbnail"><img width="1200" height="628" src="https://codecut.ai/wp-content/uploads/2025/09/cloudquery.png" class="attachment-full size-full wp-image-16862" alt="" decoding="async" srcset="https://codecut.ai/wp-content/uploads/2025/09/cloudquery.png 1200w, https://codecut.ai/wp-content/uploads/2025/09/cloudquery-986x516.png 986w, https://codecut.ai/wp-content/uploads/2025/09/cloudquery-768x402.png 768w, https://codecut.ai/wp-content/uploads/2025/09/cloudquery-600x314.png 600w" sizes="(max-width: 1200px) 100vw, 1200px" /></div></a>
				<div class="elementor-post__text">
				<p class="elementor-post__title">
			<a href="https://codecut.ai/cloudquery-pgvector-rag-pipelines/" >
				Hacker News Semantic Search: Production RAG with CloudQuery and Postgres			</a>
		</p>
				</div>
				<div class="elementor-post__meta-data">
					<span class="elementor-post-date">
			September 22, 2025		</span>
				</div>
					</div>
		</article>
				<article class="elementor-post elementor-grid-item post-16823 post type-post status-publish format-standard has-post-thumbnail hentry category-blog category-python-utilities tag-documentation tag-jupyter tag-latex tag-mathematics tag-python" role="listitem">
			<div class="elementor-post__card">
				<a class="elementor-post__thumbnail__link" href="https://codecut.ai/python-code-to-latex-jupyter-notebooks/" tabindex="-1" ><div class="elementor-post__thumbnail"><img loading="lazy" width="1200" height="628" src="https://codecut.ai/wp-content/uploads/2025/09/latex-1.png" class="attachment-full size-full wp-image-16848" alt="" decoding="async" srcset="https://codecut.ai/wp-content/uploads/2025/09/latex-1.png 1200w, https://codecut.ai/wp-content/uploads/2025/09/latex-1-986x516.png 986w, https://codecut.ai/wp-content/uploads/2025/09/latex-1-768x402.png 768w, https://codecut.ai/wp-content/uploads/2025/09/latex-1-600x314.png 600w" sizes="(max-width: 1200px) 100vw, 1200px" /></div></a>
				<div class="elementor-post__text">
				<p class="elementor-post__title">
			<a href="https://codecut.ai/python-code-to-latex-jupyter-notebooks/" >
				3 Tools That Automatically Convert Python Code to LaTeX Math			</a>
		</p>
				</div>
				<div class="elementor-post__meta-data">
					<span class="elementor-post-date">
			September 14, 2025		</span>
				</div>
					</div>
		</article>
				<article class="elementor-post elementor-grid-item post-16796 post type-post status-publish format-standard has-post-thumbnail hentry category-blog category-llm category-machine-learning-article tag-entity-extraction tag-gliner tag-langextract tag-nlp tag-python tag-regex tag-spacy" role="listitem">
			<div class="elementor-post__card">
				<a class="elementor-post__thumbnail__link" href="https://codecut.ai/langextract-vs-spacy-entity-extraction-comparison/" tabindex="-1" ><div class="elementor-post__thumbnail"><img loading="lazy" width="1200" height="628" src="https://codecut.ai/wp-content/uploads/2025/09/entity-extraction.png" class="attachment-full size-full wp-image-16801" alt="" decoding="async" srcset="https://codecut.ai/wp-content/uploads/2025/09/entity-extraction.png 1200w, https://codecut.ai/wp-content/uploads/2025/09/entity-extraction-986x516.png 986w, https://codecut.ai/wp-content/uploads/2025/09/entity-extraction-768x402.png 768w, https://codecut.ai/wp-content/uploads/2025/09/entity-extraction-600x314.png 600w" sizes="(max-width: 1200px) 100vw, 1200px" /></div></a>
				<div class="elementor-post__text">
				<p class="elementor-post__title">
			<a href="https://codecut.ai/langextract-vs-spacy-entity-extraction-comparison/" >
				langextract vs spaCy: AI-Powered vs Rule-Based Entity Extraction			</a>
		</p>
				</div>
				<div class="elementor-post__meta-data">
					<span class="elementor-post-date">
			September 10, 2025		</span>
				</div>
					</div>
		</article>
				</div>
		
						</div>
				</div>
					</div>
				</div>
				<section class="elementor-section elementor-top-section elementor-element elementor-element-dd6b76b elementor-section-boxed elementor-section-height-default elementor-section-height-default" data-id="dd6b76b" data-element_type="section" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
						<div class="elementor-container elementor-column-gap-default">
					<div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-1d18334" data-id="1d18334" data-element_type="column">
			<div class="elementor-widget-wrap elementor-element-populated">
						<div class="elementor-element elementor-element-959881c elementor-widget elementor-widget-post-comments" data-id="959881c" data-element_type="widget" data-widget_type="post-comments.theme_comments">
				<div class="elementor-widget-container">
							<div id="comments" class="comments-area comment-form-position-below ">
	
	
	
	
		<div id="respond" class="comment-respond">
		<h3 id="reply-title" class="comment-reply-title">Leave a Comment <small><a rel="nofollow" id="cancel-comment-reply-link" href="/building-multi-agent-ai-langgraph-tutorial/#respond" style="display:none;">Cancel Reply</a></small></h3><form action="https://codecut.ai/wp-comments-post.php" method="post" id="ast-commentform" class="comment-form"><p class="comment-notes"><span id="email-notes">Your email address will not be published.</span> <span class="required-field-message">Required fields are marked <span class="required">*</span></span></p><div class="ast-row comment-textarea"><fieldset class="comment-form-comment"><legend class ="comment-form-legend"></legend><div class="comment-form-textarea ast-col-lg-12"><label for="comment" class="screen-reader-text">Type here..</label><textarea id="comment" name="comment" placeholder="Type here.." cols="45" rows="8" aria-required="true"></textarea></div></fieldset></div><div class="ast-comment-formwrap ast-row">
			<p class="comment-form-author ast-col-xs-12 ast-col-sm-12 ast-col-md-4 ast-col-lg-4">
				<label for="author" class="screen-reader-text">Name</label>
				<input id="author" name="author" type="text" 
					value="" 
					placeholder="Name" 
					size="30" autocomplete="name" />
			</p>
<p class="comment-form-email ast-col-xs-12 ast-col-sm-12 ast-col-md-4 ast-col-lg-4">
			<label for="email" class="screen-reader-text">Email</label>
			<input id="email" name="email" type="text" 
				value="" 
				placeholder="Email" 
				size="30" autocomplete="email" />
		</p>
<p class="comment-form-url ast-col-xs-12 ast-col-sm-12 ast-col-md-4 ast-col-lg-4">
			<label for="url" class="screen-reader-text">Website</label>
			<input id="url" name="url" type="text" 
				value="" 
				placeholder="Website" 
				size="30" autocomplete="url" />
		</p>
		</div>
<p class="comment-form-cookies-consent"><input id="wp-comment-cookies-consent" name="wp-comment-cookies-consent" type="checkbox" value="yes" /> <label for="wp-comment-cookies-consent">Save my name, email, and website in this browser for the next time I comment.</label></p>
<p class="form-submit"><input name="submit" type="submit" id="submit" class="submit" value="Post Comment &raquo;" /> <input type='hidden' name='comment_post_ID' value='15293' id='comment_post_ID' />
<input type='hidden' name='comment_parent' id='comment_parent' value='0' />
</p><p style="display: none;"><input type="hidden" id="akismet_comment_nonce" name="akismet_comment_nonce" value="81d49e1bf3" /></p><p style="display: none !important;" class="akismet-fields-container" data-prefix="ak_"><label>&#916;<textarea name="ak_hp_textarea" cols="45" rows="8" maxlength="100"></textarea></label><input type="hidden" id="ak_js_1" name="ak_js" value="122"/><script>document.getElementById( "ak_js_1" ).setAttribute( "value", ( new Date() ).getTime() );</script></p></form>	</div><!-- #respond -->
	
	
</div><!-- #comments -->

				</div>
				</div>
					</div>
		</div>
					</div>
		</section>
				</div>
					</main><!-- #main -->
			
		
	</div><!-- #primary -->


	</div> <!-- ast-container -->
	</div><!-- #content -->
		<footer data-elementor-type="footer" data-elementor-id="7720" class="elementor elementor-7720 elementor-location-footer" data-elementor-post-type="elementor_library">
			<div class="elementor-element elementor-element-68e855b e-flex e-con-boxed e-con e-parent" data-id="68e855b" data-element_type="container">
					<div class="e-con-inner">
		<div class="elementor-element elementor-element-6846aef e-con-full e-flex e-con e-child" data-id="6846aef" data-element_type="container">
				<div class="elementor-element elementor-element-a2c6279 elementor-widget elementor-widget-heading" data-id="a2c6279" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h4 class="elementor-heading-title elementor-size-default">Stay up-to-date with<br /> data skills using <br /><span style="color:#e583b6">CodeCut</span></h4>				</div>
				</div>
				<div class="elementor-element elementor-element-d24a60a elementor-widget__width-initial elementor-widget-tablet__width-inherit elementor-widget-mobile__width-inherit elementor-widget elementor-widget-text-editor" data-id="d24a60a" data-element_type="widget" data-widget_type="text-editor.default">
				<div class="elementor-widget-container">
									<p>CodeCut is a platform that offers short and visually appealing code snippets related to data science, data analysis, data engineering, and Python programming.</p>								</div>
				</div>
				</div>
		<div class="elementor-element elementor-element-10561f5 e-con-full e-flex e-con e-child" data-id="10561f5" data-element_type="container">
		<div class="elementor-element elementor-element-9db092d e-con-full e-flex e-con e-child" data-id="9db092d" data-element_type="container">
		<div class="elementor-element elementor-element-8e86b63 e-con-full e-flex e-con e-child" data-id="8e86b63" data-element_type="container">
				<div class="elementor-element elementor-element-23b4596 elementor-view-default elementor-widget elementor-widget-icon" data-id="23b4596" data-element_type="widget" data-widget_type="icon.default">
				<div class="elementor-widget-container">
							<div class="elementor-icon-wrapper">
			<a class="elementor-icon" href="mailto:<EMAIL>">
			<i aria-hidden="true" class="hm hm-envelop"></i>			</a>
		</div>
						</div>
				</div>
				<div class="elementor-element elementor-element-f059a0a elementor-widget elementor-widget-heading" data-id="f059a0a" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h5 class="elementor-heading-title elementor-size-default"><a href="mailto:<EMAIL>">Drop a line</a></h5>				</div>
				</div>
				<div class="elementor-element elementor-element-5ca2933 elementor-widget-tablet__width-inherit elementor-widget elementor-widget-text-editor" data-id="5ca2933" data-element_type="widget" data-widget_type="text-editor.default">
				<div class="elementor-widget-container">
									<p><EMAIL></p>								</div>
				</div>
				</div>
		<div class="elementor-element elementor-element-c022a4c e-con-full e-flex e-con e-child" data-id="c022a4c" data-element_type="container">
				<div class="elementor-element elementor-element-b5de218 elementor-view-default elementor-widget elementor-widget-icon" data-id="b5de218" data-element_type="widget" data-widget_type="icon.default">
				<div class="elementor-widget-container">
							<div class="elementor-icon-wrapper">
			<a class="elementor-icon" href="https://codecut.ai/contact-us-page-new/">
			<i aria-hidden="true" class="hm hm-chat-bubble-single"></i>			</a>
		</div>
						</div>
				</div>
				<div class="elementor-element elementor-element-9f9b58a elementor-widget elementor-widget-heading" data-id="9f9b58a" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h5 class="elementor-heading-title elementor-size-default"><a href="https://codecut.ai/contact-us-page-new/">Get in touch</a></h5>				</div>
				</div>
				<div class="elementor-element elementor-element-69ae15f elementor-widget elementor-widget-text-editor" data-id="69ae15f" data-element_type="widget" data-widget_type="text-editor.default">
				<div class="elementor-widget-container">
									<p>I&#8217;d love to connect with you!</p>								</div>
				</div>
				</div>
				</div>
		<div class="elementor-element elementor-element-06eb9d5 e-con-full e-flex e-con e-child" data-id="06eb9d5" data-element_type="container">
				<div class="elementor-element elementor-element-85c914c elementor-shape-circle elementor-grid-0 e-grid-align-center elementor-widget elementor-widget-social-icons" data-id="85c914c" data-element_type="widget" data-widget_type="social-icons.default">
				<div class="elementor-widget-container">
							<div class="elementor-social-icons-wrapper elementor-grid" role="list">
							<span class="elementor-grid-item" role="listitem">
					<a class="elementor-icon elementor-social-icon elementor-social-icon-linkedin-in elementor-repeater-item-fddf51d" href="https://www.linkedin.com/in/khuyen-tran-1ab926151/" target="_blank">
						<span class="elementor-screen-only">Linkedin-in</span>
						<i aria-hidden="true" class="fab fa-linkedin-in"></i>					</a>
				</span>
							<span class="elementor-grid-item" role="listitem">
					<a class="elementor-icon elementor-social-icon elementor-social-icon-twitter elementor-repeater-item-3590114" href="https://twitter.com/KhuyenTran16" target="_blank">
						<span class="elementor-screen-only">Twitter</span>
						<i aria-hidden="true" class="fab fa-twitter"></i>					</a>
				</span>
							<span class="elementor-grid-item" role="listitem">
					<a class="elementor-icon elementor-social-icon elementor-social-icon- elementor-repeater-item-106cbff" href="https://github.com/khuyentran1401/" target="_blank">
						<span class="elementor-screen-only"></span>
						<svg xmlns="http://www.w3.org/2000/svg" width="800px" height="800px" viewBox="0 0 24 24" fill="none"><path d="M4.0744 2.9938C4.13263 1.96371 4.37869 1.51577 5.08432 1.15606C5.84357 0.768899 7.04106 0.949072 8.45014 1.66261C9.05706 1.97009 9.11886 1.97635 10.1825 1.83998C11.5963 1.65865 13.4164 1.65929 14.7213 1.84164C15.7081 1.97954 15.7729 1.97265 16.3813 1.66453C18.3814 0.651679 19.9605 0.71795 20.5323 1.8387C20.8177 2.39812 20.8707 3.84971 20.6494 5.04695C20.5267 5.71069 20.5397 5.79356 20.8353 6.22912C22.915 9.29385 21.4165 14.2616 17.8528 16.1155C17.5801 16.2574 17.3503 16.3452 17.163 16.4167C16.5879 16.6363 16.4133 16.703 16.6247 17.7138C16.7265 18.2 16.8491 19.4088 16.8973 20.4002C16.9844 22.1922 16.9831 22.2047 16.6688 22.5703C16.241 23.0676 15.6244 23.076 15.2066 22.5902C14.9341 22.2734 14.9075 22.1238 14.9075 20.9015C14.9075 19.0952 14.7095 17.8946 14.2417 16.8658C13.6854 15.6415 14.0978 15.185 15.37 14.9114C17.1383 14.531 18.5194 13.4397 19.2892 11.8146C20.0211 10.2698 20.1314 8.13501 18.8082 6.83668C18.4319 6.3895 18.4057 5.98446 18.6744 4.76309C18.7748 4.3066 18.859 3.71768 18.8615 3.45425C18.8653 3.03823 18.8274 2.97541 18.5719 2.97541C18.4102 2.97541 17.7924 3.21062 17.1992 3.49805L16.2524 3.95695C16.1663 3.99866 16.07 4.0147 15.975 4.0038C13.5675 3.72746 11.2799 3.72319 8.86062 4.00488C8.76526 4.01598 8.66853 3.99994 8.58215 3.95802L7.63585 3.49882C7.04259 3.21087 6.42482 2.97541 6.26317 2.97541C5.88941 2.97541 5.88379 3.25135 6.22447 4.89078C6.43258 5.89203 6.57262 6.11513 5.97101 6.91572C5.06925 8.11576 4.844 9.60592 5.32757 11.1716C5.93704 13.1446 7.4295 14.4775 9.52773 14.9222C10.7926 15.1903 11.1232 15.5401 10.6402 16.9905C10.26 18.1319 10.0196 18.4261 9.46707 18.4261C8.72365 18.4261 8.25796 17.7821 8.51424 17.1082C8.62712 16.8112 8.59354 16.7795 7.89711 16.5255C5.77117 15.7504 4.14514 14.0131 3.40172 11.7223C2.82711 9.95184 3.07994 7.64739 4.00175 6.25453C4.31561 5.78028 4.32047 5.74006 4.174 4.83217C4.09113 4.31822 4.04631 3.49103 4.0744 2.9938Z" fill="#0F0F0F"></path><path d="M3.33203 15.9454C3.02568 15.4859 2.40481 15.3617 1.94528 15.6681C1.48576 15.9744 1.36158 16.5953 1.66793 17.0548C1.8941 17.3941 2.16467 17.6728 2.39444 17.9025C2.4368 17.9449 2.47796 17.9858 2.51815 18.0257C2.71062 18.2169 2.88056 18.3857 3.05124 18.5861C3.42875 19.0292 3.80536 19.626 4.0194 20.6962C4.11474 21.1729 4.45739 21.4297 4.64725 21.5419C4.85315 21.6635 5.07812 21.7352 5.26325 21.7819C5.64196 21.8774 6.10169 21.927 6.53799 21.9559C7.01695 21.9877 7.53592 21.998 7.99999 22.0008C8.00033 22.5527 8.44791 23.0001 8.99998 23.0001C9.55227 23.0001 9.99998 22.5524 9.99998 22.0001V21.0001C9.99998 20.4478 9.55227 20.0001 8.99998 20.0001C8.90571 20.0001 8.80372 20.0004 8.69569 20.0008C8.10883 20.0026 7.34388 20.0049 6.67018 19.9603C6.34531 19.9388 6.07825 19.9083 5.88241 19.871C5.58083 18.6871 5.09362 17.8994 4.57373 17.2891C4.34391 17.0194 4.10593 16.7834 3.91236 16.5914C3.87612 16.5555 3.84144 16.5211 3.80865 16.4883C3.5853 16.265 3.4392 16.1062 3.33203 15.9454Z" fill="#0F0F0F"></path></svg>					</a>
				</span>
							<span class="elementor-grid-item" role="listitem">
					<a class="elementor-icon elementor-social-icon elementor-social-icon-youtube elementor-repeater-item-ff446bb" href="https://www.youtube.com/@datasciencesimplified" target="_blank">
						<span class="elementor-screen-only">Youtube</span>
						<i aria-hidden="true" class="fab fa-youtube"></i>					</a>
				</span>
					</div>
						</div>
				</div>
				<div class="elementor-element elementor-element-4977a03 elementor-widget elementor-widget-heading" data-id="4977a03" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h5 class="elementor-heading-title elementor-size-default">Follow Us on Social Media</h5>				</div>
				</div>
				</div>
				</div>
					</div>
				</div>
		<div class="elementor-element elementor-element-f93946e e-flex e-con-boxed e-con e-parent" data-id="f93946e" data-element_type="container">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-4e1fdf0 elementor-widget-divider--view-line elementor-widget elementor-widget-divider" data-id="4e1fdf0" data-element_type="widget" data-widget_type="divider.default">
				<div class="elementor-widget-container">
							<div class="elementor-divider">
			<span class="elementor-divider-separator">
						</span>
		</div>
						</div>
				</div>
				<div class="elementor-element elementor-element-2644821 elementor-widget elementor-widget-heading" data-id="2644821" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<p class="elementor-heading-title elementor-size-default">Copyright © 2025 Code Cut - All rights reserved.</p>				</div>
				</div>
					</div>
				</div>
				</footer>
			</div><!-- #page -->

<div id="um_upload_single" style="display:none;"></div>

<div id="um_view_photo" style="display:none;">
	<a href="javascript:void(0);" data-action="um_remove_modal" class="um-modal-close" aria-label="Close view photo modal">
		<i class="um-faicon-times"></i>
	</a>

	<div class="um-modal-body photo">
		<div class="um-modal-photo"></div>
	</div>
</div>
<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/astra-child\/*","\/wp-content\/themes\/astra\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
        <script>
            // Do not change this comment line otherwise Speed Optimizer won't be able to detect this script

            (function () {
                const calculateParentDistance = (child, parent) => {
                    let count = 0;
                    let currentElement = child;

                    // Traverse up the DOM tree until we reach parent or the top of the DOM
                    while (currentElement && currentElement !== parent) {
                        currentElement = currentElement.parentNode;
                        count++;
                    }

                    // If parent was not found in the hierarchy, return -1
                    if (!currentElement) {
                        return -1; // Indicates parent is not an ancestor of element
                    }

                    return count; // Number of layers between element and parent
                }
                const isMatchingClass = (linkRule, href, classes, ids) => {
                    return classes.includes(linkRule.value)
                }
                const isMatchingId = (linkRule, href, classes, ids) => {
                    return ids.includes(linkRule.value)
                }
                const isMatchingDomain = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href)) {
                        return false
                    }

                    const url = new URL(href)

                    return linkRule.value === url.host
                }
                const isMatchingExtension = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href)) {
                        return false
                    }

                    const url = new URL(href)

                    return url.pathname.endsWith('.' + linkRule.value)
                }
                const isMatchingSubdirectory = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href)) {
                        return false
                    }

                    const url = new URL(href)

                    return url.pathname.startsWith('/' + linkRule.value + '/')
                }
                const isMatchingProtocol = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href)) {
                        return false
                    }

                    const url = new URL(href)

                    return url.protocol === linkRule.value + ':'
                }
                const isMatchingExternal = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href) || !URL.canParse(document.location.href)) {
                        return false
                    }

                    const matchingProtocols = ['http:', 'https:']
                    const siteUrl = new URL(document.location.href)
                    const linkUrl = new URL(href)

                    // Links to subdomains will appear to be external matches according to JavaScript,
                    // but the PHP rules will filter those events out.
                    return matchingProtocols.includes(linkUrl.protocol) && siteUrl.host !== linkUrl.host
                }
                const isMatch = (linkRule, href, classes, ids) => {
                    switch (linkRule.type) {
                        case 'class':
                            return isMatchingClass(linkRule, href, classes, ids)
                        case 'id':
                            return isMatchingId(linkRule, href, classes, ids)
                        case 'domain':
                            return isMatchingDomain(linkRule, href, classes, ids)
                        case 'extension':
                            return isMatchingExtension(linkRule, href, classes, ids)
                        case 'subdirectory':
                            return isMatchingSubdirectory(linkRule, href, classes, ids)
                        case 'protocol':
                            return isMatchingProtocol(linkRule, href, classes, ids)
                        case 'external':
                            return isMatchingExternal(linkRule, href, classes, ids)
                        default:
                            return false;
                    }
                }
                const track = (element) => {
                    const href = element.href ?? null
                    const classes = Array.from(element.classList)
                    const ids = [element.id]
                    const linkRules = [{"type":"domain","value":"github.com"},{"type":"extension","value":"pdf"},{"type":"extension","value":"zip"},{"type":"protocol","value":"mailto"},{"type":"protocol","value":"tel"}]
                    if(linkRules.length === 0) {
                        return
                    }

                    // For link rules that target an id, we need to allow that id to appear
                    // in any ancestor up to the 7th ancestor. This loop looks for those matches
                    // and counts them.
                    linkRules.forEach((linkRule) => {
                        if(linkRule.type !== 'id') {
                            return;
                        }

                        const matchingAncestor = element.closest('#' + linkRule.value)

                        if(!matchingAncestor || matchingAncestor.matches('html, body')) {
                            return;
                        }

                        const depth = calculateParentDistance(element, matchingAncestor)

                        if(depth < 7) {
                            ids.push(linkRule.value)
                        }
                    });

                    // For link rules that target a class, we need to allow that class to appear
                    // in any ancestor up to the 7th ancestor. This loop looks for those matches
                    // and counts them.
                    linkRules.forEach((linkRule) => {
                        if(linkRule.type !== 'class') {
                            return;
                        }

                        const matchingAncestor = element.closest('.' + linkRule.value)

                        if(!matchingAncestor || matchingAncestor.matches('html, body')) {
                            return;
                        }

                        const depth = calculateParentDistance(element, matchingAncestor)

                        if(depth < 7) {
                            classes.push(linkRule.value)
                        }
                    });

                    const hasMatch = linkRules.some((linkRule) => {
                        return isMatch(linkRule, href, classes, ids)
                    })

                    if(!hasMatch) {
                        return
                    }

                    const url = "https://codecut.ai/wp-content/plugins/independent-analytics-pro/iawp-click-endpoint.php";
                    const body = {
                        href: href,
                        classes: classes.join(' '),
                        ids: ids.join(' '),
                        ...{"payload":{"resource":"singular","singular_id":15293,"page":1},"signature":"f3f43fe45d3c26f81e9703355ab8990b"}                    };

                    if (navigator.sendBeacon) {
                        let blob = new Blob([JSON.stringify(body)], {
                            type: "application/json"
                        });
                        navigator.sendBeacon(url, blob);
                    } else {
                        const xhr = new XMLHttpRequest();
                        xhr.open("POST", url, true);
                        xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
                        xhr.send(JSON.stringify(body))
                    }
                }
                document.addEventListener('mousedown', function (event) {
                                        if (navigator.webdriver || /bot|crawler|spider|crawling|semrushbot|chrome-lighthouse/i.test(navigator.userAgent)) {
                        return;
                    }
                    
                    const element = event.target.closest('a')

                    if(!element) {
                        return
                    }

                    const isPro = true
                    if(!isPro) {
                        return
                    }

                    // Don't track left clicks with this event. The click event is used for that.
                    if(event.button === 0) {
                        return
                    }

                    track(element)
                })
                document.addEventListener('click', function (event) {
                                        if (navigator.webdriver || /bot|crawler|spider|crawling|semrushbot|chrome-lighthouse/i.test(navigator.userAgent)) {
                        return;
                    }
                    
                    const element = event.target.closest('a, button, input[type="submit"], input[type="button"]')

                    if(!element) {
                        return
                    }

                    const isPro = true
                    if(!isPro) {
                        return
                    }

                    track(element)
                })
                document.addEventListener('play', function (event) {
                                        if (navigator.webdriver || /bot|crawler|spider|crawling|semrushbot|chrome-lighthouse/i.test(navigator.userAgent)) {
                        return;
                    }
                    
                    const element = event.target.closest('audio, video')

                    if(!element) {
                        return
                    }

                    const isPro = true
                    if(!isPro) {
                        return
                    }

                    track(element)
                }, true)
                document.addEventListener("DOMContentLoaded", function (e) {
                    if (document.hasOwnProperty("visibilityState") && document.visibilityState === "prerender") {
                        return;
                    }

                                            if (navigator.webdriver || /bot|crawler|spider|crawling|semrushbot|chrome-lighthouse/i.test(navigator.userAgent)) {
                            return;
                        }
                    
                    let referrer_url = null;

                    if (typeof document.referrer === 'string' && document.referrer.length > 0) {
                        referrer_url = document.referrer;
                    }

                    const params = location.search.slice(1).split('&').reduce((acc, s) => {
                        const [k, v] = s.split('=');
                        return Object.assign(acc, {[k]: v});
                    }, {});

                    const url = "https://codecut.ai/wp-json/iawp/search";
                    const body = {
                        referrer_url,
                        utm_source: params.utm_source,
                        utm_medium: params.utm_medium,
                        utm_campaign: params.utm_campaign,
                        utm_term: params.utm_term,
                        utm_content: params.utm_content,
                        gclid: params.gclid,
                        ...{"payload":{"resource":"singular","singular_id":15293,"page":1},"signature":"f3f43fe45d3c26f81e9703355ab8990b"}                    };

                    if (navigator.sendBeacon) {
                        let blob = new Blob([JSON.stringify(body)], {
                            type: "application/json"
                        });
                        navigator.sendBeacon(url, blob);
                    } else {
                        const xhr = new XMLHttpRequest();
                        xhr.open("POST", url, true);
                        xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
                        xhr.send(JSON.stringify(body))
                    }
                });
            })();
        </script>
        <div class="xoo-wsc-markup-notices"></div>
<div class="xoo-wsc-markup">
	<div class="xoo-wsc-modal">

		
<div class="xoo-wsc-container">

	<div class="xoo-wsc-basket">

					<span class="xoo-wsc-items-count">0</span>
		

		<span class="xoo-wsc-bki xoo-wsc-icon-basket1"></span>

		
	</div>

	<div class="xoo-wsc-header">

		
		
<div class="xoo-wsch-top">

			<div class="xoo-wsc-notice-container" data-section="cart"><ul class="xoo-wsc-notices"></ul></div>	
			<div class="xoo-wsch-basket">
			<span class="xoo-wscb-icon xoo-wsc-icon-bag2"></span>
			<span class="xoo-wscb-count">0</span>
		</div>
	
			<span class="xoo-wsch-text">Your Cart</span>
	
			<span class="xoo-wsch-close xoo-wsc-icon-cross"></span>
	
</div>
		
	</div>


	<div class="xoo-wsc-body">

		
		
<div class="xoo-wsc-empty-cart"><span>Your cart is empty</span></div>
		
	</div>

	<div class="xoo-wsc-footer">

		
		




<div class="xoo-wsc-ft-buttons-cont">

	<a href="#" class="xoo-wsc-ft-btn xoo-wsc-btn xoo-wsc-cart-close xoo-wsc-ft-btn-continue" >Continue Shopping</a>
</div>


		
	</div>

	<span class="xoo-wsc-loader"></span>
	<span class="xoo-wsc-icon-spinner8 xoo-wsc-loader-icon"></span>

</div>
		<span class="xoo-wsc-opac"></span>

	</div>
</div>
<div id="ast-scroll-top" tabindex="0" class="ast-scroll-top-icon ast-scroll-to-top-right" data-on-devices="both">
		<span class="screen-reader-text">Scroll to Top</span>
</div>
		<div data-elementor-type="popup" data-elementor-id="11778" class="elementor elementor-11778 elementor-location-popup" data-elementor-settings="{&quot;a11y_navigation&quot;:&quot;yes&quot;,&quot;triggers&quot;:[],&quot;timing&quot;:[]}" data-elementor-post-type="elementor_library">
			<div class="elementor-element elementor-element-3d340d1 e-flex e-con-boxed e-con e-parent" data-id="3d340d1" data-element_type="container">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-06ef1c6 elementor-widget elementor-widget-heading" data-id="06ef1c6" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h2 class="elementor-heading-title elementor-size-default">Work with Khuyen Tran</h2>				</div>
				</div>
				<div class="elementor-element elementor-element-a6edbb3 elementor-button-align-stretch elementor-widget elementor-widget-form" data-id="a6edbb3" data-element_type="widget" data-settings="{&quot;step_next_label&quot;:&quot;Continue&quot;,&quot;step_previous_label&quot;:&quot;Back&quot;,&quot;step_icon_shape&quot;:&quot;rounded&quot;,&quot;button_width&quot;:&quot;100&quot;,&quot;step_type&quot;:&quot;number_text&quot;}" data-widget_type="form.default">
				<div class="elementor-widget-container">
							<form class="elementor-form" method="post" name="Premium Package " aria-label="Premium Package ">
			<input type="hidden" name="post_id" value="11778"/>
			<input type="hidden" name="form_id" value="a6edbb3"/>
			<input type="hidden" name="referer_title" value="Home | CodeCut" />

							<input type="hidden" name="queried_id" value="12316"/>
			
			<div class="elementor-form-fields-wrapper elementor-labels-above">
								<div class="elementor-field-type-step elementor-field-group elementor-column elementor-field-group-Step1 elementor-col-100">
							<div class="e-field-step elementor-hidden" data-label="" data-previousButton="Back" data-nextButton="Continue" data-iconUrl="" data-iconLibrary="" data-icon="" ></div>

						</div>
								<div class="elementor-field-type-number elementor-field-group elementor-column elementor-field-group-name elementor-col-100 elementor-field-required">
												<label for="form-field-name" class="elementor-field-label">
								Enter Your Total Budget							</label>
									<input type="number" name="form_fields[name]" id="form-field-name" class="elementor-field elementor-size-sm  elementor-field-textual" placeholder="Usd" required="required" min="" max="" >
						</div>
								<div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-email elementor-col-100 elementor-field-required">
												<label for="form-field-email" class="elementor-field-label">
								Please explain the goals of you campaign							</label>
						<textarea class="elementor-field-textual elementor-field  elementor-size-sm" name="form_fields[email]" id="form-field-email" rows="4" placeholder="Start Typing..." required="required"></textarea>				</div>
								<div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-message elementor-col-100">
												<label for="form-field-message" class="elementor-field-label">
								Why Do you want to collaborate with Khuyen Tran							</label>
						<textarea class="elementor-field-textual elementor-field  elementor-size-sm" name="form_fields[message]" id="form-field-message" rows="4" placeholder="Start Typing..."></textarea>				</div>
								<div class="elementor-field-type-step elementor-field-group elementor-column elementor-field-group-step2 elementor-col-100">
							<div class="e-field-step elementor-hidden" data-label="" data-previousButton="" data-nextButton="" data-iconUrl="" data-iconLibrary="fas fa-star" data-icon="" ></div>

						</div>
								<div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-field_22dbf67 elementor-col-100">
												<label for="form-field-field_22dbf67" class="elementor-field-label">
								Add date preferences							</label>
						<textarea class="elementor-field-textual elementor-field  elementor-size-sm" name="form_fields[field_22dbf67]" id="form-field-field_22dbf67" rows="4" placeholder="Start Typing..."></textarea>				</div>
								<div class="elementor-field-type-step elementor-field-group elementor-column elementor-field-group-step3 elementor-col-100">
							<div class="e-field-step elementor-hidden" data-label="" data-previousButton="" data-nextButton="" data-iconUrl="" data-iconLibrary="" data-icon="" ></div>

						</div>
								<div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-field_2e0d59e elementor-col-100">
												<label for="form-field-field_2e0d59e" class="elementor-field-label">
								Enter your contact details							</label>
						<textarea class="elementor-field-textual elementor-field  elementor-size-sm" name="form_fields[field_2e0d59e]" id="form-field-field_2e0d59e" rows="4" placeholder="Start Typing..."></textarea>				</div>
								<div class="elementor-field-group elementor-column elementor-field-type-submit elementor-col-100 e-form__buttons">
					<button class="elementor-button elementor-size-sm" type="submit">
						<span class="elementor-button-content-wrapper">
																						<span class="elementor-button-text">Submit</span>
													</span>
					</button>
				</div>
			</div>
		</form>
						</div>
				</div>
					</div>
				</div>
				</div>
				<div data-elementor-type="popup" data-elementor-id="11767" class="elementor elementor-11767 elementor-location-popup" data-elementor-settings="{&quot;a11y_navigation&quot;:&quot;yes&quot;,&quot;triggers&quot;:[],&quot;timing&quot;:[]}" data-elementor-post-type="elementor_library">
			<div class="elementor-element elementor-element-70bc584 e-flex e-con-boxed e-con e-parent" data-id="70bc584" data-element_type="container">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-81e0fe4 elementor-widget elementor-widget-heading" data-id="81e0fe4" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h2 class="elementor-heading-title elementor-size-default">Work with Khuyen Tran</h2>				</div>
				</div>
				<div class="elementor-element elementor-element-a9ef7a5 elementor-button-align-stretch elementor-widget elementor-widget-form" data-id="a9ef7a5" data-element_type="widget" data-settings="{&quot;step_next_label&quot;:&quot;Continue&quot;,&quot;step_previous_label&quot;:&quot;Back&quot;,&quot;step_icon_shape&quot;:&quot;rounded&quot;,&quot;button_width&quot;:&quot;100&quot;,&quot;step_type&quot;:&quot;number_text&quot;}" data-widget_type="form.default">
				<div class="elementor-widget-container">
							<form class="elementor-form" method="post" name="Starter Package " aria-label="Starter Package ">
			<input type="hidden" name="post_id" value="11767"/>
			<input type="hidden" name="form_id" value="a9ef7a5"/>
			<input type="hidden" name="referer_title" value="Home | CodeCut" />

							<input type="hidden" name="queried_id" value="12316"/>
			
			<div class="elementor-form-fields-wrapper elementor-labels-above">
								<div class="elementor-field-type-step elementor-field-group elementor-column elementor-field-group-Step1 elementor-col-100">
							<div class="e-field-step elementor-hidden" data-label="" data-previousButton="Back" data-nextButton="Continue" data-iconUrl="" data-iconLibrary="" data-icon="" ></div>

						</div>
								<div class="elementor-field-type-number elementor-field-group elementor-column elementor-field-group-name elementor-col-100 elementor-field-required">
												<label for="form-field-name" class="elementor-field-label">
								Enter Your Total Budget							</label>
									<input type="number" name="form_fields[name]" id="form-field-name" class="elementor-field elementor-size-sm  elementor-field-textual" placeholder="Usd" required="required" min="" max="" >
						</div>
								<div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-email elementor-col-100 elementor-field-required">
												<label for="form-field-email" class="elementor-field-label">
								Please explain the goals of you campaign							</label>
						<textarea class="elementor-field-textual elementor-field  elementor-size-sm" name="form_fields[email]" id="form-field-email" rows="4" placeholder="Start Typing..." required="required"></textarea>				</div>
								<div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-message elementor-col-100">
												<label for="form-field-message" class="elementor-field-label">
								Why Do you want to collaborate with Khuyen Tran							</label>
						<textarea class="elementor-field-textual elementor-field  elementor-size-sm" name="form_fields[message]" id="form-field-message" rows="4" placeholder="Start Typing..."></textarea>				</div>
								<div class="elementor-field-type-step elementor-field-group elementor-column elementor-field-group-step2 elementor-col-100">
							<div class="e-field-step elementor-hidden" data-label="" data-previousButton="" data-nextButton="" data-iconUrl="" data-iconLibrary="fas fa-star" data-icon="" ></div>

						</div>
								<div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-field_22dbf67 elementor-col-100">
												<label for="form-field-field_22dbf67" class="elementor-field-label">
								Add date preferences							</label>
						<textarea class="elementor-field-textual elementor-field  elementor-size-sm" name="form_fields[field_22dbf67]" id="form-field-field_22dbf67" rows="4" placeholder="Start Typing..."></textarea>				</div>
								<div class="elementor-field-type-step elementor-field-group elementor-column elementor-field-group-step3 elementor-col-100">
							<div class="e-field-step elementor-hidden" data-label="" data-previousButton="" data-nextButton="" data-iconUrl="" data-iconLibrary="" data-icon="" ></div>

						</div>
								<div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-field_2e0d59e elementor-col-100">
												<label for="form-field-field_2e0d59e" class="elementor-field-label">
								Enter your contact details							</label>
						<textarea class="elementor-field-textual elementor-field  elementor-size-sm" name="form_fields[field_2e0d59e]" id="form-field-field_2e0d59e" rows="4" placeholder="Start Typing..."></textarea>				</div>
								<div class="elementor-field-group elementor-column elementor-field-type-submit elementor-col-100 e-form__buttons">
					<button class="elementor-button elementor-size-sm" type="submit">
						<span class="elementor-button-content-wrapper">
																						<span class="elementor-button-text">Submit</span>
													</span>
					</button>
				</div>
			</div>
		</form>
						</div>
				</div>
					</div>
				</div>
				</div>
		<script type="application/ld+json">{"@context":"https:\/\/schema.org\/","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":{"name":"Home","@id":"https:\/\/codecut.ai"}},{"@type":"ListItem","position":2,"item":{"name":"Blog","@id":"https:\/\/codecut.ai\/blog\/"}},{"@type":"ListItem","position":3,"item":{"name":"Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial","@id":"https:\/\/codecut.ai\/building-multi-agent-ai-langgraph-tutorial\/"}}]}</script>			<script>
				const lazyloadRunObserver = () => {
					const lazyloadBackgrounds = document.querySelectorAll( `.e-con.e-parent:not(.e-lazyloaded)` );
					const lazyloadBackgroundObserver = new IntersectionObserver( ( entries ) => {
						entries.forEach( ( entry ) => {
							if ( entry.isIntersecting ) {
								let lazyloadBackground = entry.target;
								if( lazyloadBackground ) {
									lazyloadBackground.classList.add( 'e-lazyloaded' );
								}
								lazyloadBackgroundObserver.unobserve( entry.target );
							}
						});
					}, { rootMargin: '200px 0px 200px 0px' } );
					lazyloadBackgrounds.forEach( ( lazyloadBackground ) => {
						lazyloadBackgroundObserver.observe( lazyloadBackground );
					} );
				};
				const events = [
					'DOMContentLoaded',
					'elementor/lazyload/observe',
				];
				events.forEach( ( event ) => {
					document.addEventListener( event, lazyloadRunObserver );
				} );
			</script>
			<style id="mutliple-author-box-inline-style">    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .box-header-title {
        font-size: 30px !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .box-header-title {
        text-align: center !important; 
    }

.pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-avatar img { 
        width: 100px !important; 
        height: 100px !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-avatar img {
        border-style: none !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-avatar img {
        border-radius: 50% !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-name a {
        font-size: 20px !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-name a {
        font-weight: 500 !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-name a {
        text-transform: capitalize !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-meta a {
        background-color: #655997 !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-meta a {
        color: #ffffff !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-meta a:hover {
        color: #ffffff !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-recent-posts-title {
        border-bottom-style: dotted !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-recent-posts-item {
        text-align: left !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-multiple-authors-boxes-li {
        padding-right: 100px !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-multiple-authors-boxes-li {
        border-style: none !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-multiple-authors-boxes-li {
        color: #3c434a !important; 
    }

.pp-multiple-authors-layout-inline ul.pp-multiple-authors-boxes-ul {
    display: flex;
}

.pp-multiple-authors-layout-inline ul.pp-multiple-authors-boxes-ul li {
    margin-right: 10px
}

.pp-multiple-authors-layout-inline ul.pp-multiple-authors-boxes-ul li.has-avatar .pp-author-boxes-avatar,
.pp-multiple-authors-layout-inline ul.pp-multiple-authors-boxes-ul li.has-avatar .pp-author-boxes-avatar-details {
    display: inline-block;
}</style>	<script>
		(function () {
			var c = document.body.className;
			c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
			document.body.className = c;
		})();
	</script>
	<link rel='stylesheet' id='wc-stripe-blocks-checkout-style-css' href='https://codecut.ai/wp-content/plugins/woocommerce-gateway-stripe/build/upe-blocks.css?ver=f674640426dacb65cf29a54b02345e01' media='all' />
<link rel='stylesheet' id='wc-ppcp-blocks-styles-css' href='https://codecut.ai/wp-content/plugins/pymntpl-paypal-woocommerce/packages/blocks/build/styles.css?ver=1.1.13' media='all' />
<link rel='stylesheet' id='wc-ppcp-style-css' href='https://codecut.ai/wp-content/plugins/pymntpl-paypal-woocommerce/build/css/styles.css?ver=1.1.13' media='all' />
<link rel='stylesheet' id='wc-blocks-style-css' href='https://codecut.ai/wp-content/plugins/woocommerce/assets/client/blocks/wc-blocks.css?ver=wc-10.2.2' media='all' />
<link rel='stylesheet' id='multiple-authors-widget-css-css' href='https://codecut.ai/wp-content/plugins/publishpress-authors/src/assets/css/multiple-authors-widget.css?ver=4.7.6' media='all' />
<style id='multiple-authors-widget-css-inline-css'>
:root { --ppa-color-scheme: #655997; --ppa-color-scheme-active: #514779; }
</style>
<link rel='stylesheet' id='multiple-authors-fontawesome-css' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.1/css/all.min.css?ver=4.7.6' media='all' />
<link rel='stylesheet' id='elementor-icons-ekiticons-css' href='https://codecut.ai/wp-content/plugins/elementskit-lite/modules/elementskit-icon-pack/assets/css/ekiticons.css?ver=3.5.4' media='all' />
<script src="https://codecut.ai/wp-includes/js/comment-reply.min.js?ver=6.8.2" id="comment-reply-js" async data-wp-strategy="async"></script>
<script id="astra-theme-js-js-extra">
var astra = {"break_point":"10","isRtl":"","is_scroll_to_id":"","is_scroll_to_top":"1","is_header_footer_builder_active":"","responsive_cart_click":"flyout","is_dark_palette":""};
</script>
<script src="https://codecut.ai/wp-content/themes/astra/assets/js/minified/style.min.js?ver=4.11.12" id="astra-theme-js-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/vendor/react.min.js?ver=********" id="react-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/vendor/react-dom.min.js?ver=********" id="react-dom-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/vendor/react-jsx-runtime.min.js?ver=18.3.1" id="react-jsx-runtime-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/dom-ready.min.js?ver=f77871ff7694fffea381" id="wp-dom-ready-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6" id="wp-hooks-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6" id="wp-i18n-js"></script>
<script id="wp-i18n-js-after">
wp.i18n.setLocaleData( { 'text direction\u0004ltr': [ 'ltr' ] } );
</script>
<script src="https://codecut.ai/wp-includes/js/dist/a11y.min.js?ver=3156534cc54473497e14" id="wp-a11y-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/vendor/wp-polyfill.min.js?ver=3.15.0" id="wp-polyfill-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/url.min.js?ver=c2964167dfe2477c14ea" id="wp-url-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/api-fetch.min.js?ver=3623a576c78df404ff20" id="wp-api-fetch-js"></script>
<script id="wp-api-fetch-js-after">
wp.apiFetch.use( wp.apiFetch.createRootURLMiddleware( "https://codecut.ai/wp-json/" ) );
wp.apiFetch.nonceMiddleware = wp.apiFetch.createNonceMiddleware( "6ab77a347f" );
wp.apiFetch.use( wp.apiFetch.nonceMiddleware );
wp.apiFetch.use( wp.apiFetch.mediaUploadMiddleware );
wp.apiFetch.nonceEndpoint = "https://codecut.ai/wp-admin/admin-ajax.php?action=rest-nonce";
</script>
<script src="https://codecut.ai/wp-includes/js/dist/blob.min.js?ver=9113eed771d446f4a556" id="wp-blob-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/block-serialization-default-parser.min.js?ver=14d44daebf663d05d330" id="wp-block-serialization-default-parser-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/autop.min.js?ver=9fb50649848277dd318d" id="wp-autop-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/deprecated.min.js?ver=e1f84915c5e8ae38964c" id="wp-deprecated-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/dom.min.js?ver=80bd57c84b45cf04f4ce" id="wp-dom-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/escape-html.min.js?ver=6561a406d2d232a6fbd2" id="wp-escape-html-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/element.min.js?ver=a4eeeadd23c0d7ab1d2d" id="wp-element-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/is-shallow-equal.min.js?ver=e0f9f1d78d83f5196979" id="wp-is-shallow-equal-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/keycodes.min.js?ver=034ff647a54b018581d3" id="wp-keycodes-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/priority-queue.min.js?ver=9c21c957c7e50ffdbf48" id="wp-priority-queue-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/compose.min.js?ver=84bcf832a5c99203f3db" id="wp-compose-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/private-apis.min.js?ver=0f8478f1ba7e0eea562b" id="wp-private-apis-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/redux-routine.min.js?ver=8bb92d45458b29590f53" id="wp-redux-routine-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/data.min.js?ver=fe6c4835cd00e12493c3" id="wp-data-js"></script>
<script id="wp-data-js-after">
( function() {
	var userId = 0;
	var storageKey = "WP_DATA_USER_" + userId;
	wp.data
		.use( wp.data.plugins.persistence, { storageKey: storageKey } );
} )();
</script>
<script src="https://codecut.ai/wp-includes/js/dist/html-entities.min.js?ver=2cd3358363e0675638fb" id="wp-html-entities-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/rich-text.min.js?ver=74178fc8c4d67d66f1a8" id="wp-rich-text-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/shortcode.min.js?ver=b7747eee0efafd2f0c3b" id="wp-shortcode-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/warning.min.js?ver=ed7c8b0940914f4fe44b" id="wp-warning-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/blocks.min.js?ver=84530c06a3c62815b497" id="wp-blocks-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/vendor/moment.min.js?ver=2.30.1" id="moment-js"></script>
<script id="moment-js-after">
moment.updateLocale( 'en_US', {"months":["January","February","March","April","May","June","July","August","September","October","November","December"],"monthsShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"weekdays":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"weekdaysShort":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"week":{"dow":1},"longDateFormat":{"LT":"g:i a","LTS":null,"L":null,"LL":"F j, Y","LLL":"F j, Y g:i a","LLLL":null}} );
</script>
<script src="https://codecut.ai/wp-includes/js/dist/date.min.js?ver=85ff222add187a4e358f" id="wp-date-js"></script>
<script id="wp-date-js-after">
wp.date.setSettings( {"l10n":{"locale":"en_US","months":["January","February","March","April","May","June","July","August","September","October","November","December"],"monthsShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"weekdays":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"weekdaysShort":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"meridiem":{"am":"am","pm":"pm","AM":"AM","PM":"PM"},"relative":{"future":"%s from now","past":"%s ago","s":"a second","ss":"%d seconds","m":"a minute","mm":"%d minutes","h":"an hour","hh":"%d hours","d":"a day","dd":"%d days","M":"a month","MM":"%d months","y":"a year","yy":"%d years"},"startOfWeek":1},"formats":{"time":"g:i a","date":"F j, Y","datetime":"F j, Y g:i a","datetimeAbbreviated":"M j, Y g:i a"},"timezone":{"offset":-5,"offsetFormatted":"-5","string":"America\/Chicago","abbr":"CDT"}} );
</script>
<script src="https://codecut.ai/wp-includes/js/dist/primitives.min.js?ver=aef2543ab60c8c9bb609" id="wp-primitives-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/components.min.js?ver=865f2ec3b5f5195705e0" id="wp-components-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/keyboard-shortcuts.min.js?ver=32686e58e84193ce808b" id="wp-keyboard-shortcuts-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/commands.min.js?ver=14ee29ad1743be844b11" id="wp-commands-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/notices.min.js?ver=673a68a7ac2f556ed50b" id="wp-notices-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/preferences-persistence.min.js?ver=9307a8c9e3254140a223" id="wp-preferences-persistence-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/preferences.min.js?ver=4aa23582b858c882a887" id="wp-preferences-js"></script>
<script id="wp-preferences-js-after">
( function() {
				var serverData = false;
				var userId = "0";
				var persistenceLayer = wp.preferencesPersistence.__unstableCreatePersistenceLayer( serverData, userId );
				var preferencesStore = wp.preferences.store;
				wp.data.dispatch( preferencesStore ).setPersistenceLayer( persistenceLayer );
			} ) ();
</script>
<script src="https://codecut.ai/wp-includes/js/dist/style-engine.min.js?ver=08cc10e9532531e22456" id="wp-style-engine-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/token-list.min.js?ver=3b5f5dcfde830ecef24f" id="wp-token-list-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/block-editor.min.js?ver=b3b0b55b35e04df52f7c" id="wp-block-editor-js"></script>
<script src="https://codecut.ai/wp-includes/js/dist/server-side-render.min.js?ver=345a014347e34be995f0" id="wp-server-side-render-js"></script>
<script src="https://codecut.ai/wp-content/plugins/faster-youtube-embed/block/build/faster-youtube.js?ver=b4c7955cb0da4b73ca514c131e8ec697" id="ht-youtub-embed-blocks-js"></script>
<script src="https://codecut.ai/wp-content/plugins/faster-youtube-embed/assets/js/LiteYTEmbed.js?ver=b4c7955cb0da4b73ca514c131e8ec697" id="ht-youtube-embed-js-js"></script>
<script id="cr-frontend-js-js-extra">
var cr_ajax_object = {"ajax_url":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php"};
var cr_ajax_object = {"ajax_url":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","disable_lightbox":"0"};
</script>
<script src="https://codecut.ai/wp-content/plugins/customer-reviews-woocommerce/js/frontend.js?ver=5.80.2" id="cr-frontend-js-js"></script>
<script src="https://codecut.ai/wp-content/plugins/customer-reviews-woocommerce/js/colcade.js?ver=5.80.2" id="cr-colcade-js"></script>
<script id="bt_conversion_scripts-js-extra">
var btab_vars = {"is_admin":"","post_id":"15293","is_preview":"","is_agency":"","is_free":"","tagging":"1","do_fingerprint":"0","advanced_tracking":"0","abst_server_convert_woo":"0","plugins_uri":"https:\/\/codecut.ai\/wp-content\/plugins\/bt-bb-ab\/","domain":"https:\/\/codecut.ai"};
</script>
<script data-cfasync="false" src="https://codecut.ai/wp-content/plugins/bt-bb-ab/js/bt_conversion-min.js?ver=2.0.0" id="bt_conversion_scripts-js"></script>
<script src="https://codecut.ai/wp-content/plugins/prismatic/lib/prism/js/prism-core.js?ver=3.5" id="prismatic-prism-js"></script>
<script src="https://codecut.ai/wp-content/plugins/prismatic/lib/prism/js/plugin-toolbar.js?ver=3.5" id="prismatic-prism-toolbar-js"></script>
<script src="https://codecut.ai/wp-content/plugins/prismatic/lib/prism/js/plugin-show-language.js?ver=3.5" id="prismatic-prism-show-language-js"></script>
<script src="https://codecut.ai/wp-content/plugins/prismatic/lib/prism/js/plugin-copy-clipboard.js?ver=3.5" id="prismatic-copy-clipboard-js"></script>
<script src="https://codecut.ai/wp-content/plugins/prismatic/lib/prism/js/plugin-command-line.js?ver=3.5" id="prismatic-command-line-js"></script>
<script src="https://codecut.ai/wp-content/plugins/prismatic/lib/prism/js/lang-bash.js?ver=3.5" id="prismatic-prism-bash-js"></script>
<script src="https://codecut.ai/wp-content/plugins/prismatic/lib/prism/js/lang-python.js?ver=3.5" id="prismatic-prism-python-js"></script>
<script id="wp-postviews-cache-js-extra">
var viewsCacheL10n = {"admin_ajax_url":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","nonce":"f4a03d8d2c","post_id":"15293"};
</script>
<script src="https://codecut.ai/wp-content/plugins/wp-postviews/postviews-cache.js?ver=1.77" id="wp-postviews-cache-js"></script>
<script src="https://codecut.ai/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3" id="jquery-ui-core-js"></script>
<script src="https://codecut.ai/wp-includes/js/jquery/ui/datepicker.min.js?ver=1.13.3" id="jquery-ui-datepicker-js"></script>
<script id="jquery-ui-datepicker-js-after">
jQuery(function(jQuery){jQuery.datepicker.setDefaults({"closeText":"Close","currentText":"Today","monthNames":["January","February","March","April","May","June","July","August","September","October","November","December"],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"nextText":"Next","prevText":"Previous","dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"dayNamesMin":["S","M","T","W","T","F","S"],"dateFormat":"MM d, yy","firstDay":1,"isRTL":false});});
</script>
<script src="https://codecut.ai/wp-content/plugins/pre-orders-for-woocommerce/media/js/date-picker.js?ver=2.1" id="preorders-field-date-js-js"></script>
<script id="preorders-main-js-js-extra">
var DBData = {"default_add_to_cart_text":"Add to cart","preorders_add_to_cart_text":"Pre Order Now!"};
</script>
<script src="https://codecut.ai/wp-content/plugins/pre-orders-for-woocommerce/media/js/main.js?ver=2.1" id="preorders-main-js-js"></script>
<script id="woosb-frontend-js-extra">
var woosb_vars = {"wc_price_decimals":"2","wc_price_format":"%1$s%2$s","wc_price_thousand_separator":",","wc_price_decimal_separator":".","wc_currency_symbol":"$","price_decimals":"2","price_format":"%1$s%2$s","price_thousand_separator":",","price_decimal_separator":".","currency_symbol":"$","trim_zeros":"","round_price":"1","change_image":"yes","bundled_price":"price","bundled_price_from":"sale_price","change_price":"yes","price_selector":"","saved_text":"(saved [d])","price_text":"Bundle price:","selected_text":"Selected:","alert_selection":"Please select a purchasable variation for [name] before adding this bundle to the cart.","alert_unpurchasable":"Product [name] is unpurchasable. Please remove it before adding the bundle to the cart.","alert_empty":"Please choose at least one product before adding this bundle to the cart.","alert_min":"Please choose at least a total quantity of [min] products before adding this bundle to the cart.","alert_max":"Sorry, you can only choose at max a total quantity of [max] products before adding this bundle to the cart.","alert_total_min":"The total must meet the minimum amount of [min].","alert_total_max":"The total must meet the maximum amount of [max]."};
</script>
<script src="https://codecut.ai/wp-content/plugins/woo-product-bundle/assets/js/frontend.js?ver=8.3.2" id="woosb-frontend-js"></script>
<script src="https://codecut.ai/wp-content/plugins/3d-flipbook-dflip-lite/assets/js/dflip.min.js?ver=2.3.75" id="dflip-script-js"></script>
<script id="wd-asl-ajaxsearchlite-js-before">
window.ASL = typeof window.ASL !== 'undefined' ? window.ASL : {}; window.ASL.wp_rocket_exception = "DOMContentLoaded"; window.ASL.ajaxurl = "https:\/\/codecut.ai\/wp-admin\/admin-ajax.php"; window.ASL.backend_ajaxurl = "https:\/\/codecut.ai\/wp-admin\/admin-ajax.php"; window.ASL.js_scope = "jQuery"; window.ASL.asl_url = "https:\/\/codecut.ai\/wp-content\/plugins\/ajax-search-lite\/"; window.ASL.detect_ajax = 1; window.ASL.media_query = 4774; window.ASL.version = 4774; window.ASL.pageHTML = ""; window.ASL.additional_scripts = []; window.ASL.script_async_load = false; window.ASL.init_only_in_viewport = true; window.ASL.font_url = "https:\/\/codecut.ai\/wp-content\/plugins\/ajax-search-lite\/css\/fonts\/icons2.woff2"; window.ASL.highlight = {"enabled":false,"data":[]}; window.ASL.analytics = {"method":0,"tracking_id":"","string":"?ajax_search={asl_term}","event":{"focus":{"active":true,"action":"focus","category":"ASL","label":"Input focus","value":"1"},"search_start":{"active":false,"action":"search_start","category":"ASL","label":"Phrase: {phrase}","value":"1"},"search_end":{"active":true,"action":"search_end","category":"ASL","label":"{phrase} | {results_count}","value":"1"},"magnifier":{"active":true,"action":"magnifier","category":"ASL","label":"Magnifier clicked","value":"1"},"return":{"active":true,"action":"return","category":"ASL","label":"Return button pressed","value":"1"},"facet_change":{"active":false,"action":"facet_change","category":"ASL","label":"{option_label} | {option_value}","value":"1"},"result_click":{"active":true,"action":"result_click","category":"ASL","label":"{result_title} | {result_url}","value":"1"}}};
window.ASL_INSTANCES = [];window.ASL_INSTANCES[1] = {"homeurl":"https:\/\/codecut.ai\/","resultstype":"vertical","resultsposition":"hover","itemscount":4,"charcount":0,"highlight":0,"highlightwholewords":1,"singleHighlight":0,"scrollToResults":{"enabled":0,"offset":0},"resultareaclickable":1,"autocomplete":{"enabled":1,"lang":"en","trigger_charcount":0},"mobile":{"menu_selector":"#menu-toggle"},"trigger":{"click":"results_page","click_location":"same","update_href":0,"return":"results_page","return_location":"same","facet":1,"type":1,"redirect_url":"?s={phrase}","delay":300},"animations":{"pc":{"settings":{"anim":"fadedrop","dur":300},"results":{"anim":"fadedrop","dur":300},"items":"voidanim"},"mob":{"settings":{"anim":"fadedrop","dur":300},"results":{"anim":"fadedrop","dur":300},"items":"voidanim"}},"autop":{"state":"disabled","phrase":"","count":1},"resPage":{"useAjax":0,"selector":"#main","trigger_type":1,"trigger_facet":1,"trigger_magnifier":0,"trigger_return":0},"resultsSnapTo":"left","results":{"width":"auto","width_tablet":"auto","width_phone":"auto"},"settingsimagepos":"right","closeOnDocClick":1,"overridewpdefault":1,"override_method":"get"};
</script>
<script src="https://codecut.ai/wp-content/plugins/ajax-search-lite/js/min/plugin/merged/asl.min.js?ver=4774" id="wd-asl-ajaxsearchlite-js"></script>
<script src="https://codecut.ai/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.32.3" id="elementor-webpack-runtime-js"></script>
<script src="https://codecut.ai/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.32.3" id="elementor-frontend-modules-js"></script>
<script id="elementor-frontend-js-before">
var elementorFrontendConfig = {"environmentMode":{"edit":false,"wpPreview":false,"isScriptDebug":false},"i18n":{"shareOnFacebook":"Share on Facebook","shareOnTwitter":"Share on Twitter","pinIt":"Pin it","download":"Download","downloadImage":"Download image","fullscreen":"Fullscreen","zoom":"Zoom","share":"Share","playVideo":"Play Video","previous":"Previous","next":"Next","close":"Close","a11yCarouselPrevSlideMessage":"Previous slide","a11yCarouselNextSlideMessage":"Next slide","a11yCarouselFirstSlideMessage":"This is the first slide","a11yCarouselLastSlideMessage":"This is the last slide","a11yCarouselPaginationBulletMessage":"Go to slide"},"is_rtl":false,"breakpoints":{"xs":0,"sm":480,"md":768,"lg":1025,"xl":1440,"xxl":1600},"responsive":{"breakpoints":{"mobile":{"label":"Mobile Portrait","value":767,"default_value":767,"direction":"max","is_enabled":true},"mobile_extra":{"label":"Mobile Landscape","value":880,"default_value":880,"direction":"max","is_enabled":false},"tablet":{"label":"Tablet Portrait","value":1024,"default_value":1024,"direction":"max","is_enabled":true},"tablet_extra":{"label":"Tablet Landscape","value":1200,"default_value":1200,"direction":"max","is_enabled":false},"laptop":{"label":"Laptop","value":1366,"default_value":1366,"direction":"max","is_enabled":true},"widescreen":{"label":"Widescreen","value":2400,"default_value":2400,"direction":"min","is_enabled":true}},"hasCustomBreakpoints":true},"version":"3.32.3","is_static":false,"experimentalFeatures":{"additional_custom_breakpoints":true,"container":true,"theme_builder_v2":true,"nested-elements":true,"home_screen":true,"global_classes_should_enforce_capabilities":true,"e_variables":true,"cloud-library":true,"e_opt_in_v4_page":true,"import-export-customization":true,"e_pro_variables":true},"urls":{"assets":"https:\/\/codecut.ai\/wp-content\/plugins\/elementor\/assets\/","ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","uploadUrl":"https:\/\/codecut.ai\/wp-content\/uploads"},"nonces":{"floatingButtonsClickTracking":"da354521c9"},"swiperClass":"swiper","settings":{"page":[],"editorPreferences":[]},"kit":{"active_breakpoints":["viewport_mobile","viewport_tablet","viewport_laptop","viewport_widescreen"],"global_image_lightbox":"yes","lightbox_enable_counter":"yes","lightbox_enable_fullscreen":"yes","lightbox_enable_zoom":"yes","lightbox_enable_share":"yes","lightbox_title_src":"title","lightbox_description_src":"description","woocommerce_notices_elements":[]},"post":{"id":15293,"title":"Building%20Coordinated%20AI%20Agents%20with%20LangGraph%3A%20A%20Hands-On%20Tutorial%20%7C%20CodeCut","excerpt":"","featuredImage":"https:\/\/codecut.ai\/wp-content\/uploads\/2025\/06\/langraph-featured-image.png"}};
</script>
<script src="https://codecut.ai/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.32.3" id="elementor-frontend-js"></script>
<script src="https://codecut.ai/wp-content/plugins/elementor-pro/assets/lib/smartmenus/jquery.smartmenus.min.js?ver=1.2.1" id="smartmenus-js"></script>
<script src="https://codecut.ai/wp-includes/js/imagesloaded.min.js?ver=5.0.0" id="imagesloaded-js"></script>
<script src="https://codecut.ai/wp-content/plugins/elementskit-lite/libs/framework/assets/js/frontend-script.js?ver=3.5.4" id="elementskit-framework-js-frontend-js"></script>
<script id="elementskit-framework-js-frontend-js-after">
		var elementskit = {
			resturl: 'https://codecut.ai/wp-json/elementskit/v1/',
		}

		
</script>
<script src="https://codecut.ai/wp-content/plugins/elementskit-lite/widgets/init/assets/js/widget-scripts.js?ver=3.5.4" id="ekit-widget-scripts-js"></script>
<script src="https://codecut.ai/wp-content/plugins/woocommerce/assets/js/sourcebuster/sourcebuster.min.js?ver=10.2.2" id="sourcebuster-js-js"></script>
<script id="wc-order-attribution-js-extra">
var wc_order_attribution = {"params":{"lifetime":1.0e-5,"session":30,"base64":false,"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","prefix":"wc_order_attribution_","allowTracking":true},"fields":{"source_type":"current.typ","referrer":"current_add.rf","utm_campaign":"current.cmp","utm_source":"current.src","utm_medium":"current.mdm","utm_content":"current.cnt","utm_id":"current.id","utm_term":"current.trm","utm_source_platform":"current.plt","utm_creative_format":"current.fmt","utm_marketing_tactic":"current.tct","session_entry":"current_add.ep","session_start_time":"current_add.fd","session_pages":"session.pgs","session_count":"udata.vst","user_agent":"udata.uag"}};
</script>
<script src="https://codecut.ai/wp-content/plugins/woocommerce/assets/js/frontend/order-attribution.min.js?ver=10.2.2" id="wc-order-attribution-js"></script>
<script id="xoo-wsc-main-js-js-extra">
var xoo_wsc_params = {"adminurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","qtyUpdateDelay":"500","notificationTime":"5000","html":{"successNotice":"<ul class=\"xoo-wsc-notices\"><li class=\"xoo-wsc-notice-success\"><span class=\"xoo-wsc-icon-check_circle\"><\/span>%s%<\/li><\/ul>","errorNotice":"<ul class=\"xoo-wsc-notices\"><li class=\"xoo-wsc-notice-error\"><span class=\"xoo-wsc-icon-cross\"><\/span>%s%<\/li><\/ul>"},"strings":{"maxQtyError":"Only %s% in stock","stepQtyError":"Quantity can only be purchased in multiple of %s%","calculateCheckout":"Please use checkout form to calculate shipping","couponEmpty":"Please enter promo code"},"isCheckout":"","isCart":"","sliderAutoClose":"1","shippingEnabled":"1","couponsEnabled":"1","autoOpenCart":"yes","addedToCart":"","ajaxAddToCart":"yes","skipAjaxForData":[],"showBasket":"hide_empty","flyToCart":"no","productFlyClass":"","refreshCart":"no","fetchDelay":"200","triggerClass":"","productLayout":"rows","cardAnimate":{"enable":"yes","type":"slideUp","event":"back_hover","duration":"0.5"}};
</script>
<script src="https://codecut.ai/wp-content/plugins/side-cart-woocommerce/assets/js/xoo-wsc-main.js?ver=2.6.8" id="xoo-wsc-main-js-js" defer data-wp-strategy="defer"></script>
<script src="https://codecut.ai/wp-includes/js/underscore.min.js?ver=1.13.7" id="underscore-js"></script>
<script id="wp-util-js-extra">
var _wpUtilSettings = {"ajax":{"url":"\/wp-admin\/admin-ajax.php"}};
</script>
<script src="https://codecut.ai/wp-includes/js/wp-util.min.js?ver=6.8.2" id="wp-util-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/tipsy/tipsy.min.js?ver=1.0.0a" id="um_tipsy-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/um-confirm/um-confirm.min.js?ver=1.0" id="um_confirm-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/pickadate/picker.min.js?ver=3.6.2" id="um_datetime-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/pickadate/picker.date.min.js?ver=3.6.2" id="um_datetime_date-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/pickadate/picker.time.min.js?ver=3.6.2" id="um_datetime_time-js"></script>
<script id="um_common-js-extra">
var um_common_variables = {"locale":"en_US"};
var um_common_variables = {"locale":"en_US"};
</script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/common.min.js?ver=2.10.5" id="um_common-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/cropper/cropper.min.js?ver=1.6.1" id="um_crop-js"></script>
<script id="um_frontend_common-js-extra">
var um_frontend_common_variables = [];
</script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/common-frontend.min.js?ver=2.10.5" id="um_frontend_common-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/um-modal.min.js?ver=2.10.5" id="um_modal-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/jquery-form/jquery-form.min.js?ver=2.10.5" id="um_jquery_form-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/fileupload/fileupload.js?ver=2.10.5" id="um_fileupload-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/um-functions.min.js?ver=2.10.5" id="um_functions-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/um-responsive.min.js?ver=2.10.5" id="um_responsive-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/um-conditional.min.js?ver=2.10.5" id="um_conditional-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/select2/select2.full.min.js?ver=4.0.13" id="select2-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/select2/i18n/en.js?ver=4.0.13" id="um_select2_locale-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/libs/raty/um-raty.min.js?ver=2.6.0" id="um_raty-js"></script>
<script id="um_scripts-js-extra">
var um_scripts = {"max_upload_size":"**********","nonce":"f920946e91"};
</script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/um-scripts.min.js?ver=2.10.5" id="um_scripts-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/um-profile.min.js?ver=2.10.5" id="um_profile-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ultimate-member/assets/js/um-account.min.js?ver=2.10.5" id="um_account-js"></script>
<script id="ivory-search-scripts-js-extra">
var IvorySearchVars = {"is_analytics_enabled":"1"};
</script>
<script src="https://codecut.ai/wp-content/plugins/add-search-to-menu/public/js/ivory-search.min.js?ver=5.5.11" id="ivory-search-scripts-js"></script>
<script defer src="https://codecut.ai/wp-content/plugins/akismet/_inc/akismet-frontend.js?ver=**********" id="akismet-frontend-js"></script>
<script src="https://codecut.ai/wp-content/plugins/ele-custom-skin-pro/assets/js/ecspro.js?ver=3.2.5" id="ecspro-js"></script>
<script src="https://codecut.ai/wp-content/plugins/elementor-pro/assets/js/webpack-pro.runtime.min.js?ver=3.32.2" id="elementor-pro-webpack-runtime-js"></script>
<script id="elementor-pro-frontend-js-before">
var ElementorProFrontendConfig = {"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","nonce":"d6fc6c1011","urls":{"assets":"https:\/\/codecut.ai\/wp-content\/plugins\/elementor-pro\/assets\/","rest":"https:\/\/codecut.ai\/wp-json\/"},"settings":{"lazy_load_background_images":true},"popup":{"hasPopUps":true},"shareButtonsNetworks":{"facebook":{"title":"Facebook","has_counter":true},"twitter":{"title":"Twitter"},"linkedin":{"title":"LinkedIn","has_counter":true},"pinterest":{"title":"Pinterest","has_counter":true},"reddit":{"title":"Reddit","has_counter":true},"vk":{"title":"VK","has_counter":true},"odnoklassniki":{"title":"OK","has_counter":true},"tumblr":{"title":"Tumblr"},"digg":{"title":"Digg"},"skype":{"title":"Skype"},"stumbleupon":{"title":"StumbleUpon","has_counter":true},"mix":{"title":"Mix"},"telegram":{"title":"Telegram"},"pocket":{"title":"Pocket","has_counter":true},"xing":{"title":"XING","has_counter":true},"whatsapp":{"title":"WhatsApp"},"email":{"title":"Email"},"print":{"title":"Print"},"x-twitter":{"title":"X"},"threads":{"title":"Threads"}},"woocommerce":{"menu_cart":{"cart_page_url":"https:\/\/codecut.ai\/cart\/","checkout_page_url":"https:\/\/codecut.ai\/checkout\/","fragments_nonce":"5210d041a3"}},"facebook_sdk":{"lang":"en_US","app_id":""},"lottie":{"defaultAnimationUrl":"https:\/\/codecut.ai\/wp-content\/plugins\/elementor-pro\/modules\/lottie\/assets\/animations\/default.json"}};
</script>
<script src="https://codecut.ai/wp-content/plugins/elementor-pro/assets/js/frontend.min.js?ver=3.32.2" id="elementor-pro-frontend-js"></script>
<script src="https://codecut.ai/wp-content/plugins/elementor-pro/assets/js/elements-handlers.min.js?ver=3.32.2" id="pro-elements-handlers-js"></script>
<script src="https://codecut.ai/wp-content/plugins/elementskit-lite/widgets/init/assets/js/animate-circle.min.js?ver=3.5.4" id="animate-circle-js"></script>
<script id="elementskit-elementor-js-extra">
var ekit_config = {"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","nonce":"2a6deaa770"};
</script>
<script src="https://codecut.ai/wp-content/plugins/elementskit-lite/widgets/init/assets/js/elementor.js?ver=3.5.4" id="elementskit-elementor-js"></script>
			<script>
			/(trident|msie)/i.test(navigator.userAgent)&&document.getElementById&&window.addEventListener&&window.addEventListener("hashchange",function(){var t,e=location.hash.substring(1);/^[A-z0-9_-]+$/.test(e)&&(t=document.getElementById(e))&&(/^(?:a|select|input|button|textarea)$/i.test(t.tagName)||(t.tabIndex=-1),t.focus())},!1);
			</script>
			        <script data-cfasync="false">
          var dFlipLocation = 'https://codecut.ai/wp-content/plugins/3d-flipbook-dflip-lite/assets/';
          var dFlipWPGlobal = {"text":{"toggleSound":"Turn on\/off Sound","toggleThumbnails":"Toggle Thumbnails","toggleOutline":"Toggle Outline\/Bookmark","previousPage":"Previous Page","nextPage":"Next Page","toggleFullscreen":"Toggle Fullscreen","zoomIn":"Zoom In","zoomOut":"Zoom Out","toggleHelp":"Toggle Help","singlePageMode":"Single Page Mode","doublePageMode":"Double Page Mode","downloadPDFFile":"Download PDF File","gotoFirstPage":"Goto First Page","gotoLastPage":"Goto Last Page","share":"Share","mailSubject":"I wanted you to see this FlipBook","mailBody":"Check out this site {{url}}","loading":"DearFlip: Loading "},"viewerType":"flipbook","moreControls":"download,pageMode,startPage,endPage,sound","hideControls":"","scrollWheel":"false","backgroundColor":"#777","backgroundImage":"","height":"auto","paddingLeft":"20","paddingRight":"20","controlsPosition":"bottom","duration":800,"soundEnable":"true","enableDownload":"true","showSearchControl":"false","showPrintControl":"false","enableAnnotation":false,"enableAnalytics":"false","webgl":"true","hard":"none","maxTextureSize":"1600","rangeChunkSize":"524288","zoomRatio":1.5,"stiffness":3,"pageMode":"0","singlePageMode":"0","pageSize":"0","autoPlay":"false","autoPlayDuration":5000,"autoPlayStart":"false","linkTarget":"2","sharePrefix":"flipbook-"};
        </script>
              <script type="text/javascript">
        window.addEventListener("load", function() {
            const postId = 15293;
            const ajaxUrl = 'https://codecut.ai/wp-admin/admin-ajax.php';
            if (!localStorage.getItem('post_viewed_' + postId)) {
                fetch(ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
                    },
                    body: new URLSearchParams({
                        'action': 'track_post_views',
                        'post_id': postId
                    })
                }).then(response => {
                    if (response.ok) {
                        localStorage.setItem('post_viewed_' + postId, 'true');
                    }
                });
            }
        });
        </script>
        	</body>
</html>

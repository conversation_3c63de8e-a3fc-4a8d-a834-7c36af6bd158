["tests/test_config.py::TestSEOConfig::test_config_default_values", "tests/test_config.py::TestSEOConfig::test_config_temperature_range", "tests/test_config.py::TestSEOConfig::test_config_types", "tests/test_config.py::test_config_with_pytest", "tests/test_seo_metrics.py::TestSEOMetrics::test_analyze_seo_metrics_basic", "tests/test_seo_metrics.py::TestSEOMetrics::test_analyze_seo_metrics_empty_content", "tests/test_seo_metrics.py::TestSEOMetrics::test_analyze_seo_metrics_headers", "tests/test_seo_metrics.py::TestSEOMetrics::test_analyze_seo_metrics_links", "tests/test_seo_metrics.py::TestSEOMetrics::test_analyze_seo_metrics_word_count", "tests/test_seo_metrics.py::test_seo_metrics_with_pytest", "tests/test_web_scrape.py::TestWebScrape::test_extract_title_no_title", "tests/test_web_scrape.py::TestWebScrape::test_extract_title_valid_content", "tests/test_web_scrape.py::TestWebScrape::test_web_scrape_invalid_url", "tests/test_web_scrape.py::TestWebScrape::test_web_scrape_valid_url", "tests/test_web_scrape.py::test_web_scrape_with_pytest"]
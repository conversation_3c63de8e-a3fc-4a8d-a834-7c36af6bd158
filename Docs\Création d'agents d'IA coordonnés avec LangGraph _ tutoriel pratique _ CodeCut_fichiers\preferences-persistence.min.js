/*! This file is auto-generated */
(()=>{"use strict";var e={n:r=>{var t=r&&r.__esModule?()=>r.default:()=>r;return e.d(t,{a:t}),t},d:(r,t)=>{for(var n in t)e.o(t,n)&&!e.o(r,n)&&Object.defineProperty(r,n,{enumerable:!0,get:t[n]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},r={};e.r(r),e.d(r,{__unstableCreatePersistenceLayer:()=>m,create:()=>c});const t=window.wp.apiFetch;var n=e.n(t);const o={},s=window.localStorage;function c({preloadedData:e,localStorageRestoreKey:r="WP_PREFERENCES_RESTORE_DATA",requestDebounceMS:t=2500}={}){let c=e;const i=function(e,r){let t,n;return async function(...o){return n||t?(n&&await n,t&&(clearTimeout(t),t=null),new Promise(((s,c)=>{t=setTimeout((()=>{n=e(...o).then(((...e)=>{s(...e)})).catch((e=>{c(e)})).finally((()=>{n=null,t=null}))}),r)}))):new Promise(((r,t)=>{n=e(...o).then(((...e)=>{r(...e)})).catch((e=>{t(e)})).finally((()=>{n=null}))}))}}(n(),t);return{get:async function(){if(c)return c;const e=await n()({path:"/wp/v2/users/me?context=edit"}),t=e?.meta?.persisted_preferences,i=JSON.parse(s.getItem(r)),a=Date.parse(t?._modified)||0,d=Date.parse(i?._modified)||0;return c=t&&a>=d?t:i||o,c},set:function(e){const t={...e,_modified:(new Date).toISOString()};c=t,s.setItem(r,JSON.stringify(t)),i({path:"/wp/v2/users/me",method:"PUT",keepalive:!0,data:{meta:{persisted_preferences:t}}}).catch((()=>{}))}}}function i(e,r){const t="core/preferences",n="core/interface",o=e?.[n]?.preferences?.features?.[r],s=e?.[r]?.preferences?.features,c=o||s;if(!c)return e;const i=e?.[t]?.preferences;if(i?.[r])return e;let a,d;if(o){const t=e?.[n],o=e?.[n]?.preferences?.features;a={[n]:{...t,preferences:{features:{...o,[r]:void 0}}}}}if(s){const t=e?.[r],n=e?.[r]?.preferences;d={[r]:{...t,preferences:{...n,features:void 0}}}}return{...e,[t]:{preferences:{...i,[r]:c}},...a,...d}}const a=e=>e;function d(e,{from:r,to:t},n,o=a){const s="core/preferences",c=e?.[r]?.preferences?.[n];if(void 0===c)return e;const i=e?.[s]?.preferences?.[t]?.[n];if(i)return e;const d=e?.[s]?.preferences,l=e?.[s]?.preferences?.[t],f=e?.[r],u=e?.[r]?.preferences,p=o({[n]:c});return{...e,[s]:{preferences:{...d,[t]:{...l,...p}}},[r]:{...f,preferences:{...u,[n]:void 0}}}}function l(e){var r;const t=null!==(r=e?.panels)&&void 0!==r?r:{};return Object.keys(t).reduce(((e,r)=>{const n=t[r];return!1===n?.enabled&&e.inactivePanels.push(r),!0===n?.opened&&e.openPanels.push(r),e}),{inactivePanels:[],openPanels:[]})}function f(e){if(e)return e=i(e,"core/edit-widgets"),e=i(e,"core/customize-widgets"),e=i(e,"core/edit-post"),e=d(e=function(e){var r,t,n;const o="core/interface",s="core/preferences",c=e?.[o]?.enableItems;if(!c)return e;const i=null!==(r=e?.[s]?.preferences)&&void 0!==r?r:{},a=null!==(t=c?.singleEnableItems?.complementaryArea)&&void 0!==t?t:{},d=Object.keys(a).reduce(((e,r)=>{const t=a[r];return e?.[r]?.complementaryArea?e:{...e,[r]:{...e[r],complementaryArea:t}}}),i),l=null!==(n=c?.multipleEnableItems?.pinnedItems)&&void 0!==n?n:{},f=Object.keys(l).reduce(((e,r)=>{const t=l[r];return e?.[r]?.pinnedItems?e:{...e,[r]:{...e[r],pinnedItems:t}}}),d),u=e[o];return{...e,[s]:{preferences:f},[o]:{...u,enableItems:void 0}}}(e=function(e){const r="core/interface",t="core/preferences",n=e?.[r]?.preferences?.features,o=n?Object.keys(n):[];return o?.length?o.reduce((function(e,o){if(o.startsWith("core"))return e;const s=n?.[o];if(!s)return e;const c=e?.[t]?.preferences?.[o];if(c)return e;const i=e?.[t]?.preferences,a=e?.[r],d=e?.[r]?.preferences?.features;return{...e,[t]:{preferences:{...i,[o]:s}},[r]:{...a,preferences:{features:{...d,[o]:void 0}}}}}),e):e}(e=i(e,"core/edit-site"))),{from:"core/edit-post",to:"core/edit-post"},"hiddenBlockTypes"),e=d(e,{from:"core/edit-post",to:"core/edit-post"},"editorMode"),e=d(e,{from:"core/edit-post",to:"core/edit-post"},"panels",l),e=d(e,{from:"core/editor",to:"core"},"isPublishSidebarEnabled"),e=d(e,{from:"core/edit-post",to:"core"},"isPublishSidebarEnabled"),e=d(e,{from:"core/edit-site",to:"core/edit-site"},"editorMode"),e?.["core/preferences"]?.preferences}function u(e){const r=function(e){const r=`WP_DATA_USER_${e}`,t=window.localStorage.getItem(r);return JSON.parse(t)}(e);return f(r)}function p(e){let r=(t=e,Object.keys(t).reduce(((e,r)=>{const n=t[r];if(n?.complementaryArea){const t={...n};return delete t.complementaryArea,t.isComplementaryAreaVisible=!0,e[r]=t,e}return e}),t));var t;return r=function(e){var r,t;let n=e;return["allowRightClickOverrides","distractionFree","editorMode","fixedToolbar","focusMode","hiddenBlockTypes","inactivePanels","keepCaretInsideBlock","mostUsedBlocks","openPanels","showBlockBreadcrumbs","showIconLabels","showListViewByDefault","isPublishSidebarEnabled","isComplementaryAreaVisible","pinnedItems"].forEach((r=>{void 0!==e?.["core/edit-post"]?.[r]&&(n={...n,core:{...n?.core,[r]:e["core/edit-post"][r]}},delete n["core/edit-post"][r]),void 0!==e?.["core/edit-site"]?.[r]&&delete n["core/edit-site"][r]})),0===Object.keys(null!==(r=n?.["core/edit-post"])&&void 0!==r?r:{})?.length&&delete n["core/edit-post"],0===Object.keys(null!==(t=n?.["core/edit-site"])&&void 0!==t?t:{})?.length&&delete n["core/edit-site"],n}(r),r}function m(e,r){const t=`WP_PREFERENCES_USER_${r}`,n=JSON.parse(window.localStorage.getItem(t)),o=Date.parse(e&&e._modified)||0,s=Date.parse(n&&n._modified)||0;let i;return i=e&&o>=s?p(e):n?p(n):u(r),c({preloadedData:i,localStorageRestoreKey:t})}(window.wp=window.wp||{}).preferencesPersistence=r})();
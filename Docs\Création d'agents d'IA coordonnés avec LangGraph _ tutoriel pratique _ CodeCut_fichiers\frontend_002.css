p.cr-comment-image-text { margin-bottom: 0.5em; font-size: 0.9em; }
p.cr-comment-video-text { margin-bottom: 0.5em; font-size: 0.9em; }
.iv-comment-image {
	float: left;
	margin-right: 5px;
	margin-left: 0;
	margin-bottom: 5px;
	margin-top: 0px;
	border-radius: 5px;
	overflow: hidden;
	border: 1px solid #e4e1e3;
	filter: brightness(100%);
}
.iv-comment-image:hover,
.cr-comment-video:hover {
	filter: brightness(95%);
}
.cr-comment-image-top {
	display: inline-block;
	margin-right: 5px;
	vertical-align: top;
	max-height: 150px;
	border-radius: 5px;
	overflow: hidden;
	border: 1px solid #e4e1e3;
	filter: brightness(100%);
	box-sizing: content-box;
	position: relative;
}
.cr-comment-image-top:hover {
	filter: brightness(95%);
}
.cr-comment-video {
	float: left;
	margin-right: 5px;
	margin-bottom: 5px;
	position: relative;
	cursor: pointer;
	border-radius: 5px;
	overflow: hidden;
	border: 1px solid #e4e1e3;
	filter: brightness(100%);
}
.cr-comment-video video.cr-video-a {
	width: 75px;
	height: 75px;
	display: block;
	object-fit: cover;
}
.cr-comment-video-modal.cr-comment-video video.cr-video-a {
	width: auto;
	height: auto;
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
	background-color: rgba(0,0,0,0.4);
}
.cr-comment-video .cr-comment-videoicon,
.cr-comment-image-top .cr-comment-videoicon {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 32px;
	height: auto;
	margin-top: -16px;
	margin-left: -16px;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav .cr-ajax-reviews-slide-nav .cr-comment-videoicon,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav .cr-ajax-reviews-slide-nav .cr-comment-videoicon {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 26px;
	height: auto;
	margin-top: -13px;
	margin-left: -13px;
}
.cr-comment-image-top img,
.cr-comment-image-top video {
	width: 150px;
	height: 150px;
	box-shadow: none;
	padding: 0;
	border-radius: 0;
	object-fit: cover;
	cursor: pointer;
}
#reviews div.iv-comment-image img,
div.iv-comment-image img {
	box-shadow: none;
	width: 75px;
	height: 75px;
	max-width: 75px;
	max-height: 75px;
	object-fit:cover;
	padding: 0;
	border-radius: 0;
	display: block;
	position: relative;
}
.cr-comment-images, .cr-comment-videos {
	margin-bottom: 1em;
}
.cr-all-reviews-shortcode .cr-comment-images, .cr-comment-videos {
	margin: 0;
}
.cr-comment-videos .cr-comment-video .cr-comment-video-close {
	display: none;
}
.cr-comment-videos .cr-comment-video.cr-comment-video-modal .cr-comment-video-close {
	display: block;
	background-color: #7A7A7A;
	color: #FFFFFF;
	border: 0px solid;
	border-radius: 10px;
	position: absolute;
	top: 0px;
	right: 0px;
	padding: 1px;
	line-height: 0;
	cursor: pointer;
}
.cr-comment-videos .cr-comment-video.cr-comment-video-modal .cr-comment-video-close .cr-close-button-svg {
	display: block;
	width: 18px;
	height: 18px;
}
.cr-comment-videos .cr-comment-video.cr-comment-video-modal .cr-comment-video-close .cr-close-button-svg-p {
	fill: #ffffff;
}
.cr-comment-videos .cr-comment-video .cr-video-cont {
	display: flex;
	position: relative;
	margin: auto;
}
.cr-comment-videos .cr-comment-video.cr-comment-video-modal .cr-video-cont {
	padding: 10px;
	max-width: 100%;
	max-height: 100%;
}
.cr-comment-videos-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 99999;
	overflow: auto;
	background-color: rgba(0,0,0,0.4);
	display: flex;
	align-items: center;
	justify-content: center;
}
.cr-comment-videos-modal .cr-comment-video,
.cr-comment-videos-modal .iv-comment-image {
	display: none;
}
.cr-comment-video.cr-comment-video-modal {
	width: 80%;
	height: 80%;
	float: none;
	margin: auto;
	z-index: 1;
	display: flex;
	justify-content: center;
	cursor: auto;
	border-width: 0;
	filter: brightness(100%);
}
.cr-recaptcha { margin-bottom: 1.5em; }
#commentform.comment-form .cr-upload-local-images, .comment-form .cr-upload-local-images { width: 100%; flex: none; }
#commentform.comment-form .cr-upload-local-images label, .comment-form .cr-upload-local-images label {
	display: block;
	transform: none;
	position: relative;
	left: auto;
	top: auto;
}
#commentform.comment-form .cr-upload-local-images input, .comment-form .cr-upload-local-images input { display: block; width: 100%; }
.ivole-meter {
	overflow: hidden;
	background: #f2f2f2;
	background: -webkit-linear-gradient(top,#eee,#f6f6f6);
	background: linear-gradient(to bottom,#eee,#f6f6f6);
	background-color: #f3f3f3;
	height: 22px;
	border-radius: 1px;
	box-shadow: inset 0 1px 2px rgba(0,0,0,.4),inset 0 0 0 1px rgba(0,0,0,.1);
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}
.ivole-meter .ivole-meter-bar {
	border-radius: 1px;
	background: #fb0;
	background: -webkit-linear-gradient(top,#ffce00,#ffa700);
	background: linear-gradient(to bottom,#ffce00,#ffa700);
	background-color: #ffce00;
	box-shadow: inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -1px 0 rgba(0,0,0,.05);
	-webkit-transition: width .5s ease;
	transition: width .5s ease;
	float: left;
	font-size: 0;
	height: 100%;
	width: 0;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}
.ivole-meter .ivole-meter-bar:before {
	background-color: rgba(255,255,255,.25);
	content: "";
	display: block;
	font-size: 0;
	height: 1px;
	margin: 1px
}
.cr-histogramTable tr.ivole-histogramRow .ivole-histogramCell1 {
	white-space: nowrap;
	width: auto;
}
.cr-histogramTable tr.ivole-histogramRow .ivole-histogramCell2 {
	width: 70%;
}
.cr-all-reviews-shortcode .cr-histogramTable tr.ivole-histogramRow .ivole-histogramCell2 {
	min-width: 100px;
}
.cr-histogramTable tr.ivole-histogramRow .ivole-histogramCell3 {
	text-align: right;
	white-space: nowrap;
	width: auto;
}
#reviews .cr-summaryBox-wrap,
.cr-all-reviews-shortcode .cr-summaryBox-wrap,
.cr-reviews-grid .cr-summaryBox-wrap {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 2px;
	align-items: center;
}
.cr-all-reviews-shortcode.cr-all-reviews-new-review .cr-summaryBox-wrap {
	display: none;
}
.cr-reviews-grid.cr-reviews-grid-new-review .cr-summaryBox-wrap,
.cr-reviews-grid.cr-reviews-grid-new-review .cr-reviews-grid-empty {
	display: none;
}
#reviews .cr-summaryBox-wrap {
	margin: 0 0 1em 0;
	background-color: #F9F9F9;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap,
.cr-reviews-grid .cr-summaryBox-wrap {
	margin: 0;
	background-color: #F9F9F9;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-summary-separator,
.cr-reviews-grid .cr-summaryBox-wrap .cr-summary-separator {
	width: auto;
	height: 40px;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-summary-separator,
.cr-reviews-grid .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-summary-separator {
	flex: 2 2 auto;
	display: flex;
	justify-content: center;
	align-items: center;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-summary-separator-side,
.cr-reviews-grid .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-summary-separator-side {
	flex: 1 1 auto;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-summary-separator-int,
.cr-reviews-grid .cr-summaryBox-wrap .cr-summary-separator-int {
	width: 2px;
	height: 40px;
	background-color: #BCCCD4;
}
.cr-reviews-grid .cr-summaryBox-wrap {
	margin: 0 10px 20px 10px;
}
#reviews .cr-summaryBox-wrap .ivole-summaryBox,
#reviews .cr-summaryBox-wrap .cr-overall-rating-wrap,
.cr-all-reviews-shortcode .cr-summaryBox-wrap .ivole-summaryBox,
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-overall-rating-wrap,
.cr-reviews-grid .cr-summaryBox-wrap .ivole-summaryBox,
.cr-reviews-grid .cr-summaryBox-wrap .cr-overall-rating-wrap {
	margin: 0px;
	width: 49%;
	flex: 0 0 49%;
	padding: 10px;
	border-radius: 4px;
	border: 0px solid #e4e1e3;
	box-sizing: border-box;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap.cr-summaryBox-add-review .ivole-summaryBox,
.cr-reviews-grid .cr-summaryBox-wrap.cr-summaryBox-add-review .ivole-summaryBox {
	width: 35%;
	flex: 1 0 35%;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-overall-rating-wrap,
.cr-all-reviews-shortcode .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-add-review-wrap,
.cr-reviews-grid .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-overall-rating-wrap,
.cr-reviews-grid .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-add-review-wrap {
	width: auto;
	flex: 0 0 auto;
}
#reviews .cr-summaryBox-wrap .cr-overall-rating-wrap,
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-overall-rating-wrap,
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-add-review-wrap,
.cr-reviews-grid .cr-summaryBox-wrap .cr-overall-rating-wrap,
.cr-reviews-grid .cr-summaryBox-wrap .cr-add-review-wrap {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-wrap: wrap;
	flex-direction: column;
}
#reviews .cr-summaryBox-wrap .cr-average-rating,
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-average-rating,
.cr-reviews-grid .cr-summaryBox-wrap .cr-average-rating {
	font-size: 36px;
	font-weight: bold;
	line-height: 50px;
	color: #0E252C;
}
#reviews .cr-summaryBox-wrap .cr-total-rating-count,
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-total-rating-count,
.cr-reviews-grid .cr-summaryBox-wrap .cr-total-rating-count {
	font-size: 14px;
	color: #0E252C;
	line-height: 25px;
}
.cr-all-reviews-shortcode .cr-review-form-wrap,
.cr-reviews-grid .cr-review-form-wrap,
.cr-qna-block .cr-qna-new-q-form {
	display: none;
}
.cr-all-reviews-shortcode.cr-all-reviews-new-review .cr-review-form-wrap,
.cr-ajax-reviews-review-form .cr-review-form-wrap,
.cr-reviews-grid.cr-reviews-grid-new-review .cr-review-form-wrap,
.cr-qna-block.cr-qna-new-q-form-open .cr-qna-new-q-form {
	display: block;
}
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-nav,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-nav,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-nav,
.cr-qna-block .cr-review-form-nav {
	display: flex;
	justify-content: space-between;
	border-top: 1px solid #E5E9EB;
	border-bottom: 1px solid #E5E9EB;
	height: 50px;
}
.cr-qna-block .cr-qna-list-inl-answ .cr-review-form-nav {
	margin: 0 0 20px 0;
}
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-nav .cr-nav-left,
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-nav .cr-nav-right,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-nav .cr-nav-left,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-nav .cr-nav-right,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-nav .cr-nav-left,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-nav .cr-nav-right,
.cr-qna-block .cr-review-form-nav .cr-nav-left,
.cr-qna-block .cr-review-form-nav .cr-nav-right {
	display: flex;
	align-items: center;
}
.cr-ajax-reviews-review-form.cr-ajax-reviews-review-form-nc .cr-review-form-wrap .cr-review-form-nav .cr-nav-right {
	display: none;
}
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-nav .cr-nav-left svg,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-nav .cr-nav-left svg,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-nav .cr-nav-left svg,
.cr-qna-block .cr-review-form-nav .cr-nav-left svg {
	margin: 0 5px 0 0;
}
.cr-ajax-reviews-review-form.cr-ajax-reviews-review-form-nc .cr-review-form-wrap .cr-review-form-nav .cr-nav-left svg {
	pointer-events: none;
}
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-nav .cr-nav-left svg:hover,
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-nav .cr-nav-right svg:hover,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-nav .cr-nav-left svg:hover,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-nav .cr-nav-right svg:hover,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-nav .cr-nav-left svg:hover,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-nav .cr-nav-right svg:hover,
.cr-qna-block .cr-review-form-nav .cr-nav-left svg:hover,
.cr-qna-block .cr-review-form-nav .cr-nav-right svg:hover {
	cursor: pointer;
}
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-nav .cr-nav-left span,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-nav .cr-nav-left span,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-nav .cr-nav-left span,
.cr-qna-block .cr-review-form-nav .cr-nav-left span {
	font-size: 16px;
	color: #0E252C;
}
.cr-all-reviews-shortcode .cr-review-form-item,
.cr-ajax-reviews-review-form .cr-review-form-item,
.cr-reviews-grid .cr-review-form-item,
.cr-qna-block .cr-review-form-item {
	display: flex;
	max-width: 500px;
	margin: 0 auto;
	padding: 30px 0;
	border-bottom: 1px solid #E5E9EB;
	position: relative;
	align-items: center;
}
.cr-all-reviews-shortcode .cr-review-form-item img,
.cr-ajax-reviews-review-form .cr-review-form-item img,
.cr-reviews-grid .cr-review-form-item img,
.cr-qna-block .cr-review-form-item img {
	display: block;
	height: 60px;
	width: auto;
	margin: 0 20px 0 0;
}
.rtl .cr-all-reviews-shortcode .cr-review-form-item img,
.rtl .cr-ajax-reviews-review-form .cr-review-form-item img,
.rtl .cr-reviews-grid .cr-review-form-item img,
.rtl .cr-qna-block .cr-review-form-item img {
	margin: 0 0 0 20px;
}
.cr-all-reviews-shortcode .cr-review-form-item span,
.cr-ajax-reviews-review-form .cr-review-form-item span,
.cr-reviews-grid .cr-review-form-item span,
.cr-qna-block .cr-review-form-item span {
	display: block;
	font-size: 22px;
	font-weight: 500;
	color: #0E252C;
}
.cr-all-reviews-shortcode .cr-review-form-rating,
.cr-ajax-reviews-review-form .cr-review-form-rating,
.cr-reviews-grid .cr-review-form-rating {
	max-width: 500px;
	margin: 0 auto;
	padding: 30px 0 30px 0;
	position: relative;
}
.cr-onsite-ratings .cr-review-form-rating,
.cr-review-form-rating-overall .cr-review-form-rating {
	max-width: 500px;
	margin: 1em 0 1em 0;
	padding: 0;
	position: relative;
}
.cr-review-form-rating-overall.cr-review-form-rating-ovonly .cr-review-form-rating {
	margin: 1em 0 1.5em 0;
}
.cr-review-form-wrap .cr-onsite-ratings .cr-review-form-rating,
.cr-review-form-wrap .cr-review-form-rating-overall .cr-review-form-rating {
	margin-left: auto;
	margin-right: auto;
}
.cr-review-form-wrap .cr-onsite-ratings .cr-review-form-rating-label {
	font-size: 12px;
	color: #4D5D64;
}
.cr-all-reviews-shortcode .cr-review-form-rating .cr-review-form-rating-cont,
.cr-onsite-ratings .cr-review-form-rating .cr-review-form-rating-cont,
.cr-review-form-rating-overall .cr-review-form-rating .cr-review-form-rating-cont,
.cr-reviews-grid .cr-review-form-rating .cr-review-form-rating-cont {
	display: flex;
	position: relative;
	height: auto;
	padding: 5px 0;
}
.cr-all-reviews-shortcode .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner,
.cr-onsite-ratings .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner,
.cr-reviews-grid .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner {
	height: 20px;
	padding: 0 5px;
	line-height: 0;
}
.cr-review-form-rating-overall .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner {
	height: 30px;
	padding: 0 5px;
}
.cr-review-form-rating-overall .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner svg {
	height: 30px;
	width: 33px;
}
.cr-onsite-ratings .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner:first-child,
.cr-review-form-rating-overall .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner:first-child {
	padding-left: 0;
}
.cr-all-reviews-shortcode .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner .cr-rating-act,
.cr-onsite-ratings .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner .cr-rating-act,
.cr-review-form-rating-overall .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner .cr-rating-act,
.cr-ajax-reviews-review-form .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner .cr-rating-act,
.cr-reviews-grid .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner .cr-rating-act {
	display: none;
}
.cr-all-reviews-shortcode .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner svg:hover,
.cr-ajax-reviews-review-form .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner svg:hover,
.cr-reviews-grid .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-inner svg:hover {
	cursor: pointer;
}
.cr-all-reviews-shortcode .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-nbr,
.cr-onsite-ratings .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-nbr,
.cr-reviews-grid .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-nbr {
	font-size: 14px;
	line-height: 20px;
	height: 20px;
	margin: 0 0 0 10px;
}
.cr-review-form-rating-overall .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-nbr {
	font-size: 18px;
	line-height: 30px;
	height: 30px;
	margin: 0 0 0 10px;
}
.rtl .cr-review-form-rating-overall .cr-review-form-rating .cr-review-form-rating-cont .cr-review-form-rating-nbr {
	margin: 0 10px 0 0;
}
.cr-review-form-rating-overall .cr-review-form-rating-label {
	font-size: 18px;
}
.cr-all-reviews-shortcode .cr-review-form-rating-overall .cr-review-form-rating-label,
.cr-ajax-reviews-review-form .cr-review-form-rating-overall .cr-review-form-rating-label,
.cr-reviews-grid .cr-review-form-rating-overall .cr-review-form-rating-label {
	font-size: 16px;
}
.cr-all-reviews-shortcode .cr-review-form-field-error,
.cr-onsite-ratings .cr-review-form-field-error,
.cr-review-form-rating-overall .cr-review-form-field-error,
.cr-onsite-questions .cr-onsite-question .cr-review-form-field-error,
.cr-ajax-reviews-review-form .cr-review-form-field-error,
.cr-reviews-grid .cr-review-form-field-error,
.cr-qna-block .cr-review-form-field-error {
	font-size: 11px;
	color: #CE2B37;
	margin: 5px 0 0 0;
	display: none;
}
.cr-all-reviews-shortcode .cr-review-form-rating.cr-review-form-error .cr-review-form-rating-cont,
.cr-onsite-ratings .cr-review-form-rating.cr-review-form-error .cr-review-form-rating-cont,
.cr-review-form-rating-overall .cr-review-form-rating.cr-review-form-error .cr-review-form-rating-cont,
.cr-reviews-grid .cr-review-form-rating.cr-review-form-error .cr-review-form-rating-cont {
	background-color: #fae9eb;
}
.cr-all-reviews-shortcode .cr-review-form-rating.cr-review-form-error .cr-review-form-field-error,
.cr-ajax-reviews-review-form .cr-review-form-rating.cr-review-form-error .cr-review-form-field-error,
.cr-onsite-ratings .cr-review-form-rating.cr-review-form-error .cr-review-form-field-error,
.cr-review-form-rating-overall .cr-review-form-rating.cr-review-form-error .cr-review-form-field-error,
.cr-onsite-questions .cr-onsite-question.cr-review-form-error .cr-review-form-field-error,
.cr-all-reviews-shortcode .cr-review-form-comment.cr-review-form-error .cr-review-form-field-error,
.cr-all-reviews-shortcode .cr-review-form-name.cr-review-form-error .cr-review-form-field-error,
.cr-all-reviews-shortcode .cr-review-form-captcha.cr-review-form-error .cr-review-form-field-error,
.cr-all-reviews-shortcode .cr-review-form-email.cr-review-form-error .cr-review-form-field-error,
.cr-all-reviews-shortcode .cr-form-item-media.cr-review-form-error .cr-review-form-field-error,
.cr-all-reviews-shortcode .cr-review-form-terms.cr-review-form-error .cr-review-form-field-error,
.cr-ajax-reviews-review-form .cr-review-form-comment.cr-review-form-error .cr-review-form-field-error,
.cr-ajax-reviews-review-form .cr-review-form-name.cr-review-form-error .cr-review-form-field-error,
.cr-ajax-reviews-review-form .cr-review-form-email.cr-review-form-error .cr-review-form-field-error,
.cr-ajax-reviews-review-form .cr-review-form-captcha.cr-review-form-error .cr-review-form-field-error,
.cr-ajax-reviews-review-form .cr-form-item-media.cr-review-form-error .cr-review-form-field-error,
.cr-ajax-reviews-review-form .cr-review-form-terms.cr-review-form-error .cr-review-form-field-error,
.cr-reviews-grid .cr-review-form-comment.cr-review-form-error .cr-review-form-field-error,
.cr-reviews-grid .cr-review-form-name.cr-review-form-error .cr-review-form-field-error,
.cr-reviews-grid .cr-review-form-email.cr-review-form-error .cr-review-form-field-error,
.cr-reviews-grid .cr-review-form-captcha.cr-review-form-error .cr-review-form-field-error,
.cr-reviews-grid .cr-form-item-media.cr-review-form-error .cr-review-form-field-error,
.cr-reviews-grid .cr-review-form-terms.cr-review-form-error .cr-review-form-field-error,
.cr-qna-block .cr-review-form-comment.cr-review-form-error .cr-review-form-field-error,
.cr-qna-block .cr-review-form-name.cr-review-form-error .cr-review-form-field-error,
.cr-qna-block .cr-review-form-email.cr-review-form-error .cr-review-form-field-error,
.cr-qna-block .cr-review-form-terms.cr-review-form-error .cr-review-form-field-error {
	display: block;
}
.cr-all-reviews-shortcode .cr-review-form-comment,
.cr-ajax-reviews-review-form .cr-review-form-comment,
.cr-reviews-grid .cr-review-form-comment,
.cr-qna-block .cr-review-form-comment {
	max-width: 500px;
	margin: 0 auto;
	padding: 0 0 20px 0;
	position: relative;
}
.cr-all-reviews-shortcode .cr-review-form-comment .cr-review-form-comment-txt,
.cr-ajax-reviews-review-form .cr-review-form-comment .cr-review-form-comment-txt,
.cr-reviews-grid .cr-review-form-comment .cr-review-form-comment-txt,
.cr-qna-block .cr-review-form-comment .cr-review-form-comment-txt {
	border: solid 1px #D8E2E7;
	background-color: #FFFFFF;
	box-shadow: none;
	position: relative;
	padding: 12px 16px;
	color: #0E252C;
	font-size: 14px;
	width: 100%;
	margin: 0;
}
.cr-all-reviews-shortcode .cr-review-form-comment.cr-review-form-error .cr-review-form-comment-txt,
.cr-ajax-reviews-review-form .cr-review-form-comment.cr-review-form-error .cr-review-form-comment-txt,
.cr-reviews-grid .cr-review-form-comment.cr-review-form-error .cr-review-form-comment-txt,
.cr-qna-block .cr-review-form-comment.cr-review-form-error .cr-review-form-comment-txt {
	border: #CE2B37 solid 2px;
}
.cr-all-reviews-shortcode .cr-review-form-comment .cr-review-form-lbl,
.cr-all-reviews-shortcode .cr-review-form-name .cr-review-form-lbl,
.cr-all-reviews-shortcode .cr-review-form-email .cr-review-form-lbl,
.cr-ajax-reviews-review-form .cr-review-form-comment .cr-review-form-lbl,
.cr-ajax-reviews-review-form .cr-review-form-name .cr-review-form-lbl,
.cr-ajax-reviews-review-form .cr-review-form-email .cr-review-form-lbl,
.cr-reviews-grid .cr-review-form-comment .cr-review-form-lbl,
.cr-reviews-grid .cr-review-form-name .cr-review-form-lbl,
.cr-reviews-grid .cr-review-form-email .cr-review-form-lbl,
.cr-qna-block .cr-review-form-lbl {
	position: absolute;
	top: -7px;
	z-index: 1;
	left: 16px;
	background-color: #FFFFFF;
	padding: 0 5px;
	font-size: 12px;
	line-height: 12px;
	height: 12px;
	color: #4D5D64;
}
.rtl .cr-all-reviews-shortcode .cr-review-form-comment .cr-review-form-lbl,
.rtl .cr-all-reviews-shortcode .cr-review-form-name .cr-review-form-lbl,
.rtl .cr-all-reviews-shortcode .cr-review-form-email .cr-review-form-lbl,
.rtl .cr-ajax-reviews-review-form .cr-review-form-comment .cr-review-form-lbl,
.rtl .cr-ajax-reviews-review-form .cr-review-form-name .cr-review-form-lbl,
.rtl .cr-ajax-reviews-review-form .cr-review-form-email .cr-review-form-lbl,
.rtl .cr-reviews-grid .cr-review-form-comment .cr-review-form-lbl,
.rtl .cr-reviews-grid .cr-review-form-name .cr-review-form-lbl,
.rtl .cr-reviews-grid .cr-review-form-email .cr-review-form-lbl,
.rtl .cr-qna-block .cr-review-form-lbl {
	left: auto;
	right: 16px;
}
.cr-all-reviews-shortcode .cr-form-item-media,
.cr-ajax-reviews-review-form .cr-form-item-media,
.cr-reviews-grid .cr-form-item-media {
	max-width: 500px;
	margin: 0 auto;
	padding: 0 0 20px 0;
}
.cr-all-reviews-shortcode .cr-review-form-terms,
.cr-ajax-reviews-review-form .cr-review-form-terms,
.cr-reviews-grid .cr-review-form-terms,
.cr-qna-block .cr-captcha-terms,
.cr-qna-block .cr-review-form-terms {
	max-width: 500px;
	margin: 0 auto;
	padding: 0 0 20px 0;
	line-height: 1;
	display: block;
}
.cr-all-reviews-shortcode .cr-review-form-terms label,
.cr-ajax-reviews-review-form .cr-review-form-terms label,
.cr-reviews-grid .cr-review-form-terms label,
.cr-qna-block .cr-review-form-terms label {
	display: inline-block;
	font-size: 14px;
	font-weight: normal;
	margin: 0;
}
.cr-all-reviews-shortcode .cr-review-form-terms .cr-review-form-checkbox,
.cr-ajax-reviews-review-form .cr-review-form-terms .cr-review-form-checkbox,
.cr-reviews-grid .cr-review-form-terms .cr-review-form-checkbox,
.cr-qna-block .cr-review-form-terms .cr-review-form-checkbox {
	appearance: auto;
	width: auto;
	height: auto;
	border: none;
	display: inline-block;
	vertical-align: middle;
	margin: 0 5px 0 0;
	accent-color: #4D5D64;
}
.rtl .cr-all-reviews-shortcode .cr-review-form-terms .cr-review-form-checkbox,
.rtl .cr-ajax-reviews-review-form .cr-review-form-terms .cr-review-form-checkbox,
.rtl .cr-reviews-grid .cr-review-form-terms .cr-review-form-checkbox,
.rtl .cr-qna-block .cr-review-form-terms .cr-review-form-checkbox {
	margin: 0 0 0 5px;
}
.cr-all-reviews-shortcode .cr-review-form-terms span,
.cr-ajax-reviews-review-form .cr-review-form-terms span,
.cr-reviews-grid .cr-review-form-terms span,
.cr-qna-block .cr-review-form-terms span {
	display: inline;
	vertical-align: middle;
	color: #0E252C;
	line-height: 1.3;
}
.cr-all-reviews-shortcode .cr-review-form-terms .cr-review-form-checkbox::after,
.cr-ajax-reviews-review-form .cr-review-form-terms .cr-review-form-checkbox::after,
.cr-reviews-grid .cr-review-form-terms .cr-review-form-checkbox::after,
.cr-qna-block .cr-review-form-terms .cr-review-form-checkbox::after {
	border: none;
	content: normal;
}
.cr-qna-block .cr-captcha-terms {
	font-size: 12px;
	font-weight: normal;
	vertical-align: middle;
	color: #0E252C;
}
.cr-all-reviews-shortcode .cr-review-form-ne,
.cr-ajax-reviews-review-form .cr-review-form-ne,
.cr-reviews-grid .cr-review-form-ne,
.cr-qna-block .cr-review-form-ne {
	max-width: 500px;
	margin: 0 auto;
	padding: 0 0 20px 0;
	position: relative;
	display: flex;
	gap: 10px;
}
.cr-all-reviews-shortcode .cr-review-form-ne .cr-review-form-name,
.cr-all-reviews-shortcode .cr-review-form-ne .cr-review-form-email,
.cr-ajax-reviews-review-form .cr-review-form-ne .cr-review-form-name,
.cr-ajax-reviews-review-form .cr-review-form-ne .cr-review-form-email,
.cr-reviews-grid .cr-review-form-ne .cr-review-form-name,
.cr-reviews-grid .cr-review-form-ne .cr-review-form-email,
.cr-qna-block .cr-review-form-ne .cr-review-form-name,
.cr-qna-block .cr-review-form-ne .cr-review-form-email {
	width: 50%;
	position: relative;
}
.cr-all-reviews-shortcode .cr-review-form-ne .cr-review-form-name input,
.cr-all-reviews-shortcode .cr-review-form-ne .cr-review-form-email input,
.cr-ajax-reviews-review-form .cr-review-form-ne .cr-review-form-name input,
.cr-ajax-reviews-review-form .cr-review-form-ne .cr-review-form-email input,
.cr-reviews-grid .cr-review-form-ne .cr-review-form-name input,
.cr-reviews-grid .cr-review-form-ne .cr-review-form-email input,
.cr-qna-block .cr-review-form-ne .cr-review-form-name input,
.cr-qna-block .cr-review-form-ne .cr-review-form-email input {
	width: 100%;
}
.cr-all-reviews-shortcode .cr-review-form-ne .cr-review-form-txt,
.cr-ajax-reviews-review-form .cr-review-form-ne .cr-review-form-txt,
.cr-reviews-grid .cr-review-form-ne .cr-review-form-txt,
.cr-qna-block .cr-review-form-ne .cr-review-form-txt {
	border: solid 1px #D8E2E7;
	background-color: #FFFFFF;
	box-shadow: none;
	position: relative;
	padding: 12px 16px;
	color: #0E252C;
	font-size: 14px;
	margin: 0;
}
.cr-review-form-wrap .cr-onsite-question .cr-onsite-question-inp {
	border: solid 1px #D8E2E7;
	background-color: #FFFFFF;
	box-shadow: none;
	padding: 12px 16px;
	color: #0E252C;
	font-size: 14px;
	width: 50%;
}
.cr-review-form-wrap .cr-onsite-question label {
	position: absolute;
	top: -7px;
	z-index: 1;
	left: 16px;
	background-color: #FFFFFF;
	padding: 0 5px;
	font-size: 12px;
	line-height: 12px;
	height: 12px;
	color: #4D5D64;
	font-weight: normal;
}
.rtl .cr-review-form-wrap .cr-onsite-question label {
	left: auto;
	right: 16px;
}
.cr-review-form-wrap .cr-onsite-questions {
	max-width: 500px;
	margin: 0 auto;
}
.cr-all-reviews-shortcode .cr-review-form-name.cr-review-form-error .cr-review-form-txt,
.cr-all-reviews-shortcode .cr-review-form-email.cr-review-form-error .cr-review-form-txt,
.cr-ajax-reviews-review-form .cr-review-form-name.cr-review-form-error .cr-review-form-txt,
.cr-ajax-reviews-review-form .cr-review-form-email.cr-review-form-error .cr-review-form-txt,
.cr-onsite-questions .cr-onsite-question.cr-review-form-error input[type='text'],
.cr-onsite-questions .cr-onsite-question.cr-review-form-error input[type='number'],
.cr-single-product-review .cr-review-form-textbox.cr-review-form-error,
.cr-reviews-grid .cr-review-form-name.cr-review-form-error .cr-review-form-txt,
.cr-reviews-grid .cr-review-form-email.cr-review-form-error .cr-review-form-txt,
.cr-qna-block .cr-review-form-name.cr-review-form-error .cr-review-form-txt,
.cr-qna-block .cr-review-form-email.cr-review-form-error .cr-review-form-txt {
	border: #CE2B37 solid 2px;
}
.cr-all-reviews-shortcode .cr-review-form-captcha,
.cr-ajax-reviews-review-form .cr-review-form-captcha,
.cr-reviews-grid .cr-review-form-captcha {
	max-width: 500px;
	margin: 0 auto;
	padding: 0 0 20px 0;
	display: flex;
	flex-direction: column;
	align-items: start;
}
.cr-all-reviews-shortcode .cr-review-form-captcha .cr-recaptcha,
.cr-ajax-reviews-review-form .cr-review-form-captcha .cr-recaptcha,
.cr-reviews-grid .cr-review-form-captcha .cr-recaptcha {
	margin: 0;
}
.cr-all-reviews-shortcode .cr-review-form-buttons,
.cr-ajax-reviews-review-form .cr-review-form-buttons,
.cr-reviews-grid .cr-review-form-buttons,
.cr-qna-block .cr-review-form-buttons {
	max-width: 500px;
	margin: 0 auto;
	padding: 0 0 30px 0;
	position: relative;
	display: flex;
	gap: 10px;
}
.cr-qna-block .cr-qna-list-inl-answ .cr-review-form-buttons {
	padding: 0;
}
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-buttons .cr-review-form-submit,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-buttons .cr-review-form-submit,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-buttons .cr-review-form-submit,
.cr-qna-block .cr-review-form-buttons .cr-review-form-submit {
	display: flex;
	background-color: #31856C;
	color: #ffffff;
	border: 0;
	height: 40px;
	line-height: 20px;
	font-size: 15px;
	width: 50%;
	padding: 0 10px;
	margin: 0;
	align-items: center;
	justify-content: center;
}
.cr-ajax-reviews-review-form.cr-ajax-reviews-review-form-nc .cr-review-form-buttons .cr-review-form-submit {
	width: 100%;
}
.cr-all-reviews-shortcode .cr-review-form-buttons .cr-review-form-submit:hover,
.cr-ajax-reviews-review-form .cr-review-form-buttons .cr-review-form-submit:hover,
.cr-reviews-grid .cr-review-form-buttons .cr-review-form-submit:hover,
.cr-qna-block .cr-review-form-buttons .cr-review-form-submit:hover {
	background-color: #276A56;
	text-decoration: none;
	cursor: pointer;
}
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-cancel,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-rating .cr-review-form-rating-inner,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-rating .cr-review-form-rating-inner,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-comment .cr-review-form-comment-txt,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-ne .cr-review-form-txt,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-captcha,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-onsite-question,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-onsite-question-inp,
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-terms,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-cancel,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-rating .cr-review-form-rating-inner,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-rating .cr-review-form-rating-inner,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-comment .cr-review-form-comment-txt,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-ne .cr-review-form-txt,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-captcha,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-onsite-question,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-onsite-question-inp,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-terms,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-cancel,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-rating .cr-review-form-rating-inner,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-rating .cr-review-form-rating-inner,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-comment .cr-review-form-comment-txt,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-ne .cr-review-form-txt,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-captcha,
.cr-reviews-grid .cr-review-form-submitting .cr-onsite-question,
.cr-reviews-grid .cr-review-form-submitting .cr-onsite-question-inp,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-terms,
.cr-qna-block .cr-review-form-submitting.cr-qna-new-q-form,
.cr-qna-block .cr-review-form-submitting.cr-qna-list-inl-answ {
	pointer-events: none;
}
.cr-all-reviews-shortcode .cr-review-form-buttons .cr-review-form-submit span,
.cr-ajax-reviews-review-form .cr-review-form-buttons .cr-review-form-submit span,
.cr-reviews-grid .cr-review-form-buttons .cr-review-form-submit span {
	display: block;
}
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit span,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit span,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit span,
.cr-qna-block .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit span {
	display: none;
}
.cr-all-reviews-shortcode .cr-review-form-buttons .cr-review-form-submit img,
.cr-ajax-reviews-review-form .cr-review-form-buttons .cr-review-form-submit img,
.cr-reviews-grid .cr-review-form-buttons .cr-review-form-submit img,
.cr-qna-block .cr-review-form-buttons .cr-review-form-submit img {
	display: none;
	height: 10px;
}
.cr-all-reviews-shortcode .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit img,
.cr-ajax-reviews-review-form .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit img,
.cr-reviews-grid .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit img,
.cr-qna-block .cr-review-form-submitting .cr-review-form-buttons .cr-review-form-submit img {
	display: block;
}
.cr-all-reviews-shortcode .cr-review-form-wrap .cr-review-form-buttons .cr-review-form-cancel,
.cr-ajax-reviews-review-form .cr-review-form-wrap .cr-review-form-buttons .cr-review-form-cancel,
.cr-reviews-grid .cr-review-form-wrap .cr-review-form-buttons .cr-review-form-cancel,
.cr-qna-block .cr-review-form-buttons .cr-review-form-cancel {
	display: flex;
	background-color: #ffffff;
	color: #0E252C;
	border: 0;
	height: 40px;
	line-height: 20px;
	font-size: 15px;
	width: 50%;
	border: solid 1px #D8E2E7;
	padding: 0 10px;
	margin: 0;
	align-items: center;
	justify-content: center;
}
.cr-ajax-reviews-review-form.cr-ajax-reviews-review-form-nc .cr-review-form-buttons .cr-review-form-cancel {
	display: none;
}
.cr-all-reviews-shortcode .cr-review-form-buttons .cr-review-form-cancel:hover,
.cr-ajax-reviews-review-form .cr-review-form-buttons .cr-review-form-cancel:hover,
.cr-reviews-grid .cr-review-form-buttons .cr-review-form-cancel:hover,
.cr-qna-block .cr-review-form-buttons .cr-review-form-cancel:hover {
	border: solid 1px #4D5D64;
	text-decoration: none;
	cursor: pointer;
}
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-rating,
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-comment,
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-ne,
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-captcha,
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-buttons,
.cr-all-reviews-shortcode .cr-review-form-res .cr-onsite-ratings,
.cr-all-reviews-shortcode .cr-review-form-res .cr-onsite-question,
.cr-all-reviews-shortcode .cr-review-form-res .cr-form-item-media,
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-terms,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-rating,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-comment,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-ne,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-captcha,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-buttons,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-onsite-ratings,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-onsite-question,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-form-item-media,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-terms,
.cr-reviews-grid .cr-review-form-res .cr-review-form-rating,
.cr-reviews-grid .cr-review-form-res .cr-review-form-comment,
.cr-reviews-grid .cr-review-form-res .cr-review-form-ne,
.cr-reviews-grid .cr-review-form-res .cr-review-form-captcha,
.cr-reviews-grid .cr-review-form-res .cr-review-form-buttons,
.cr-reviews-grid .cr-review-form-res .cr-onsite-ratings,
.cr-reviews-grid .cr-review-form-res .cr-onsite-question,
.cr-reviews-grid .cr-review-form-res .cr-form-item-media,
.cr-reviews-grid .cr-review-form-res .cr-review-form-terms,
.cr-qna-block .cr-review-form-res .cr-review-form-comment,
.cr-qna-block .cr-review-form-res .cr-review-form-ne,
.cr-qna-block .cr-review-form-res .cr-review-form-buttons,
.cr-qna-block .cr-review-form-res .cr-captcha-terms,
.cr-qna-block .cr-review-form-res .cr-review-form-terms {
	display: none;
}
.cr-all-reviews-shortcode .cr-review-form-result,
.cr-ajax-reviews-review-form .cr-review-form-result,
.cr-all-reviews-shortcode .cr-review-form-not-logged-in,
.cr-ajax-reviews-review-form .cr-review-form-not-logged-in,
.cr-reviews-grid .cr-review-form-result,
.cr-reviews-grid .cr-review-form-not-logged-in,
.cr-qna-block .cr-review-form-not-logged-in,
.cr-qna-block .cr-review-form-result {
	max-width: 500px;
	margin: 0 auto;
	display: none;
	position: relative;
	padding: 40px 0;
}
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-result,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-result,
.cr-all-reviews-shortcode .cr-review-form-not-logged-in,
.cr-ajax-reviews-review-form .cr-review-form-not-logged-in,
.cr-reviews-grid .cr-review-form-res .cr-review-form-result,
.cr-reviews-grid .cr-review-form-not-logged-in,
.cr-qna-block .cr-review-form-res .cr-review-form-result,
.cr-qna-block .cr-review-form-not-logged-in {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-result span,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-result span,
.cr-all-reviews-shortcode .cr-review-form-not-logged-in span,
.cr-ajax-reviews-review-form .cr-review-form-not-logged-in span,
.cr-reviews-grid .cr-review-form-res .cr-review-form-result span,
.cr-reviews-grid .cr-review-form-not-logged-in span,
.cr-qna-block .cr-review-form-res .cr-review-form-result span,
.cr-qna-block .cr-review-form-not-logged-in span {
	padding: 0 0 20px 0;
}
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-result .cr-review-form-continue,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-result .cr-review-form-continue,
.cr-all-reviews-shortcode .cr-review-form-not-logged-in .cr-review-form-continue,
.cr-ajax-reviews-review-form .cr-review-form-not-logged-in .cr-review-form-continue,
.cr-reviews-grid .cr-review-form-res .cr-review-form-result .cr-review-form-continue,
.cr-reviews-grid .cr-review-form-not-logged-in .cr-review-form-continue,
.cr-qna-block .cr-review-form-res .cr-review-form-result .cr-review-form-continue,
.cr-qna-block .cr-review-form-not-logged-in .cr-review-form-continue {
	display: flex;
	background-color: #31856C;
	color: #ffffff;
	border: 0;
	height: 40px;
	line-height: 20px;
	font-size: 15px;
	width: auto;
	text-decoration: none;
	align-items: center;
	justify-content: center;
}
.cr-all-reviews-shortcode .cr-review-form-not-logged-in .cr-review-form-continue,
.cr-ajax-reviews-review-form .cr-review-form-not-logged-in .cr-review-form-continue,
.cr-reviews-grid .cr-review-form-not-logged-in .cr-review-form-continue,
.cr-qna-block .cr-review-form-not-logged-in .cr-review-form-continue {
	padding: 10px 20px;
	min-width: 100px;
	text-align: center;
}
.cr-all-reviews-shortcode .cr-review-form-res .cr-review-form-result .cr-review-form-continue:hover,
.cr-ajax-reviews-review-form .cr-review-form-res .cr-review-form-result .cr-review-form-continue:hover,
.cr-all-reviews-shortcode .cr-review-form-not-logged-in .cr-review-form-continue:hover,
.cr-ajax-reviews-review-form .cr-review-form-not-logged-in .cr-review-form-continue:hover,
.cr-reviews-grid .cr-review-form-res .cr-review-form-result .cr-review-form-continue:hover,
.cr-reviews-grid .cr-review-form-not-logged-in .cr-review-form-continue:hover,
.cr-qna-block .cr-review-form-res .cr-review-form-result .cr-review-form-continue:hover,
.cr-qna-block .cr-review-form-not-logged-in .cr-review-form-continue:hover {
	background-color: #276A56;
	text-decoration: none;
	cursor: pointer;
}
.ivole-summaryBox {
	max-width: 100%;
	font-size: 14px;
}
div.ivole-summaryBox {
	margin-bottom: 2rem;
}
.cr-summaryBox-wrap .cr-histogramTable {
	margin-bottom: 0px;
	border-collapse: collapse;
	border: 0px;
	width: 100%;
	max-width: 320px;
	min-width: auto;
	margin: 0 auto;
}
.cr-histogramTable .ivole-histogramRow {
	border: 0px;
}
.cr-histogramTable tr.ivole-histogramRow td {
	padding: 0.2rem 0.3rem;
	vertical-align: middle;
	border: 0px;
	background-color: transparent;
}
.cr-histogramTable tr.ivole-histogramRow:first-child td {
	padding-top: 0
}
.cr-histogramTable tr.ivole-histogramRow:last-child td {
	padding-bottom: 0
}
.cr-histogramTable tr.ivole-histogramRow td:first-child {
	padding-left: 0
}
.cr-histogramTable tr.ivole-histogramRow td:last-child {
	padding-right: 0
}
#reviews .cr-credits-div,
.cr-all-reviews-shortcode .cr-credits-div,
.cr-reviews-grid .cr-credits-div {
	font-size: 10px;
	text-align: right;
	width: 100%;
	padding: 0 5px;
	margin: 7px 0;
	line-height: 16px;
	display: flex;
	align-items: center;
	justify-content: right;
	box-sizing: border-box;
}
.cr-reviews-grid .cr-credits-div {
	margin-top: 0;
	padding: 0 10px;
}
.cr-all-reviews-shortcode .cr-credits-div a,
#reviews .cr-credits-div a,
.cr-reviews-grid .cr-credits-div a {
	text-decoration: none;
}
.cr-all-reviews-shortcode .cr-credits-div a img,
#reviews .cr-credits-div a img,
.cr-reviews-grid .cr-credits-div a img {
	display: inline;
	height: 16px;
	margin: 0 0 0 4px;
	padding: 0;
	vertical-align: middle;
}
#reviews .cr-summaryBox-wrap .cr-count-filtered-reviews,
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-count-filtered-reviews,
.cr-reviews-grid .cr-summaryBox-wrap .cr-count-filtered-reviews {
	width: 100%;
	padding: 5px 10px;
	margin-top: 0;
	font-size: 14px;
	color: #0E252C;
}
.cr-histogramTable tr.ivole-histogramRow .ivole-histogram-a,
.cr-histogramTable tr.ivole-histogramRow .cr-histogram-a {
	display: inline;
	cursor: pointer;
	color: #0E252C;
}
.cr-histogramTable tr.ivole-histogramRow.ivole-histogramRow-s .ivole-histogram-a,
.cr-histogramTable tr.ivole-histogramRow.ivole-histogramRow-s .cr-histogram-a {
	display: inline;
	font-weight: bold;
}
#reviews.cr-reviews-ajax-reviews {
	width: 100%;
}
.cr-all-reviews-shortcode .cr-count-row,
#reviews.cr-reviews-ajax-reviews .cr-count-row {
	background-color: #F9F9F9;
	padding: 10px 12px;
	font-size: 12px;
	color: #0E252C;
	margin: 24px 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.cr-all-reviews-shortcode.cr-all-reviews-new-review .cr-count-row {
	display: none;
}
.cr-all-reviews-shortcode .cr-count-row a,
.cr-reviews-ajax-comments .cr-count-row a {
	color: #0E252C;
	text-decoration: underline;
}
.cr-all-reviews-shortcode .cr-count-row a:hover,
.cr-reviews-ajax-comments .cr-count-row a:hover {
	cursor: pointer;
	color: #0E252C;
}
div.ivole-summaryBox.cr-summaryBox-ajax.cr-summaryBar-updating,
div.ivole-summaryBox.cr-all-reviews-ajax.cr-summaryBar-updating,
div.ivole-summaryBox.cr-summaryBar-updating,
.cr-count-row .cr-seeAll-updating,
.cr-ajax-reviews-sort.cr-sort-updating,
.cr-review-tags-filter.cr-tags-updating {
	opacity: 0.5;
	pointer-events: none;
}
.cr-voting-cont {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}
.cr-voting-cont img, .cr-voting-cont-uni svg {
	margin-left: 0px;
	margin-right: 7px;
	margin-top: 0px;
	margin-bottom: 0px;
	width: 13px;
	height: 16px;
	cursor: pointer;
	pointer-events: auto;
}
.rtl .cr-voting-cont img, .rtl .cr-voting-cont-uni svg {
	margin-right: 0px;
	margin-left: 7px;
}
.cr-voting-cont-uni .cr-voting-update svg {
	pointer-events: none;
}
.cr-voting-cont-uni span.cr-voting-upvote svg:hover {
	color: #000000;
}
.cr-voting-cont-uni span {
	display: block;
	line-height: 11px;
	font-size: 11px;
	margin-left: 0px;
	margin-right: 0px;
	margin-top: 0px;
	margin-bottom: 0px;
}
.cr-voting-cont-uni span.cr-voting-upvote svg .cr-voting-svg-int {
	fill: none;
}
.cr-voting-cont-uni span.cr-voting-upvote svg .cr-voting-svg-ext {
	fill: #18B394;
}
.cr-voting-cont-uni span.cr-voting-upvote.cr-voting-active svg .cr-voting-svg-int {
	fill: #00A382;
	fill-opacity: 0.4;
}
.cr-voting-cont-uni span.cr-voting-upvote.cr-voting-active svg .cr-voting-svg-ext {
	fill: #00A382;
}
.cr-voting-cont-uni span.cr-voting-upvote svg:hover .cr-voting-svg-ext,
.cr-voting-cont-uni span.cr-voting-downvote svg:hover .cr-voting-svg-ext,
.cr-voting-cont-uni span.cr-voting-upvote.cr-voting-active svg:hover .cr-voting-svg-ext,
.cr-voting-cont-uni span.cr-voting-downvote.cr-voting-active svg:hover .cr-voting-svg-ext,
.cr-voting-cont-uni span.cr-voting-upvote.cr-voting-update svg .cr-voting-svg-ext,
.cr-voting-cont-uni span.cr-voting-downvote.cr-voting-update svg .cr-voting-svg-ext {
	fill: #202020;
}
.cr-voting-cont-uni span.cr-voting-upvote.cr-voting-update svg .cr-voting-svg-int,
.cr-voting-cont-uni span.cr-voting-downvote.cr-voting-update svg .cr-voting-svg-int {
	fill: none;
	animation: pulse 1s infinite;
}
@keyframes pulse {
	0% {
		fill: #FFFFFF;
	}
	50% {
		fill: #A9A9A9;
	}
	100% {
		fill: #FFFFFF;
	}
}
.cr-voting-cont-uni span.cr-voting-downvote svg .cr-voting-svg-int {
	fill: none;
}
.cr-voting-cont-uni span.cr-voting-downvote svg .cr-voting-svg-ext {
	fill: #CE2B37;
}
.cr-voting-cont-uni span.cr-voting-downvote.cr-voting-active svg .cr-voting-svg-int {
	fill: #CA2430;
	fill-opacity: 0.4;
}
.cr-voting-cont-uni span.cr-voting-downvote.cr-voting-active svg .cr-voting-svg-ext {
	fill: #CA2430;
}
.cr-voting-cont-uni span.cr-voting-upvote-count {
	margin-right: 10px;
	color: #18B394;
}
.rtl .cr-voting-cont-uni span.cr-voting-upvote-count {
	margin-right: 0px;
	margin-left: 10px;
}
.cr-voting-cont-uni span.cr-voting-downvote-count {
	color: #CE2B37;
}
.cr-all-reviews-shortcode ol li.comment, .cr-all-reviews-shortcode ol li.review {
	list-style: none;
	margin-right: 0;
	margin-bottom: 2.5em;
	position: relative;
	border: 0;
}
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list .comment_container,
.cr-all-reviews-shortcode ol li.comment .comment_container,
.cr-all-reviews-shortcode ol li.review .comment_container {
	position: relative;
	padding: 0px;
}
.cr-reviews-ajax-reviews .cr-reviews-ajax-comments .cr-ajax-reviews-list .comment_container .cr-avatar,
.cr-all-reviews-shortcode ol li.comment .comment_container .cr-avatar,
.cr-all-reviews-shortcode ol li.review .comment_container .cr-avatar {
	float: left;
	position: absolute;
	padding: 0;
	top: 0;
	left: 0;
	width: 50px;
	height: auto;
	border: 0px solid #e4e1e3;
	margin: 0;
	box-shadow: none;
	border-radius: 25px;
	max-height: none;
}
.rtl .cr-reviews-ajax-reviews .cr-reviews-ajax-comments .cr-ajax-reviews-list .comment_container .cr-avatar,
.rtl .cr-all-reviews-shortcode ol li.comment .comment_container .cr-avatar,
.rtl .cr-all-reviews-shortcode ol li.review .comment_container .cr-avatar {
	float: right;
	position: absolute;
	padding: 0;
	top: 0;
	right: 0;
	width: 50px;
	height: auto;
	border: 0px solid #e4e1e3;
	margin: 0;
	box-shadow: none;
	border-radius: 25px;
	max-height: none;
}
.cr-all-reviews-shortcode ol li.review .comment_container.cr-comment-no-avatar .cr-avatar {
	display: none;
}
.cr-avatar-check {
	display: none;
}
.cr-all-reviews-shortcode ol li.comment .comment_container .cr-avatar-check,
.cr-all-reviews-shortcode ol li.review .comment_container .cr-avatar-check,
.cr-reviews-ajax-reviews .cr-reviews-ajax-comments .cr-ajax-reviews-list .cr-avatar-check,
.cr-reviews-grid .cr-review-card .cr-avatar-check,
.cr-reviews-slider .cr-review-card .cr-avatar-check {
	width: 20px;
	height: 20px;
	background-color: #31856C;
	position: absolute;
	left: 34px;
	top: 28px;
	border-radius: 10px;
	border: 2px solid #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	z-index: 10;
}
.rtl .cr-all-reviews-shortcode ol li.comment .comment_container .cr-avatar-check,
.rtl .cr-all-reviews-shortcode ol li.review .comment_container .cr-avatar-check,
.rtl .cr-reviews-ajax-reviews .cr-reviews-ajax-comments .cr-ajax-reviews-list .cr-avatar-check,
.rtl .cr-reviews-grid .cr-review-card .cr-avatar-check,
.rtl .cr-reviews-slider .cr-review-card .cr-avatar-check {
	right: 34px;
	left: auto;
}
.cr-all-reviews-shortcode ol li.review .comment_container .meta,
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list .comment_container .meta {
	display: flex;
	align-items: baseline;
	justify-content: space-between;
	color: #767676;
	font-size: 14px;
	flex-wrap: wrap;
}
.cr-all-reviews-shortcode ol li div.comment_container a.cr-comment-a {
	box-shadow: none;
}
.cr-all-reviews-shortcode ol li div.comment_container p.iv-comment-product {
	font-size: 90%;
}
.cr-all-reviews-shortcode ol li div.comment_container a.iv-comment-product-a {
	box-shadow: none;
	line-height: 0;
}
.cr-all-reviews-shortcode ol li .comment_container .iv-comment-product-img {
	width: 20px;
	height: auto;
	vertical-align: top;
	margin-right: 5px;
	display: inline;
	border-radius: 3px;
}
.cr-all-reviews-shortcode .commentlist.cr-pagination-load {
	opacity: 0.5;
	pointer-events: none;
}
.cr-reviews-ajax-reviews .cr-reviews-ajax-comments .cr-ajax-reviews-list .comment_container .comment-text,
.cr-all-reviews-shortcode ol.commentlist li .comment-text {
	margin: 0 0 0 70px;
	border-top: 0 solid #e4e1e3;
	border-left: 0 solid #e4e1e3;
	border-right: 0 solid #e4e1e3;
	border-bottom: 1px solid #e4e1e3;
	padding: 0 0 1em 0;
}
.rtl .cr-reviews-ajax-reviews .cr-reviews-ajax-comments .cr-ajax-reviews-list .comment_container .comment-text,
.rtl .cr-all-reviews-shortcode ol.commentlist li .comment-text {
	margin: 0 70px 0 0;
}
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list .comment_container .comment-text.comment-text-no-avatar,
.cr-all-reviews-shortcode ol.commentlist li .comment-text.comment-text-no-avatar {
	margin: 0;
	float: none;
	width: 100%;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text p,
.cr-reviews-ajax-reviews .cr-reviews-ajax-comments .cr-ajax-reviews-list .comment-text p {
	margin: 1em 0;
	text-align: left;
}
.rtl .cr-reviews-ajax-reviews .cr-reviews-ajax-comments .cr-ajax-reviews-list .comment-text p {
	text-align: right;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .meta {
	font-size: 16px;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .meta time {
	font-style: normal;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .meta .cr-meta-author-featured-date,
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list .comment-text .meta .cr-meta-author-featured-date {
	display: flex;
	flex-direction: row;
	column-gap: 20px;
	align-items: baseline;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .cr-rating-product-name {
	display: flex;
	align-items: center;
	column-gap: 20px;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .cr-rating-product-name .cr-product-name-picture {
	display: flex;
	align-items: center;
	margin: 0 0 0 0;
	column-gap: 7px;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .cr-rating-product-name .cr-product-name-picture .cr-comment-productname-a {
	font-size: 14px;
	color: #0E252C;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .cr-rating-product-name .cr-product-name-picture .cr-comment-productname-a:hover {
	text-decoration: underline;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .meta .woocommerce-review__author,
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list .comment-text .meta .woocommerce-review__author {
	color: #0E252C;
	font-size: 16px;
	display: inline;
	font-weight: 500;
	margin: 0;
	text-indent: 0;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .meta .woocommerce-review__verified,
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list .comment-text .meta .woocommerce-review__verified {
	display: block;
	color: #4D5D64;
	font-size: 14px;
	font-weight: 400;
	margin: 0;
	text-indent: 0;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .meta .cr-meta-author-date time {
	color: #4D5D64;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .meta .cr-meta-author-title {
	display: flex;
	align-items: flex-start;
	flex-direction: column;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text .meta .cr-meta-author-title svg {
	width: 18px;
	height: 18px;
	display: block;
	margin: 0 0 0 10px;
	color: #4D5D64;
}
.cr-tag {
	color: #3d3d3d;
	background-color: #EEEFF2;
	display: inline-block;
	padding: .2em .4em;
	font-size: 12px;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	border-radius: 0.7em;
	border: 1px solid #c3c6d1;
	border-color: #31856C;
	transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
.cr-review-tags-filter .cr-tag {
	font-size: 14px;
	border-radius: 0.7em;
	margin-right: 5px;
	margin-bottom: 7px;
	border-color: #D0D0D0;
}
.cr-review-tags-filter span.cr-tag:hover {
	border-color: #31856C;
	cursor: pointer;
}
.cr-review-tags-filter {
	padding-top: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-bottom: 0px;
	pointer-events: auto;
	opacity: 1;
}
.cr-review-tags-filter span.cr-tag.cr-tag-selected {
	border-color: #31856C;
	background-color: #D3D3D3;
}
div.cr-review-tags-filter.cr-review-tags-filter-disabled {
	pointer-events: none;
	opacity: 0.5;
}
.cr-review-tags {
	margin-bottom: 1em;
}
.cr-incentivized-badge {
	display: flex;
	column-gap: 4px;
	align-items: center;
	font-size: 10px;
	line-height: 10px;
	margin: 0 0 1em 0;
}
.cr-reviews-grid .cr-review-card .cr-incentivized-row .cr-incentivized-badge,
.cr-reviews-slider .cr-review-card .cr-incentivized-row .cr-incentivized-badge {
	margin: 0;
}
.cr-incentivized-badge .cr-incentivized-icon {
	display: block;
	height: 12px;
}
.cr-incentivized-badge .cr-incentivized-icon .cr-incentivized-svg {
	width: 12px;
	height: 12px;
}
.cr-all-reviews-shortcode ol.commentlist ul.children,
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list ul.children {
	list-style: none outside;
	margin: 20px 0 0 50px;
	margin-inline-start: 70px;
	padding-inline-start: 0;
}
.ivole-verified-badge {
	font-size: 12px;
	vertical-align: middle;
}
.ivole-verified-badge-text {
	vertical-align: middle;
}

.ivole-verified-badge span.ivole-review-country-text, p.ivole-verified-badge span.ivole-review-country-space {
	vertical-align: middle;
}
.cr-all-reviews-shortcode ol.commentlist,
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list {
	list-style-type: none;
	margin: 0;
	padding: 0 0 1.5em 0;
	display: block;
	position: relative;
}
.cr-all-reviews-shortcode ol.commentlist li,
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list li {
	margin: 0 0 30px 0;
	list-style: none;
	clear: both;
	border: 0;
	padding: 0;
}
.cr-all-reviews-shortcode.cr-all-reviews-new-review ol.commentlist,
.cr-reviews-grid.cr-reviews-grid-new-review .cr-reviews-grid-inner,
.cr-all-reviews-shortcode.cr-all-reviews-new-review .cr-review-tags-filter,
.cr-reviews-grid.cr-reviews-grid-new-review .cr-review-tags-filter {
	display: none;
}
.cr-all-reviews-shortcode .ivole-review-country-icon,
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list .ivole-review-country-icon {
	display: inline;
	height: 12px;
	width: auto;
	margin: 0 0 0 5px;
	border-radius: 0;
}
.woocommerce #content #reviews p.ivole-verified-badge img.ivole-review-country-icon,
#reviews p.ivole-verified-badge img.ivole-review-country-icon {
	float: none;
	height: 12px;
	width: auto;
	vertical-align:middle;
	margin: 0px 5px 0px 0px;
	display: inline;
	border-radius: 0;
}
.woocommerce #content div.product #reviews .comment img.ivole-verified-badge-icon,
.woocommerce #content div.Product #reviews .comment_container img.ivole-verified-badge-icon,
.woocommerce div.product #reviews .comment img.ivole-verified-badge-icon,
.woocommerce-page #content div.product #reviews .comment img.ivole-verified-badge-icon,
.woocommerce-page div.product #reviews .comment img.ivole-verified-badge-icon,
#reviews p.ivole-verified-badge img.ivole-verified-badge-icon,
p.ivole-verified-badge img.ivole-verified-badge-icon,
#reviews img.ivole-verified-badge-icon,
img.ivole-verified-badge-icon {
	float: none;
	height: 20px;
	width: auto;
	vertical-align:middle;
	margin: 0px 5px 0px 0px;
	display: inline;
	border-radius: 0;
}
.woocommerce #content div.product #reviews .comment img.ivole-verified-badge-ext-icon,
.woocommerce #content div.Product #reviews .comment_container img.ivole-verified-badge-ext-icon,
.woocommerce div.product #reviews .comment img.ivole-verified-badge-ext-icon,
.woocommerce-page #content div.product #reviews .comment img.ivole-verified-badge-ext-icon,
.woocommerce-page div.product #reviews .comment img.ivole-verified-badge-ext-icon,
#reviews p.ivole-verified-badge .ivole-verified-badge-text img.ivole-verified-badge-ext-icon,
p.ivole-verified-badge .ivole-verified-badge-text img.ivole-verified-badge-ext-icon,
#reviews img.ivole-verified-badge-ext-icon,
img.ivole-verified-badge-ext-icon {
	float: none;
	height: 11px;
	width: 11px;
	vertical-align:middle;
	margin: 0px 0px 0px 5px;
	display: inline;
	border-radius: 0;
}
.cr-form-item-media-none {
	padding: 24px;
	text-align: left;
	display: flex;
	align-items: center;
	cursor: pointer;
	border: 1px dashed #D8E2E7;
	border-radius: 2px;
}
.cr-review-form-error .cr-form-item-media-none {
	border-color: #CE2B37;
}
.cr-form-visible .cr-form-item-media-none {
	display: none;
}
.cr-form-item-media-none .cr-form-item-media-icon {
	width: 60px;
	height: 60px;
	padding: 8px;
	border: 1px solid #e0e0e0;
	fill: #e0e0e0;
	border-radius: 2px;
	flex-shrink: 0;
}
.cr-form-item-media-none .cr-form-item-media-icon:first-child {
	margin: 0 5px 0 0;
}
.rtl .cr-form-item-media-none .cr-form-item-media-icon:first-child {
	margin: 0 0 0 5px;
}
.cr-rtl .cr-form-item-media-none .cr-form-item-media-icon:first-child {
	margin-right: 0;
	margin-left: 5px;
}
.cr-form-item-media-none span {
	display: inline-block;
	vertical-align: top;
	margin: 0 0 0 20px;
	color: #4D5D64;
	font-size: 12px;
}
.rtl .cr-form-item-media-none span {
	margin: 0 20px 0 0;
}
.cr-rtl .cr-form-item-media-none span {
	margin-left: 0;
	margin-right: 20px;
}
.cr-form-item-media-none:active {
	border-color: #c9c9c9;
}
.cr-form-item-media-none:active .cr-form-item-media-icon {
	border: 1px solid #c9c9c9;
	fill: #c9c9c9;
}
.cr-form-item-media .cr-form-item-media-file {
	display: none;
}
.cr-form-item-media-preview {
	display: none;
	margin: 0;
	flex-wrap: wrap;
}
.cr-form-visible .cr-form-item-media-preview {
	display: flex;
	row-gap: 9px;
}
.cr-form-item-media-preview .cr-upload-images-containers {
	width: 108px;
	height: 108px;
	margin-bottom: 0px;
	margin-right: 9px;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	pointer-events: auto;
	border: 1px solid #D8E2E7;
	padding: 0;
	overflow: hidden;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-delete-pending {
	pointer-events: none;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-ok {
	padding: 0px;
}
.cr-form-item-media-preview .cr-upload-images-containers img {
	margin: 0 0 0 0;
	opacity: 0.3;
	object-fit: cover;
}
.cr-form-item-media-preview .cr-upload-images-containers .cr-upload-video-thumbnail {
	width: 108px;
	height: 108px;
	margin: 0 0 0 0;
	opacity: 0.3;
	padding: 15px;
	border: 1px solid #e0e0e0;
	fill: #e0e0e0;
	border-radius: 2px;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-ok img {
	opacity: 1;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-video-thumbnail {
	opacity: 0.6;
	border-width: 0;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-ok.cr-upload-delete-pending img {
	opacity: 0.2;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-ok.cr-upload-delete-pending .cr-upload-video-thumbnail {
	opacity: 0.2;
}
.cr-form-item-media-preview .cr-upload-images-containers .cr-upload-images-delete {
	padding: 1px;
	position: absolute;
	top: 2px;
	right: 2px;
	width: 16px;
	height: 16px;
	display: none;
	background-color: #CE2B37;
	border-radius: 8px;
	border: 0px solid;
	line-height: 0;
	cursor: pointer;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-images-delete {
	display: block;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-ok.cr-upload-delete-pending .cr-upload-images-delete {
	opacity: 0.2;
}
.cr-form-item-media-preview .cr-upload-images-containers .cr-upload-images-delete .cr-no-icon {
	fill: #ffffff;
}
.cr-form-item-media-preview .cr-upload-images-containers .cr-upload-images-delete-spinner {
	margin: 0;
	background: url(../img/spinner-2x.gif) no-repeat;
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
	display: none;
	text-decoration: none;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-delete-pending .cr-upload-images-delete-spinner {
	display: block;
}
.cr-form-item-media-preview .cr-form-item-media-add {
	cursor: pointer;
	width: 108px;
	height: 108px;
	text-align: center;
	line-height: 108px;
	font-size: 40px;
	color: #e0e0e0;
	border: 1px dashed #e0e0e0;
	margin-bottom: 0px;
	display: inline-block;
}
.cr-form-item-media-preview .cr-form-item-media-add:active {
	border-color: #c9c9c9;
	color: #c9c9c9;
}
.cr-form-item-media-preview .cr-upload-images-containers .cr-upload-images-pbar {
	width: 94px;
	height: 10px;
	margin: 0 5px 0 5px;
	padding: 0;
	position: absolute;
	top: 50%;
	left: 0;
	transform: translate(0, -50%);
	background-color: #dddddd;
	border-radius: 5px;
}
.cr-form-item-media-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-images-pbar {
	display: none;
}
.cr-form-item-media-preview .cr-upload-images-containers .cr-upload-images-pbar .cr-upload-images-pbarin {
	width: 0;
	height: 100%;
	margin: 0;
	padding: 0;
	background-color: #1AB394;
	border-radius: 5px;
}
.cr-pswp {
	display: none;
}
.cr-reviews-rating {
	display: flex;
	column-gap: 10px;
	align-items: center;
	margin: 0;
	padding: 0;
	line-height: 0;
	.cr-qna-separator {
		margin: 0;
	}
}
.cr-average-rating-stars {
	line-height: 0;
}
.crstar-rating-svg {
	margin: 8px 0;
	position: relative;
	display: inline-block;
}
.cr-reviews-grid .cr-review-card .crstar-rating-svg,
.cr-reviews-slider .cr-review-card .crstar-rating-svg,
.cr-reviews-rating .crstar-rating-svg,
.cr-ajax-reviews-slide-main-comment .crstar-rating-svg,
.crf-custom-question-rating-cont .crstar-rating-svg {
	margin: 0;
}
.cr-average-rating-stars .crstar-rating-svg {
	margin: 5px 0 0 0;
}
.crstar-rating-svg .cr-rating-icon-base {
	display: flex;
	height: 20px;
}
.crstar-rating-svg .cr-rating-icon-frnt {
	display: flex;
	height: 20px;
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
}
.cr-reviews-grid .cr-review-card .crstar-rating-svg .cr-rating-icon-base,
.cr-reviews-grid .cr-review-card .crstar-rating-svg .cr-rating-icon-frnt,
.cr-reviews-slider .cr-review-card .crstar-rating-svg .cr-rating-icon-base,
.cr-reviews-slider .cr-review-card .crstar-rating-svg .cr-rating-icon-frnt {
	height: 25px;
	column-gap: 3px;
}
.crf-custom-question-rating-cont .crstar-rating-svg .cr-rating-icon-base,
.crf-custom-question-rating-cont .crstar-rating-svg .cr-rating-icon-frnt,
.cr-reviews-slider .cr-sldr-custom-question .crstar-rating-svg .cr-rating-icon-base,
.cr-reviews-slider .cr-sldr-custom-question .crstar-rating-svg .cr-rating-icon-frnt {
	height: 18px;
	column-gap: 0;
}
.rtl .crstar-rating-svg .cr-rating-icon-frnt {
	right: 0;
	left: auto;
}
.crstar-rating-svg .cr-rating-icon-base .cr-rating-icon-bg {
	width: 20px;
	height: 20px;
	fill: none;
	stroke: #FFBC00;
}
.crstar-rating-svg .cr-rating-icon-frnt .cr-rating-icon {
	width: 20px;
	height: 20px;
	fill: #FFBC00;
	flex-shrink: 0;
}
.cr-reviews-grid .cr-review-card .crstar-rating-svg .cr-rating-icon-base .cr-rating-icon-bg,
.cr-reviews-grid .cr-review-card .crstar-rating-svg .cr-rating-icon-frnt .cr-rating-icon,
.cr-reviews-slider .cr-review-card .crstar-rating-svg .cr-rating-icon-base .cr-rating-icon-bg,
.cr-reviews-slider .cr-review-card .crstar-rating-svg .cr-rating-icon-frnt .cr-rating-icon {
	width: 25px;
	height: 25px;
}
.crf-custom-question-rating-cont .crstar-rating-svg .cr-rating-icon-base .cr-rating-icon-bg,
.crf-custom-question-rating-cont .crstar-rating-svg .cr-rating-icon-frnt .cr-rating-icon,
.cr-reviews-slider .cr-sldr-custom-question .crstar-rating-svg .cr-rating-icon-base .cr-rating-icon-bg,
.cr-reviews-slider .cr-sldr-custom-question .crstar-rating-svg .cr-rating-icon-frnt .cr-rating-icon {
	width: 18px;
	height: 18px;
}
.cr-reviews-slider .cr-sldr-custom-question .crstar-rating-svg .cr-rating-icon-base .cr-rating-icon-bg {
	stroke: #aaaaaa;
}
.cr-reviews-slider .cr-sldr-custom-question .crstar-rating-svg .cr-rating-icon-frnt .cr-rating-icon {
	fill: #aaaaaa;
}
@media (max-width: 500px) {
	#reviews .cr-summaryBox-wrap .ivole-summaryBox,
	#reviews .cr-summaryBox-wrap .cr-overall-rating-wrap,
	.cr-all-reviews-shortcode .cr-summaryBox-wrap .ivole-summaryBox,
	.cr-all-reviews-shortcode .cr-summaryBox-wrap.cr-summaryBox-add-review .ivole-summaryBox,
	.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-overall-rating-wrap,
	.cr-all-reviews-shortcode .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-overall-rating-wrap,
	.cr-all-reviews-shortcode .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-add-review-wrap,
	.cr-reviews-grid .cr-summaryBox-wrap .ivole-summaryBox,
	.cr-reviews-grid .cr-summaryBox-wrap.cr-summaryBox-add-review .ivole-summaryBox,
	.cr-reviews-grid .cr-summaryBox-wrap .cr-overall-rating-wrap,
	.cr-reviews-grid .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-overall-rating-wrap,
	.cr-reviews-grid .cr-summaryBox-wrap.cr-summaryBox-add-review .cr-add-review-wrap {
		width: 100%;
		flex: 0 0 100%;
	}
	.cr-all-reviews-shortcode ol.commentlist {
		margin-left: 0px;
		margin-right: 0px;
	}
	.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-summary-separator,
	.cr-reviews-grid .cr-summaryBox-wrap .cr-summary-separator {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-summary-separator-int,
	.cr-reviews-grid .cr-summaryBox-wrap .cr-summary-separator-int {
		width: 50px;
		height: 2px;
	}
	.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-summary-separator-side,
	.cr-reviews-grid .cr-summaryBox-wrap .cr-summary-separator-side {
		display: none;
	}
	.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-add-review-wrap,
	.cr-reviews-grid .cr-summaryBox-wrap .cr-add-review-wrap {
		width: 100%;
		padding: 10px;
	}
}
.woocommerce .product #reviews .comment-text p.ivf-custom-question-checkbox {font-weight: 550;margin: 0.6em 0;}
.woocommerce .product #reviews .comment-text ul.ivf-custom-question-ul {margin: 0.6em 0;}
.woocommerce .product #reviews .comment-text ul.ivf-custom-question-ul li {margin:0;}
.woocommerce .product #reviews .comment-text span.ivf-custom-question-radio {font-weight: 550;}
.woocommerce .product #reviews .comment-text hr.ivf-custom-question-hr,
.cr-all-reviews-shortcode .commentlist hr.ivf-custom-question-hr {
	margin: 0.6em 0;
}
.cr-all-reviews-shortcode .commentlist hr.ivf-custom-question-hr {
	display: none;
}
.cr-all-reviews-shortcode .commentlist .comment-text .ivf-custom-question-p {
	font-size: 14px;
	margin: 0.5em 0;
}
.woocommerce .product #reviews .comment-text .crf-custom-question-rating-cont,
.cr-all-reviews-shortcode .commentlist .crf-custom-question-rating-cont {
	margin: 0.6em 0;
	display: flex;
	align-items: center;
	flex-direction: row;
}
.cr-all-reviews-shortcode .commentlist .crf-custom-question-rating-cont {
	font-size: 14px;
}
.woocommerce .product #reviews .comment-text p.ivf-custom-question-p,
.cr-all-reviews-shortcode .commentlist p.ivf-custom-question-p {
	margin: 0.6em 0;
}
.woocommerce .product #reviews .comment-text .crf-custom-question-rating,
.cr-all-reviews-shortcode .commentlist .crf-custom-question-rating {
	padding-right: 7px;
	font-weight: 550;
}
.woocommerce .product #reviews .comment-text .crf-custom-question-rating-cont .star-rating,
.cr-all-reviews-shortcode .commentlist .crf-custom-question-rating-cont .star-rating {
	font-size: 1em;
	height: 1.1em;
	line-height: 1.1em;
	float: none;
	margin: 0;
}
.cr-onsite-questions .cr-onsite-question {
	display: block;
	margin: 1.5em 0;
	position: relative;
}
.cr-onsite-questions .cr-onsite-question .required,
.cr-onsite-ratings .cr-review-form-rating .required,
.cr-review-form-rating-overall .cr-review-form-rating .required {
	margin: 0 0 0 3px;
}
.cr-onsite-questions .cr-onsite-question label,
.cr-onsite-questions .cr-onsite-question input {
	display: block;
}
.cr-onsite-questions .cr-onsite-question.cr-full-width label,
.cr-onsite-questions .cr-onsite-question.cr-full-width input {
	width: 100%;
}
.cr-review-form-wrap .cr-onsite-questions .cr-onsite-question.cr-full-width label {
	width: auto;
}
.cr-onsite-ratings {
	border-top: 1px solid #DFE4E7;
	border-bottom: 1px solid #DFE4E7;
}
.cr-onsite-ratings.cr-onsite-ratings-only {
	border-bottom: none;
}
.cr-review-form-wrap .cr-onsite-ratings {
	max-width: 500px;
	margin: 0 auto;
}
.cr-customer-consent .checkbox input.cr-customer-consent-checkbox {
	margin-right:7px;
	position: relative;
}
#kco-extra-checkout-fields .cr-customer-consent {
	display: none;
}
.cr-show-more{
	width:100%;
}
.cr-reviews-ajax-reviews .cr-show-more-reviews-prd,
.cr-all-reviews-shortcode .cr-show-more-button,
.cr-reviews-grid .cr-show-more-button,
.cr-qna-block .cr-show-more-que {
	display: block;
	margin: 10px auto;
	height: 40px;
	padding: 0 20px 0 20px;
	border: solid 1px #BCCCD4;
	font-size: 14px;
	background-color: transparent;
	color: #0E252C;
}
.cr-qna-block .cr-show-more-que {
	margin: 20px auto;
}
.cr-all-reviews-shortcode.cr-all-reviews-new-review .cr-show-more-button,
.cr-reviews-grid.cr-reviews-grid-new-review .cr-show-more-button {
	display: none;
}
.cr-reviews-ajax-reviews .cr-show-more-reviews-prd:hover,
.cr-reviews-ajax-reviews .cr-show-more-reviews-prd:focus,
.cr-all-reviews-shortcode .cr-show-more-button:hover,
.cr-all-reviews-shortcode .cr-show-more-button:focus,
.cr-reviews-grid .cr-show-more-button:hover,
.cr-reviews-grid .cr-show-more-button:focus,
.cr-qna-block .cr-show-more-que:hover,
.cr-qna-block .cr-show-more-que:focus {
	border: solid 1px #00A79D;
	background-color: transparent;
	color: #0E252C;
}
.cr-show-more-review-spinner,
.cr-show-more .cr-show-more-spinner,
.cr-all-reviews-shortcode .commentlist.cr-pagination-load .cr-pagination-review-spinner {
	display: block;
	margin: 20px auto;
	background: url(../img/spinner-2x.gif) no-repeat;
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
}
.cr-all-reviews-shortcode .commentlist.cr-pagination-load .cr-pagination-review-spinner {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	margin: auto;
}
.cr-all-reviews-shortcode.cr-all-reviews-new-review .cr-show-more-review-spinner,
.cr-reviews-grid.cr-reviews-grid-new-review .cr-show-more-review-spinner,
.cr-all-reviews-shortcode .commentlist .cr-pagination-review-spinner {
	display: none;
}
.cr-show-more-review-spinner-cnt {
	width: 100%;
	height: 60px;
	display: flex;
	align-items: center;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-sort-div .cr-ajax-reviews-sort,
.cr-all-reviews-shortcode .cr-ajax-reviews-sort-div .cr-ajax-reviews-sort {
	display: block;
	margin: 0;
	background: transparent;
	border: 0px;
	padding: 4px;
	width: auto;
	height: auto;
	appearance: auto;
	color: #0E252C;
	font-size: 12px;
	font-weight: 500;
}
.cr-ajax-reviews-sort-div {
	display: flex;
	justify-content: space-between;
}
.cr-all-reviews-shortcode.cr-all-reviews-new-review .cr-search-no-reviews {
	display: none;
}
#reviews.cr-reviews-ajax-reviews .cr-button-search,
.cr-all-reviews-shortcode .cr-button-search {
	border: 1px solid;
	border-radius: 4px 4px 4px 4px;
	border-color: #ADB1B8 #A2A6AC #8D9096;
	padding: 3px 10px;
	background: -webkit-linear-gradient(top,#f7f8fa,#e7e9ec);
	background: linear-gradient(to bottom,#f7f8fa,#e7e9ec);
	font-weight: normal;
	color: black;
	letter-spacing: normal;
	font-size: 1em;
	text-transform: none;
	line-height: 1.5em;
	margin-top: 0px;
	margin-bottom: 0px;
	margin-left: 5px;
	margin-right: 0px;
	white-space: nowrap;
	width: auto;
}
.rtl #reviews.cr-reviews-ajax-reviews .cr-button-search,
.rtl .cr-all-reviews-shortcode .cr-button-search {
	margin-left: 0px;
	margin-right: 5px;
}
#reviews.cr-reviews-ajax-reviews .cr-button-search:hover,
.cr-all-reviews-shortcode .cr-button-search:hover {
	background: #e0e3e9;
	background: -webkit-linear-gradient(top,#e7eaf0,#d9dce1);
	background: linear-gradient(to bottom,#e7eaf0,#d9dce1);
	text-decoration: none;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-search,
.cr-all-reviews-shortcode .cr-ajax-search {
	display: flex;
	padding: 0;
	margin: 24px 0;
	width: 100%;
}
.cr-all-reviews-shortcode.cr-all-reviews-new-review .cr-ajax-search {
	display: none;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-search div,
.cr-all-reviews-shortcode .cr-ajax-search div,
.cr-qna-block .cr-qna-search-block div {
	position: relative;
	flex-grow: 1;
}
#reviews.cr-reviews-ajax-reviews .cr-input-text,
.cr-all-reviews-shortcode .cr-ajax-search .cr-input-text,
.cr-ajax-search input[type="text"] {
	border: 1px solid;
	border-radius: 4px 4px 4px 4px;
	border-color: #ADB1B8 #A2A6AC #8D9096;
	background-color: #ffffff;
	padding: 3px 10px;
	padding-left: calc(1.5em + .75rem);
	padding-right: 1.8em;
	width: 100%;
	line-height: 1.5em;
	margin: 0px;
	font-size: 1em;
	box-sizing: border-box;
}
.rtl #reviews.cr-reviews-ajax-reviews .cr-input-text,
.rtl .cr-all-reviews-shortcode .cr-ajax-search .cr-input-text,
.rtl .cr-ajax-search input[type="text"] {
	padding-right: calc(1.5em + .75rem);
	padding-left: 1.8em;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-search .cr-ajax-search-icon,
.cr-all-reviews-shortcode .cr-ajax-search .cr-ajax-search-icon,
.cr-ajax-search .cr-ajax-search-icon {
	position: absolute;
	display: inline-block;
	top: 50%;
	left: 9px;
	transform: translateY(-50%);
	width: 1.1em;
	height: 1.1em;
}
.rtl #reviews.cr-reviews-ajax-reviews .cr-ajax-search .cr-ajax-search-icon,
.rtl .cr-all-reviews-shortcode .cr-ajax-search .cr-ajax-search-icon,
.rtl .cr-ajax-search .cr-ajax-search-icon {
	right: 9px;
}
.cr-qna-block .cr-input-text, .cr-ajax-search input[type="text"] {
	border: 1px solid;
	border-radius: 4px 4px 4px 4px;
	border-color: #ADB1B8 #A2A6AC #8D9096;
	background-color: #ffffff;
	color: #898F92;
	padding: 3px 10px;
	padding-left: calc(1.5em + .75rem);
	padding-right: 1.5em;
	width: 100%;
	margin: 0px;
	font-size: 1em;
	box-shadow: none;
	outline: none;
}
.cr-ajax-search input[type="text"] {
	height: 42px;
}
.cr-qna-block .cr-input-text {
	height: 100%;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-nosummary {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-nosummary .cr-nosummary-rating-cnt {
	height: 40px;
	display: flex;
	align-items: center;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-nosummary .cr-nosummary-rating-cnt .cr-nosummary-rating-val {
	font-size: 25px;
	font-weight: bold;
	margin: 0 0 0 15px;
	color: #0E252C;
}
.rtl .cr-reviews-ajax-reviews .cr-ajax-reviews-nosummary .cr-nosummary-rating-cnt .cr-nosummary-rating-val {
	margin: 0 15px 0 0;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-nosummary .cr-nosummary-rating-cnt .cr-nosummary-rating-lbl {
	font-size: 20px;
	font-weight: normal;
	margin: 0 0 0 15px;
	color: #4D5D64;
}
.rtl .cr-reviews-ajax-reviews .cr-ajax-reviews-nosummary .cr-nosummary-rating-cnt .cr-nosummary-rating-lbl {
	margin: 0 15px 0 0;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-nosummary .cr-nosummary-add {
	margin: 0 0 0 15px;
	cursor: pointer;
	background-color: #31856C;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 40px;
	text-transform: none;
	line-height: 20px;
	font-size: 15px;
	border: none;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-nosummary .cr-nosummary-add:hover {
	background-color: #276A56;
}
#reviews.cr-reviews-ajax-reviews .cr-clear-input,
.cr-all-reviews-shortcode .cr-clear-input,
.cr-qna-block .cr-qna-search-block .cr-clear-input {
	display:none;
	position:absolute;
	cursor: pointer;
	margin-left: -1.7em ;
	margin-top: -8px;
	height: 16px;
	top: 50%;
	right: 8px;
}
.rtl #reviews.cr-reviews-ajax-reviews .cr-clear-input,
.rtl .cr-all-reviews-shortcode .cr-clear-input,
.rtl .cr-qna-block .cr-qna-search-block .cr-clear-input {
	left: 8px;
	right: auto;
	margin-left: 0;
}
.cr-all-reviews-shortcode .cr-clear-input.cr-visible {
	display: inline-block;
}
#reviews.cr-reviews-ajax-reviews .cr-clear-input svg,
.cr-all-reviews-shortcode .cr-clear-input svg,
.cr-qna-block .cr-qna-search-block .cr-clear-input svg {
	display: block;
	height: 16px;
}
#reviews.cr-reviews-ajax-reviews .cr-qna-search-icon,
.cr-all-reviews-shortcode .cr-qna-search-icon,
.cr-qna-block .cr-qna-search-block .cr-qna-search-icon {
	fill: #18B394;
	display: inline-block;
	position:absolute;
	width: 1.1em;
	height: 1.1em;
	left: 9px;
	top: 50%;
	transform: translateY(-50%);
}
#reviews.cr-reviews-ajax-reviews .cr-input-text:focus,
.cr-all-reviews-shortcode .cr-input-text:focus,
.cr-qna-block .cr-input-text:focus,
.cr-ajax-search input[type="text"]:focus {
	background-color: #ffffff;
	outline:none;
	border-color: #e77600;
	box-shadow: 0 0 3px 2px rgba(228,121,17,.5);
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-add-review {
	display: block;
	margin: 1em 0px;
	border: 1px solid;
	border-radius: 4px 4px 4px 4px;
	border-color: #ADB1B8 #A2A6AC #8D9096;
	padding: 3px 10px;
	background: -webkit-linear-gradient(top,#f7f8fa,#e7e9ec);
	background: linear-gradient(to bottom,#f7f8fa,#e7e9ec);
	font-weight: 600;
	color: black;
	letter-spacing: normal;
	font-size: 1em;
	text-transform: none;
	line-height: normal;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-add-review:hover {
	background: #e0e3e9;
	background: -webkit-linear-gradient(top,#e7eaf0,#d9dce1);
	background: linear-gradient(to bottom,#e7eaf0,#d9dce1);
	text-decoration: none;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-add-review-wrap .cr-all-reviews-add-review,
.cr-reviews-grid .cr-summaryBox-wrap .cr-add-review-wrap .cr-all-reviews-add-review {
	display: block;
	background-color: #31856C;
	color: #ffffff;
	border: 0;
	height: 40px;
	line-height: 20px;
	font-size: 15px;
	width: auto;
	padding: 10px 20px;
	margin: 0;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap .cr-add-review-wrap .cr-all-reviews-add-review:hover,
.cr-reviews-grid .cr-summaryBox-wrap .cr-add-review-wrap .cr-all-reviews-add-review:hover {
	background-color: #276A56;
	text-decoration: none;
	cursor: pointer;
}
.cr-ajax-reviews-review-form {
	display: none;
}
.cr-ajax-reviews-review-form.cr-ajax-reviews-review-form-nc {
	display: block;
}
#reviews.cr-reviews-ajax-reviews .comment-reply-title {
	display: block;
	font-size: 1.5em;
}
.cr-ajax-reviews-review-form p.form-submit {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-div,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-div {
	margin: 12px 0 24px 0;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-div .cr-ajax-reviews-cus-images-title,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-div .cr-ajax-reviews-cus-images-title {
	font-size: 1em;
	margin: 0 auto 5px auto;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-div2,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-div2 {
	white-space: nowrap;
	overflow-x: auto;
	overflow-y: hidden;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal-cont,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal-cont {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(120, 120, 120, 0.8);
	z-index: 100001;
	display: none;
	opacity: 0;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal-cont.cr-mask-active,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal-cont.cr-mask-active {
	visibility: visible;
	opacity: 1;
	display: flex;
	justify-content: center;
	align-items: center;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal {
	visibility: visible;
	position: relative;
	max-width: 100vw;
	width: 700px;
	background: #fff;
	z-index: 100001;
	display: block;
	border-radius: 3px;
	opacity: 1;
	max-height: 100vh;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal div.cr-ajax-reviews-cus-images-hdr,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal div.cr-ajax-reviews-cus-images-hdr {
	height: 36px;
	background: #f2f2f2;
	background: #f1f1f1;
	background: -webkit-linear-gradient(top,#f7f7f7,#eaeaea);
	background: linear-gradient(to bottom,#f7f7f7,#eaeaea);
	box-shadow: 0 1px 0 rgba(255,255,255,.5) inset,0 -1px 0 rgba(255,255,255,.4) inset;
	position: relative;
	border-bottom: 1px solid #cdcdcd;
	border-bottom-color: rgba(0,0,0,.2);
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal button.cr-ajax-reviews-cus-images-close,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal button.cr-ajax-reviews-cus-images-close {
	display: block;
	zoom: 1;
	background-color: transparent;
	border: none;
	float: right;
	padding: 8px;
	position: absolute;
	top: 50%;
	right: 5px;
	margin: -15px 0 0;
	line-height: 0;
	min-height: auto;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal button.cr-ajax-reviews-cus-images-close svg,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal button.cr-ajax-reviews-cus-images-close svg {
	opacity: .64;
	width: 15px;
	height: 15px;
	font-size: 15px;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-close .cr-no-icon,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-close .cr-no-icon {
	fill: #333333;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-slide-main-flex,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-slide-main-flex {
	display: flex;
	flex-direction: row;
	justify-content: space-evenly;
	gap: 20px;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav {
	width: 80%;
	margin: 0 auto 35px auto;
	background-color: #f4f4f4;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav .cr-ajax-reviews-slide-nav img,
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav .cr-ajax-reviews-slide-nav video,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav .cr-ajax-reviews-slide-nav img,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav .cr-ajax-reviews-slide-nav video {
	max-height: 75px;
	margin: 0 auto;
	width: auto;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav .cr-ajax-reviews-slide-nav,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-nav .cr-ajax-reviews-slide-nav {
	padding: 0px;
	line-height: 0;
	text-align: center;
	position: relative;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main img,
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main video,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main img,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main video {
	max-width: 400px;
	max-height: 400px;
	width: auto;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment {
	width: 250px;
	padding: 0;
	line-height: 1.5;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment p,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment p {
	margin: 0px;
	display: block;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment time,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment time {
	opacity: 0.5;
	font-size: 0.875em;
	display: block;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment .cr-ajax-reviews-slide-main-comment-body,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment .cr-ajax-reviews-slide-main-comment-body {
	max-height: 300px;
	overflow-x: hidden;
	overflow-y: scroll;
	line-height: normal;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main.slick-initialized .slick-slide,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main.slick-initialized .slick-slide {
	float: none;
	display: inline-block;
	vertical-align: middle;
}
#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-vote,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-vote {
	box-sizing: border-box;
	margin: 1em 0 0 0;
}
.cr-search-highlight{
	background: linear-gradient(0deg, #FFEB82, #FFEB82), #F9F9F9;
}
.cr-noscroll {
	overflow: hidden;
}
.product_meta .cr_gtin_val, .product_meta .cr_mpn_val, .product_meta .cr_brand_val {
	display: inline;
}
.cr-reviews-slider .cr-slider-read-more a, .cr-reviews-slider .cr-slider-read-less a {
	font-size: 0.8em;
}
.cr-reviews-slider .cr-slider-details{
	display:none;
}
.cr-qna-block .cr-qna-search-block {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 30px;
}
.cr-qna-block .cr-qna-list-block {
	display: block;
}
.cr-qna-block.cr-qna-new-q-form-open .cr-qna-search-block,
.cr-qna-block.cr-qna-new-q-form-open .cr-qna-list-block {
	display: none;
}
.cr-qna-block .cr-qna-search-block .cr-qna-ask-button {
	font-size: 16px;
	font-weight: 400;
	background: #31856C;
	border: 0px solid;
	border-radius: 0;
	min-height: 42px;
	color: #FFFFFF;
	cursor: pointer;
	display: inline-block;
	outline-style: none;
	margin-top: 0px;
	margin-bottom: 0px;
	margin-left: 10px;
	margin-right: 0px;
	padding: 5px 15px;
}
.cr-qna-block .cr-qna-search-block .cr-qna-ask-button:hover {
	background: #276A56;
}
.rtl .cr-qna-block .cr-qna-search-block .cr-qna-ask-button {
	margin-left: 0px;
	margin-right: 10px;
}
.cr-qna-block .cr-qna-list-block div.cr-qna-list-empty {
	padding: 30px;
	text-align: center;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont {
	margin-top: 20px;
	margin-bottom: 0;
	margin-right: 0;
	margin-left: 0;
	overflow-wrap: break-word;
	word-break: break-word;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-q,
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a,
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b {
	display: flex;
	margin: 0px;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a,
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b {
	margin-top: 20px;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-q .cr-qna-list-q-q-l {
	min-width: 50px;
	display: flex;
	align-items: center;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a .cr-qna-list-q-a-l,
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b .cr-qna-list-q-b-l {
	min-width: 50px;
	display: flex;
	align-items: flex-start;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-icon {
	width: 30px;
	height: 30px;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-icon circle {
	stroke: #31856C;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-icon path {
	fill: #31856C;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-v-icon {
	width: 16px;
	height: 16px;
	max-width: 100%;
	max-height: 100%;
	display: inline-block;
	vertical-align: middle;
	margin-right: 10px;
}
.rtl .cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-v-icon {
	margin-left: 10px;
	margin-right: 0px;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-q .cr-qna-list-q-q-r,
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a .cr-qna-list-q-a-r {
	display: block;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b .cr-qna-list-q-b-r {
	padding-bottom: 20px;
	border-bottom: solid 2px #F9F9F9;
	border-top: solid 0px;
	border-right: solid 0px;
	border-left: solid 0px;
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b .cr-qna-list-q-b-r.cr-qna-list-q-b-r-no-ans {
	justify-content: right;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b.cr-qna-ans-form-open .cr-qna-list-q-b-r {
	display: none;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b .cr-qna-list-inl-answ {
	padding-bottom: 20px;
	border-bottom: solid 2px #F9F9F9;
	border-top: solid 0px;
	border-right: solid 0px;
	border-left: solid 0px;
	width: 100%;
	display: none;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b.cr-qna-ans-form-open .cr-qna-list-inl-answ {
	display: block;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b .cr-qna-list-q-b-r .cr-qna-q-voting {
	display: flex;
	align-items: center;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a .cr-qna-list-q-a-r .cr-qna-list-answer {
	display: block;
	border-left: solid 3px #BCCCD4;
	border-top: solid 0px;
	border-right: solid 0px;
	border-bottom: solid 0px;
	padding-left: 16px;
	padding-right: 0px;
	padding-top: 0px;
	padding-bottom: 0px;
	margin-bottom: 10px;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a .cr-qna-list-q-a-r .cr-qna-list-answer.cr-qna-list-last {
	margin-bottom: 0px;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-q .cr-qna-list-q-q-r .cr-qna-list-question {
	font-size: 18px;
	font-weight: 600;
	line-height: 1.2;
	display: block;
	color: #203741;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a .cr-qna-list-q-a-r .cr-qna-list-answer-s {
	font-size: 16px;
	font-weight: 400;
	line-height: 1.2;
	display: block;
	color: #203741;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a .cr-qna-list-q-a-r .cr-qna-list-answer-s p {
	margin: 0;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a .cr-qna-list-q-a-r .cr-qna-list-answer-s a {
	text-decoration: underline;
	color: #6d6d6d;
	cursor: pointer;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-q .cr-qna-list-q-q-r .cr-qna-list-q-author,
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-a .cr-qna-list-q-a-r .cr-qna-list-q-author {
	font-size: 14px;
	font-weight: 400;
	line-height: 1.2;
	display: block;
	color: #276A56;
	font-style: italic;
	margin-top: 5px;
	margin-bottom: 0px;
	margin-left: 0px;
	margin-right: 0px;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-author .cr-qna-list-q-author-b {
	font-weight: 600;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-author-verified {
	font-size: 14px;
	color: #276A56;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-ans-button {
	font-size: 14px;
	font-weight: 600;
	background: #FFFFFF;
	border: 1px solid #BCCCD4;
	border-radius: 0;
	height: 42px;
	color: #276A56;
	cursor: pointer;
	display: inline-block;
	outline-style: none;
	padding: 4px 15px;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-ans-button:hover {
	border-color: #4D5D64;
}
.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b-r-no-ans .cr-qna-ans-button {
	display: none;
}
.cr-qna-block #cr-show-more-q-spinner {
	display: block;
	margin: 30px auto;
	background: url(../img/spinner-2x.gif) no-repeat;
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
}
.cr-qna-block .cr-search-no-qna {
	margin: 30px auto;
	text-align: center;
	color: #203741;
}
#reviews.cr-reviews-ajax-reviews p.cr-featured-badge,
.cr-all-reviews-shortcode p.cr-featured-badge {
	margin: 0px 0px 10px 0px;
}
#reviews.cr-reviews-ajax-reviews .cr-featured-badge span,
#reviews.cr-reviews-ajax-reviews .cr-all-featured-badge span,
.cr-all-reviews-shortcode .cr-all-featured-badge span {
	text-transform: uppercase;
	display: inline-block;
	font-size: 12px;
	-moz-box-shadow: inset 0px 1px 0px 0px #fff6af;
	-webkit-box-shadow: inset 0px 1px 0px 0px #fff6af;
	box-shadow: inset 0px 1px 0px 0px #fff6af;
	background-color: #ffec64;
	-webkit-border-top-left-radius: 3px;
	-moz-border-radius-topleft: 3px;
	border-top-left-radius: 3px;
	-webkit-border-top-right-radius: 3px;
	-moz-border-radius-topright: 3px;
	border-top-right-radius: 3px;
	-webkit-border-bottom-right-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	border-bottom-right-radius: 3px;
	-webkit-border-bottom-left-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	border-bottom-left-radius: 3px;
	text-indent:0;
	border: 1px solid #ffaa22;
	color: #333333;
	font-weight: normal;
	font-style: normal;
	line-height: 20px;
	text-decoration: none;
	text-align: center;
	text-shadow: 0px 1px 0px #ffee66;
	padding: 0px 5px;
}
#reviews.cr-reviews-ajax-reviews #comments.cr-reviews-ajax-comments .cr-ajax-reviews-list .review .comment-text {
	display: block;
	border-bottom: 1px solid #e4e1e3;
	padding-bottom: 1em;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-wpml-switch {
	text-align: center;
	background-color: #F9F9F9;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-wpml-switch a {
	color: #4D5D64;
}
.cr-reviews-ajax-reviews .cr-ajax-reviews-wpml-switch a:hover,
.cr-reviews-ajax-reviews .cr-ajax-reviews-wpml-switch a:active {
	color: #0E252C;
}
#review_form .cr-upload-local-images .cr-upload-images-status,
.cr-upload-local-images .cr-upload-images-status {
	display: block;
	margin: 0px 0px 10px 0px;
	padding: 3px 5px;
}
#review_form .cr-upload-local-images #cr_review_image,
.cr-upload-local-images #cr_review_image {
	margin: 0px 0px 15px 0px;
	width: 100%;
}
#review_form .cr-upload-local-images .cr-upload-images-status.cr-upload-images-status-error,
.cr-upload-local-images .cr-upload-images-status.cr-upload-images-status-error {
	background-color: #FF4136;
	color: #303030;
}
#review_form .cr-upload-images-preview,
.cr-upload-images-preview {
	display: flex;
	margin: 0 0 0 0;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers,
.cr-upload-images-preview .cr-upload-images-containers {
	width: 75px;
	height: 75px;
	margin: 0 10px 10px 0;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	pointer-events: auto;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers .cr-upload-video-thumbnail,
.cr-upload-images-preview .cr-upload-images-containers .cr-upload-video-thumbnail {
	margin: 0;
	opacity: 0.3;
	padding: 15px;
	border: 1px solid #e0e0e0;
	fill: #e0e0e0;
	border-radius: 2px;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-delete-pending,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-delete-pending {
	pointer-events: none;
}
#review_form .cr-upload-images-preview .cr-upload-images-thumbnail,
.cr-upload-images-preview .cr-upload-images-thumbnail {
	max-width: 100%;
	max-height: 100%;
	margin: 0 0 0 0;
	opacity: 0.3;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok.cr-upload-delete-pending .cr-upload-images-thumbnail,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok.cr-upload-delete-pending .cr-upload-images-thumbnail,
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok.cr-upload-delete-pending .cr-upload-video-thumbnail,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok.cr-upload-delete-pending .cr-upload-video-thumbnail {
	opacity: 0.2;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-pbar,
.cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-pbar {
	width: 65px;
	height: 10px;
	margin: 0 5px 0 5px;
	padding: 0;
	position: absolute;
	top: 50%;
	left: 0;
	transform: translate(0, -50%);
	background-color: #dddddd;
	border-radius: 5px;
}
#review_form .cr-upload-images-preview .cr-upload-images-pbar .cr-upload-images-pbarin,
.cr-upload-images-preview .cr-upload-images-pbarin {
	width: 0;
	height: 100%;
	margin: 0;
	padding: 0;
	background-color: #4BBE67;
	border-radius: 5px;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-images-thumbnail,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-images-thumbnail {
	opacity: 1;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-video-thumbnail,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-video-thumbnail {
	opacity: 1;
	border-width: 0;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-images-pbar,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-images-pbar {
	display: none;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete,
.cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete {
	padding: 1px;
	position: absolute;
	top: 0px;
	right: 0px;
	width: 20px;
	height: 20px;
	display: none;
	background-color: #e7412c;
	border-radius: 10px;
	border: 0px solid;
	line-height: 0;
	cursor: pointer;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete:hover,
.cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete:hover {
	background-color: #db2c1b;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok .cr-upload-images-delete,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-delete-pending .cr-upload-images-delete {
	display: block;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-ok.cr-upload-delete-pending .cr-upload-images-delete,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-delete-pending .cr-upload-images-delete {
	opacity: 0.2;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete svg,
.cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete svg {
	padding: 0px;
	width: 18px;
	height: 18px;
	font-size: 18px;
	line-height: 18px;
	display: block;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete .cr-no-icon,
.cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete .cr-no-icon {
	fill: #FFFFFF;
}
#review_form .cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete-spinner,
.cr-upload-images-preview .cr-upload-images-containers .cr-upload-images-delete-spinner {
	margin: 0;
	background: url(../img/spinner-2x.gif) no-repeat;
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
	display: none;
	text-decoration: none;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
#review_form .cr-upload-images-preview .cr-upload-images-containers.cr-upload-delete-pending .cr-upload-images-delete-spinner,
.cr-upload-images-preview .cr-upload-images-containers.cr-upload-delete-pending .cr-upload-images-delete-spinner {
	display: block;
}
.cr-reviews-grid {
	padding: 20px 0 10px 0;
	border-width: 1px;
	border-style: solid;
	border-color: transparent;
	display: block;
	border-radius: 5px;
	margin: 0;
	width: 100%;
}
.cr-reviews-grid-inner {
	border-radius: inherit;
	display: flex;
}
.cr-reviews-grid-inner .cr-reviews-grid-col {
	flex-grow: 1;
	width: 0;
}
.cr-reviews-grid-inner .cr-reviews-grid-col.cr-reviews-grid-col-none {
	display: none;
}
.cr-reviews-grid-empty {
	border-radius: inherit;
	display: flex;
	padding: 0 10px;
	margin: 10px 0;
	justify-content: center;
}
.cr-reviews-slider{
	padding: 15px 0px;
	margin: 0 25px;
	opacity: 0;
	visibility: hidden;
	transition: opacity 1s ease;
	-webkit-transition: opacity 1s ease;
}

.cr-reviews-slider.slick-initialized {
	visibility: visible;
	opacity: 1;
}
.cr-reviews-grid .cr-review-card {
	padding: 0 0 0 0;
	border-width: 1px;
	border-style: solid;
	border-color: transparent;
	border-radius: 10px;
	margin: 0 10px 20px 10px;
	max-width: 100%;
	overflow: hidden;
	visibility: hidden;
	display: none;
}
.cr-reviews-grid .cr-reviews-grid-col .cr-review-card {
	display: block;
}
.cr-reviews-grid .cr-reviews-grid-inner.cr-colcade-loaded .cr-review-card {
	visibility: visible;
}

.cr-reviews-slider .cr-review-card {
	box-sizing: border-box;
	padding: 0px 10px;
	height: 100%;
}

.cr-reviews-slider .cr-review-card .cr-review-card-inner {
	margin: 0 auto;
	padding: 10px;
	border-width: 1px;
	border-style: solid;
	border-color: transparent;
	border-radius: inherit;
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;
}

.cr-reviews-grid .breaker {
	display: block;
	margin: 5px 0px;
	height: 0px;
	width: 100%;
}

.cr-reviews-grid .cr-review-card .image-row {
	display: block;
	width: 100%;
	position: relative;
	line-height: 0;
}

.cr-reviews-grid .cr-review-card .image-row .image-row-img {
	display: block;
	max-width: 100%;
	height: auto;
	border-style: none;
	border-radius: 0;
	margin: 0 auto;
	cursor: pointer;
}

.cr-reviews-grid .cr-review-card .image-row .image-row-vid {
	display: block;
	max-width: 100%;
	height: auto;
	border-style: none;
	border-radius: 0;
	margin: 0 auto;
	width: 100%;
	object-fit: cover;
	cursor: pointer;
}

.cr-reviews-grid .cr-review-card .image-row .image-row-img.image-row-img-none,
.cr-reviews-grid .cr-review-card .image-row .image-row-vid.image-row-vid-none {
	display: none;
}

.cr-ajax-reviews-video {
	display: block;
	position: relative;
	line-height: 0;
	cursor: pointer;
	text-align: center;
}

.cr-review-card-content .cr-comment-videoicon,
.cr-ajax-reviews-video .cr-comment-videoicon {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 40px;
	margin-top: -20px;
	margin-left: -20px;
	cursor: pointer;
}

#reviews.cr-reviews-ajax-reviews .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main .cr-comment-videoicon,
.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main .cr-comment-videoicon {
	max-width: none;
	max-height: none;
	width: 40px;
}

.cr-review-card-content .cr-vid-playing .cr-comment-videoicon,
.cr-ajax-reviews-video.cr-vid-playing .cr-comment-videoicon {
	display: none;
}

.cr-reviews-grid .cr-review-card .image-row .image-row-count,
.cr-reviews-grid .cr-review-card .image-row .media-row-count {
	position: absolute;
	max-width: 100%;
	width: 100%;
	height: 60px;
	border-style: none;
	border-radius: 0;
	bottom: 0;
	background: #20374199;
	color: #FFFFFF;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	gap: 5px;
}

.cr-reviews-grid .cr-review-card .image-row.cr-vid-playing .media-row-count {
	display: none;
}

.cr-reviews-grid .cr-review-card .top-row {
	display: flex;
	padding: 16px 0 16px 0;
	border-bottom: solid 2px #F4F4F4;
	margin: 0 15px 0 15px;
}
.pswp .cr-video-wrapper {
	display: flex;
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	align-items: center;
	justify-content: center;
}
.cr-reviews-slider .cr-review-card .top-row {
	display: flex;
	padding: 16px 0 16px 0;
	border-bottom: solid 2px #aaaaaa;
	margin: 0 10px 0 10px;
}

.cr-reviews-grid .cr-review-card .top-row .rating,
.cr-reviews-slider .cr-review-card .top-row .rating {
	flex: 0 0 50%;
}

.cr-reviews-grid .cr-review-card .top-row .reviewer,
.cr-reviews-slider .cr-review-card .top-row .reviewer {
	text-align: left;
	display: flex;
	flex-direction: column;
	justify-content: center;
	row-gap: 7px;
}

.cr-reviews-grid .cr-review-card .top-row .reviewer .reviewer-verified,
.cr-reviews-slider .cr-review-card .top-row .reviewer .reviewer-verified {
	display: flex;
	align-items: center;
	line-height: 1.2;
	font-size: 16px;
}

.cr-reviews-grid .cr-review-card .top-row .rating .datetime,
.cr-reviews-slider .cr-review-card .top-row .rating .datetime {
	font-size: 0.9em;
	color: #676767;
}

.cr-reviews-grid .cr-review-card .top-row .reviewer .reviewer-name,
.cr-reviews-slider .cr-review-card .top-row .reviewer .reviewer-name {
	font-family: sans-serif;
	font-style: normal;
	font-weight: bold;
	font-size: 18px;
	line-height: 1.2;
	color: #203741;
	display: flex;
	align-items: center;
}

.cr-reviews-grid .cr-review-card .top-row .reviewer .reviewer-name .ivole-grid-country-icon,
.cr-reviews-slider .cr-review-card .top-row .reviewer .reviewer-name .ivole-grid-country-icon {
	height: 15px;
	display: block;
	margin: 0 0 0 8px;
	border-radius: 3px;
}

.cr-reviews-grid .cr-review-card .top-row .reviewer .reviewer-verified {
	font-family: sans-serif;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
	letter-spacing: 0em;
	text-align: left;
	color: #18B394;
	display: flex;
	align-items: center;
	margin: 0;
}

.cr-reviews-grid .cr-review-card .top-row .reviewer .reviewer-verified .cr-reviewer-verified,
.cr-reviews-slider .cr-review-card .top-row .reviewer .reviewer-verified .cr-reviewer-verified {
	height: 16px;
	width: 16px;
	display: inline;
	vertical-align: middle;
	margin: 0 6px 0 0;
}

.rtl .cr-reviews-slider .cr-review-card .top-row .reviewer .reviewer-verified .cr-reviewer-verified {
	margin: 0 0 0 6px;
}

.cr-reviews-grid .cr-review-card .middle-row,
.cr-reviews-grid .cr-review-card .cr-incentivized-row {
	padding: 16px;
	background: #FCFCFC;
	border: 1px solid #F9F9F9;
	border-radius: 6px;
	box-sizing: border-box;
	font-family: sans-serif;
	font-style: normal;
	font-weight: 400;
	line-height: 130%;
	font-size: 16px;
	margin: 0 15px 15px 15px;
}
.cr-reviews-slider .cr-review-card .middle-row,
.cr-reviews-slider .cr-review-card .cr-incentivized-row {
	padding: 16px;
	background: #FCFCFC;
	border: 1px solid #F9F9F9;
	border-radius: 6px;
	box-sizing: border-box;
	font-family: sans-serif;
	font-style: normal;
	font-weight: 400;
	line-height: 130%;
	font-size: 16px;
	margin: 0 10px 15px 10px;
	flex: 1;
}
.cr-reviews-slider .cr-review-card .cr-incentivized-row {
	flex: 0;
}

.cr-reviews-grid .cr-review-card .top-row .review-thumbnail,
.cr-reviews-slider .cr-review-card .top-row .review-thumbnail {
	flex-basis: 56px;
	flex-shrink: 0;
	margin-right: 16px;
	position: relative;
	display: flex;
}

.cr-reviews-grid .cr-review-card .top-row .review-thumbnail img,
.cr-reviews-slider .cr-review-card .top-row .review-thumbnail img {
	max-width: 50px;
	max-height: 50px;
	border-radius: 25px;
}

.cr-reviews-grid .cr-review-card .rating-row {
	padding: 17px 0 17px 0;
	display: flex;
	align-items: center;
	margin: 0 15px 0 15px;
	line-height: 0;
}
.cr-reviews-slider .cr-review-card .rating-row {
	padding: 17px 0 17px 0;
	display: flex;
	align-items: center;
	margin: 0 10px 0 10px;
	overflow: hidden;
	line-height: 0;
}

.cr-reviews-grid .cr-review-card .rating-row .rating-label,
.cr-reviews-slider .cr-review-card .rating-row .rating-label {
	margin: 0 0 0 15px;
	font-family: sans-serif;
	font-size: 20px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px;
	letter-spacing: 0.1em;
	text-align: left;
	color: #898F92;
}

.rtl .cr-reviews-slider .cr-review-card .rating-row .rating-label {
	margin: 0 16px 0 0;
}

.cr-reviews-grid .cr-review-card .middle-row .review-content,
.cr-reviews-slider .cr-review-card .middle-row .review-content {
	margin: 0 0 12px 0;
	color: #484E51;
}

.cr-reviews-grid .cr-review-card .middle-row .review-content p,
.cr-reviews-slider .cr-review-card .middle-row p {
	margin: 0;
}

.cr-reviews-grid .cr-review-card .middle-row .datetime,
.cr-reviews-slider .cr-review-card .middle-row .datetime {
	font-weight: 300;
	font-size: 14px;
	color: #898F92;
}

.cr-reviews-grid .cr-review-card .verified-review-row,
.cr-reviews-slider .cr-review-card .verified-review-row {
	margin: 0 0 12px 0;
	text-align: left;
	font-family: sans-serif;
}

.cr-reviews-grid .cr-review-card .verified-review-row p.ivole-verified-badge,
.cr-reviews-slider .cr-review-card .verified-review-row p.ivole-verified-badge {
	margin: 0px;
}

.cr-reviews-grid .cr-review-card .verified-review-row .verified-badge-empty,
.cr-reviews-slider .cr-review-card .verified-review-row .verified-badge-empty {
	min-height: 20px;
}

.cr-reviews-grid .cr-review-card .review-product,
.cr-reviews-slider .cr-review-card .review-product {
	border-radius: inherit;
	display: flex;
	padding: 10px;
	margin-top: 10px;
	line-height: 1;
}

.cr-reviews-slider .cr-review-card .cr-sldr-custom-questions {
	border-radius: inherit;
	display: block;
	padding: 0;
	margin: 0 10px 17px 10px;
	line-height: 1;
	border-top: 1px dashed #aaaaaa;
	border-bottom: 1px dashed #aaaaaa;
	font-size: 14px;
}

.cr-reviews-grid .cr-review-card .review-product .product-thumbnail,
.cr-reviews-slider .cr-review-card .review-product .product-thumbnail {
	flex: 0 0 20%;
}

.cr-reviews-grid .cr-review-card .review-product .product-thumbnail img,
.cr-reviews-slider .cr-review-card .review-product .product-thumbnail img {
	width: 50px;
	height: 50px;
	max-width: none;
	max-height: none;
}

.cr-reviews-grid .cr-review-card .review-product .product-title,
.cr-reviews-slider .cr-review-card .review-product .product-title {
	margin: auto 0;
	padding-left: 5px;
	font-family: Helvetica;
	font-size: 16px;
	font-style: normal;
	font-weight: 700;
	line-height: 21px;
	letter-spacing: 0em;
	text-align: left;
	color: #203741;
}

.rtl .cr-reviews-slider .cr-review-card .review-product .product-title {
	padding-left: 0;
	padding-right: 5px;
}

p.ivole-verified-badge {
	font-size: 12px;
	vertical-align:middle;
}

span.ivole-verified-badge-text {
	vertical-align:middle;
}

.woocommerce #content div.product #reviews .comment img.ivole-verified-badge-icon,
.woocommerce div.product #reviews .comment img.ivole-verified-badge-icon,
.woocommerce-page #content div.product #reviews .comment img.ivole-verified-badge-icon,
.woocommerce-page div.product #reviews .comment img.ivole-verified-badge-icon,
#reviews p.ivole-verified-badge img.ivole-verified-badge-icon,
p.ivole-verified-badge img.ivole-verified-badge-icon,
#reviews img.ivole-verified-badge-icon,
img.ivole-verified-badge-icon {
	float: none;
	height: 20px;
	width: 17px;
	vertical-align:middle;
	margin: 0px 5px 0px 0px;
	display: inline;
	border-radius: 0;
}

.woocommerce #content div.product #reviews .comment img.ivole-verified-badge-ext-icon,
.woocommerce div.product #reviews .comment img.ivole-verified-badge-ext-icon,
.woocommerce-page #content div.product #reviews .comment img.ivole-verified-badge-ext-icon,
.woocommerce-page div.product #reviews .comment img.ivole-verified-badge-ext-icon,
#reviews p.ivole-verified-badge img.ivole-verified-badge-ext-icon,
p.ivole-verified-badge img.ivole-verified-badge-ext-icon,
#reviews img.ivole-verified-badge-ext-icon,
img.ivole-verified-badge-ext-icon {
	float: none;
	height: 11px;
	width: 11px;
	vertical-align:middle;
	margin: 0px 0px 0px 5px;
	display: inline;
	border-radius: 0;
}

.cr-reviews-slider .cr-review-card .cr-sldr-custom-question {
	display: flex;
	align-items: center;
	margin: 15px 0;
	column-gap: 10px;
}

.cr-reviews-slider .cr-review-card .cr-sldr-custom-question.cr-sldr-checkbox {
	flex-direction: column;
	align-items: flex-start;
}

.cr-reviews-slider .cr-review-card .cr-sldr-custom-question .cr-sldr-p {
	margin: 0;
}

.cr-reviews-slider .cr-review-card .cr-sldr-custom-question.cr-sldr-checkbox .cr-sldr-p {
	margin-bottom: 7px;
}

.cr-reviews-slider .cr-review-card .cr-sldr-custom-question.cr-sldr-checkbox ul {
	margin: 0 0 0 25px;
}

.cr-reviews-slider .cr-review-card .cr-sldr-custom-question .cr-sldr-label {
	font-weight: 550;
}

/* Slider */
@font-face
{
	font-family: 'slick';
	font-weight: normal;
	font-style: normal;
	font-display: swap;
	src: url('../fonts/slick.eot');
	src: url('../fonts/slick.eot?#iefix') format('embedded-opentype'), url('../fonts/slick.woff') format('woff'), url('../fonts/slick.ttf') format('truetype'), url('../fonts/slick.svg#slick') format('svg');
}
.slick-slider
{
	position: relative;

	display: block;
	box-sizing: border-box;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	-webkit-touch-callout: none;
	-khtml-user-select: none;
	-ms-touch-action: pan-y;
	touch-action: pan-y;
	-webkit-tap-highlight-color: transparent;
}

.cr-reviews-slider .slick-list
{
	position: relative;
	display: block;
	overflow: hidden;
	margin: 0;
	padding: 0;
	opacity: 1;
}
.slick-list:focus
{
	outline: none;
}
.slick-list.dragging
{
	cursor: pointer;
}

.slick-slider .slick-track,
.slick-slider .slick-list
{
	-webkit-transform: translate3d(0, 0, 0);
	-moz-transform: translate3d(0, 0, 0);
	-ms-transform: translate3d(0, 0, 0);
	-o-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.cr-reviews-slider.slick-slider .slick-track {
	display: flex;
}
.cr-reviews-slider.slick-slider .slick-slide {
	height: inherit;
}
.cr-reviews-slider.slick-slider .slick-slide > div {
	height: 100%;
}
.cr-ajax-reviews-cus-images-slider-nav.cr-reviews-slider.slick-slider .slick-slide > div {
	line-height: 0;
}
.slick-track {
	position: relative;
	top: 0;
	left: 0;

	display: block;
	margin-left: auto;
	margin-right: auto;
}
.slick-track:before,
.slick-track:after
{
	display: table;

	content: '';
}
.slick-track:after
{
	clear: both;
}
.slick-loading .slick-track
{
	visibility: hidden;
}

.slick-slide
{
	display: none;
	float: left;

	height: 100%;
	min-height: 1px;
}
[dir='rtl'] .slick-slide
{
	float: right;
}
.slick-slide img
{
	display: block;
}
.slick-slide.slick-loading img
{
	display: none;
}
.slick-slide.dragging img
{
	pointer-events: none;
}
.slick-initialized .slick-slide
{
	display: block;
}
.slick-loading .slick-slide
{
	visibility: hidden;
}
.slick-vertical .slick-slide
{
	display: block;

	height: auto;

	border: 1px solid transparent;
}
.slick-arrow.slick-hidden {
	display: none;
}

.cr-reviews-slider .slick-arrow.slick-prev,
.cr-reviews-slider .slick-arrow.slick-next,
.cr-ajax-reviews-cus-images-modal .cr-reviews-slider.cr-ajax-reviews-cus-images-slider-nav .slick-prev,
.cr-ajax-reviews-cus-images-modal .cr-reviews-slider.cr-ajax-reviews-cus-images-slider-nav .slick-next
{
	font-size: 0;
	line-height: 0;

	position: absolute;
	top: 50%;

	display: block;

	width: 20px;
	height: 20px;
	padding: 0;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	transform: translate(0, -50%);

	cursor: pointer;

	color: transparent;
	border: none;
	outline: none;
	background: transparent;
}

.cr-reviews-slider .slick-arrow.slick-prev:not(:hover),
.cr-reviews-slider .slick-arrow.slick-next:not(:hover) {
	background: transparent;
}

.cr-reviews-slider .slick-prev:hover,
.cr-reviews-slider .slick-prev:focus,
.cr-reviews-slider .slick-next:hover,
.cr-reviews-slider .slick-next:focus
{
	color: transparent;
	outline: none;
	background: transparent;
}
.cr-reviews-slider .slick-prev:hover:before,
.cr-reviews-slider .slick-prev:focus:before,
.cr-reviews-slider .slick-next:hover:before,
.cr-reviews-slider .slick-next:focus:before
{
	opacity: 1;
}
.cr-reviews-slider .slick-prev.slick-disabled:before,
.cr-reviews-slider .slick-next.slick-disabled:before
{
	opacity: .25;
}

.cr-reviews-slider .slick-prev:before,
.cr-reviews-slider .slick-next:before
{
	font-family: 'slick';
	font-size: 20px;
	line-height: 1;

	opacity: .75;
	color:grey;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.cr-reviews-slider.slick-slider .slick-prev
{
	left: -25px;
}
[dir='rtl'] .cr-reviews-slider .slick-prev
{
	right: -25px;
	left: auto;
}
.cr-reviews-slider .slick-prev:before
{
	content: '←';
}
[dir='rtl'] .cr-reviews-slider .slick-prev:before
{
	content: '→';
}

.cr-reviews-slider.slick-slider .slick-next
{
	right: -25px;
}
[dir='rtl'] .cr-reviews-slider .slick-next
{
	right: auto;
	left: -25px;
}
.cr-reviews-slider .slick-next:before
{
	content: '→';
}
[dir='rtl'] .cr-reviews-slider .slick-next:before
{
	content: '←';
}

.slick-dotted.slick-slider
{
	margin-bottom: 30px;
}

.cr-reviews-slider .slick-dots,
.cr-ajax-reviews-cus-images-modal .cr-reviews-slider .slick-dots
{
	position: absolute;
	bottom: -25px;

	display: block;

	width: 100%;
	padding: 0;
	margin: 0;

	list-style: none;
	text-align: center;
	border: none;
}
.cr-reviews-slider .slick-dots li
{
	position: relative;
	display: inline-block;
	width: 20px;
	height: 20px;
	margin: 0 5px;
	padding: 0;
	cursor: pointer;
}
#reviews.cr-reviews-ajax-reviews div.cr-ajax-reviews-cus-images-modal div.cr-ajax-reviews-cus-images-slider-nav .slick-dots li
{
	position: relative;
	display: inline-block;
	width: 20px;
	height: 20px;
	margin: 0 5px;
	padding: 0;
	cursor: pointer;
}
.cr-reviews-slider .slick-dots li button,
.cr-ajax-reviews-cus-images-modal .cr-reviews-slider .slick-dots li button
{
	font-size: 0;
	line-height: 0;

	display: block;

	width: 20px;
	height: 20px;
	padding: 5px;

	cursor: pointer;

	color: transparent;
	border: 0;
	outline: none;
	background: transparent;
}
.cr-reviews-slider .slick-dots li button:not(:hover),
.cr-ajax-reviews-cus-images-modal .cr-reviews-slider .slick-dots li button:not(:hover) {
	background: transparent;
}
.cr-reviews-slider .slick-dots li button:hover,
.cr-reviews-slider .slick-dots li button:focus
{
	outline: none;
}
.cr-reviews-slider .slick-dots li button:hover:before,
.cr-reviews-slider .slick-dots li button:focus:before
{
	opacity: 1;
}
.cr-reviews-slider .slick-dots li button:before
{
	font-family: 'slick';
	font-size: 6px;
	line-height: 20px;

	position: absolute;
	top: 0;
	left: 0;

	width: 20px;
	height: 20px;

	content: '•';
	text-align: center;

	opacity: .25;
	color: black;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.cr-reviews-slider .slick-dots li.slick-active button:before
{
	opacity: .75;
	color: black;
}
.cr-qna-separator {
	display: inline;
	margin: 0 6px;
}
.cr-all-reviews-pagination {
	text-align: center;
	font-size: 15px;
}
.cr-all-reviews-pagination.cr-pagination-load {
	pointer-events: none;
}
.cr-all-reviews-pagination .cr-page-numbers-a {
	cursor: pointer;
}
.cr-all-reviews-pagination .cr-page-numbers-a.next,
.cr-all-reviews-pagination .cr-page-numbers-a.prev {
	text-decoration: none;
}
.cr-all-reviews-pagination .cr-page-numbers.current {
	font-weight: bold;
}
@media all and (max-width: 700px) {
	#reviews.cr-reviews-ajax-reviews div.cr-ajax-reviews-cus-images-modal div.cr-ajax-reviews-slide-main-flex,
	.cr-all-reviews-shortcode .cr-ajax-reviews-cus-images-modal .cr-ajax-reviews-slide-main-flex {
		flex-direction: column;
	}
	#reviews.cr-reviews-ajax-reviews div.cr-ajax-reviews-cus-images-modal div.cr-ajax-reviews-cus-images-slider-main .cr-ajax-reviews-slide-main-comment {
		width: 100%;
		padding-top: 10px;
		padding-right: 10px;
		padding-left: 10px;
		max-width: 400px;
		margin: 0 auto;
	}
	#reviews.cr-reviews-ajax-reviews div.cr-ajax-reviews-cus-images-modal {
		overflow-y: scroll;
	}
}
@media all and (max-width: 600px) {
	#reviews.cr-reviews-ajax-reviews div.cr-ajax-reviews-cus-images-modal {
		width: 100%;
		height: 100%;
		left: 0px;
		margin-left: 0px;
		margin-top: 0px;
		top: 0px;
		max-width: 100%;
		max-height: 100%;
	}
	#reviews.cr-reviews-ajax-reviews div.cr-ajax-reviews-cus-images-modal div.cr-ajax-reviews-cus-images-slider-nav div.cr-ajax-reviews-slide-nav img {
		width: 80px;
	}
}
@media all and (max-width: 415px) {
	#reviews.cr-reviews-ajax-reviews div.cr-ajax-reviews-cus-images-modal div.cr-ajax-reviews-cus-images-slider-nav div.cr-ajax-reviews-slide-nav img {
		width: 60px;
	}
	.cr-qna-block .cr-qna-search-block {
		flex-wrap: wrap;
	}
	.cr-qna-block .cr-qna-search-block div {
		width: 100%;
	}
	.cr-qna-block .cr-qna-search-block button.cr-qna-ask-button {
		width: 100%;
		margin-top: 10px;
		margin-bottom: 0px;
		margin-left: 0px;
		margin-right: 0px;
	}
	.cr-all-reviews-shortcode ol.commentlist li .comment-text .cr-rating-product-name {
		flex-direction: column;
		align-items: flex-start;
	}
}
@media all and (max-width: 370px) {
	.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b .cr-qna-list-q-b-r {
		display: block;
	}
	.cr-qna-block .cr-qna-list-block .cr-qna-list-q-cont .cr-qna-list-q-b .cr-qna-list-q-b-r .cr-qna-q-voting {
		margin-top: 15px;
	}
}
@media only screen and (max-width: 800px) {
	.cr-reviews-grid-inner .cr-reviews-grid-col3 {
		display: none;
	}
}
@media only screen and (max-width: 550px) {
	.cr-reviews-grid-inner .cr-reviews-grid-col2 {
		display: none;
	}
}

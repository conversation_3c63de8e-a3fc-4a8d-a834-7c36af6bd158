from os.path import join, dirname
from typing import Optional, Dict, Any
import logging
from datetime import datetime

# https://github.com/langchain-ai/langchain
from langchain.agents import tool
# https://github.com/langchain-ai/langchain/tree/master/libs/partners/ollama
from langchain_ollama import ChatOllama
# https://github.com/langchain-ai/langgraph
from langgraph.prebuilt import create_react_agent
# https://github.com/langchain-ai/langgraph-supervisor-py
from langgraph_supervisor import create_supervisor
# https://github.com/theskumar/python-dotenv
from dotenv import load_dotenv
# https://requests.readthedocs.io/en/latest/
import requests

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SEOConfig:
    """Configuration centralisée pour le système SEO."""

    # Configuration Ollama
    OLLAMA_BASE_URL: str = "https://ollama.com"
    OLLAMA_MODEL: str = "gpt-oss:20b"
    OLLAMA_TEMPERATURE: float = 0.1

    # Configuration Jina AI
    JINA_TIMEOUT: int = 30
    JINA_MAX_RETRIES: int = 3

    # Fichiers de sortie
    OUTPUT_DIR: str = "audits"
    OUTPUT_FORMAT: str = "md"


@tool
def web_scrape(url: str) -> str:
    """
    Récupère le contenu textuel d'une page web en utilisant l'API Jina AI.

    Cette fonction prend une URL en entrée, la soumet à l'API Jina AI (r.jina.ai)
    pour en extraire le contenu principal sous forme de texte brut (Markdown).
    Elle gère les erreurs de requête avec retry et retourne un message d'erreur en cas d'échec.

    Args:
        url (str): L'URL de la page web à scraper.

    Returns:
        str: Le contenu textuel de la page en cas de succès, ou un message
             d'erreur si la requête échoue.
    """
    jina_url = f"https://r.jina.ai/{url}"
    headers = {
        "X-No-Cache": "false",
        "X-With-Generated-Alt": "true",
        "X-Return-Format": "markdown",
    }

    for attempt in range(SEOConfig.JINA_MAX_RETRIES):
        try:
            logger.info("Tentative %d/%d pour scraper: %s", attempt + 1, SEOConfig.JINA_MAX_RETRIES, url)

            response = requests.get(
                url=jina_url,
                headers=headers,
                timeout=SEOConfig.JINA_TIMEOUT
            )
            response.raise_for_status()

            content = response.text
            logger.info("Scraping réussi. Taille du contenu: %d caractères", len(content))
            return content

        except requests.Timeout:
            logger.warning("Timeout lors du scraping (tentative %d)", attempt + 1)
            if attempt == SEOConfig.JINA_MAX_RETRIES - 1:
                return f"❌ Erreur: Timeout après {SEOConfig.JINA_MAX_RETRIES} tentatives pour {url}"

        except requests.HTTPError as e:
            logger.error("Erreur HTTP %s: %s", e.response.status_code, e)
            return f"❌ Erreur HTTP {e.response.status_code} lors du scraping de {url}"

        except requests.RequestException as e:
            logger.error("Erreur de requête: %s", e)
            if attempt == SEOConfig.JINA_MAX_RETRIES - 1:
                return f"❌ Erreur de scraping: {str(e)}"

    return f"❌ Échec du scraping après {SEOConfig.JINA_MAX_RETRIES} tentatives"


@tool
def analyze_seo_metrics(content: str) -> str:
    """
    Analyse les métriques SEO basiques du contenu.

    Args:
        content (str): Contenu de la page au format Markdown

    Returns:
        str: Rapport des métriques SEO
    """
    lines = content.split('\n')
    word_count = len(content.split())

    # Extraction des titres
    h1_count = sum(1 for line in lines if line.startswith('# '))
    h2_count = sum(1 for line in lines if line.startswith('## '))
    h3_count = sum(1 for line in lines if line.startswith('### '))

    # Extraction des liens
    link_count = content.count('[')

    metrics = f"""
📊 **Métriques SEO basiques:**
- Nombre de mots: {word_count}
- Titres H1: {h1_count}
- Titres H2: {h2_count}
- Titres H3: {h3_count}
- Liens détectés: {link_count}
"""
    return metrics


class SEOAuditSystem:
    """Système d'audit SEO avec agents IA."""

    def __init__(self, config: Optional[SEOConfig] = None):
        """
        Initialise le système d'audit SEO.

        Args:
            config: Configuration personnalisée (utilise SEOConfig par défaut)
        """
        self.config = config or SEOConfig()
        self.llm = self._initialize_llm()
        self.workflow = self._create_workflow()

    def _initialize_llm(self) -> ChatOllama:
        """Initialise le modèle de langage."""
        logger.info("Initialisation du LLM: %s", self.config.OLLAMA_MODEL)
        return ChatOllama(
            base_url=self.config.OLLAMA_BASE_URL,
            model=self.config.OLLAMA_MODEL,
            temperature=self.config.OLLAMA_TEMPERATURE,
        )

    def _create_workflow(self):
        """Crée le workflow d'agents avec supervision."""
        logger.info("Création du workflow d'agents")

        # Agent de scraping web
        web_scrape_agent = create_react_agent(
            model=self.llm,
            tools=[web_scrape, analyze_seo_metrics],
            name="web_scrape_expert",
            prompt=(
                "Vous êtes un expert en extraction et analyse de contenu web pour le SEO.\n\n"
                "**Vos responsabilités:**\n"
                "1. Extraire l'URL demandée par l'utilisateur\n"
                "2. Utiliser l'outil `web_scrape` pour récupérer le contenu\n"
                "3. Utiliser l'outil `analyze_seo_metrics` pour analyser les métriques basiques\n"
                "4. Fournir un résumé structuré du contenu récupéré\n\n"
                "**Format de réponse attendu:**\n"
                "- URL analysée\n"
                "- Statut du scraping (succès/échec)\n"
                "- Métriques SEO extraites\n"
                "- Aperçu du contenu"
            ),
        )

        # Superviseur
        workflow = create_supervisor(
            agents=[web_scrape_agent],
            model=self.llm,
            output_mode="full_history",
            add_handoff_back_messages=True,
            prompt=(
                "Vous êtes le superviseur d'une équipe d'experts SEO.\n\n"
                "**Votre rôle:**\n"
                "- Coordonner les agents pour réaliser des audits SEO complets\n"
                "- Utiliser `web_scrape_expert` pour extraire et analyser le contenu\n"
                "- Synthétiser les résultats de manière claire et actionnable\n"
                "- Fournir des recommandations SEO concrètes\n\n"
                "**Critères d'audit SEO à évaluer:**\n"
                "1. Structure du contenu (titres, paragraphes)\n"
                "2. Optimisation des mots-clés\n"
                "3. Qualité et longueur du contenu\n"
                "4. Présence de liens internes/externes\n"
                "5. Lisibilité et formatage\n"
                "6. Recommandations d'amélioration"
            ),
        )

        return workflow.compile()

    def audit_url(self, url: str, save_result: bool = True) -> Dict[str, Any]:
        """
        Réalise un audit SEO complet d'une URL.

        Args:
            url: URL de la page à auditer
            save_result: Sauvegarder le résultat dans un fichier

        Returns:
            Dictionnaire contenant les résultats de l'audit
        """
        logger.info("Démarrage de l'audit SEO pour: %s", url)

        try:
            result = self.workflow.invoke({
                "messages": [{
                    "role": "user",
                    "content": f"Réalise un audit SEO complet et détaillé de cette page web : {url}"
                }]
            })

            audit_content = result['messages'][-1].content

            if save_result:
                self._save_audit(url, audit_content)

            logger.info("Audit SEO terminé avec succès")
            return {
                "success": True,
                "url": url,
                "content": audit_content,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error("Erreur lors de l'audit: %s", e)
            return {
                "success": False,
                "url": url,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _save_audit(self, url: str, content: str) -> None:
        """Sauvegarde les résultats de l'audit dans un fichier."""
        from pathlib import Path

        # Créer le dossier de sortie si nécessaire
        output_dir = Path(self.config.OUTPUT_DIR)
        output_dir.mkdir(exist_ok=True)

        # Générer un nom de fichier unique
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        domain = url.split('/')[2].replace('.', '_')
        filename = f"audit_seo_{domain}_{timestamp}.{self.config.OUTPUT_FORMAT}"
        filepath = output_dir / filename

        # Sauvegarder avec métadonnées
        full_content = f"""# Audit SEO - {url}
Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

---

{content}
"""

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(full_content)

        logger.info("✅ Résultats sauvegardés dans: %s", filepath)


def main():
    """Point d'entrée principal du programme."""
    # Charger les variables d'environnement
    dotenv_path = join(dirname(__file__), ".env")
    load_dotenv(dotenv_path)

    # Initialiser le système d'audit
    seo_system = SEOAuditSystem()

    # Exemples d'utilisation
    urls_to_audit = [
        "https://zonetuto.fr/intelligence-artificielle/generation-gratuit-image-openrouter-nano-banana/",
    ]

    for url in urls_to_audit:
        result = seo_system.audit_url(url, save_result=True)

        if result["success"]:
            print(f"\n{'='*80}")
            print(f"✅ Audit réussi pour: {url}")
            print(f"{'='*80}\n")
        else:
            print(f"\n{'='*80}")
            print(f"❌ Échec de l'audit pour: {url}")
            print(f"Erreur: {result['error']}")
            print(f"{'='*80}\n")


if __name__ == "__main__":
    main()

"use strict";this.default_tr=this.default_tr||{};(function(_){var window=this;
try{
_.la("navigationui");
var Yx=function(a,b){b=_.rg(b);b!==void 0&&a.open(b,void 0,void 0)},$x=function(a){var b=[new Zx];if(b.length===0)throw Error("v");if(b.map(function(c){if(c instanceof Zx)c=c.g;else throw Error("v");return c}).every(function(c){return"translate".indexOf(c)!==0}))throw Error("w`translate");a.setAttribute("translate","no")},ay=function(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(function(k){return e(k,h)}):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}var f=b.length?
"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(function(g){return e(g[1],g[0])}):d.forEach(e);return _.Na(a+b+c)},by=function(a){var b=a,c;return function(){if(b){var d=b;b=void 0;c=d.apply(this,arguments)}return c}},cy=function(){return function(a){a=a.data.XWlnHf;var b=window.google.translate.TranslateService.getInstance();switch(a.type){case "translate":b.translatePage(a.sourceLanguage,a.targetLanguage,function(){});break;case "restore":b.restore()}}},ey=function(a){var b=
0;return function(){var c=window.scrollY;b!==0&&c!==0||a.style.width!=="100%"||a.style.height==="100%"||dy(a);b=c}},hy=function(a){return function(b){switch(b.data.Ib5Lyf){case "open":a.classList.add("DNkvFf");break;case "closed":a.classList.remove("DNkvFf");break;default:a.classList.remove("DNkvFf")}fy(a,!gy.matches)}},fy=function(a,b){a.classList.contains("DNkvFf")&&b?document.documentElement.style.overflow="hidden":document.documentElement.style.overflow=iy},my=function(a,b){return function(c){c=
c.data.XAAHIb;c==="cleanup"?jy=new Set:jy.add(c);switch(c){case "topbar.exit":dy(a);a.style.transition="top 0.2s ease";document.body.style.transition="margin-top 0.2s ease";a.style.top=ky;document.body.style.marginTop="0";break;case "bubble.shape":ly(a,b,{top:ky,transition:"top 0.5s ease"});break;case "bubble.entry":a.style.top="56px";break;case "bubble.exit":a.style.top=ky;break;case "topbar.shape":dy(a,{top:ky,transition:"top 0.2s ease"},{marginTop:"0",transition:"margin-top 0.2s ease"});break;
case "topbar.entry":a.style.top="0";document.body.style.marginTop="56px";break;case "cleanup":a.style.transition="none",document.body.style.transition="none"}}},py=function(a,b){return function(c){c=c.data.rcuQ6b;switch(c.type){case "topbar":c=c.source;c in ny&&(ny[c]===1?delete ny[c]:--ny[c]);c=jy.size;var d=Object.keys(ny).length;c===0&&d===0&&dy(a);break;case "fullscreen":c=c.source;ny[c]=c in ny?ny[c]+1:1;_.Si(a,oy);document.body.style.marginTop="56px";break;case "bubble":ly(a,b);break;default:dy(a)}}},
dy=function(a,b,c){b=b===void 0?{}:b;c=c===void 0?{}:c;_.Si(a,qy(window.scrollY===0?ry:sy,b));_.Si(document.body,qy({marginTop:"56px"},c))},ly=function(a,b,c){c=c===void 0?{}:c;b=b?qy(ty,{left:"16px",right:"auto"}):ty;_.Si(a,qy(b,c));document.body.style.marginTop="0"},qy=function(a,b){return Object.keys(b).length!==0?Object.assign({},a,b):a},vy=function(a){return uy.some(function(b){return b.test(a)})},zy=function(a,b){var c;var d=(c=a.formContent)==null?void 0:c.locale;c=d==null?void 0:d.split("-")[0];
d=d&&wy.includes(d)?d:c&&wy.includes(c)?c:void 0;d=(d!=null?d:"en").replaceAll("-","_").toLowerCase();var e;a=((e=a.initializationData)==null?0:e.useNightlyRelease)?"nightly":"live";var f;return(b==null?0:(f=b.getEnableAlohaBinarySplit)==null?0:f.call(b))?_.Zh(xy,a):_.Zh(yy,a,d)},Cy=function(a,b,c,d){if(Ay&&By===b)return Ay;By=b;var e=zy(a,d);return Ay=b.feedbackV2GlobalObject?Promise.resolve(b.feedbackV2GlobalObject):new Promise(function(f,g){var h=_.fd(document,"SCRIPT");_.Ta(h,e);h.onload=function(){b.feedbackV2GlobalObject?
f(b.feedbackV2GlobalObject):g(Error("Ua"))};h.onerror=function(){g(Error("Va`"+e.toString()))};c.body.appendChild(h)})},Fy=function(a,b,c,d){if(Dy&&Ey===b)return Dy;Ey=b;var e=zy(a,d);return Dy=b.feedbackV2GlobalObject?Promise.resolve(b.feedbackV2GlobalObject):new Promise(function(f,g){var h=_.fd(document,"SCRIPT");_.Ta(h,e);h.onload=function(){b.feedbackV2GlobalObject?f(b.feedbackV2GlobalObject):g(Error("Ua"))};h.onerror=function(){g(Error("Va`"+e.toString()))};c.body.appendChild(h)})},Gy=function(a,
b,c,d,e){e=e===void 0?!0:e;var f,g,h,k,m;return _.Fi(function(n){switch(n.g){case 1:return f=Date.now(),_.M(n,Cy(a,c,d,b),2);case 2:g=n.h;if(!(e||((k=a.initializationData)==null?0:k.useNightlyRelease)||((m=a.initializationData)==null?0:m.isLocalServer))){h=g.initializeFeedbackClient(a,f,b);n.Ka(3);break}return _.M(n,g.initializeFeedbackClientAsync(a,f,b),4);case 4:h=n.h;case 3:return h.initiateAloha(),n.return(h)}})},Hy=function(a,b,c,d){var e,f,g;return _.Fi(function(h){if(h.g==1)return e=Date.now(),
_.M(h,Fy(a,c,d.document,b),2);if(h.g!=3)return f=h.h,_.M(h,f.initializeFeedbackClientAsync(a,e,b,d),3);g=h.h;g.initiateAloha();return h.return(g)})},Iy=function(a,b,c){var d=!0;d=d===void 0?!0:d;var e,f,g,h,k,m,n,p,q,r;return _.Fi(function(u){e=c||_.t;if((f=b)==null?0:(h=(g=f).getEnableAlohaBinarySplit)==null?0:h.call(g)){k=e;if(k.isFormOpened)throw m=Error("Wa"),m.name="DuplicateFormError",m;k.isFormOpened=!0;a.callbacks=a.callbacks||{};n=a.callbacks.onClose||function(){};a.callbacks.onClose=function(A){k.isFormOpened=
!1;n(A)};try{return u.return(Hy(a,b,k,e))}catch(A){throw k.isFormOpened=!1,A;}}else{p=e;if(p.isFormOpened)throw q=Error("Wa"),q.name="DuplicateFormError",q;p.isFormOpened=!0;a.callbacks=a.callbacks||{};r=a.callbacks.onClose||function(){};a.callbacks.onClose=function(A){p.isFormOpened=!1;r(A)};try{return u.return(Gy(a,b,p,e.document,d))}catch(A){throw p.isFormOpened=!1,A;}}u.g=0})},Py=function(a,b){return _.Fi(function(c){return c.return(new Promise(function(d,e){Number.isInteger(Number(a))&&Number(a)>
0?(e=Jy(b!=null?b:"")+"/aloha_form_properties?productId="+a,Ky(e,function(f){var g=f.target;f=null;try{var h=JSON,k=h.stringify;if(g.g){var m=g.g.responseText;m.indexOf(")]}'\n")==0&&(m=m.substring(5));b:{if(_.t.JSON)try{var n=_.t.JSON.parse(m);break b}catch(p){}n=Ly(m)}}else n=void 0;f=_.Qi(My,k.call(h,n))}catch(p){k=new My,n=Ny(),n=Oy(n,13,!1),n=Oy(n,14,!0),n=Oy(n,15,!0),n=Oy(n,20,!1),f=_.N(k,1,n)}d(f)})):e(Error("Xa`"+a))}))})},Jy=function(a){return vy(a)?a:"https://www.google.com/tools/feedback"},
Sy=function(a){return function(b){b=b.data.N7Eqid;var c=String(Number("2147483646")+1),d=Qy().find(function(f){return f.Wa()==a.client});d=d&&_.Oi(d,6).trim()||"ProxyWebsiteTranslation";var e={};Ry({locale:"fr",productData:{WEBSITE_TRANSLATION_PROXY_URL:a.kh,WEBSITE_TRANSLATION_SOURCE_URL:a.Ma,SOURCE_LANGUAGE:a.cd,TARGET_LANGUAGE:a.dd}}).h((e.allowNonLoggedInFeedback=!0,e.bucket=d,e.customZIndex=c,e.productVersion=b,e))}},Ty=function(a){return a.some(function(b){return b.type==="childList"&&Array.from(b.addedNodes).some(function(c){return c.id===
"google-feedback-wizard"})})},Uy=function(a,b){var c=b.lg,d=b.Di,e=b.Mj;b=new Map([["parent",b.ge],["pfu",b.kh],["u",b.Ma],["sl",b.cd],["tl",b.dd],["opu",b.yk],["client",b.client]]);c&&b.set("hl",c);d&&b.set("lang",d);e&&b.set("op","true");a=_.Oa(a).toString();c=a.split(/[?#]/);d=/[?]/.test(a)?"?"+c[1]:"";return ay(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)},$y=function(a){switch(a){case "autopush":return _.Zh(Vy);case "dev":return _.Zh(Wy);case "staging":return _.Zh(Xy);case "local":return _.Zh(Yy);
default:return _.Zh(Zy)}},az=function(a){a=new URL(a.toString());return a.protocol+"//"+a.hostname},bz=function(a,b,c,d,e){return function(f){f.data.rcuQ6b&&a(f);f.data.N7Eqid&&b(f);f.data.XAAHIb&&c(f);f.data.Ib5Lyf&&d(f);f.data.XWlnHf&&e(f);f.Uh.send("DONE",void 0,void 0)}},Ly=function(a){try{return _.t.JSON.parse(a)}catch(b){}a=String(a);if(/^\s*$/.test(a)?0:/^[\],:{}\s\u2028\u2029]*$/.test(a.replace(/\\["\\\/bfnrtu]/g,"@").replace(/(?:"[^"\\\n\r\u2028\u2029\x00-\x08\x0a-\x1f]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)[\s\u2028\u2029]*(?=:|,|]|}|$)/g,
"]").replace(/(?:^|:|,)(?:[\s\u2028\u2029]*\[)+/g,"")))try{return eval("("+a+")")}catch(b){}throw Error("R`"+a);},cz={},dz={},ez=function(a,b){this.g=a===cz&&b||"";this.h=dz};ez.prototype.toString=function(){return this.g};var Zx=function(){this.g=fz[0].toLowerCase()};Zx.prototype.toString=function(){return this.g};
var gz=function(a){return new _.de(function(b,c){c(a)})},Ky=function(a,b){var c=new _.Jf;_.Mf.push(c);b&&c.K("complete",b);c.Cb("ready",c.O);c.B=2E3;c.D=!0;c.send(a,"GET","",{})},Oy=function(a,b,c){return _.oi(a,b,c==null?c:_.bi(c),!1)};var hz=function(a){this.g=a;a.then((0,_.B)(function(){},this),function(){},this)},iz=function(a,b){a.g.then(function(c){var d=c.startFeedback;if(!d)throw Error("Qa`startFeedback");return d.apply(c,b)})},lz=function(a,b,c){for(var d=Array(arguments.length-2),e=2;e<arguments.length;e++)d[e-2]=arguments[e];e=jz(a,b).then(function(f){return f.apply(null,d)},function(f){f=Error("Ra`"+b+"`"+a+"`"+f,{cause:f});delete kz[b];return gz(f)});return new hz(e)},kz={},jz=function(a,b){var c=kz[b];if(c)return c;
c=(c=_.Pb(b))?_.Ii(c):(new _.de(function(d,e){var f=(new _.$c(document)).createElement("SCRIPT");f.async=!0;_.Ta(f,_.Na(a instanceof ez&&a.constructor===ez&&a.h===dz?a.g:"type_error:Const"));f.onload=f.onreadystatechange=function(){f.readyState&&f.readyState!="loaded"&&f.readyState!="complete"||d()};f.onerror=function(g){e(Error("Sa`"+b+"`"+a,{cause:g}))};(document.head||document.getElementsByTagName("head")[0]).appendChild(f)})).then(function(){var d=_.Pb(b);if(!d)throw Error("Ta`"+b+"`"+a);return d});
return kz[b]=c};var mz={il:"https://www.google.com",Tl:"https://support.google.com",Cl:"https://play.google.com"},oz=function(){var a=this;this.g=[];this.h=[];this.initialize=by(function(){return _.Fi(function(b){if(b.g==1)return typeof document==="undefined"||document.requestStorageAccessFor===void 0||navigator.permissions===void 0||navigator.permissions.query===void 0||location.hostname.match(".+\\.google\\.com$")?b.return(Promise.resolve()):_.M(b,nz(a),2);a.g.length>0&&document.addEventListener("click",a.j);b.g=
0})});this.j=function(){if(!(a.h.length>0)){for(var b=_.z(a.g),c=b.next();!c.done;c=b.next()){c=c.value;try{a.h.push(document.requestStorageAccessFor(c))}catch(d){}}Promise.all(a.h).then(function(){}).catch(function(){}).finally(function(){a.reset()})}}};oz.prototype.reset=function(){document.removeEventListener("click",this.j)};
var nz=function(a){var b,c,d,e;return _.Fi(function(f){switch(f.g){case 1:b=_.z(Object.values(mz)),c=b.next();case 2:if(c.done){f.Ka(0);break}d=c.value;_.xi(f,5);return _.M(f,navigator.permissions.query({name:"top-level-storage-access",requestedOrigin:d}),7);case 7:e=f.h;e.state!=="granted"&&a.g.push(d);f.g=3;f.l=0;break;case 5:_.yi(f);f.Ka(0);break;case 3:c=b.next(),f.Ka(2)}})};(new oz).initialize();var pz=function(a){this.g=a};pz.prototype.h=function(a){iz(this.g,arguments)};
var Ry=function(a){a=a||{};a=lz(qz,"help.service.Lazy.create","101820",{apiKey:a.apiKey||a.apiKey,asxUiUri:a.asxUiUri||a.asxUiUri,environment:a.environment||a.environment,flow:a.flow||a.flow,frdProductData:a.frdProductData||a.frdProductData,frdProductDataSerializedJspb:a.Dm||a.frdProductDataSerializedJspb,helpCenterPath:a.helpCenterPath||a.helpCenterPath,locale:a.locale||a.locale||"fr".replace(/-/g,"_"),nonce:a.nonce||a.nonce,productData:a.productData||a.productData,receiverUri:a.receiverUri||a.receiverUri,
renderApiUri:a.renderApiUri||a.renderApiUri,theme:a.theme||a.theme,window:a.window||a.window});return new pz(a)},qz=new ez(cz,"https://www.gstatic.com/feedback/js/help/prod/service/lazy.min.js");jz(qz,"help.service.Lazy.create").D(function(){});var rz=parseInt("56px",10)+parseInt("1px",10)+"px",ky="-"+(parseInt("56px",10)+parseInt("5px",10)+"px"),gy=window.matchMedia("(min-width: 1280px)"),iy=document.documentElement.style.overflow||"auto",sz={border:"none",borderRadius:"0",boxShadow:"none",height:"auto",left:"auto",right:"auto",position:"fixed",top:"auto",transition:"none",width:"auto",zIndex:"2147483646"},ry=qy(sz,{height:rz,left:"0",top:"0",width:"100%"}),sy=qy(ry,{height:"56px",boxShadow:"0px 1px 2px rgba(60, 64, 67, 0.3), 0px 2px 6px 2px rgba(60, 64, 67, 0.15)"}),
oy=qy(ry,{height:"100%"}),ty=qy(sz,{borderRadius:"50%",boxShadow:"0px 4px 8px 3px rgba(60, 64, 67, 0.15), 0px 1px 3px rgba(60, 64, 67, 0.3)",height:"56px",left:"auto",right:"16px",top:"56px",width:"56px"}),jy=new Set,ny={};var tz=function(a){this.H=_.I(a)};_.y(tz,_.J);tz.prototype.Wa=function(){return _.Oi(this,1)};tz.prototype.Se=function(){return _.Pi(this,16)};var uz=function(a){this.H=_.I(a)};_.y(uz,_.J);
var Qy=function(){var a=vz||(vz=wz('[[["webapp","Translate Web App",null,null,"harpoon-rosetta","ProxyWebsiteTranslationWebapp","wt","wt_lib",1,null,"wapp",null,null,null,true,30],["srp","Toledo",null,null,"harpoon-search-translate","ProxyWebsiteTranslationToledo","wt_srp","wt_srp_lib",1,null,"tc",null,true,null,true,31],["search","Translate This Page","prev","search","harpoon-rosetta","ProxyWebsiteTranslationTranslateThisPage","wt_search","wt_search_lib",1,null,"sc",null,null,null,true,32],["sdn","Search Diner",null,null,"harpoon-rosetta","ProxyWebsiteTranslationDiner","wt_sdn","wt_sdn_lib",1,null,"sdn",null,null,null,null,33],["wa","Web Answers",null,null,"harpoon-rosetta","ProxyWebsiteTranslationWebAnswers","wt_wa","wt_wa_lib",1,"wt_search_lib","wa",null,true,null,null,35],["rq","Related Questions",null,null,"harpoon-rosetta","ProxyWebsiteTranslationRelatedQuestions","wt_rq","wt_rq_lib",1,"wt_search_lib","rq",null,true,null,true,41],["mauka","Mauka",null,null,"harpoon-rosetta","ProxyWebsiteTranslationMauka","wt_mk","wt_mk_lib",1,"wt_search_lib","mk",null,true,null,null,42],["nrp","Mundo News",null,null,"harpoon-rosetta","ProxyWebsiteTranslation","wt_mn","wt_mn_lib",1,"wt_search_lib","mn",null,null,null,null,43],["imgs","Image Search",null,null,"harpoon-rosetta","ProxyWebsiteTranslation","wt_imgs","wt_imgs_lib",1,"wt_search_lib","imgs",null,true,null,null,44],["sge","Search Generative Experience (aka Magi)",null,null,"harpoon-search-translate","ProxyWebsiteTranslation","wt_sge","wt_sge_lib",1,"wt_search_lib","sge",null,true,null,null,45],["tr","Translate Proxy Misc (scrapers, etc.)",null,null,null,"ProxyWebsiteTranslation",null,null,null,null,null,null,null,null,null,40],["demo","Translate Proxy Demo",null,null,null,"ProxyWebsiteTranslation",null,null,1,null,"demo"],["test","Translate Proxy Test",null,null,null,"ProxyWebsiteTranslation",null,null,1,null,"test",[[1,1],[2,1],[3,1]]],["chrome","Chrome",null,null,null,null,null,"te_lib",2,null,null,null,null,null,true,36],["go","Android Go / Assistant Go",null,null,null,null,null,"go_lib",2,null,null,null,null,null,null,39],["tee","Chrome Extension",null,null,null,null,"tee","tee",3,null,null,null,null,null,true,37],["te","Widget",null,null,null,null,null,"te",3,null,null,null,null,null,null,38],["sn","Search News",null,null,null,null,null,null,null,null,null,null,null,null,null,34]]]'));return _.Mi(a,
tz,1)};var wz=_.si(uz);var vz;var xz=function(a){this.H=_.I(a)};_.y(xz,_.J);_.l=xz.prototype;_.l.getEnableSsEngine=function(){return _.Ni(this,2)};_.l.getEnableAwr=function(){return _.Ni(this,3)};_.l.getAlohaAutoGaRollout=function(){return _.Ni(this,5)};_.l.getEnableConfigurator=function(){return _.Ni(this,6)};_.l.getEnableMweb=function(){return _.Ni(this,7)};var zz=function(){var a=yz();return Oy(a,7,!0)};xz.prototype.getEnableCtlConsentCheckbox=function(){return _.Ni(this,8)};
xz.prototype.getEnableIframe=function(){return _.Ni(this,9)};var yz=function(){var a=new xz;a=Oy(a,5,!0);a=Oy(a,2,!0);a=Oy(a,4,!1);a=Oy(a,8,!0);return Oy(a,9,!0)};xz.prototype.getEnableScreenshotNudge=function(){return _.Ni(this,10)};var Az=function(){var a=zz();return Oy(a,10,!0)};xz.prototype.getEnableWebStartupConfigEndpoint=function(){return _.Ni(this,11)};xz.prototype.getEnableJunkNudge=function(){return _.Ni(this,12)};var Ny=function(){var a=Az();return Oy(a,12,!0)};_.l=xz.prototype;
_.l.getEnableConfiguratorLocale=function(){return _.Ni(this,13)};_.l.getEnableTinyNoPointer=function(){return _.Ni(this,14)};_.l.getEnableSupportSessionLogging=function(){return _.Ni(this,15)};_.l.getEnableFileUploadForScreenshot=function(){return _.Ni(this,16)};_.l.getEnableDirectDeflectionForSingleCategory=function(){return _.Ni(this,17)};_.l.getEnableImageSanitization=function(){return _.Ni(this,18)};_.l.getEnableAlohaBinarySplit=function(){return _.Ni(this,19)};
_.l.getEnableDbFeedbackIntents=function(){return _.Ni(this,20)};_.l.getEnableMarkMandatoryFieldsWithRequired=function(){return _.Ni(this,21)};_.l.getEnableFeedbackCategoryCustomUi=function(){return _.Ni(this,22)};_.l.getEnableRealtimeCtl=function(){return _.Ni(this,23)};var My=function(a){this.H=_.I(a)};_.y(My,_.J);var uy=[/https:\/\/sandbox\.google\.com\/tools\/feedback/,/https:\/\/feedback-frontend-qual[a-z0-9.]*\.google\.com\/inapp/,/https:\/\/feedback-frontend-qual[a-z0-9.]*\.google\.com\/tools\/feedback/,/https:\/\/.*\.googleusercontent\.com\/inapp/];var wy="af am ar-EG ar-JO ar-MA ar-SA ar-XB ar az be bg bn bs ca cs cy da de-AT de-CH de el en en-GB en-AU en-CA en-IE en-IN en-NZ en-SG en-XA en-XC en-ZA es es-419 es-AR es-BO es-CL es-CO es-CR es-DO es-EC es-GT es-HN es-MX es-NI es-PA es-PE es-PR es-PY es-SV es-US es-UY es-VE et eu fa fi fil fr-CA fr-CH fr gl gsw gu he hi hr hu hy id in is it iw ja ka kk km kn ko ky ln lo lt lv mk ml mn mo mr ms my nb ne nl no pa pl pt pt-BR pt-PT ro ru si sk sl sq sr-Latn sr sv sw ta te th tl tr uk ur uz vi zh zh-CN zh-HK zh-TW zu".split(" ");var xy=_.L(["https://www.gstatic.com/uservoice/feedback/client/web/","/main_light_binary.js"]),yy=_.L(["https://www.gstatic.com/uservoice/feedback/client/web/","/main_binary__",".js"]);var Ay,Dy,By,Ey;var Bz=function(a,b,c){a.timeOfStartCall=(new Date).getTime();var d=c||_.t,e=d.document,f=a.nonce||_.Pa("script",d.document);f&&!a.nonce&&(a.nonce=f);if(a.flow=="help"){var g=_.Pb("document.location.href",d);!a.helpCenterContext&&g&&(a.helpCenterContext=g.substring(0,1200));g=!0;if(b&&JSON&&JSON.stringify){var h=JSON.stringify(b);(g=h.length<=1200)&&(a.psdJson=h)}g||(b={invalidPsd:!0})}b=[a,b,c];d.GOOGLE_FEEDBACK_START_ARGUMENTS=b;c=a.feedbackServerUri||"//www.google.com/tools/feedback";if(g=d.GOOGLE_FEEDBACK_START)g.apply(d,
b);else{d=c+"/load.js?";for(var k in a)b=a[k],b==null||_.za(b)||(d+=encodeURIComponent(k)+"="+encodeURIComponent(b)+"&");a=_.db(e).createElement("SCRIPT");f&&a.setAttribute("nonce",f);_.Ta(a,_.Na(d));e.body.appendChild(a)}},Cz=function(a,b,c,d){var e,f;_.Fi(function(g){e=c||_.t;var h=a.serverEnvironment==="DEV",k=c||_.t;k=a.nonce||_.Pa("script",k.document);h={integrationKeys:{productId:a.productId,feedbackBucket:a.bucket,triggerId:a.triggerId},callbacks:{onClose:a.callback,onLoad:a.onLoadCallback},
formContent:{locale:a.locale,disableScreenshot:a.disableScreenshotting,productDisplayName:void 0,announcement:void 0,issueCategories:void 0,includeSeveritySelection:void 0,customImageSrc:void 0,thankYouMessage:void 0,On:void 0,defaultFormInputValues:void 0,defaultFormInputValuesString:void 0,abuseLink:a.abuseLink,additionalDataConsent:a.additionalDataConsent},initializationData:{isLocalServer:h,nonce:k,useNightlyRelease:h,feedbackJsUrl:void 0,feedbackCssUrl:void 0,feedbackJsUrlSerialized:void 0,feedbackCssUrlSerialized:void 0,
submissionServerUri:a.feedbackServerUri,colorScheme:a.colorScheme},extraData:{productVersion:a.productVersion,authUser:a.authuser,configuratorId:a.configuratorId,customZIndex:a.customZIndex,tinyNoPointer:a.tinyNoPointer,allowNonLoggedInFeedback:a.allowNonLoggedInFeedback,enableAnonymousFeedback:a.enableAnonymousFeedback}};b&&(k=new Map(Object.entries(b)),h.extraData.productSpecificData=k);f=h;return _.M(g,Iy(f,d,e),0)})};
_.$b("userfeedback.api.startFeedback",function(a,b,c){try{if(a.flow==="help"){var d=a.helpCenterPath.replace(/^\//,"");Yx(c||window,"https://support.google.com/"+d)}else a.flow==="submit"?Bz(a,b,c):Py(a.productId,a.feedbackServerUri).then(function(e){e=_.Li(e,xz,1);var f=!_.Fc||(e==null?void 0:e.getEnableMweb()),g=!a.tinyNoPointer||(e==null?void 0:e.getEnableTinyNoPointer());!e||e.getAlohaAutoGaRollout()&&f&&g?Cz(a,b,c,e):Bz(a,b,c)},function(e){e&&e.name!=="DuplicateFormError"&&Bz(a,b,c)})}catch(e){Cz(a,
b,c,null)}});var fz=_.L(["translate"]);var Vy=_.L(["https://translate-autopush.corp.google.com/websitetranslationui"]),Wy=_.L(["https://translate-dev.corp.google.com/websitetranslationui"]),Xy=_.L(["https://translate-daily-1.corp.google.com/websitetranslationui"]),Yy=_.L(["https://translate.google.com/websitetranslationui"]),Zy=_.L(["https://translate.google.com/websitetranslationui"]);var Dz=Promise;var Ez=function(a){this.g=a};Ez.prototype.send=function(a,b,c){this.g.then(function(d){d.send(a,b,c)})};var Fz=function(a,b){this.data=a;this.Uh=b};var Gz=function(a){this.g=a};Gz.prototype.send=function(a,b,c){c=c===void 0?[]:c;var d=new MessageChannel;Hz(d.port1,b);this.g.postMessage(a,[d.port2].concat(c))};var Iz=function(a,b){Hz(a,b);return new Gz(a)},Hz=function(a,b){b&&(a.onmessage=function(c){b(new Fz(c.data,Iz(c.ports[0])))})};var Jz=function(a){this.g=a},Kz=function(a){var b=Object.create(null);(typeof a==="string"?[a]:a).forEach(function(c){if(c==="null")throw Error("Ya");b[c]=!0});return function(c){return b[c]===!0}};var Mz=function(a){var b=a.destination;var c=a.Sb;var d=a.origin;var e=a.yd===void 0?"ZNWN1d":a.yd;var f=a.onMessage===void 0?void 0:a.onMessage;a=a.be===void 0?void 0:a.be;return Lz({destination:b,Se:function(){return c.contentWindow},jk:d instanceof Jz?d:typeof d==="function"?new Jz(d):new Jz(Kz(d)),yd:e,onMessage:f,be:a})},Lz=function(a){var b=a.destination;var c=a.Se;var d=a.jk;var e=a.Ak===void 0?void 0:a.Ak;var f=a.yd;var g=a.onMessage===void 0?void 0:a.onMessage;var h=a.be===void 0?void 0:
a.be;return new Ez(new Dz(function(k,m){var n=function(p){p.source&&p.source===c()&&d.g(p.origin)&&(p.data.n||p.data)===f&&(b.removeEventListener("message",n,!1),e&&p.data.t!==e?m(Error("Za`"+f+"`"+e+"`"+p.data.t)):(k(Iz(p.ports[0],g)),h&&h(p)))};b.addEventListener("message",n,!1)}))};var Nz=document.currentScript.getAttribute("data-environment"),Oz=document.currentScript.getAttribute("data-proxy-url"),Pz=document.currentScript.getAttribute("data-proxy-full-url"),Qz=document.currentScript.getAttribute("data-source-url"),Rz=document.currentScript.getAttribute("data-source-language"),Sz=document.currentScript.getAttribute("data-target-language"),Tz=document.currentScript.getAttribute("data-display-language"),Uz=document.currentScript.getAttribute("data-detected-source-language"),
Vz=document.currentScript.getAttribute("data-is-source-untranslated"),Wz=document.currentScript.getAttribute("data-source-untranslated-url"),Xz=document.currentScript.getAttribute("data-client");
(function(a){var b=_.fd(document,"IFRAME"),c=$y(a.environment),d=Uy(c,a),e=_.kf.test(a.lg),f=ey(b),g=py(b,e);e=my(b,e);var h=hy(b);a=Sy(a);var k=cy();dy(b);b.title="Navigation avec Google\u00a0Traduction";b.allowTransparency="true";b.frameBorder="0";b.id="gt-nvframe";b.src=_.Oa(d).toString();document.body.prepend(b);var m=Mz({yd:"zREjac",destination:window,Sb:b,origin:az(c),onMessage:bz(g,a,e,h,k)});c={};m.send((c.woLtV="inprogress",c));_.E(window,"load",function(){var n={};m.send((n.woLtV="complete",
n))});_.E(window,"scroll",f);gy.addEventListener("change",function(n){return fy(b,!n.matches)})})({environment:Nz,ge:Oz,kh:Pz,Ma:Qz,cd:Rz,dd:Sz,lg:Tz,Di:Uz,Mj:Vz.toLowerCase()=="true",yk:Wz,client:Xz});
(function(a){if(a===void 0?0:a){var b=_.fd(document,"DIV");b.id="OQ2Y6";b.style.height="100%";b.style.width="100%";b.style.position="fixed";b.style.top="56px";b.style.left="0";b.style.zIndex=String(Number("2147483646")+1);b.style.backgroundColor="rgba(255, 255, 255, 0.6)";document.body.prepend(b);_.E(window,"load",function(){b.remove()})}})();var Yz={};(new MutationObserver(function(a){Ty(a)&&(a=document.getElementById("google-feedback-wizard"),$x(a))})).observe(document.body,(Yz.childList=!0,Yz));
_.na();
}catch(e){_._DumpException(e)}
}).call(this,this.default_tr);
// Google Inc.

"""
Configuration pytest pour les tests SEO AI Agents.

Ce fichier contient les fixtures et configurations communes
utilisées par tous les tests.
"""
import pytest
import sys
import os

# Ajouter le répertoire parent au path pour importer les modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

@pytest.fixture
def sample_url():
    """URL de test pour les tests de web scraping."""
    return "https://toscrape.com/"

@pytest.fixture
def sample_content():
    """Contenu de test pour les analyses SEO."""
    return """
# Titre principal
## Sous-titre 1
### Sous-titre 2
Ceci est un contenu de test avec des [liens](http://example.com) et du texte.
## Autre sous-titre
Plus de contenu pour tester les métriques SEO.
"""

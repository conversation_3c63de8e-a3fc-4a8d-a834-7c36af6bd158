
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"undefined"}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":2,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_autoEmailEnabled":true,"vtp_autoPhoneEnabled":false,"vtp_autoAddressEnabled":false,"vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":10},{"function":"__ccd_ga_first","priority":1,"vtp_instanceDestinationId":"UA-185909341-1","tag_id":13},{"function":"__rep","vtp_containerId":"UA-185909341-1","vtp_remoteConfig":["map"],"tag_id":1},{"function":"__zone","vtp_childContainers":["list",["map","publicId","G-Z8PYV0X4FJ"]],"vtp_enableConfiguration":false,"tag_id":3},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"UA-185909341-1","tag_id":12}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",2,3]],[["if",1],["add",0,4,1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_ga_first",[46,"a"],[50,"d",[46,"e"],[2,[15,"c"],"A",[7,[15,"e"]]]],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_taskPlatformDetection"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"e"],["d",[15,"e"]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"DJ"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"W"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"CH"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"BZ"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"X"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CH"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"CI"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__rep",[46,"a"],[52,"b",["require","internal.registerDestination"]],[22,[28,["b",[17,[15,"a"],"containerId"],[17,[15,"a"],"remoteConfig"]]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_custom_scripts"],[52,"v","allow_direct_google_requests"],[52,"w","allow_enhanced_conversions"],[52,"x","allow_google_signals"],[52,"y","auid"],[52,"z","aw_remarketing_only"],[52,"aA","discount"],[52,"aB","aw_feed_country"],[52,"aC","aw_feed_language"],[52,"aD","items"],[52,"aE","aw_merchant_id"],[52,"aF","aw_basket_type"],[52,"aG","client_id"],[52,"aH","conversion_cookie_prefix"],[52,"aI","conversion_id"],[52,"aJ","conversion_linker"],[52,"aK","conversion_api"],[52,"aL","cookie_deprecation"],[52,"aM","cookie_expires"],[52,"aN","cookie_prefix"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","developer_id"],[52,"aX","shipping"],[52,"aY","engagement_time_msec"],[52,"aZ","estimated_delivery_date"],[52,"bA","event_developer_id_string"],[52,"bB","event"],[52,"bC","event_timeout"],[52,"bD","first_party_collection"],[52,"bE","match_id"],[52,"bF","gdpr_applies"],[52,"bG","google_analysis_params"],[52,"bH","_google_ng"],[52,"bI","gpp_sid"],[52,"bJ","gpp_string"],[52,"bK","gsa_experiment_id"],[52,"bL","gtag_event_feature_usage"],[52,"bM","iframe_state"],[52,"bN","ignore_referrer"],[52,"bO","is_passthrough"],[52,"bP","_lps"],[52,"bQ","language"],[52,"bR","merchant_feed_label"],[52,"bS","merchant_feed_language"],[52,"bT","merchant_id"],[52,"bU","new_customer"],[52,"bV","page_hostname"],[52,"bW","page_path"],[52,"bX","page_referrer"],[52,"bY","page_title"],[52,"bZ","_platinum_request_status"],[52,"cA","quantity"],[52,"cB","restricted_data_processing"],[52,"cC","screen_resolution"],[52,"cD","send_page_view"],[52,"cE","server_container_url"],[52,"cF","session_duration"],[52,"cG","session_engaged_time"],[52,"cH","session_id"],[52,"cI","_shared_user_id"],[52,"cJ","delivery_postal_code"],[52,"cK","topmost_url"],[52,"cL","transaction_id"],[52,"cM","transport_url"],[52,"cN","update"],[52,"cO","_user_agent_architecture"],[52,"cP","_user_agent_bitness"],[52,"cQ","_user_agent_full_version_list"],[52,"cR","_user_agent_mobile"],[52,"cS","_user_agent_model"],[52,"cT","_user_agent_platform"],[52,"cU","_user_agent_platform_version"],[52,"cV","_user_agent_wow64"],[52,"cW","user_data"],[52,"cX","user_data_auto_latency"],[52,"cY","user_data_auto_meta"],[52,"cZ","user_data_auto_multi"],[52,"dA","user_data_auto_selectors"],[52,"dB","user_data_auto_status"],[52,"dC","user_data_mode"],[52,"dD","user_id"],[52,"dE","user_properties"],[52,"dF","us_privacy_string"],[52,"dG","value"],[52,"dH","_fpm_parameters"],[52,"dI","_host_name"],[52,"dJ","_in_page_command"],[52,"dK","non_personalized_ads"],[52,"dL","conversion_label"],[52,"dM","page_location"],[52,"dN","global_developer_id_string"],[52,"dO","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"F",[15,"e"],"H",[15,"f"],"I",[15,"g"],"J",[15,"h"],"K",[15,"i"],"L",[15,"j"],"X",[15,"k"],"AC",[15,"l"],"AD",[15,"m"],"AE",[15,"n"],"AG",[15,"o"],"AH",[15,"p"],"AJ",[15,"q"],"AN",[15,"r"],"AX",[15,"s"],"BE",[15,"t"],"BF",[15,"u"],"BG",[15,"v"],"BI",[15,"w"],"BJ",[15,"x"],"BP",[15,"y"],"BS",[15,"z"],"BT",[15,"aA"],"BU",[15,"aB"],"BV",[15,"aC"],"BW",[15,"aD"],"BX",[15,"aE"],"BY",[15,"aF"],"CG",[15,"aG"],"CL",[15,"aH"],"CM",[15,"aI"],"JS",[15,"dL"],"CN",[15,"aJ"],"CP",[15,"aK"],"CQ",[15,"aL"],"CS",[15,"aM"],"CW",[15,"aN"],"CX",[15,"aO"],"CY",[15,"aP"],"CZ",[15,"aQ"],"DA",[15,"aR"],"DB",[15,"aS"],"DC",[15,"aT"],"DD",[15,"aU"],"DH",[15,"aV"],"DI",[15,"aW"],"DU",[15,"aX"],"DW",[15,"aY"],"EA",[15,"aZ"],"ED",[15,"bA"],"EF",[15,"bB"],"EH",[15,"bC"],"EM",[15,"bD"],"EV",[15,"bE"],"FF",[15,"bF"],"JU",[15,"dN"],"FJ",[15,"bG"],"FK",[15,"bH"],"FN",[15,"bI"],"FO",[15,"bJ"],"FQ",[15,"bK"],"FR",[15,"bL"],"FT",[15,"bM"],"FU",[15,"bN"],"FZ",[15,"bO"],"GA",[15,"bP"],"GB",[15,"bQ"],"GI",[15,"bR"],"GJ",[15,"bS"],"GK",[15,"bT"],"GO",[15,"bU"],"GR",[15,"bV"],"JT",[15,"dM"],"GS",[15,"bW"],"GT",[15,"bX"],"GU",[15,"bY"],"HC",[15,"bZ"],"HE",[15,"cA"],"HI",[15,"cB"],"HM",[15,"cC"],"HP",[15,"cD"],"HR",[15,"cE"],"HT",[15,"cF"],"HV",[15,"cG"],"HW",[15,"cH"],"HY",[15,"cI"],"HZ",[15,"cJ"],"JV",[15,"dO"],"IE",[15,"cK"],"IH",[15,"cL"],"II",[15,"cM"],"IK",[15,"cN"],"IN",[15,"cO"],"IO",[15,"cP"],"IP",[15,"cQ"],"IQ",[15,"cR"],"IR",[15,"cS"],"IS",[15,"cT"],"IT",[15,"cU"],"IU",[15,"cV"],"IV",[15,"cW"],"IW",[15,"cX"],"IX",[15,"cY"],"IY",[15,"cZ"],"IZ",[15,"dA"],"JA",[15,"dB"],"JB",[15,"dC"],"JD",[15,"dD"],"JE",[15,"dE"],"JG",[15,"dF"],"JH",[15,"dG"],"JJ",[15,"dH"],"JK",[15,"dI"],"JL",[15,"dJ"],"JP",[15,"dK"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","allow_ad_personalization"],[52,"e","consent_state"],[52,"f","consent_updated"],[52,"g","conversion_linker_enabled"],[52,"h","cookie_options"],[52,"i","em_event"],[52,"j","event_start_timestamp_ms"],[52,"k","event_usage"],[52,"l","ga4_collection_subdomain"],[52,"m","handle_internally"],[52,"n","hit_type"],[52,"o","hit_type_override"],[52,"p","is_conversion"],[52,"q","is_external_event"],[52,"r","is_first_visit"],[52,"s","is_first_visit_conversion"],[52,"t","is_fpm_encryption"],[52,"u","is_fpm_split"],[52,"v","is_gcp_conversion"],[52,"w","is_google_signals_allowed"],[52,"x","is_server_side_destination"],[52,"y","is_session_start"],[52,"z","is_session_start_conversion"],[52,"aA","is_sgtm_ga_ads_conversion_study_control_group"],[52,"aB","is_sgtm_prehit"],[52,"aC","is_split_conversion"],[52,"aD","is_syn"],[52,"aE","prehit_for_retry"],[52,"aF","redact_ads_data"],[52,"aG","redact_click_ids"],[52,"aH","send_ccm_parallel_ping"],[52,"aI","send_user_data_hit"],[52,"aJ","speculative"],[52,"aK","syn_or_mod"],[52,"aL","transient_ecsid"],[52,"aM","transmission_type"],[52,"aN","user_data"],[52,"aO","user_data_from_automatic"],[52,"aP","user_data_from_automatic_getter"],[52,"aQ","user_data_from_code"],[52,"aR","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"D",[15,"d"],"I",[15,"e"],"J",[15,"f"],"K",[15,"g"],"L",[15,"h"],"R",[15,"i"],"X",[15,"j"],"Y",[15,"k"],"AG",[15,"l"],"AI",[15,"m"],"AJ",[15,"n"],"AK",[15,"o"],"AO",[15,"p"],"AQ",[15,"q"],"AS",[15,"r"],"AT",[15,"s"],"AV",[15,"t"],"AW",[15,"u"],"AX",[15,"v"],"AY",[15,"w"],"BC",[15,"x"],"BD",[15,"y"],"BE",[15,"z"],"BF",[15,"aA"],"BG",[15,"aB"],"BI",[15,"aC"],"BJ",[15,"aD"],"BO",[15,"aE"],"BR",[15,"aF"],"BS",[15,"aG"],"BU",[15,"aH"],"BY",[15,"aI"],"CA",[15,"aJ"],"CD",[15,"aK"],"CE",[15,"aL"],"CF",[15,"aM"],"CG",[15,"aN"],"CH",[15,"aO"],"CI",[15,"aP"],"CJ",[15,"aQ"],"CK",[15,"aR"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",42],[52,"f",43],[52,"g",44],[52,"h",45],[52,"i",46],[52,"j",47],[52,"k",113],[52,"l",129],[52,"m",142],[52,"n",156],[52,"o",168],[52,"p",174],[52,"q",178],[52,"r",212],[52,"s",240],[52,"t",241],[52,"u",243],[52,"v",252],[52,"w",253],[52,"x",254],[36,[8,"EP",[15,"v"],"DC",[15,"o"],"V",[15,"b"],"W",[15,"c"],"X",[15,"d"],"AC",[15,"e"],"AD",[15,"f"],"AE",[15,"g"],"AF",[15,"h"],"AG",[15,"i"],"AH",[15,"j"],"DG",[15,"p"],"DJ",[15,"q"],"EI",[15,"s"],"BN",[15,"k"],"EK",[15,"u"],"BZ",[15,"l"],"EJ",[15,"t"],"ER",[15,"x"],"EQ",[15,"w"],"CM",[15,"m"],"DU",[15,"r"],"CV",[15,"n"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_platformDetection",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"f",[46],[68,"l",[53,[22,[28,["e",[17,[15,"b"],"EK"]]],[46,[53,[36]]]],[52,"l",[7]],[22,["g"],[46,[2,[15,"l"],"push",[7,"ac"]]]],[22,["h"],[46,[2,[15,"l"],"push",[7,"sqs"]]]],[22,["i"],[46,[2,[15,"l"],"push",[7,"dud"]]]],[22,["j"],[46,[2,[15,"l"],"push",[7,"woo"]]]],[22,["k"],[46,[2,[15,"l"],"push",[7,"fw"]]]],[22,[18,[17,[15,"l"],"length"],0],[46,[36,[8,"plf",[2,[15,"l"],"join",[7,"."]]]]]]],[46]]],[50,"g",[46],[68,"l",[53,[36,[28,[28,["c","script[data-requiremodule^=\"mage/\"]"]]]]],[46]],[36,false]],[50,"h",[46],[68,"l",[53,[36,[28,[28,["c","script[src^=\"//assets.squarespace.com/\"]"]]]]],[46]],[36,false]],[50,"i",[46],[68,"l",[53,[36,[28,[28,["c","script[id=\"d-js-core\"]"]]]]],[46]],[36,false]],[50,"j",[46],[68,"l",[53,[36,[28,[28,["c",[0,[0,"script[src*=\"woocommerce\"],","link[href*=\"woocommerce\"],"],"[class|=\"woocommerce\"]"]]]]]],[46]],[36,false]],[50,"k",[46],[68,"l",[53,[36,[28,[28,["c",[0,[0,"meta[content*=\"fourthwall\"],","script[src*=\"fourthwall\"],"],"link[href*=\"fourthwall\"]"]]]]]],[46]],[36,false]],[52,"b",[15,"__module_featureFlags"]],[52,"c",["require","internal.getFirstElementByCssSelector"]],[52,"d",[15,"__module_gtagSchema"]],[52,"e",["require","internal.isFeatureEnabled"]],[36,[8,"A",[15,"f"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_taskPlatformDetection",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"d",[46,"e"],[52,"f",[2,[15,"c"],"A",[7]]],[22,[15,"f"],[46,[53,[2,[15,"e"],"mergeHitDataForKey",[7,[17,[15,"b"],"FJ"],[15,"f"]]]]]]],[52,"b",[15,"__module_gtagSchema"]],[52,"c",[15,"__module_platformDetection"]],[36,[8,"A",[15,"d"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}
,
"__rep":{"5":true}


}
,"blob":{"1":"1","10":"UA-185909341-1","14":"59q1","15":"0","16":"MTQ5NDE4NjI2Njc0ODAyMzU1ODA=","17":"","19":"dataLayer","20":"","21":"www.googletagmanager.com","22":"eyIwIjoiRlIiLCIxIjoiRlItTk9SIiwiMiI6ZmFsc2UsIjMiOiJnb29nbGUuZnIiLCI0IjoicmVnaW9uMSIsIjUiOmZhbHNlLCI2Ijp0cnVlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"FR","31":"FR-NOR","32":false,"33":"region1","34":"UA-185909341-1","35":"UA","36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BJSABQstju6966GwbcWBTTkW0H2SrqWLGL6395IoWp/8dQWuDpEyuI8iupDhkMiUcKUwHi9NvFl1MKLS8HfW8pc=\",\"version\":0},\"id\":\"e6a0509a-c365-4ed8-99b1-a5aa1cdef66b\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BFZ3rjxgcPw2KpHjJ9AzZLgA5eJ4/cIchU+JP+t/zFrfj6fQMJwuH8x+p/tPWOFGYD/xUoR7cXhK5NTRShkUgnY=\",\"version\":0},\"id\":\"c36bc6e9-3e4e-414b-bb32-11afe2a57b00\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BLyUqLHiPorFuqGeCUmn/x48bmaT40yTjtlYIJ3AqGTutX2mo4qbfmblHUc5Okwc48yx95oYZ1meg3QaYCHyuo4=\",\"version\":0},\"id\":\"89eab74e-9620-4645-995e-e1fd35f16563\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BEwdmtxFisR7dc1M3zWG/C3Fh+zXSayB6Z18Rv2GXvCKjJaWlIodk+D46zAj29EGCYviDiO3RS+e9XqmEqI6caI=\",\"version\":0},\"id\":\"41915128-df0c-40a7-943a-6f830f7f8547\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BFQG89KvKUvP5+fr/zAQdUWHAPohK9uQ/HdIUWqKBpzY08Q9V7fwvgdvkT1c14ced95FMzOtxAejxAyUQLcjmCI=\",\"version\":0},\"id\":\"0a9823f2-deef-43b9-95d5-b323c1a2247a\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211","45":true,"46":{"1":"1000","10":"5940","11":"59f0","14":"1000","16":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","17":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","2":"9","20":"5000","21":"5000","22":"3.2.0","23":"0.0.0","25":"1","26":"4000","27":"100","3":"5","4":"ad_storage|analytics_storage|ad_user_data|ad_personalization","44":"15000","48":"30000","5":"ad_storage|analytics_storage|ad_user_data","6":"1","60":"0","7":"10"},"48":true,"5":"UA-185909341-1","55":["UA-185909341-1"],"56":[],"8":"res_ts:1683569491391341,srv_cl:812694647,ds:live,cv:1","9":"UA-185909341-1"}
,"permissions":{
"__c":{}
,
"__ccd_ga_first":{"read_dom_elements":{"allowedCssSelectors":"any"}}
,
"__ccd_ga_last":{}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__rep":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__rep"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=da(this),ha=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ma={},na={},oa=function(a,b,c){if(!c||a!=null){var d=na[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},pa=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ma?g=ma:g=fa;for(var h=0;h<d.length-1;h++){var l=d[h];if(!(l in g))break a;g=g[l]}var n=d[d.length-1],p=ha&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ba(ma,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(na[n]===void 0){var r=
Math.random()*1E9>>>0;na[n]=ha?fa.Symbol(n):"$jscp$"+r+"$"+n}ba(g,na[n],{configurable:!0,writable:!0,value:q})}}};pa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var qa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ra;if(ha&&typeof Object.setPrototypeOf=="function")ra=Object.setPrototypeOf;else{var sa;a:{var ua={a:!0},va={};try{va.__proto__=ua;sa=va.a;break a}catch(a){}sa=!1}ra=sa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var wa=ra,za=function(a,b){a.prototype=qa(b.prototype);a.prototype.constructor=a;if(wa)wa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.vr=b.prototype},m=function(a){var b=typeof ma.Symbol!="undefined"&&ma.Symbol.iterator&&a[ma.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},Aa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},Ba=function(a){return a instanceof Array?a:Aa(m(a))},Da=function(a){return Ca(a,a)},Ca=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ea=ha&&typeof oa(Object,"assign")=="function"?oa(Object,"assign"):function(a,b){if(a==null)throw new TypeError("No nullish arg");a=Object(a);for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};
pa("Object.assign",function(a){return a||Ea},"es6");var Fa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Ga=this||self,Ha=function(a,b){function c(){}c.prototype=b.prototype;a.vr=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Cs=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ia=function(a,b){this.type=a;this.data=b};var Ja=function(){this.map={};this.C={}};Ja.prototype.get=function(a){return this.map["dust."+a]};Ja.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ja.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ja.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ka=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ja.prototype.Aa=function(){return Ka(this,1)};Ja.prototype.sc=function(){return Ka(this,2)};Ja.prototype.Zb=function(){return Ka(this,3)};var La=function(){};La.prototype.reset=function(){};var Ma=function(a,b){this.R=a;this.parent=b;this.M=this.C=void 0;this.yb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ja};Ma.prototype.add=function(a,b){Na(this,a,b,!1)};Ma.prototype.rh=function(a,b){Na(this,a,b,!0)};var Na=function(a,b,c,d){if(!a.yb)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ma.prototype;k.set=function(a,b){this.yb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.jb=function(){var a=new Ma(this.R,this);this.C&&a.Mb(this.C);a.Uc(this.H);a.Od(this.M);return a};k.Hd=function(){return this.R};k.Mb=function(a){this.C=a};k.Tm=function(){return this.C};k.Uc=function(a){this.H=a};k.kj=function(){return this.H};k.Sa=function(){this.yb=!0};k.Od=function(a){this.M=a};k.lb=function(){return this.M};var Oa=function(){this.value={};this.prefix="gtm."};Oa.prototype.set=function(a,b){this.value[this.prefix+String(a)]=b};Oa.prototype.get=function(a){return this.value[this.prefix+String(a)]};Oa.prototype.has=function(a){return this.value.hasOwnProperty(this.prefix+String(a))};function Pa(){try{if(Map)return new Map}catch(a){}return new Oa};var Qa=function(){this.values=[]};Qa.prototype.add=function(a){this.values.indexOf(a)===-1&&this.values.push(a)};Qa.prototype.has=function(a){return this.values.indexOf(a)>-1};var Ra=function(a,b){this.ja=a;this.parent=b;this.R=this.H=void 0;this.yb=!1;this.M=function(d,e,f){return d.apply(e,f)};this.C=Pa();var c;a:{try{if(Set){c=new Set;break a}}catch(d){}c=new Qa}this.U=c};Ra.prototype.add=function(a,b){Sa(this,a,b,!1)};Ra.prototype.rh=function(a,b){Sa(this,a,b,!0)};var Sa=function(a,b,c,d){a.yb||a.U.has(b)||(d&&a.U.add(b),a.C.set(b,c))};k=Ra.prototype;
k.set=function(a,b){this.yb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.U.has(a)||this.C.set(a,b))};k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.jb=function(){var a=new Ra(this.ja,this);this.H&&a.Mb(this.H);a.Uc(this.M);a.Od(this.R);return a};k.Hd=function(){return this.ja};k.Mb=function(a){this.H=a};k.Tm=function(){return this.H};
k.Uc=function(a){this.M=a};k.kj=function(){return this.M};k.Sa=function(){this.yb=!0};k.Od=function(a){this.R=a};k.lb=function(){return this.R};var Ta=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.fn=a;this.Lm=c===void 0?!1:c;this.debugInfo=[];this.C=b};za(Ta,Error);var Ua=function(a){return a instanceof Ta?a:new Ta(a,void 0,!0)};var Wa=[];function Xa(a){return Wa[a]===void 0?!1:Wa[a]};var Ya=Pa();function Za(a,b){for(var c,d=m(b),e=d.next();!e.done&&!(c=ab(a,e.value),c instanceof Ia);e=d.next());return c}
function ab(a,b){try{if(Xa(17)){var c=b[0],d=b.slice(1),e=String(c),f=Ya.has(e)?Ya.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Ua(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=m(b),h=g.next().value,l=Aa(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Ua(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(Ba(l)))}catch(q){var p=a.Tm();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var bb=function(){this.H=new La;this.C=Xa(17)?new Ra(this.H):new Ma(this.H)};k=bb.prototype;k.Hd=function(){return this.H};k.Mb=function(a){this.C.Mb(a)};k.Uc=function(a){this.C.Uc(a)};k.execute=function(a){return this.Ij([a].concat(Ba(Fa.apply(1,arguments))))};k.Ij=function(){for(var a,b=m(Fa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=ab(this.C,c.value);return a};
k.ap=function(a){var b=Fa.apply(1,arguments),c=this.C.jb();c.Od(a);for(var d,e=m(b),f=e.next();!f.done;f=e.next())d=ab(c,f.value);return d};k.Sa=function(){this.C.Sa()};var cb=function(){this.Fa=!1;this.da=new Ja};k=cb.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Aa=function(){return this.da.Aa()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};k.Sa=function(){this.Fa=!0};k.yb=function(){return this.Fa};function db(){for(var a=eb,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function fb(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var eb,gb;function hb(a){eb=eb||fb();gb=gb||db();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,l=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(eb[l],eb[n],eb[p],eb[q])}return b.join("")}
function ib(a){function b(l){for(;d<a.length;){var n=a.charAt(d++),p=gb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return l}eb=eb||fb();gb=gb||db();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var jb={};function kb(a,b){jb[a]=jb[a]||[];jb[a][b]=!0}function lb(){delete jb.GA4_EVENT}function mb(){jb.GTAG_EVENT_FEATURE_CHANNEL=nb}function ob(a){var b=jb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return hb(c.join("")).replace(/\.+$/,"")};function pb(){}function qb(a){return typeof a==="function"}function rb(a){return typeof a==="string"}function sb(a){return typeof a==="number"&&!isNaN(a)}function tb(a){return Array.isArray(a)?a:[a]}function ub(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function vb(a,b){if(!sb(a)||!sb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function wb(a,b){for(var c=new xb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function yb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function zb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function Ab(a){return Math.round(Number(a))||0}function Bb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function Cb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function Db(a){return a?a.replace(/^\s+|\s+$/g,""):""}function Eb(){return new Date(Date.now())}function Fb(){return Eb().getTime()}var xb=function(){this.prefix="gtm.";this.values={}};xb.prototype.set=function(a,b){this.values[this.prefix+a]=b};xb.prototype.get=function(a){return this.values[this.prefix+a]};xb.prototype.contains=function(a){return this.get(a)!==void 0};
function Gb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Hb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Ib(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Jb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Kb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Mb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Nb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Ob=/^\w{1,9}$/;function Pb(a,b){a=a||{};b=b||",";var c=[];yb(a,function(d,e){Ob.test(d)&&e&&c.push(d)});return c.join(b)}function Qb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Rb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Sb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var l=""+f+g+h;l[l.length-1]==="/"&&(l=l.substring(0,l.length-1));return l}
function Tb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Ub(){var a=w,b;a:{var c=a.crypto||a.msCrypto;if(c&&c.getRandomValues)try{var d=new Uint8Array(25);c.getRandomValues(d);b=btoa(String.fromCharCode.apply(String,Ba(d))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(e){}b=void 0}return b};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Vb=globalThis.trustedTypes,Wb;function Xb(){var a=null;if(!Vb)return a;try{var b=function(c){return c};a=Vb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Yb(){Wb===void 0&&(Wb=Xb());return Wb};var Zb=function(a){this.C=a};Zb.prototype.toString=function(){return this.C+""};function $b(a){var b=a,c=Yb(),d=c?c.createScriptURL(b):b;return new Zb(d)}function ac(a){if(a instanceof Zb)return a.C;throw Error("");};var dc=Da([""]),ec=Ca(["\x00"],["\\0"]),fc=Ca(["\n"],["\\n"]),hc=Ca(["\x00"],["\\u0000"]);function ic(a){return a.toString().indexOf("`")===-1}ic(function(a){return a(dc)})||ic(function(a){return a(ec)})||ic(function(a){return a(fc)})||ic(function(a){return a(hc)});var jc=function(a){this.C=a};jc.prototype.toString=function(){return this.C};var kc=function(a){this.Hq=a};function lc(a){return new kc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var mc=[lc("data"),lc("http"),lc("https"),lc("mailto"),lc("ftp"),new kc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function nc(a){var b;b=b===void 0?mc:b;if(a instanceof jc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof kc&&d.Hq(a))return new jc(a)}}var oc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function pc(a){var b;if(a instanceof jc)if(a instanceof jc)b=a.C;else throw Error("");else b=oc.test(a)?a:void 0;return b};function qc(a,b){var c=pc(b);c!==void 0&&(a.action=c)};function rc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var sc=function(a){this.C=a};sc.prototype.toString=function(){return this.C+""};var uc=function(){this.C=tc[0].toLowerCase()};uc.prototype.toString=function(){return this.C};function vc(a,b){var c=[new uc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof uc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var wc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function xc(a){return a===null?"null":a===void 0?"undefined":a};var w=window,yc=window.history,A=document,zc=navigator;function Ac(){var a;try{a=zc.serviceWorker}catch(b){return}return a}var Bc=A.currentScript,Cc=Bc&&Bc.src;function Dc(a,b){var c=w,d=c[a];c[a]=d===void 0?b:d;return c[a]}function Ec(a){return(zc.userAgent||"").indexOf(a)!==-1}function Fc(){return Ec("Firefox")||Ec("FxiOS")}function Gc(){return(Ec("GSA")||Ec("GoogleApp"))&&(Ec("iPhone")||Ec("iPad"))}function Hc(){return Ec("Edg/")||Ec("EdgA/")||Ec("EdgiOS/")}
var Ic={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Jc={height:1,onload:1,src:1,style:1,width:1};function Kc(a,b,c){b&&yb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Lc(a,b,c,d,e){var f=A.createElement("script");Kc(f,d,Ic);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=$b(xc(a));f.src=ac(g);var h,l=f.ownerDocument;l=l===void 0?document:l;var n,p,q=(p=(n=l).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function Mc(){if(Cc){var a=Cc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Nc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);Kc(g,c,Jc);d&&yb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var l=A.body&&A.body.lastChild||A.body||A.head;l.parentNode.insertBefore(g,l)}b&&(g.onload=b);return g}
function Oc(a,b,c,d){return Pc(a,b,c,d)}function Qc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Rc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Sc(a){w.setTimeout(a,0)}function Tc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Uc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Vc(a){var b=A.createElement("div"),c=b,d,e=xc("A<div>"+a+"</div>"),f=Yb(),g=f?f.createHTML(e):e;d=new sc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof sc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var l=[];b&&b.firstChild;)l.push(b.removeChild(b.firstChild));return l}
function Wc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Xc(a,b,c){var d;try{d=zc.sendBeacon&&zc.sendBeacon(a)}catch(e){kb("TAGGING",15)}d?b==null||b():Pc(a,b,c)}function Yc(a,b){try{return zc.sendBeacon(a,b)}catch(c){kb("TAGGING",15)}return!1}var Zc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function $c(a,b,c,d,e){if(cd()){var f=oa(Object,"assign").call(Object,{},Zc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=w.fetch(a,f);if(g)return g.then(function(l){l&&(l.ok||l.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(l){}}if(c&&c.pg)return e==null||e(),
!1;if(b){var h=Yc(a,b);h?d==null||d():e==null||e();return h}dd(a,d,e);return!0}function cd(){return typeof w.fetch==="function"}function ed(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function fd(){var a=w.performance;if(a&&qb(a.now))return a.now()}
function gd(){var a,b=w.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function hd(){return w.performance||void 0}function id(){var a=w.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Pc=function(a,b,c,d){var e=new Image(1,1);Kc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},dd=Xc;function jd(a,b){return this.evaluate(a)&&this.evaluate(b)}function kd(a,b){return this.evaluate(a)===this.evaluate(b)}function ld(a,b){return this.evaluate(a)||this.evaluate(b)}function md(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function nd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function od(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=w.location.href;d instanceof cb&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var pd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,qd=function(a){if(a==null)return String(a);var b=pd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},rd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},sd=function(a){if(!a||qd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!rd(a,"constructor")&&!rd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
rd(a,b)},td=function(a,b){var c=b||(qd(a)=="array"?[]:{}),d;for(d in a)if(rd(a,d)){var e=a[d];qd(e)=="array"?(qd(c[d])!="array"&&(c[d]=[]),c[d]=td(e,c[d])):sd(e)?(sd(c[d])||(c[d]={}),c[d]=td(e,c[d])):c[d]=e}return c};function ud(a){if(a==void 0||Array.isArray(a)||sd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function vd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var wd=function(a){a=a===void 0?[]:a;this.da=new Ja;this.values=[];this.Fa=!1;for(var b in a)a.hasOwnProperty(b)&&(vd(b)?this.values[Number(b)]=a[Number(b)]:this.da.set(b,a[b]))};k=wd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof wd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Fa)if(a==="length"){if(!vd(b))throw Ua(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else vd(a)?this.values[Number(a)]=b:this.da.set(a,b)};k.get=function(a){return a==="length"?this.length():vd(a)?this.values[Number(a)]:this.da.get(a)};k.length=function(){return this.values.length};k.Aa=function(){for(var a=this.da.Aa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.sc=function(){for(var a=this.da.sc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Zb=function(){for(var a=this.da.Zb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){vd(a)?delete this.values[Number(a)]:this.Fa||this.da.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,Ba(Fa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=Fa.apply(2,arguments);return b===void 0&&c.length===0?new wd(this.values.splice(a)):new wd(this.values.splice.apply(this.values,[a,b||0].concat(Ba(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,Ba(Fa.apply(0,arguments)))};k.has=function(a){return vd(a)&&this.values.hasOwnProperty(a)||this.da.has(a)};k.Sa=function(){this.Fa=!0;Object.freeze(this.values)};k.yb=function(){return this.Fa};
function xd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var yd=function(a,b){this.functionName=a;this.Gd=b;this.da=new Ja;this.Fa=!1};k=yd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new wd(this.Aa())};k.invoke=function(a){return this.Gd.call.apply(this.Gd,[new zd(this,a)].concat(Ba(Fa.apply(1,arguments))))};k.apply=function(a,b){return this.Gd.apply(new zd(this,a),b)};k.Kb=function(a){var b=Fa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(Ba(b)))}catch(c){}};
k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Aa=function(){return this.da.Aa()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};k.Sa=function(){this.Fa=!0};k.yb=function(){return this.Fa};var Ad=function(a,b){yd.call(this,a,b)};za(Ad,yd);var Bd=function(a,b){yd.call(this,a,b)};za(Bd,yd);var zd=function(a,b){this.Gd=a;this.J=b};
zd.prototype.evaluate=function(a){var b=this.J;return Array.isArray(a)?ab(b,a):a};zd.prototype.getName=function(){return this.Gd.getName()};zd.prototype.Hd=function(){return this.J.Hd()};var Cd=function(){this.map=new Map};Cd.prototype.set=function(a,b){this.map.set(a,b)};Cd.prototype.get=function(a){return this.map.get(a)};var Dd=function(){this.keys=[];this.values=[]};Dd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};Dd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function Ed(){try{return Map?new Cd:new Dd}catch(a){return new Dd}};var Fd=function(a){if(a instanceof Fd)return a;if(ud(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};Fd.prototype.getValue=function(){return this.value};Fd.prototype.toString=function(){return String(this.value)};var Hd=function(a){this.promise=a;this.Fa=!1;this.da=new Ja;this.da.set("then",Gd(this));this.da.set("catch",Gd(this,!0));this.da.set("finally",Gd(this,!1,!0))};k=Hd.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.Aa=function(){return this.da.Aa()};k.sc=function(){return this.da.sc()};k.Zb=function(){return this.da.Zb()};
var Gd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new Ad("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof Ad||(d=void 0);e instanceof Ad||(e=void 0);var f=this.J.jb(),g=function(l){return function(n){try{return c?(l.invoke(f),a.promise):l.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new Fd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Hd(h)})};Hd.prototype.Sa=function(){this.Fa=!0};Hd.prototype.yb=function(){return this.Fa};function B(a,b,c){var d=Ed(),e=function(g,h){for(var l=g.Aa(),n=0;n<l.length;n++)h[l[n]]=f(g.get(l[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof wd){var l=[];d.set(g,l);for(var n=g.Aa(),p=0;p<n.length;p++)l[n[p]]=f(g.get(n[p]));return l}if(g instanceof Hd)return g.promise.then(function(t){return B(t,b,1)},function(t){return Promise.reject(B(t,b,1))});if(g instanceof cb){var q={};d.set(g,q);e(g,q);return q}if(g instanceof Ad){var r=function(){for(var t=
[],v=0;v<arguments.length;v++)t[v]=Id(arguments[v],b,c);var x=new Ma(b?b.Hd():new La);b&&x.Od(b.lb());return f(Xa(17)?g.apply(x,t):g.invoke.apply(g,[x].concat(Ba(t))))};d.set(g,r);e(g,r);return r}var u=!1;switch(c){case 1:u=!0;break;case 2:u=!1;break;case 3:u=!1;break;default:}if(g instanceof Fd&&u)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Id(a,b,c){var d=Ed(),e=function(g,h){for(var l in g)g.hasOwnProperty(l)&&h.set(l,f(g[l]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||zb(g)){var l=new wd;d.set(g,l);for(var n in g)g.hasOwnProperty(n)&&l.set(n,f(g[n]));return l}if(sd(g)){var p=new cb;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new Ad("",function(){for(var t=Fa.apply(0,arguments),v=[],x=0;x<t.length;x++)v[x]=B(this.evaluate(t[x]),b,c);return f(this.J.kj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var u=!1;switch(c){case 1:u=!0;break;case 2:u=!1;break;default:}if(g!==void 0&&u)return new Fd(g)};return f(a)};var Jd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof wd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new wd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new wd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new wd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
Ba(Fa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ua(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ua(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ua(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ua(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=xd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new wd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=xd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(Ba(Fa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,Ba(Fa.apply(1,arguments)))}};var Kd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Ld=new Ia("break"),Md=new Ia("continue");function Nd(a,b){return this.evaluate(a)+this.evaluate(b)}function Od(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Pd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof wd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ua(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=B(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ua(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Kd.hasOwnProperty(e)){var l=2;l=1;var n=B(f,void 0,l);return Id(d[e].apply(d,n),this.J)}throw Ua(Error("TypeError: "+e+" is not a function"));}if(d instanceof wd){if(d.has(e)){var p=d.get(String(e));if(p instanceof Ad){var q=xd(f);return Xa(17)?p.apply(this.J,q):p.invoke.apply(p,[this.J].concat(Ba(q)))}throw Ua(Error("TypeError: "+e+" is not a function"));
}if(Jd.supportedMethods.indexOf(e)>=0){var r=xd(f);return Jd[e].call.apply(Jd[e],[d,this.J].concat(Ba(r)))}}if(d instanceof Ad||d instanceof cb||d instanceof Hd){if(d.has(e)){var u=d.get(e);if(u instanceof Ad){var t=xd(f);return Xa(17)?u.apply(this.J,t):u.invoke.apply(u,[this.J].concat(Ba(t)))}throw Ua(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof Ad?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof Fd&&e==="toString")return d.toString();
throw Ua(Error("TypeError: Object has no '"+e+"' property."));}function Qd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.J;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Rd(){var a=Fa.apply(0,arguments),b=this.J.jb(),c=Za(b,a);if(c instanceof Ia)return c}function Sd(){return Ld}
function Td(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ia)return d}}function Ud(){for(var a=this.J,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.rh(c,d)}}}function Vd(){return Md}function Wd(a,b){return new Ia(a,this.evaluate(b))}
function Xd(a,b){var c=Fa.apply(2,arguments),d;d=new wd;for(var e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(Ba(c));this.J.add(a,this.evaluate(g))}function Yd(a,b){return this.evaluate(a)/this.evaluate(b)}function Zd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof Fd,f=d instanceof Fd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function $d(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}
function ae(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Za(f,d);if(g instanceof Ia){if(g.type==="break")break;if(g.type==="return")return g}}}function be(a,b,c){if(typeof b==="string")return ae(a,function(){return b.length},function(f){return f},c);if(b instanceof cb||b instanceof Hd||b instanceof wd||b instanceof Ad){var d=b.Aa(),e=d.length;return ae(a,function(){return e},function(f){return d[f]},c)}}
function ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){g.set(d,h);return g},e,f)}function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){var l=g.jb();l.rh(d,h);return l},e,f)}function ee(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return be(function(h){var l=g.jb();l.add(d,h);return l},e,f)}
function fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){g.set(d,h);return g},e,f)}function he(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){var l=g.jb();l.rh(d,h);return l},e,f)}function ie(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ge(function(h){var l=g.jb();l.add(d,h);return l},e,f)}
function ge(a,b,c){if(typeof b==="string")return ae(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof wd)return ae(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ua(Error("The value is not iterable."));}
function je(a,b,c,d){function e(q,r){for(var u=0;u<f.length();u++){var t=f.get(u);r.add(t,q.get(t))}}var f=this.evaluate(a);if(!(f instanceof wd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.J,h=this.evaluate(d),l=g.jb();for(e(g,l);ab(l,b);){var n=Za(l,h);if(n instanceof Ia){if(n.type==="break")break;if(n.type==="return")return n}var p=g.jb();e(l,p);ab(p,c);l=p}}
function ke(a,b){var c=Fa.apply(2,arguments),d=this.J,e=this.evaluate(b);if(!(e instanceof wd))throw Error("Error: non-List value given for Fn argument names.");return new Ad(a,function(){return function(){var f=Fa.apply(0,arguments),g=d.jb();g.lb()===void 0&&g.Od(this.J.lb());for(var h=[],l=0;l<f.length;l++){var n=this.evaluate(f[l]);h[l]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new wd(h));var r=Za(g,c);if(r instanceof Ia)return r.type===
"return"?r.data:r}}())}function le(a){var b=this.evaluate(a),c=this.J;if(me&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ne(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ua(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof cb||d instanceof Hd||d instanceof wd||d instanceof Ad)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:vd(e)&&(c=d[e]);else if(d instanceof Fd)return;return c}function oe(a,b){return this.evaluate(a)>this.evaluate(b)}function pe(a,b){return this.evaluate(a)>=this.evaluate(b)}
function qe(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof Fd&&(c=c.getValue());d instanceof Fd&&(d=d.getValue());return c===d}function re(a,b){return!qe.call(this,a,b)}function se(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Za(this.J,d);if(e instanceof Ia)return e}var me=!1;
function te(a,b){return this.evaluate(a)<this.evaluate(b)}function ue(a,b){return this.evaluate(a)<=this.evaluate(b)}function ve(){for(var a=new wd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function we(){for(var a=new cb,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function xe(a,b){return this.evaluate(a)%this.evaluate(b)}
function ye(a,b){return this.evaluate(a)*this.evaluate(b)}function ze(a){return-this.evaluate(a)}function Ae(a){return!this.evaluate(a)}function Be(a,b){return!Zd.call(this,a,b)}function Ce(){return null}function De(a,b){return this.evaluate(a)||this.evaluate(b)}function Ee(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Fe(a){return this.evaluate(a)}function Ge(){return Fa.apply(0,arguments)}function He(a){return new Ia("return",this.evaluate(a))}
function Ie(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ua(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof Ad||d instanceof wd||d instanceof cb)&&d.set(String(e),f);return f}function Je(a,b){return this.evaluate(a)-this.evaluate(b)}
function Ke(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,l=0;l<e.length;l++)if(h||d===this.evaluate(e[l]))if(g=this.evaluate(f[l]),g instanceof Ia){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ia&&(g.type==="return"||g.type==="continue")))return g}
function Le(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Me(a){var b=this.evaluate(a);return b instanceof Ad?"function":typeof b}function Ne(){for(var a=this.J,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Oe(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Za(this.J,e);if(f instanceof Ia){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Za(this.J,e);if(g instanceof Ia){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Pe(a){return~Number(this.evaluate(a))}function Qe(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Re(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Se(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Te(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ue(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ve(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function We(){}
function Xe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ia)return d}catch(h){if(!(h instanceof Ta&&h.Lm))throw h;var e=this.J.jb();a!==""&&(h instanceof Ta&&(h=h.fn),e.add(a,new Fd(h)));var f=this.evaluate(c),g=Za(e,f);if(g instanceof Ia)return g}}function Ye(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ta&&f.Lm))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ia)return e;if(c)throw c;if(d instanceof Ia)return d};var $e=function(){this.C=new bb;Ze(this)};$e.prototype.execute=function(a){return this.C.Ij(a)};var Ze=function(a){var b=function(c,d){var e=new Bd(String(c),d);e.Sa();var f=String(c);a.C.C.set(f,e);Ya.set(f,e)};b("map",we);b("and",jd);b("contains",md);b("equals",kd);b("or",ld);b("startsWith",nd);b("variable",od)};$e.prototype.Mb=function(a){this.C.Mb(a)};var bf=function(){this.H=!1;this.C=new bb;af(this);this.H=!0};bf.prototype.execute=function(a){return cf(this.C.Ij(a))};var df=function(a,b,c){return cf(a.C.ap(b,c))};bf.prototype.Sa=function(){this.C.Sa()};
var af=function(a){var b=function(c,d){var e=String(c),f=new Bd(e,d);f.Sa();a.C.C.set(e,f);Ya.set(e,f)};b(0,Nd);b(1,Od);b(2,Pd);b(3,Qd);b(56,Te);b(57,Qe);b(58,Pe);b(59,Ve);b(60,Re);b(61,Se);b(62,Ue);b(53,Rd);b(4,Sd);b(5,Td);b(68,Xe);b(52,Ud);b(6,Vd);b(49,Wd);b(7,ve);b(8,we);b(9,Td);b(50,Xd);b(10,Yd);b(12,Zd);b(13,$d);b(67,Ye);b(51,ke);b(47,ce);b(54,de);b(55,ee);b(63,je);b(64,fe);b(65,he);b(66,ie);b(15,le);b(16,ne);b(17,ne);b(18,oe);b(19,pe);b(20,qe);b(21,re);b(22,se);b(23,te);b(24,ue);b(25,xe);b(26,
ye);b(27,ze);b(28,Ae);b(29,Be);b(45,Ce);b(30,De);b(32,Ee);b(33,Ee);b(34,Fe);b(35,Fe);b(46,Ge);b(36,He);b(43,Ie);b(37,Je);b(38,Ke);b(39,Le);b(40,Me);b(44,We);b(41,Ne);b(42,Oe)};bf.prototype.Hd=function(){return this.C.Hd()};bf.prototype.Mb=function(a){this.C.Mb(a)};bf.prototype.Uc=function(a){this.C.Uc(a)};
function cf(a){if(a instanceof Ia||a instanceof Ad||a instanceof wd||a instanceof cb||a instanceof Hd||a instanceof Fd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var ef=function(a){this.message=a};function ff(a){a.Is=!0;return a};var gf=ff(function(a){return typeof a==="string"});function hf(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new ef("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function jf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var kf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function lf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+hf(e)+c}a<<=2;d||(a|=32);return c=""+hf(a|b)+c}
function mf(a,b){var c;var d=a.Ih,e=a.Wm;d===void 0?c="":(e||(e=0),c=""+lf(1,1)+hf(d<<2|e));var f=a.Hp,g="4"+c+(f?""+lf(2,1)+hf(f):""),h,l=a.qn;h=l&&kf.test(l)?""+lf(3,2)+l:"";var n,p=a.nn;n=p?""+lf(4,1)+hf(p):"";var q;var r=a.ctid;if(r&&b){var u=lf(5,3),t=r.split("-"),v=t[0].toUpperCase();if(v!=="GTM"&&v!=="OPT")q="";else{var x=t[1];q=""+u+hf(1+x.length)+(a.Iq||0)+x}}else q="";var y=a.rr,z=a.canonicalId,D=a.Oa,E=a.Ms,M=g+h+n+q+(y?""+lf(6,1)+hf(y):"")+(z?""+lf(7,3)+hf(z.length)+z:"")+(D?""+lf(8,3)+
hf(D.length)+D:"")+(E?""+lf(9,3)+hf(E.length)+E:""),G;var L=a.Np;L=L===void 0?{}:L;for(var U=[],ia=m(Object.keys(L)),S=ia.next();!S.done;S=ia.next()){var ca=S.value;U[Number(ca)]=L[ca]}if(U.length){var ta=lf(10,3),ka;if(U.length===0)ka=hf(0);else{for(var ea=[],X=0,la=!1,ya=0;ya<U.length;ya++){la=!0;var xa=ya%6;U[ya]&&(X|=1<<xa);xa===5&&(ea.push(hf(X)),X=0,la=!1)}la&&ea.push(hf(X));ka=ea.join("")}var Va=ka;G=""+ta+hf(Va.length)+Va}else G="";var $a=a.Oq,bc=a.gr,cc=a.ur;return M+G+($a?""+lf(11,3)+hf($a.length)+
$a:"")+(bc?""+lf(13,3)+hf(bc.length)+bc:"")+(cc?""+lf(14,1)+hf(cc):"")};var nf=function(){function a(b){return{toString:function(){return b}}}return{Hn:a("consent"),hk:a("convert_case_to"),ik:a("convert_false_to"),jk:a("convert_null_to"),kk:a("convert_true_to"),lk:a("convert_undefined_to"),Ir:a("debug_mode_metadata"),Qa:a("function"),gh:a("instance_name"),fp:a("live_only"),hp:a("malware_disabled"),METADATA:a("metadata"),lp:a("original_activity_id"),hs:a("original_vendor_template_id"),es:a("once_on_load"),kp:a("once_per_event"),hm:a("once_per_load"),ls:a("priority_override"),
rs:a("respected_consent_types"),rm:a("setup_tags"),qh:a("tag_id"),Cm:a("teardown_tags")}}();var Jf;var Kf=[],Lf=[],Mf=[],Nf=[],Of=[],Pf,Qf,Rf;function Sf(a){Rf=Rf||a}
function Tf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Kf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Nf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Mf.push(f[g]);for(var h=a.rules||[],l=0;l<h.length;l++){for(var n=h[l],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Uf(p[r])}Lf.push(p)}}
function Uf(a){}var Vf,Wf=[],Xf=[];function Yf(a,b){var c={};c[nf.Qa]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Zf(a,b,c){try{return Qf($f(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var $f=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=ag(a[e],b,c));return d},ag=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(ag(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Kf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[nf.gh]);try{var l=$f(g,b,c);l.vtp_gtmEventId=b.id;b.priorityId&&(l.vtp_gtmPriorityId=b.priorityId);d=bg(l,{event:b,index:f,type:2,
name:h});Vf&&(d=Vf.Op(d,l))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[ag(a[n],b,c)]=ag(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=ag(a[q],b,c);Rf&&(p=p||Rf.Eq(r));d.push(r)}return Rf&&p?Rf.Tp(d):d.join("");case "escape":d=ag(a[1],b,c);if(Rf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Rf.Fq(a))return Rf.Uq(d);d=String(d);for(var u=2;u<a.length;u++)uf[a[u]]&&(d=uf[a[u]](d));return d;
case "tag":var t=a[1];if(!Nf[t])throw Error("Unable to resolve tag reference "+t+".");return{Qm:a[2],index:t};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[nf.Qa]=a[1];var x=Zf(v,b,c),y=!!a[4];return y||x!==2?y!==(x===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},bg=function(a,b){var c=a[nf.Qa],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Pf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Wf.indexOf(c)!==-1,g={},h={},l;for(l in a)a.hasOwnProperty(l)&&Kb(l,"vtp_")&&(e&&(g[l]=a[l]),!e||f)&&(h[l.substring(4)]=a[l]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Kf[q];break;case 1:r=Nf[q];break;default:n="";break a}var u=r&&r[nf.gh];n=u?String(u):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var t,v,x;if(f&&Xf.indexOf(c)===-1){Xf.push(c);
var y=Fb();t=e(g);var z=Fb()-y,D=Fb();v=Jf(c,h,b);x=z-(Fb()-D)}else if(e&&(t=e(g)),!e||f)v=Jf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),ud(t)?(Array.isArray(t)?Array.isArray(v):sd(t)?sd(v):typeof t==="function"?typeof v==="function":t===v)||d.reportMacroDiscrepancy(d.id,c):t!==v&&d.reportMacroDiscrepancy(d.id,c),x!==void 0&&d.reportMacroDiscrepancy(d.id,c,x));return e?t:v};var cg=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};za(cg,Error);cg.prototype.getMessage=function(){return this.message};function dg(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)dg(a[c],b[c])}};function eg(){return function(a,b){var c;var d=fg;a instanceof Ta?(a.C=d,c=a):c=new Ta(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function fg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)sb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function gg(a){function b(r){for(var u=0;u<r.length;u++)d[r[u]]=!0}for(var c=[],d=[],e=hg(a),f=0;f<Lf.length;f++){var g=Lf[f],h=ig(g,e);if(h){for(var l=g.add||[],n=0;n<l.length;n++)c[l[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Nf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function ig(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function hg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Zf(Mf[c],a));return b[c]}};function jg(a,b){b[nf.hk]&&typeof a==="string"&&(a=b[nf.hk]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(nf.jk)&&a===null&&(a=b[nf.jk]);b.hasOwnProperty(nf.lk)&&a===void 0&&(a=b[nf.lk]);b.hasOwnProperty(nf.kk)&&a===!0&&(a=b[nf.kk]);b.hasOwnProperty(nf.ik)&&a===!1&&(a=b[nf.ik]);return a};var kg=function(){this.C={}},mg=function(a,b){var c=lg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,Ba(Fa.apply(0,arguments)))})};function ng(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new cg(c,d,g);}}
function og(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(Ba(Fa.apply(1,arguments))));ng(e,b,d,g);ng(f,b,d,g)}}}};var rg=function(a,b){var c=this;this.H={};this.C=new kg;var d={},e={},f=og(this.C,a,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(Ba(Fa.apply(1,arguments)))):{}});yb(b,function(g,h){function l(p){var q=Fa.apply(1,arguments);if(!n[p])throw pg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(Ba(q)))}var n={};yb(h,function(p,q){var r=qg(p,q);n[p]=r.assert;d[p]||(d[p]=r.V);r.Jm&&!e[p]&&(e[p]=r.Jm)});c.H[g]=function(p,q){var r=n[p];if(!r)throw pg(p,
{},"The requested permission "+p+" is not configured.");var u=Array.prototype.slice.call(arguments,0);r.apply(void 0,u);f.apply(void 0,u);var t=e[p];t&&t.apply(null,[l].concat(Ba(u.slice(1))))}})},sg=function(a){return lg.H[a]||function(){}};function qg(a,b){var c=Yf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=pg;try{return bg(c)}catch(d){return{assert:function(e){throw new cg(e,{},"Permission "+e+" is unknown.");},V:function(){throw new cg(a,{},"Permission "+a+" is unknown.");}}}}
function pg(a,b,c){return new cg(a,b,c)};var tg=!1;var ug={};ug.Dr=Bb('');ug.Gs=Bb('');var zg=[],Ag;function Bg(a,b,c){var d=Cg(a,c===void 0?!1:c);return d!==b?(Ag?Ag(a):zg.push(a),b):d}function Cg(a,b){b=b===void 0?!1:b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?!!data.blob[a]:b}function Dg(a){var b;b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}function Eg(){var a=Fg.M,b=Gg(54);return b===a||isNaN(b)&&isNaN(a)?b:(Ag?Ag(54):zg.push(54),a)}
function Gg(a){var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(a))?Number(data.blob[a]):0}function Hg(a){var b;b=b===void 0?"":b;var c=Ig(46);return c&&(c==null?0:c.hasOwnProperty(a))?String(c[a]):b}function Jg(a,b){var c=Ig(46);return c&&(c==null?0:c.hasOwnProperty(a))?Number(c[a]):b}function Ig(a){var b,c;return(b=data)==null?void 0:(c=b.blob)==null?void 0:c[a]}function Kg(){var a=Lg;Ag=a;for(var b=m(zg),c=b.next();!c.done;c=b.next())a(c.value);zg.length=0};var Mg=[];function Ng(a){switch(a){case 1:return 0;case 235:return 18;case 38:return 13;case 256:return 11;case 257:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 17;case 75:return 3;case 103:return 14;case 197:return 15;case 109:return 19;case 269:return 21;case 116:return 4;case 135:return 8;case 136:return 5;case 261:return 20}}function Og(a,b){Mg[a]=b;var c=Ng(a);c!==void 0&&(Wa[c]=b)}function C(a){Og(a,!0)}C(39);
C(145);C(153);C(144);C(120);C(5);C(111);C(139);
C(87);C(92);C(159);
C(132);C(20);C(72);
C(113);C(154);C(116);Og(23,!1),C(24);Jg(6,6E4);Jg(7,1);Jg(35,50);C(29);Pg(26,25);
C(37);C(9);C(91);C(123);C(158);C(71);
C(136);
C(127);C(27);
C(69);C(135);C(95);
C(38);C(103);C(112);C(101);
C(122);C(121);C(21);C(134);C(22);
C(141);C(90);
C(59);C(175);C(177);
C(185);
C(197);C(200);C(206);C(231);C(232);C(241);

function F(a){return!!Mg[a]}
function Pg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?C(b):C(a)};var H={P:{Pn:1,Rn:2,Dm:3,jm:4,sk:5,tk:6,Xo:7,Sn:8,Wo:9,On:10,Nn:11,wm:12,sm:13,Zj:14,An:15,Cn:16,fm:17,uk:18,dm:19,Qn:20,jp:21,Gn:22,Bn:23,Dn:24,rk:25,Xj:26,rp:27,Ll:28,Ul:29,Tl:30,Sl:31,Ol:32,Ml:33,Nl:34,Kl:35,Jl:36}};H.P[H.P.Pn]="CREATE_EVENT_SOURCE";H.P[H.P.Rn]="EDIT_EVENT";H.P[H.P.Dm]="TRAFFIC_TYPE";H.P[H.P.jm]="REFERRAL_EXCLUSION";H.P[H.P.sk]="ECOMMERCE_FROM_GTM_TAG";H.P[H.P.tk]="ECOMMERCE_FROM_GTM_UA_SCHEMA";H.P[H.P.Xo]="GA_SEND";H.P[H.P.Sn]="EM_FORM";H.P[H.P.Wo]="GA_GAM_LINK";H.P[H.P.On]="CREATE_EVENT_AUTO_PAGE_PATH";
H.P[H.P.Nn]="CREATED_EVENT";H.P[H.P.wm]="SIDELOADED";H.P[H.P.sm]="SGTM_LEGACY_CONFIGURATION";H.P[H.P.Zj]="CCD_EM_EVENT";H.P[H.P.An]="AUTO_REDACT_EMAIL";H.P[H.P.Cn]="AUTO_REDACT_QUERY_PARAM";H.P[H.P.fm]="MULTIPLE_PAGEVIEW_FROM_CONFIG";H.P[H.P.uk]="EM_EVENT_SENT_BEFORE_CONFIG";H.P[H.P.dm]="LOADED_VIA_CST_OR_SIDELOADING";H.P[H.P.Qn]="DECODED_PARAM_MATCH";H.P[H.P.jp]="NON_DECODED_PARAM_MATCH";H.P[H.P.Gn]="CCD_EVENT_SGTM";H.P[H.P.Bn]="AUTO_REDACT_EMAIL_SGTM";H.P[H.P.Dn]="AUTO_REDACT_QUERY_PARAM_SGTM";
H.P[H.P.rk]="DAILY_LIMIT_REACHED";H.P[H.P.Xj]="BURST_LIMIT_REACHED";H.P[H.P.rp]="SHARED_USER_ID_SET_AFTER_REQUEST";H.P[H.P.Ll]="GA4_MULTIPLE_SESSION_COOKIES";H.P[H.P.Ul]="INVALID_GA4_SESSION_COUNT";H.P[H.P.Tl]="INVALID_GA4_LAST_EVENT_TIMESTAMP";H.P[H.P.Sl]="INVALID_GA4_JOIN_TIMER";H.P[H.P.Ol]="GA4_STALE_SESSION_COOKIE_SELECTED";H.P[H.P.Ml]="GA4_SESSION_COOKIE_GS1_READ";H.P[H.P.Nl]="GA4_SESSION_COOKIE_GS2_READ";H.P[H.P.Kl]="GA4_DL_PARAM_RECOVERY_AVAILABLE";H.P[H.P.Jl]="GA4_DL_PARAM_RECOVERY_APPLIED";var Rg={},Sg=(Rg.uaa=!0,Rg.uab=!0,Rg.uafvl=!0,Rg.uamb=!0,Rg.uam=!0,Rg.uap=!0,Rg.uapv=!0,Rg.uaw=!0,Rg);
var $g=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Yg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,l;a:if(d.length===0)l=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Zg.exec(n[p])){l=!1;break a}l=!0}if(!l||h.length>d.length||!g&&d.length!==e.length?0:g?Kb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Zg=/^[a-z$_][\w-$]*$/i,Yg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var ah=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function bh(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function ch(a,b){return String(a).split(",").indexOf(String(b))>=0}var dh=new xb;function eh(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=dh.get(e);f||(f=new RegExp(b,d),dh.set(e,f));return f.test(a)}catch(g){return!1}}function fh(a,b){return String(a).indexOf(String(b))>=0}
function gh(a,b){return String(a)===String(b)}function hh(a,b){return Number(a)>=Number(b)}function ih(a,b){return Number(a)<=Number(b)}function jh(a,b){return Number(a)>Number(b)}function kh(a,b){return Number(a)<Number(b)}function lh(a,b){return Kb(String(a),String(b))};var sh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,th={Fn:"function",PixieMap:"Object",List:"Array"};
function uh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=sh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],l=b[d];if(l==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof l;l instanceof Ad?n="Fn":l instanceof wd?n="List":l instanceof cb?n="PixieMap":l instanceof Hd?n="PixiePromise":l instanceof Fd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((th[n]||n)+", which does not match required type ")+
((th[h]||h)+"."));}}}function I(a,b,c){for(var d=[],e=m(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof Ad?d.push("function"):g instanceof wd?d.push("Array"):g instanceof cb?d.push("Object"):g instanceof Hd?d.push("Promise"):g instanceof Fd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function vh(a){return a instanceof cb}function wh(a){return vh(a)||a===null||xh(a)}
function yh(a){return a instanceof Ad}function zh(a){return yh(a)||a===null||xh(a)}function Ah(a){return a instanceof wd}function Bh(a){return a instanceof Fd}function J(a){return typeof a==="string"}function Ch(a){return J(a)||a===null||xh(a)}function Dh(a){return typeof a==="boolean"}function Eh(a){return Dh(a)||xh(a)}function Fh(a){return Dh(a)||a===null||xh(a)}function Gh(a){return typeof a==="number"}function xh(a){return a===void 0};function Hh(a){return""+a}
function Ih(a,b){var c=[];return c};function Jh(a,b){var c=new Ad(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ua(g);}});c.Sa();return c}
function Kh(a,b){var c=new cb,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];qb(e)?c.set(d,Jh(a+"_"+d,e)):sd(e)?c.set(d,Kh(a+"_"+d,e)):(sb(e)||rb(e)||typeof e==="boolean")&&c.set(d,e)}c.Sa();return c};function Lh(a,b){if(!J(a))throw I(this.getName(),["string"],arguments);if(!Ch(b))throw I(this.getName(),["string","undefined"],arguments);var c={},d=new cb;return d=Kh("AssertApiSubject",
c)};function Mh(a,b){if(!Ch(b))throw I(this.getName(),["string","undefined"],arguments);if(a instanceof Hd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new cb;return d=Kh("AssertThatSubject",c)};function Nh(a){return function(){for(var b=Fa.apply(0,arguments),c=[],d=this.J,e=0;e<b.length;++e)c.push(B(b[e],d));return Id(a.apply(null,c))}}function Oh(){for(var a=Math,b=Ph,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=Nh(a[e].bind(a)))}return c};function Qh(a){return a!=null&&Kb(a,"__cvt_")};function Rh(a){var b;return b};function Th(a){var b;return b};function Uh(a){try{return encodeURI(a)}catch(b){}};function Vh(a){try{return encodeURIComponent(String(a))}catch(b){}};function $h(a){if(!Ch(a))throw I(this.getName(),["string|undefined"],arguments);};function ai(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};function bi(a){var b=B(a);return ai(b?""+b:"")};function ci(a,b){if(!Gh(a)||!Gh(b))throw I(this.getName(),["number","number"],arguments);return vb(a,b)};function di(){return(new Date).getTime()};function ei(a){if(a===null)return"null";if(a instanceof wd)return"array";if(a instanceof Ad)return"function";if(a instanceof Fd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function fi(a){function b(c){return function(d){try{return c(d)}catch(e){(tg||ug.Dr)&&a.call(this,e.message)}}}return{parse:b(function(c){return Id(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(B(c))}),publicName:"JSON"}};function gi(a){return Ab(B(a,this.J))};function hi(a){return Number(B(a,this.J))};function ii(a){return a===null?"null":a===void 0?"undefined":a.toString()};function ji(a,b,c){var d=null,e=!1;return e?d:null};var Ph="floor ceil round max min abs pow sqrt".split(" ");function ki(){var a={};return{jq:function(b){return a.hasOwnProperty(b)?a[b]:void 0},tn:function(b,c){a[b]=c},reset:function(){a={}}}}function li(a,b){return function(){return Ad.prototype.invoke.apply(a,[b].concat(Ba(Fa.apply(0,arguments))))}}
function mi(a,b){if(!J(a))throw I(this.getName(),["string","any"],arguments);}
function ni(a,b){if(!J(a)||!vh(b))throw I(this.getName(),["string","PixieMap"],arguments);};var oi={};var pi=function(a){var b=new cb;if(a instanceof wd)for(var c=a.Aa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof Ad)for(var f=a.Aa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var l=0;l<a.length;l++)b.set(l,a[l]);return b};
oi.keys=function(a){uh(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=pi(a);if(a instanceof cb||a instanceof Hd)return new wd(a.Aa());return new wd};
oi.values=function(a){uh(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=pi(a);if(a instanceof cb||a instanceof Hd)return new wd(a.sc());return new wd};
oi.entries=function(a){uh(this.getName(),arguments);if(a instanceof wd||a instanceof Ad||typeof a==="string")a=pi(a);if(a instanceof cb||a instanceof Hd)return new wd(a.Zb().map(function(b){return new wd(b)}));return new wd};
oi.freeze=function(a){(a instanceof cb||a instanceof Hd||a instanceof wd||a instanceof Ad)&&a.Sa();return a};oi.delete=function(a,b){if(a instanceof cb&&!a.yb())return a.remove(b),!0;return!1};function K(a,b){var c=Fa.apply(2,arguments),d=a.J.lb();if(!d)throw Error("Missing program state.");if(d.ar){try{d.Km.apply(null,[b].concat(Ba(c)))}catch(e){throw kb("TAGGING",21),e;}return}d.Km.apply(null,[b].concat(Ba(c)))};var qi=function(){this.H={};this.C={};this.M=!0;};qi.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};qi.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
qi.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:qb(b)?Jh(a,b):Kh(a,b)};function ri(a,b){var c=void 0;return c};function si(){var a={};
return a};var N={m:{Ka:"ad_personalization",aa:"ad_storage",W:"ad_user_data",ka:"analytics_storage",hc:"region",ia:"consent_updated",yg:"wait_for_update",Tn:"app_remove",Un:"app_store_refund",Vn:"app_store_subscription_cancel",Wn:"app_store_subscription_convert",Xn:"app_store_subscription_renew",Yn:"consent_update",wk:"add_payment_info",xk:"add_shipping_info",Ud:"add_to_cart",Vd:"remove_from_cart",yk:"view_cart",Yc:"begin_checkout",Wd:"select_item",jc:"view_item_list",yc:"select_promotion",kc:"view_promotion",
zb:"purchase",Xd:"refund",mc:"view_item",zk:"add_to_wishlist",Zn:"exception",ao:"first_open",bo:"first_visit",ma:"gtag.config",nc:"gtag.get",co:"in_app_purchase",Zc:"page_view",eo:"screen_view",fo:"session_start",ho:"source_update",io:"timing_complete",jo:"track_social",Yd:"user_engagement",ko:"user_id_update",Pe:"gclid_link_decoration_source",Qe:"gclid_storage_source",oc:"gclgb",ob:"gclid",Ak:"gclid_len",Zd:"gclgs",ae:"gcllp",be:"gclst",Ha:"ads_data_redaction",Re:"gad_source",Se:"gad_source_src",
bd:"gclid_url",Bk:"gclsrc",Te:"gbraid",ce:"wbraid",Ob:"allow_ad_personalization_signals",Eg:"allow_custom_scripts",Ue:"allow_direct_google_requests",Fg:"allow_display_features",Sh:"allow_enhanced_conversions",Pb:"allow_google_signals",Th:"allow_interest_groups",lo:"app_id",mo:"app_installer_id",no:"app_name",oo:"app_version",dd:"auid",Mr:"auto_detection_enabled",Ck:"aw_remarketing",Uh:"aw_remarketing_only",Gg:"discount",Hg:"aw_feed_country",Ig:"aw_feed_language",Ca:"items",Jg:"aw_merchant_id",Dk:"aw_basket_type",
Ve:"campaign_content",We:"campaign_id",Xe:"campaign_medium",Ye:"campaign_name",Ze:"campaign",af:"campaign_source",bf:"campaign_term",Qb:"client_id",Ek:"rnd",Vh:"consent_update_type",po:"content_group",qo:"content_type",Ab:"conversion_cookie_prefix",Wh:"conversion_id",pb:"conversion_linker",Xh:"conversion_linker_disabled",ed:"conversion_api",Kg:"cookie_deprecation",qb:"cookie_domain",rb:"cookie_expires",Bb:"cookie_flags",fd:"cookie_name",Rb:"cookie_path",Va:"cookie_prefix",zc:"cookie_update",gd:"country",
sb:"currency",Yh:"customer_buyer_stage",cf:"customer_lifetime_value",Zh:"customer_loyalty",ai:"customer_ltv_bucket",df:"custom_map",Lg:"gcldc",hd:"dclid",Fk:"debug_mode",Ea:"developer_id",ro:"disable_merchant_reported_purchases",jd:"dc_custom_params",so:"dc_natural_search",Gk:"dynamic_event_settings",Hk:"affiliation",Mg:"checkout_option",bi:"checkout_step",Ik:"coupon",ef:"item_list_name",di:"list_name",uo:"promotions",ee:"shipping",Jk:"tax",Ng:"engagement_time_msec",Og:"enhanced_client_id",vo:"enhanced_conversions",
Nr:"enhanced_conversions_automatic_settings",ff:"estimated_delivery_date",hf:"event_callback",wo:"event_category",Ac:"event_developer_id_string",xo:"event_label",Bc:"event",Pg:"event_settings",Qg:"event_timeout",yo:"description",zo:"fatal",Ao:"experiments",ei:"firebase_id",fe:"first_party_collection",Rg:"_x_20",qc:"_x_19",Bo:"flight_error_code",Co:"flight_error_message",Kk:"fl_activity_category",Lk:"fl_activity_group",fi:"fl_advertiser_id",Mk:"fl_ar_dedupe",jf:"match_id",Nk:"fl_random_number",Ok:"tran",
Pk:"u",Sg:"gac_gclid",he:"gac_wbraid",Qk:"gac_wbraid_multiple_conversions",Rk:"ga_restrict_domain",Sk:"ga_temp_client_id",Do:"ga_temp_ecid",ie:"gdpr_applies",Tk:"geo_granularity",kf:"value_callback",lf:"value_key",Dc:"google_analysis_params",je:"_google_ng",ke:"google_signals",Uk:"google_tld",nf:"gpp_sid",pf:"gpp_string",Tg:"groups",Vk:"gsa_experiment_id",qf:"gtag_event_feature_usage",Wk:"gtm_up",Ec:"iframe_state",rf:"ignore_referrer",gi:"internal_traffic_results",Xk:"_is_fpm",Fc:"is_legacy_converted",
Gc:"is_legacy_loaded",hi:"is_passthrough",kd:"_lps",tb:"language",Ug:"legacy_developer_id_string",Wa:"linker",tf:"accept_incoming",Hc:"decorate_forms",na:"domains",ld:"url_position",md:"merchant_feed_label",nd:"merchant_feed_language",od:"merchant_id",Yk:"method",Eo:"name",Zk:"navigation_type",uf:"new_customer",Vg:"non_interaction",Fo:"optimize_id",al:"page_hostname",vf:"page_path",Xa:"page_referrer",Cb:"page_title",Go:"passengers",bl:"phone_conversion_callback",Ho:"phone_conversion_country_code",
fl:"phone_conversion_css_class",Io:"phone_conversion_ids",il:"phone_conversion_number",jl:"phone_conversion_options",Jo:"_platinum_request_status",Ko:"_protected_audience_enabled",me:"quantity",Wg:"redact_device_info",ii:"referral_exclusion_definition",Or:"_request_start_time",Sb:"restricted_data_processing",Lo:"retoken",Mo:"sample_rate",ji:"screen_name",Ic:"screen_resolution",kl:"_script_source",No:"search_term",pd:"send_page_view",rd:"send_to",sd:"server_container_url",Oo:"session_attributes_encoded",
wf:"session_duration",Xg:"session_engaged",ki:"session_engaged_time",Tb:"session_id",Yg:"session_number",xf:"_shared_user_id",ne:"delivery_postal_code",Pr:"_tag_firing_delay",Qr:"_tag_firing_time",Rr:"temporary_client_id",li:"_timezone",mi:"topmost_url",Zg:"tracking_id",ni:"traffic_type",Pa:"transaction_id",rc:"transport_url",Po:"trip_type",ud:"update",Db:"url_passthrough",ml:"uptgs",yf:"_user_agent_architecture",zf:"_user_agent_bitness",Af:"_user_agent_full_version_list",Bf:"_user_agent_mobile",
Cf:"_user_agent_model",Df:"_user_agent_platform",Ef:"_user_agent_platform_version",Ff:"_user_agent_wow64",ub:"user_data",nl:"user_data_auto_latency",ol:"user_data_auto_meta",pl:"user_data_auto_multi",ql:"user_data_auto_selectors",rl:"user_data_auto_status",Eb:"user_data_mode",sl:"user_data_settings",La:"user_id",Ub:"user_properties",tl:"_user_region",Gf:"us_privacy_string",Ma:"value",vl:"wbraid_multiple_conversions",Jc:"_fpm_parameters",xi:"_host_name",Vl:"_in_page_command",zi:"_ip_override",Zl:"_is_passthrough_cid",
Hi:"_measurement_type",Dd:"non_personalized_ads",Pi:"_sst_parameters",qp:"sgtm_geo_user_country",de:"conversion_label",ya:"page_location",Cc:"global_developer_id_string",oe:"tc_privacy_string"}};var ti={},ui=(ti[N.m.ia]="gcu",ti[N.m.oc]="gclgb",ti[N.m.ob]="gclaw",ti[N.m.Ak]="gclid_len",ti[N.m.Zd]="gclgs",ti[N.m.ae]="gcllp",ti[N.m.be]="gclst",ti[N.m.dd]="auid",ti[N.m.Gg]="dscnt",ti[N.m.Hg]="fcntr",ti[N.m.Ig]="flng",ti[N.m.Jg]="mid",ti[N.m.Dk]="bttype",ti[N.m.Qb]="gacid",ti[N.m.de]="label",ti[N.m.ed]="capi",ti[N.m.Kg]="pscdl",ti[N.m.sb]="currency_code",ti[N.m.Yh]="clobs",ti[N.m.cf]="vdltv",ti[N.m.Zh]="clolo",ti[N.m.ai]="clolb",ti[N.m.Fk]="_dbg",ti[N.m.ff]="oedeld",ti[N.m.Ac]="edid",ti[N.m.Sg]=
"gac",ti[N.m.he]="gacgb",ti[N.m.Qk]="gacmcov",ti[N.m.ie]="gdpr",ti[N.m.Cc]="gdid",ti[N.m.je]="_ng",ti[N.m.nf]="gpp_sid",ti[N.m.pf]="gpp",ti[N.m.Vk]="gsaexp",ti[N.m.qf]="_tu",ti[N.m.Ec]="frm",ti[N.m.hi]="gtm_up",ti[N.m.kd]="lps",ti[N.m.Ug]="did",ti[N.m.md]="fcntr",ti[N.m.nd]="flng",ti[N.m.od]="mid",ti[N.m.uf]=void 0,ti[N.m.Cb]="tiba",ti[N.m.Sb]="rdp",ti[N.m.Tb]="ecsid",ti[N.m.xf]="ga_uid",ti[N.m.ne]="delopc",ti[N.m.oe]="gdpr_consent",ti[N.m.Pa]="oid",ti[N.m.ml]="uptgs",ti[N.m.yf]="uaa",ti[N.m.zf]=
"uab",ti[N.m.Af]="uafvl",ti[N.m.Bf]="uamb",ti[N.m.Cf]="uam",ti[N.m.Df]="uap",ti[N.m.Ef]="uapv",ti[N.m.Ff]="uaw",ti[N.m.nl]="ec_lat",ti[N.m.ol]="ec_meta",ti[N.m.pl]="ec_m",ti[N.m.ql]="ec_sel",ti[N.m.rl]="ec_s",ti[N.m.Eb]="ec_mode",ti[N.m.La]="userId",ti[N.m.Gf]="us_privacy",ti[N.m.Ma]="value",ti[N.m.vl]="mcov",ti[N.m.xi]="hn",ti[N.m.Vl]="gtm_ee",ti[N.m.zi]="uip",ti[N.m.Hi]="mt",ti[N.m.Dd]="npa",ti[N.m.qp]="sg_uc",ti[N.m.Wh]=null,ti[N.m.Ic]=null,ti[N.m.tb]=null,ti[N.m.Ca]=null,ti[N.m.ya]=null,ti[N.m.Xa]=
null,ti[N.m.mi]=null,ti[N.m.Jc]=null,ti[N.m.Pe]=null,ti[N.m.Qe]=null,ti[N.m.Dc]=null,ti);function vi(a,b){if(a){var c=a.split("x");c.length===2&&(wi(b,"u_w",c[0]),wi(b,"u_h",c[1]))}}
function xi(a){var b=yi;b=b===void 0?zi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var l=c;if(l){for(var n=[],p=0;p<l.length;p++){var q=l[p],r=[];q&&(r.push(Ai(q.value)),r.push(Ai(q.quantity)),r.push(Ai(q.item_id)),r.push(Ai(q.start_date)),r.push(Ai(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function zi(a){return Bi(a.item_id,a.id,a.item_name)}function Bi(){for(var a=m(Fa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function Ci(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function wi(a,b,c){c===void 0||c===null||c===""&&!Sg[b]||(a[b]=c)}function Ai(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var Di={},Ei=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=vb(0,1)===0,b=vb(0,1)===0,c++,c>30)return;return a},Gi={ir:Fi};function Fi(a,b){var c=Di[b];if(!(vb(0,9999)<c.probability*(c.controlId2?4:2)*1E4))return a;var d=c.studyId,e=c.experimentId,f=c.controlId,g=c.controlId2;if(!((a.exp||{})[e]||(a.exp||{})[f]||g&&(a.exp||{})[g])){var h=Ei()?0:1;g&&(h|=(Ei()?0:1)<<1);h===0?Hi(a,e,d):h===1?Hi(a,f,d):h===2&&Hi(a,g,d)}return a}
function Ii(a,b){return Di[b]?!!Di[b].active||Di[b].probability>.5||!!(a.exp||{})[Di[b].experimentId]:!1}function Ji(a,b){for(var c=a.exp||{},d=m(Object.keys(c).map(Number)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c[f]===b)return f}}function Hi(a,b,c){var d=a.exp||{};d[b]=c;a.exp=d};var O={N:{Yj:"call_conversion",Rd:"ccm_conversion",xa:"conversion",Qo:"floodlight",If:"ga_conversion",wd:"gcp_remarketing",Fi:"landing_page",Na:"page_view",ve:"fpm_test_hit",Hb:"remarketing",Vb:"user_data_lead",xb:"user_data_web"}};var Ni=function(){this.C=new Set;this.H=new Set},Oi=function(a){var b=Fg.U;a=a===void 0?[]:a;var c=[].concat(Ba(b.C)).concat([].concat(Ba(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Pi=function(){var a=[].concat(Ba(Fg.U.C));a.sort(function(b,c){return b-c});return a},Qi=function(){var a=Fg.U,b=Dg(44);a.C=new Set;if(b!=="")for(var c=m(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Ri={},Si={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Ti={__paused:1,__tg:1},Ui;for(Ui in Si)Si.hasOwnProperty(Ui)&&(Ti[Ui]=1);var Vi=!1;function Wi(){var a=!1;a=!0;return a}var Xi=F(218)?Bg(45,Wi()):Wi(),Yi,Zi=!1;Yi=Zi;var $i=null,aj=null,bj={},cj={},dj="";Ri.Qi=dj;var Fg=new function(){this.U=new Ni;this.H=this.C=!1;this.M=0;this.ja=this.R=!1};function ej(){var a=Dg(18),b=a.length;return a[b-1]==="/"?a.substring(0,b-1):a}function fj(){return Fg.H?F(84)?Fg.M===0:Fg.M!==1:!1}function gj(a){for(var b={},c=m(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var hj=/:[0-9]+$/,ij=/^\d+\.fls\.doubleclick\.net$/;function jj(a,b,c,d){var e=kj(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function kj(a,b,c){for(var d={},e=m(a.split("&")),f=e.next();!f.done;f=e.next()){var g=m(f.value.split("=")),h=g.next().value,l=Aa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=l.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function lj(a){try{return decodeURIComponent(a)}catch(b){}}function mj(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=nj(a.protocol)||nj(w.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:w.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||w.location.hostname).replace(hj,"").toLowerCase());return oj(a,b,c,d,e)}
function oj(a,b,c,d,e){var f,g=nj(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=pj(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(hj,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||kb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var l=f.split("/");(d||[]).indexOf(l[l.length-
1])>=0&&(l[l.length-1]="");f=l.join("/");break;case "query":f=a.search.replace("?","");e&&(f=jj(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function nj(a){return a?a.replace(":","").toLowerCase():""}function pj(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var qj={},rj=0;
function sj(a){var b=qj[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||kb("TAGGING",1),d="/"+d);var e=c.hostname.replace(hj,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};rj<5&&(qj[a]=b,rj++)}return b}function tj(a,b,c){var d=sj(a);return Sb(b,d,c)}
function uj(a){var b=sj(w.location.href),c=mj(b,"host",!1);if(c&&c.match(ij)){var d=mj(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var vj=/gtag[.\/]js/,wj=/gtm[.\/]js/,xj=!1;
function yj(a){if((a.scriptContainerId||"").indexOf("GTM-")>=0){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){for(var e=Fg.H,f=sj(d),g=e?f.pathname:""+f.hostname+f.pathname,h=A.scripts,l="",n=0;n<h.length;++n){var p=h[n];if(!(p.innerHTML.length===0||!e&&p.innerHTML.indexOf(a.scriptContainerId||"SHOULD_NOT_BE_SET")<0||p.innerHTML.indexOf(g)<0)){if(p.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){b=String(n);break a}l=String(n)}}if(l){b=l;break a}}b=void 0}var q=b;if(q)return xj=!0,q}var r=
[].slice.call(A.scripts);return a.scriptElement?String(r.indexOf(a.scriptElement)):"-1"}function zj(a){if(xj)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(vj.test(c))return"3";if(wj.test(c))return"2"}return"0"};function P(a){kb("GTM",a)};function Aj(a){var b=Bj().destinationArray[a],c=Bj().destination[a];return b&&b.length>0?b[0]:c}function Cj(a,b){var c=Bj();c.pending||(c.pending=[]);ub(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Dj(){var a=w.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=m(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Ej=function(){this.container={};this.destination={};this.destinationArray={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Dj()};
function Bj(){var a=Dc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Ej,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.destinationArray||(c.destinationArray={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Dj());return c};function Fj(){return Cg(7)&&Gj().some(function(a){return a===Dg(5)})}function Hj(){var a,b;b=b===void 0?[]:b;var c,d,e=(c=data)==null?void 0:(d=c.blob)==null?void 0:d[55];a=Array.isArray(e)?e:b;return a!=null?a:[]}function Ij(){return Dg(6)||"_"+Dg(5)}function Jj(){var a=Dg(10);return a?a.split("|"):[Dg(5)]}function Gj(){var a=Dg(9);return a?a.split("|").filter(function(b){return b.indexOf("GTM-")!==0}):[]}function Kj(){var a=Lj(Mj()),b=a&&a.parent;if(b)return Lj(b)}
function Nj(){var a=Lj(Mj());if(a){for(;a.parent;){var b=Lj(a.parent);if(!b)break;a=b}return a}}function Lj(a){var b=Bj();return a.isDestination?Aj(a.ctid):b.container[a.ctid]}function Oj(){var a=Bj();if(a.pending){for(var b,c=[],d=!1,e=Jj(),f=Gj(),g={},h=0;h<a.pending.length;g={qg:void 0},h++)g.qg=a.pending[h],ub(g.qg.target.isDestination?f:e,function(l){return function(n){return n===l.qg.target.ctid}}(g))?d||(b=g.qg.onLoad,d=!0):c.push(g.qg);a.pending=c;if(b)try{b(Ij())}catch(l){}}}
function Pj(){for(var a=Dg(5),b=Jj(),c=Gj(),d=Hj(),e=function(q,r){var u={canonicalContainerId:Dg(6),scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};Bc&&(u.scriptElement=Bc);Cc&&(u.scriptSource=Cc);Kj()===void 0&&(u.htmlLoadOrder=yj(u),u.loadScriptType=zj(u));var t,v;switch(r){case 0:t=function(z){f.container[q]=z};v=f.container[q];break;case 1:if(F(269)){t=function(z){f.destinationArray[q]=f.destinationArray[q]||[];f.destinationArray[q].unshift(z)};var x,y=((x=f.destinationArray[q])==
null?void 0:x[0])||f.destination[q];!y||y.state!==0&&y.state!==1||(v=y)}else t=function(z){f.destination[q]=z},v=Aj(q);break;case 2:F(269)?(t=function(z){f.destinationArray[q]=f.destinationArray[q]||[];f.destinationArray[q].push(z)},v=void 0):(t=function(z){f.destination[q]=z},v=Aj(q))}t&&(v?(v.state===0&&P(93),oa(Object,"assign").call(Object,v,u)):t(u))},f=Bj(),g=m(b),h=g.next();!h.done;h=g.next())e(h.value,0);for(var l=m(c),n=l.next();!n.done;n=l.next()){var p=n.value;d.includes(p)?e(p,1):e(p,2)}f.canonical[Ij()]=
{};Oj()}function Qj(){var a=Ij();return!!Bj().canonical[a]}function Rj(a){return!!Bj().container[a]}function Sj(a){var b=Aj(a);return b?b.state!==0:!1}function Mj(){return{ctid:Dg(5),isDestination:Cg(7)}}function Tj(a,b,c){var d=Mj(),e=Bj().container[a];e&&e.state!==3||(Bj().container[a]={state:1,context:b,parent:d},Cj({ctid:a,isDestination:!1},c))}function Uj(){var a=Bj().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Vj(){var a={};yb(Bj().destination,function(b,c){(c==null?void 0:c.state)===0&&(a[b]=c)});yb(Bj().destinationArray,function(b,c){var d=c[0];(d==null?void 0:d.state)===0&&(a[b]=d)});return a}function Wj(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Xj(){for(var a=Bj(),b=m(Jj()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};function Yj(a){a=a===void 0?[]:a;return Oi(a).join("~")}function Zj(){if(!F(118))return"";var a,b;return(((a=Lj(Mj()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};var ak={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},bk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function ck(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return sj(""+c+b).href}}function dk(a,b){if(fj()||Fg.C)return ck(a,b)}
function ek(){return!!Ri.Qi&&Ri.Qi.split("@@").join("")!=="SGTM_TOKEN"}function fk(a){for(var b=m([N.m.sd,N.m.rc]),c=b.next();!c.done;c=b.next()){var d=Q(a,c.value);if(d)return d}}function gk(a,b,c){c=c===void 0?"":c;if(!fj())return a;var d=b?ak[a]||"":"";d==="/gs"&&(c="");return""+ej()+d+c}function hk(a){if(!fj())return a;for(var b=m(bk),c=b.next();!c.done;c=b.next()){var d=c.value;if(Kb(a,""+ej()+d))return a+"&_uip="+encodeURIComponent("::")}return a};function ik(a){var b=String(a[nf.Qa]||"").replace(/_/g,"");return Kb(b,"cvt")?"cvt":b}var jk=w.location.search.indexOf("?gtm_latency=")>=0||w.location.search.indexOf("&gtm_latency=")>=0;var kk=Math.random(),lk,mk=Gg(27);lk=jk||kk<mk;var nk,ok=Gg(42);nk=jk||kk>=1-ok;function pk(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var qk=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};var rk,sk;a:{for(var tk=["CLOSURE_FLAGS"],uk=Ga,vk=0;vk<tk.length;vk++)if(uk=uk[tk[vk]],uk==null){sk=null;break a}sk=uk}var wk=sk&&sk[610401301];rk=wk!=null?wk:!1;function xk(){var a=Ga.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var yk,zk=Ga.navigator;yk=zk?zk.userAgentData||null:null;function Ak(a){if(!rk||!yk)return!1;for(var b=0;b<yk.brands.length;b++){var c=yk.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function Bk(a){return xk().indexOf(a)!=-1};function Ck(){return rk?!!yk&&yk.brands.length>0:!1}function Dk(){return Ck()?!1:Bk("Opera")}function Ek(){return Bk("Firefox")||Bk("FxiOS")}function Fk(){return Ck()?Ak("Chromium"):(Bk("Chrome")||Bk("CriOS"))&&!(Ck()?0:Bk("Edge"))||Bk("Silk")};function Gk(){return rk?!!yk&&!!yk.platform:!1}function Hk(){return Bk("iPhone")&&!Bk("iPod")&&!Bk("iPad")}function Ik(){Hk()||Bk("iPad")||Bk("iPod")};var Jk=function(a){Jk[" "](a);return a};Jk[" "]=function(){};Dk();Ck()||Bk("Trident")||Bk("MSIE");Bk("Edge");!Bk("Gecko")||xk().toLowerCase().indexOf("webkit")!=-1&&!Bk("Edge")||Bk("Trident")||Bk("MSIE")||Bk("Edge");xk().toLowerCase().indexOf("webkit")!=-1&&!Bk("Edge")&&Bk("Mobile");Gk()||Bk("Macintosh");Gk()||Bk("Windows");(Gk()?yk.platform==="Linux":Bk("Linux"))||Gk()||Bk("CrOS");Gk()||Bk("Android");Hk();Bk("iPad");Bk("iPod");Ik();xk().toLowerCase().indexOf("kaios");Ek();Hk()||Bk("iPod");Bk("iPad");!Bk("Android")||Fk()||Ek()||Dk()||Bk("Silk");Fk();!Bk("Safari")||Fk()||(Ck()?0:Bk("Coast"))||Dk()||(Ck()?0:Bk("Edge"))||(Ck()?Ak("Microsoft Edge"):Bk("Edg/"))||(Ck()?Ak("Opera"):Bk("OPR"))||Ek()||Bk("Silk")||Bk("Android")||Ik();var Kk={},Lk=null,Mk=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Lk){Lk={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],l=0;l<5;l++){var n=g.concat(h[l].split(""));Kk[l]=n;for(var p=0;p<n.length;p++){var q=n[p];Lk[q]===void 0&&(Lk[q]=p)}}}for(var r=Kk[f],u=Array(Math.floor(b.length/3)),t=r[64]||"",v=0,x=0;v<b.length-2;v+=3){var y=b[v],
z=b[v+1],D=b[v+2],E=r[y>>2],M=r[(y&3)<<4|z>>4],G=r[(z&15)<<2|D>>6],L=r[D&63];u[x++]=""+E+M+G+L}var U=0,ia=t;switch(b.length-v){case 2:U=b[v+1],ia=r[(U&15)<<2]||t;case 1:var S=b[v];u[x]=""+r[S>>2]+r[(S&3)<<4|U>>4]+ia+t}return u.join("")};var Nk=function(a){return decodeURIComponent(a.replace(/\+/g," "))};var Ok=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Pk=/#|$/,Qk=function(a,b){var c=a.search(Pk),d=Ok(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Nk(a.slice(d,e!==-1?e:0))},Rk=/[?&]($|#)/,Sk=function(a,b,c){for(var d,e=a.search(Pk),f=0,g,h=[];(g=Ok(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Rk,"$1");var l,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var u=d.indexOf("?"),t;u<0||u>r?(u=r,t=""):t=d.substring(u+1,r);q=[d.slice(0,u),t,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;l=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else l=d;return l};function Tk(a,b,c,d,e,f,g){var h=Qk(c,"fmt");if(d){var l=Qk(c,"random"),n=Qk(c,"label")||"";if(!l)return!1;var p=Mk(Nk(n)+":"+Nk(l));if(!pk(a,p,d))return!1}h&&Number(h)!==4&&(c=Sk(c,"rfmt",h));var q=Sk(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||Uk(g);Lc(q,function(){g==null||Vk(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||Vk(g);e==null||e()},f,r||void 0);return!0};var Wk={},Xk=(Wk[1]={},Wk[2]={},Wk[3]={},Wk[4]={},Wk);function Yk(a,b,c){var d=Zk(b,c);if(d){var e=Xk[b][d];e||(e=Xk[b][d]=[]);e.push(oa(Object,"assign").call(Object,{},a))}}function $k(a,b){var c=Zk(a,b);if(c){var d=Xk[a][c];d&&(Xk[a][c]=d.filter(function(e){return!e.on}))}}function al(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Zk(a,b){var c=b;if(b[0]==="/"){var d;c=((d=w.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function bl(a){var b=Fa.apply(1,arguments);nk&&(Yk(a,2,b[0]),Yk(a,3,b[0]));Xc.apply(null,Ba(b))}function cl(a){var b=Fa.apply(1,arguments);nk&&Yk(a,2,b[0]);return Yc.apply(null,Ba(b))}function dl(a){var b=Fa.apply(1,arguments);nk&&Yk(a,3,b[0]);Oc.apply(null,Ba(b))}
function el(a){var b=Fa.apply(1,arguments),c=b[0];nk&&(Yk(a,2,c),Yk(a,3,c));return $c.apply(null,Ba(b))}function fl(a){var b=Fa.apply(1,arguments);nk&&Yk(a,1,b[0]);Lc.apply(null,Ba(b))}function gl(a){var b=Fa.apply(1,arguments);b[0]&&nk&&Yk(a,4,b[0]);Nc.apply(null,Ba(b))}function hl(a){var b=Fa.apply(1,arguments);nk&&Yk(a,1,b[2]);return Tk.apply(null,Ba(b))};var il={Ia:{qe:0,ue:1,Ji:2}};il.Ia[il.Ia.qe]="FULL_TRANSMISSION";il.Ia[il.Ia.ue]="LIMITED_TRANSMISSION";il.Ia[il.Ia.Ji]="NO_TRANSMISSION";var jl={Z:{Gb:0,Ga:1,xc:2,Kc:3}};jl.Z[jl.Z.Gb]="NO_QUEUE";jl.Z[jl.Z.Ga]="ADS";jl.Z[jl.Z.xc]="ANALYTICS";jl.Z[jl.Z.Kc]="MONITORING";function kl(){var a=Dc("google_tag_data",{});return a.ics=a.ics||new ll}var ll=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
ll.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;kb("TAGGING",19);b==null?kb("TAGGING",18):ml(this,a,b==="granted",c,d,e,f,g)};ll.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)ml(this,a[d],void 0,void 0,"","",b,c)};
var ml=function(a,b,c,d,e,f,g,h){var l=a.entries,n=l[b]||{},p=n.region,q=d&&rb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),u={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)l[b]=u;r&&w.setTimeout(function(){l[b]===u&&u.quiet&&(kb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=ll.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var l=m(d),n=l.next();!n.done;n=l.next())nl(this,n.value)}else if(b!==void 0&&h!==b)for(var p=m(d),q=p.next();!q.done;q=p.next())nl(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,l=c&&rb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||l===e||(l===d?h!==e:!l&&!h)){var n={region:g.region,declare_region:l,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var l=b.containerScopedDefaults[g];if(l===3)return 1;if(l===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Gd:b})};var nl=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.jn=!0)}};ll.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.jn){d.jn=!1;try{d.Gd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var ol=!1,pl=!1,ql={},rl={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(ql.ad_storage=1,ql.analytics_storage=1,ql.ad_user_data=1,ql.ad_personalization=1,ql),usedContainerScopedDefaults:!1};function sl(a){var b=kl();b.accessedAny=!0;return(rb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,rl)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function tl(a){var b=kl();b.accessedAny=!0;return b.getConsentState(a,rl)}function ul(a){var b=kl();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function vl(){if(!Xa(7))return!1;var a=kl();a.accessedAny=!0;if(a.active)return!0;if(!rl.usedContainerScopedDefaults)return!1;for(var b=m(Object.keys(rl.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(rl.containerScopedDefaults[c.value]!==1)return!0;return!1}function wl(a,b){kl().addListener(a,b)}
function xl(a,b){kl().notifyListeners(a,b)}function yl(a,b){function c(){for(var e=0;e<b.length;e++)if(!ul(b[e]))return!0;return!1}if(c()){var d=!1;wl(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function zl(a,b){function c(){for(var h=[],l=0;l<e.length;l++){var n=e[l];sl(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var l=0;l<h.length;l++)f[h[l]]=!0}var e=rb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),wl(e,function(h){function l(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?l(n):w.setTimeout(function(){l(c())},500)}}))};var Al={},Bl=(Al[jl.Z.Gb]=il.Ia.qe,Al[jl.Z.Ga]=il.Ia.qe,Al[jl.Z.xc]=il.Ia.qe,Al[jl.Z.Kc]=il.Ia.qe,Al),Cl=function(a,b){this.C=a;this.consentTypes=b};Cl.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return sl(a)});case 1:return this.consentTypes.some(function(a){return sl(a)});default:rc(this.C,"consentsRequired had an unknown type")}};
var Dl={},El=(Dl[jl.Z.Gb]=new Cl(0,[]),Dl[jl.Z.Ga]=new Cl(0,["ad_storage"]),Dl[jl.Z.xc]=new Cl(0,["analytics_storage"]),Dl[jl.Z.Kc]=new Cl(1,["ad_storage","analytics_storage"]),Dl);var Gl=function(a){var b=this;this.type=a;this.C=[];wl(El[a].consentTypes,function(){Fl(b)||b.flush()})};Gl.prototype.flush=function(){for(var a=m(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var Fl=function(a){return Bl[a.type]===il.Ia.Ji&&!El[a.type].isConsentGranted()},Hl=function(a,b){Fl(a)?a.C.push(b):b()},Il=new Map;function Jl(a){Il.has(a)||Il.set(a,new Gl(a));return Il.get(a)};var Kl={X:{zn:"aw_user_data_cache",Oh:"cookie_deprecation_label",Dg:"diagnostics_page_id",Lr:"em_registry",oi:"eab",Ro:"fl_user_data_cache",Vo:"ga4_user_data_cache",se:"ip_geo_data_cache",yi:"ip_geo_fetch_in_progress",Gi:"local_cookie_cache_map",gm:"nb_data",Ki:"page_experiment_ids",we:"pt_data",im:"pt_listener_set",qm:"service_worker_endpoint",tm:"shared_user_id",vm:"shared_user_id_requested",ph:"shared_user_id_source"}};var Ll=function(a){return ff(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(Kl.X);
function Ml(a,b){b=b===void 0?!1:b;if(Ll(a)){var c,d,e=(d=(c=Dc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},l={set:function(n){f=n;l.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=m(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=l}}}
function Nl(a,b){var c=Ml(a,!0);c&&c.set(b)}function Ol(a){var b;return(b=Ml(a))==null?void 0:b.get()}function Pl(a,b){var c=Ml(a);if(!c){c=Ml(a,!0);if(!c)return;c.set(b)}return c.get()}function Ql(a,b){if(typeof b==="function"){var c;return(c=Ml(a,!0))==null?void 0:c.subscribe(b)}}function Rl(a,b){var c=Ml(a);return c?c.unsubscribe(b):!1};var Sl={},Tl=(Sl.tdp=1,Sl.exp=1,Sl.pid=1,Sl.dl=1,Sl.seq=1,Sl.t=1,Sl.v=1,Sl),Ul=["mcc"],Vl={},Wl={},Xl=!1;function Yl(a,b,c){Wl[a]=b;(c===void 0||c)&&Zl(a)}function Zl(a,b){Vl[a]!==void 0&&(b===void 0||!b)||Kb(Dg(5),"GTM-")&&a==="mcc"||(Vl[a]=!0)}function $l(a){a=a===void 0?!1:a;var b=Object.keys(Vl).filter(function(c){return Vl[c]===!0&&Wl[c]!==void 0&&(a||!Ul.includes(c))});am(b);return b.map(function(c){var d=Wl[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("")+"&z=0"}
function bm(a){var b=$l(a===void 0?!1:a),c="https://"+Dg(21),d="/td?id="+Dg(5);return""+gk(c)+d+b}function am(a){a.forEach(function(b){Tl[b]||(Vl[b]=!1)})}function cm(a){a=a===void 0?!1:a;if(Fg.ja&&nk&&Dg(5)){var b=Jl(jl.Z.Kc);if(Fl(b))Xl||(Xl=!0,Hl(b,cm));else{var c=bm(a),d={destinationId:Dg(5),endpoint:61};a?el(d,c,void 0,{pg:!0},void 0,function(){dl(d,c+"&img=1")}):dl(d,c);Xl=!1}}}function dm(){Object.keys(Vl).filter(function(a){return Vl[a]&&!Tl[a]}).length>0&&cm(!0)}var em;
function fm(){if(Ol(Kl.X.Dg)===void 0){var a=function(){Nl(Kl.X.Dg,vb());em=0};a();w.setInterval(a,864E5)}else Ql(Kl.X.Dg,function(){em=0});em=0}function gm(){fm();Yl("v","3");Yl("t","t");Yl("pid",function(){return String(Ol(Kl.X.Dg))});Yl("seq",function(){return String(++em)});Yl("exp",Yj());Qc(w,"pagehide",dm)};var hm=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],im=[N.m.sd,N.m.rc,N.m.fe,N.m.Qb,N.m.Tb,N.m.La,N.m.Wa,N.m.Va,N.m.qb,N.m.Rb],jm=!1,km=!1,lm={},mm={};function nm(){!km&&jm&&(hm.some(function(a){return rl.containerScopedDefaults[a]!==1})||om("mbc"));km=!0}function om(a){nk&&(Yl(a,"1"),cm())}function pm(a,b){if(!lm[b]&&(lm[b]=!0,mm[b]))for(var c=m(im),d=c.next();!d.done;d=c.next())if(Q(a,d.value)){om("erc");break}};function qm(a){kb("HEALTH",a)};var rm={},sm=!1;function tm(){function a(){c!==void 0&&Rl(Kl.X.se,c);try{var e=Ol(Kl.X.se);rm=JSON.parse(e)}catch(f){P(123),qm(2),rm={}}sm=!0;b()}var b=um,c=void 0,d=Ol(Kl.X.se);d?a(d):(c=Ql(Kl.X.se,a),vm())}
function vm(){function a(b){Nl(Kl.X.se,b||"{}");Nl(Kl.X.yi,!1)}if(!Ol(Kl.X.yi)){Nl(Kl.X.yi,!0);try{w.fetch("https://www.google.com/ccm/geo",{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(b){b.ok?b.text().then(function(c){a(c)},function(){a()}):a()},function(){a()})}catch(b){a()}}}function wm(){var a=Dg(22);try{return JSON.parse(ib(a))}catch(b){return P(123),qm(2),{}}}function xm(){return rm["0"]||""}function ym(){return rm["1"]||""}
function zm(){var a=!1;return a}function Am(){return rm["6"]!==!1}function Bm(){var a="";return a}function Cm(){var a="";return a};var Dm={},Em=Object.freeze((Dm[N.m.Ob]=1,Dm[N.m.Fg]=1,Dm[N.m.Sh]=1,Dm[N.m.Pb]=1,Dm[N.m.Ca]=1,Dm[N.m.qb]=1,Dm[N.m.rb]=1,Dm[N.m.Bb]=1,Dm[N.m.fd]=1,Dm[N.m.Rb]=1,Dm[N.m.Va]=1,Dm[N.m.zc]=1,Dm[N.m.df]=1,Dm[N.m.Ea]=1,Dm[N.m.Gk]=1,Dm[N.m.hf]=1,Dm[N.m.Pg]=1,Dm[N.m.Qg]=1,Dm[N.m.fe]=1,Dm[N.m.Rk]=1,Dm[N.m.Dc]=1,Dm[N.m.ke]=1,Dm[N.m.Uk]=1,Dm[N.m.Tg]=1,Dm[N.m.gi]=1,Dm[N.m.Fc]=1,Dm[N.m.Gc]=1,Dm[N.m.Wa]=1,Dm[N.m.ii]=1,Dm[N.m.Sb]=1,Dm[N.m.pd]=1,Dm[N.m.rd]=1,Dm[N.m.sd]=1,Dm[N.m.wf]=1,Dm[N.m.ki]=1,Dm[N.m.ne]=1,Dm[N.m.rc]=
1,Dm[N.m.ud]=1,Dm[N.m.sl]=1,Dm[N.m.Ub]=1,Dm[N.m.Jc]=1,Dm[N.m.Pi]=1,Dm));Object.freeze([N.m.ya,N.m.Xa,N.m.Cb,N.m.tb,N.m.ji,N.m.La,N.m.ei,N.m.po]);
var Fm={},Gm=Object.freeze((Fm[N.m.Tn]=1,Fm[N.m.Un]=1,Fm[N.m.Vn]=1,Fm[N.m.Wn]=1,Fm[N.m.Xn]=1,Fm[N.m.ao]=1,Fm[N.m.bo]=1,Fm[N.m.co]=1,Fm[N.m.fo]=1,Fm[N.m.Yd]=1,Fm)),Hm={},Im=Object.freeze((Hm[N.m.wk]=1,Hm[N.m.xk]=1,Hm[N.m.Ud]=1,Hm[N.m.Vd]=1,Hm[N.m.yk]=1,Hm[N.m.Yc]=1,Hm[N.m.Wd]=1,Hm[N.m.jc]=1,Hm[N.m.yc]=1,Hm[N.m.kc]=1,Hm[N.m.zb]=1,Hm[N.m.Xd]=1,Hm[N.m.mc]=1,Hm[N.m.zk]=1,Hm)),Jm=Object.freeze([N.m.Ob,N.m.Ue,N.m.Pb,N.m.zc,N.m.fe,N.m.rf,N.m.pd,N.m.ud]),Km=Object.freeze([].concat(Ba(Jm))),Lm=Object.freeze([N.m.rb,
N.m.Qg,N.m.wf,N.m.ki,N.m.Ng]),Mm=Object.freeze([].concat(Ba(Lm))),Nm={},Om=(Nm[N.m.aa]="1",Nm[N.m.ka]="2",Nm[N.m.W]="3",Nm[N.m.Ka]="4",Nm),Pm={},Qm=Object.freeze((Pm.search="s",Pm.youtube="y",Pm.playstore="p",Pm.shopping="h",Pm.ads="a",Pm.maps="m",Pm));function Rm(a){return typeof a!=="object"||a===null?{}:a}function Sm(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Tm(a){if(a!==void 0&&a!==null)return Sm(a)}function Um(a){return typeof a==="number"?a:Tm(a)};function Vm(a){return a&&a.indexOf("pending:")===0?Wm(a.substr(8)):!1}function Wm(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Fb();return b<c+3E5&&b>c-9E5};var Xm=!1,Ym=!1,Zm=!1,$m=0,an=!1,bn=[];function cn(a){if($m===0)an&&bn&&(bn.length>=100&&bn.shift(),bn.push(a));else if(dn()){var b=Dg(41),c=Dc(b,[]);c.length>=50&&c.shift();c.push(a)}}function en(){fn();Rc(A,"TAProdDebugSignal",en)}function fn(){if(!Ym){Ym=!0;gn();var a=bn;bn=void 0;a==null||a.forEach(function(b){cn(b)})}}
function gn(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Wm(a)?$m=1:!Vm(a)||Xm||Zm?$m=2:(Zm=!0,Qc(A,"TAProdDebugSignal",en,!1),w.setTimeout(function(){fn();Xm=!0},200))}function dn(){if(!an)return!1;switch($m){case 1:case 0:return!0;case 2:return!1;default:return!1}};var hn=!1;function jn(a,b){var c=Jj(),d=Gj();Dg(26);if(dn()){var e=kn("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;cn(e)}}
function ln(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.hb;e=a.isBatched;var f;if(f=dn()){var g;a:switch(c.endpoint){case 19:case 47:g=!0;break a;default:g=!1}f=!g}if(f){var h=kn("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);cn(h)}}function mn(a){dn()&&ln(a())}
function kn(a,b){b=b===void 0?{}:b;b.groupId=nn;var c,d=b,e=on,f={publicId:pn};d.eventId!=null&&(f.eventId=d.eventId);d.priorityId!=null&&(f.priorityId=d.priorityId);d.eventName&&(f.eventName=d.eventName);d.groupId&&(f.groupId=d.groupId);d.tagName&&(f.tagName=d.tagName);c={containerProduct:"GTM",key:f,version:e,messageType:a};c.containerProduct=hn?"OGT":"GTM";c.key.targetRef=qn;return c}var pn="",on="",qn={ctid:"",isDestination:!1},nn;
function rn(a){var b=Dg(5),c=Fj(),d=Dg(6),e=Dg(1);Dg(23);$m=0;an=!0;gn();nn=a;pn=b;on=e;hn=Xi;qn={ctid:b,isDestination:c,canonicalId:d}};var sn=[N.m.aa,N.m.ka,N.m.W,N.m.Ka],tn,un;function vn(a){var b=a[N.m.hc];b||(b=[""]);for(var c={eg:0};c.eg<b.length;c={eg:c.eg},++c.eg)yb(a,function(d){return function(e,f){if(e!==N.m.hc){var g=Sm(f),h=b[d.eg],l=xm(),n=ym();pl=!0;ol&&kb("TAGGING",20);kl().declare(e,g,h,l,n)}}}(c))}
function wn(a){nm();!un&&tn&&om("crc");un=!0;var b=a[N.m.yg];b&&P(41);var c=a[N.m.hc];c?P(40):c=[""];for(var d={fg:0};d.fg<c.length;d={fg:d.fg},++d.fg)yb(a,function(e){return function(f,g){if(f!==N.m.hc&&f!==N.m.yg){var h=Tm(g),l=c[e.fg],n=Number(b),p=xm(),q=ym();n=n===void 0?0:n;ol=!0;pl&&kb("TAGGING",20);kl().default(f,h,l,p,q,n,rl)}}}(d))}
function xn(a){rl.usedContainerScopedDefaults=!0;var b=a[N.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(ym())&&!c.includes(xm()))return}yb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}rl.usedContainerScopedDefaults=!0;rl.containerScopedDefaults[d]=e==="granted"?3:2})}
function yn(a,b){nm();tn=!0;yb(a,function(c,d){var e=Sm(d);ol=!0;pl&&kb("TAGGING",20);kl().update(c,e,rl)});xl(b.eventId,b.priorityId)}function zn(a){a.hasOwnProperty("all")&&(rl.selectedAllCorePlatformServices=!0,yb(Qm,function(b){rl.corePlatformServices[b]=a.all==="granted";rl.usedCorePlatformServices=!0}));yb(a,function(b,c){b!=="all"&&(rl.corePlatformServices[b]=c==="granted",rl.usedCorePlatformServices=!0)})}function An(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return sl(b)})}
function Bn(a,b){wl(a,b)}function Cn(a,b){zl(a,b)}function On(a,b){yl(a,b)}function Pn(){var a=[N.m.aa,N.m.Ka,N.m.W];kl().waitForUpdate(a,500,rl)}function Qn(a){for(var b=m(a),c=b.next();!c.done;c=b.next()){var d=c.value;kl().clearTimeout(d,void 0,rl)}xl()}function Rn(){if(!Yi)for(var a=Am()?gj(Hg(5)):gj(Hg(4)),b=0;b<sn.length;b++){var c=sn[b],d=c,e=a[c]?"granted":"denied";kl().implicit(d,e)}};var Sn=!1;F(218)&&(Sn=Bg(49,Sn));var Tn=!1,Un=[];function Vn(){if(!Tn){Tn=!0;for(var a=Un.length-1;a>=0;a--)Un[a]();Un=[]}};var Wn=w.google_tag_manager=w.google_tag_manager||{};function Xn(a,b){return Wn[a]=Wn[a]||b()}function Yn(){var a=Dg(5),b=Zn;Wn[a]=Wn[a]||b}function $n(){var a=Dg(19);return Wn[a]=Wn[a]||{}}function ao(){var a=Dg(19);return Wn[a]}function bo(){var a=Wn.sequence||1;Wn.sequence=a+1;return a}w.google_tag_data=w.google_tag_data||{};function co(){if(Wn.pscdl!==void 0)Ol(Kl.X.Oh)===void 0&&Nl(Kl.X.Oh,Wn.pscdl);else{var a=function(c){Wn.pscdl=c;Nl(Kl.X.Oh,c)},b=function(){a("error")};try{zc.cookieDeprecationLabel?(a("pending"),zc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var eo=0;function fo(a){nk&&a===void 0&&eo===0&&(Yl("mcc","1"),eo=1)};function go(){var a=[],b=Number('')||0,c=Number('')||0;c||(c=b/100);var d=function(){var ja=!1;ja=!0;return ja}();a.push({oa:21,studyId:21,experimentId:105102050,controlId:105102051,controlId2:105102052,probability:c,active:d,la:0});var e=
Number('')||0,f=Number('0.10')||0;f||(f=e/100);var g=function(){var ja=!1;return ja}();a.push({oa:265,studyId:265,experimentId:115691063,controlId:115691064,controlId2:115691065,
probability:f,active:g,la:0});var h=Number('')||0,l=Number('')||0;l||(l=h/100);var n=function(){var ja=!1;return ja}();a.push({oa:228,studyId:228,experimentId:105177154,controlId:105177155,controlId2:105255245,probability:l,active:n,la:0});var p=Number('')||
0,q=Number('')||0;q||(q=p/100);var r=function(){var ja=!1;return ja}();a.push({oa:256,studyId:256,experimentId:115495938,controlId:115495939,controlId2:115495940,probability:q,active:r,la:0});var u=Number('')||
0,t=Number('')||0;t||(t=u/100);var v=function(){var ja=!1;return ja}();a.push({oa:257,studyId:257,experimentId:115495941,controlId:115495942,controlId2:115495943,probability:t,
active:v,la:0});var x=Number('')||0,y=Number('')||0;y||(y=x/100);var z=function(){var ja=!1;ja=!0;return ja}();a.push({oa:219,studyId:219,experimentId:104948811,
controlId:104948812,controlId2:0,probability:y,active:z,la:0});var D=Number('')||0,E=Number('1')||0;E||(E=D/100);var M=function(){var ja=!1;
return ja}();a.push({oa:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:E,active:M,la:0});var G=Number('')||0,L=Number('0.001')||0;L||(L=G/100);var U=function(){var ja=!1;
return ja}();a.push({oa:255,studyId:255,experimentId:105391252,controlId:105391253,controlId2:105446120,probability:L,active:U,la:0});var ia=Number('')||0,S=Number('')||0;S||(S=ia/100);var ca=function(){var ja=!1;return ja}();a.push({oa:235,studyId:235,experimentId:105357150,controlId:105357151,
controlId2:0,probability:S,active:ca,la:1});var ta=Number('')||0,ka=Number('')||0;ka||(ka=ta/100);var ea=function(){var ja=!1;return ja}();a.push({oa:264,studyId:264,experimentId:115752876,controlId:115752874,controlId2:115752875,probability:ka,active:ea,la:0});var X=Number('')||
0,la=Number('0.5')||0;la||(la=X/100);var ya=function(){var ja=!1;return ja}();a.push({oa:203,studyId:203,experimentId:115480710,controlId:115480709,controlId2:115489982,probability:la,active:ya,la:0});var xa=Number('')||0,Va=Number('')||
0;Va||(Va=xa/100);var $a=function(){var ja=!1;ja=!0;return ja}();a.push({oa:197,studyId:197,experimentId:105113532,controlId:105113531,controlId2:0,probability:Va,active:$a,la:0});var bc=Number('')||0,cc=Number('0.2')||0;cc||(cc=bc/100);var Lb=function(){var ja=!1;return ja}();a.push({oa:243,studyId:243,experimentId:115616985,controlId:115616986,controlId2:0,probability:cc,active:Lb,la:0});var ad=Number('')||0,bd=Number('')||0;bd||(bd=ad/100);var Sh=function(){var ja=!1;
return ja}();a.push({oa:171,studyId:171,experimentId:104967143,controlId:104967140,controlId2:0,probability:bd,active:Sh,la:0});var jH=Number('')||0,Dn=Number('0')||0;Dn||(Dn=jH/100);var kH=function(){var ja=!1;return ja}();
a.push({oa:254,studyId:254,experimentId:115583767,controlId:115583768,controlId2:115583769,probability:Dn,active:kH,la:0});var lH=Number('')||0,En=Number('')||0;En||(En=lH/100);var mH=function(){var ja=!1;
return ja}();a.push({oa:253,studyId:253,experimentId:115583770,controlId:115583771,controlId2:115583772,probability:En,active:mH,la:0});var nH=Number('')||0,Fn=Number('')||0;Fn||(Fn=nH/100);var oH=function(){var ja=!1;
return ja}();a.push({oa:266,studyId:266,experimentId:115718529,controlId:115718530,controlId2:115718531,probability:Fn,active:oH,la:0});var pH=Number('')||0,Gn=Number('')||0;Gn||(Gn=pH/100);var qH=function(){var ja=!1;
return ja}();a.push({oa:267,studyId:267,experimentId:115718526,controlId:115718527,controlId2:115718528,probability:Gn,active:qH,la:0});var rH=Number('')||0,Hn=Number('0.01')||0;Hn||(Hn=rH/100);var sH=function(){var ja=!1;
return ja}();a.push({oa:259,studyId:259,experimentId:105322302,controlId:105322303,controlId2:105322304,probability:Hn,active:sH,la:0});var tH=Number('')||0,In=Number('')||0;In||(In=tH/100);var uH=function(){var ja=!1;return ja}();a.push({oa:249,studyId:249,experimentId:105440521,controlId:105440522,
controlId2:0,focused:!0,probability:In,active:uH,la:0});var vH=Number('')||0,Jn=Number('0.5')||0;Jn||(Jn=vH/100);var wH=function(){var ja=!1;return ja}();a.push({oa:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,probability:Jn,active:wH,la:1});var xH=Number('')||
0,Kn=Number('0.5')||0;Kn||(Kn=xH/100);var yH=function(){var ja=!1;return ja}();a.push({oa:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:Kn,active:yH,la:0});var zH=Number('')||0,Ln=Number('')||
0;Ln||(Ln=zH/100);var AH=function(){var ja=!1;ja=!0;return ja}();a.push({oa:229,studyId:229,experimentId:105359938,controlId:105359937,controlId2:105359936,probability:Ln,active:AH,la:0});var BH=Number('')||0,Mn=Number('0')||
0;Mn||(Mn=BH/100);var CH=function(){var ja=!1;return ja}();a.push({oa:261,studyId:261,experimentId:115811758,controlId:115811759,controlId2:0,probability:Mn,active:CH,la:1});var DH=Number('')||0,Nn=Number('')||0;Nn||(Nn=DH/100);
var EH=function(){var ja=!1;return ja}();a.push({oa:225,studyId:225,experimentId:105476338,controlId:105476339,controlId2:105476599,probability:Nn,active:EH,la:0});return a};var R={A:{Kh:"accept_by_default",wg:"add_tag_timing",xg:"ads_event_page_view",Wc:"allow_ad_personalization",Wj:"batch_on_navigation",bk:"client_id_source",Le:"consent_event_id",Me:"consent_priority_id",Hr:"consent_state",ia:"consent_updated",Sd:"conversion_linker_enabled",Da:"cookie_options",Ag:"create_dc_join",Bg:"create_fpm_geo_join",Cg:"create_fpm_signals_join",Td:"create_google_join",Qh:"dc_random",Oe:"em_event",Kr:"endpoint_for_debug",vk:"enhanced_client_id_source",Rh:"enhanced_match_result",
wl:"euid_logged_in_state",pe:"euid_mode_enabled",eb:"event_start_timestamp_ms",Al:"event_usage",si:"extra_tag_experiment_ids",Ur:"add_parameter",ui:"attribution_reporting_experiment",wi:"counting_method",bh:"send_as_iframe",Vr:"parameter_order",eh:"parsed_target",Uo:"ga4_collection_subdomain",Pl:"gbraid_cookie_marked",Rl:"handle_internally",ba:"hit_type",xd:"hit_type_override",Jf:"ignore_hit_success_failure",Yr:"is_config_command",hh:"is_consent_update",Kf:"is_conversion",Wl:"is_ecommerce",yd:"is_external_event",
Ai:"is_fallback_aw_conversion_ping_allowed",Lf:"is_first_visit",Xl:"is_first_visit_conversion",ih:"is_fl_fallback_conversion_flow_allowed",zd:"is_fpm_encryption",Bi:"is_fpm_split",Fb:"is_gcp_conversion",Yl:"is_google_signals_allowed",Bd:"is_merchant_center",jh:"is_new_to_site",Ci:"is_personalization",kh:"is_server_side_destination",te:"is_session_start",am:"is_session_start_conversion",Zr:"is_sgtm_ga_ads_conversion_study_control_group",bs:"is_sgtm_prehit",bm:"is_sgtm_service_worker",Di:"is_split_conversion",
Zo:"is_syn",Mf:"join_id",Ei:"join_elapsed",Nf:"join_timer_sec",xe:"tunnel_updated",ks:"prehit_for_retry",ns:"promises",qs:"record_aw_latency",Mc:"redact_ads_data",ye:"redact_click_ids",lm:"remarketing_only",Mi:"send_ccm_parallel_ping",us:"send_ccm_parallel_test_ping",Rf:"send_to_destinations",Ni:"send_to_targets",op:"send_user_data_hit",Za:"source_canonical_id",za:"speculative",xm:"speculative_in_message",ym:"suppress_script_load",zm:"syn_or_mod",Em:"transient_ecsid",Sf:"transmission_type",Ra:"user_data",
xs:"user_data_from_automatic",ys:"user_data_from_automatic_getter",Gm:"user_data_from_code",up:"user_data_from_manual",Hm:"user_data_mode",Tf:"user_id_updated"}};var ho={};function io(a){var b=a,c=a=jo[b.studyId]?oa(Object,"assign").call(Object,{},b,{active:!0}):b;c.controlId2&&c.probability<=.25||(c=oa(Object,"assign").call(Object,{},c,{controlId2:0}));Di[c.studyId]=c;a.focused&&(ho[a.studyId]=!0);if(a.la===1){var d=a.studyId;ko(Pl(Kl.X.Ki,{}),d);lo(d)&&C(d)}else if(a.la===0){var e=a.studyId;ko(mo,e);lo(e)&&C(e)}}
function ko(a,b){if(Di[b]){var c=Di[b],d=c.experimentId,e=c.probability;if(!(a.studies||{})[b]){var f=a.studies||{};f[b]=!0;a.studies=f;Di[b].active||(Di[b].probability>.5?Hi(a,d,b):e<=0||e>1||Gi.ir(a,b))}}if(!ho[b]){var g=Ji(a,b);g&&Fg.U.H.add(g)}}var mo={};function lo(a){return Ii(Pl(Kl.X.Ki,{}),a)||Ii(mo,a)}function no(a){var b=T(a,R.A.si)||[];return Yj(b)}var jo={};
function oo(){jo={};var a,b,c=((a=w)==null?void 0:(b=a.location)==null?void 0:b.hash)||"";if(c.indexOf("_te=")!==0){var d=c.substring(5);if(d)for(var e=m(d.split("~")),f=e.next();!f.done;f=e.next()){var g=Number(f.value);g&&(jo[g]=!0,C(g))}}for(var h=m(go()),l=h.next();!l.done;l=h.next())io(l.value);if(F(264)){for(var n=[],p=m(Ig(56)||[]),q=p.next();!q.done;q=p.next()){var r=q.value,u={studyId:r[1],active:!!r[2],probability:r[3]||0,experimentId:r[4]||0,controlId:r[5]||0,controlId2:r[6]||0},t=0;switch(r[7]){case 2:t=
1;break;case 1:case 0:t=0}var v;a:switch(u.studyId){case 249:v=!0;break a;default:v=!1}var x=oa(Object,"assign").call(Object,{},u,{la:t,focused:v});(x.active||x.experimentId&&x.controlId)&&n.push(x)}for(var y=m(n),z=y.next();!z.done;z=y.next())io(z.value)}};var po={Hf:{In:"cd",Jn:"ce",Kn:"cf",Ln:"cpf",Mn:"cu"}};var qo=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,ro=/\s/;
function so(a,b){if(rb(a)){a=Db(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(qo.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var l=0;l<f.length;l++)if(!f[l]||ro.test(f[l])&&(d!=="AW"||l!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function to(a,b){for(var c={},d=0;d<a.length;++d){var e=so(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[uo[1]]&&f.push(h.destinationId)}for(var l=0;l<f.length;++l)delete c[f[l]];for(var n=[],p=m(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var vo={},uo=(vo[0]=0,vo[1]=1,vo[2]=2,vo[3]=0,vo[4]=1,vo[5]=0,vo[6]=0,vo[7]=0,vo);var wo=Jg(34,500),xo={},yo={},zo={initialized:11,complete:12,interactive:13},Ao={},Bo=Object.freeze((Ao[N.m.pd]=!0,Ao)),Co=void 0;function Do(a,b){if(b.length&&nk){var c;(c=xo)[a]!=null||(c[a]=[]);yo[a]!=null||(yo[a]=[]);var d=b.filter(function(e){return!yo[a].includes(e)});xo[a].push.apply(xo[a],Ba(d));yo[a].push.apply(yo[a],Ba(d));!Co&&d.length>0&&(Zl("tdc",!0),Co=w.setTimeout(function(){cm();xo={};Co=void 0},wo))}}
function Eo(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Fo(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,u){var t;qd(u)==="object"?t=u[r]:qd(u)==="array"&&(t=u[r]);return t===void 0?Bo[r]:t},f=Eo(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,l=e(g,a),n=e(g,b),p=qd(l)==="object"||qd(l)==="array",q=qd(n)==="object"||qd(n)==="array";if(p&&q)Fo(l,n,c,h);else if(p||q||l!==n)c[h]=!0}return Object.keys(c)}
function Go(){Yl("tdc",function(){Co&&(w.clearTimeout(Co),Co=void 0);var a=[],b;for(b in xo)xo.hasOwnProperty(b)&&a.push(b+"*"+xo[b].join("."));return a.length?a.join("!"):void 0},!1)};var Ho={T:{Vj:1,Oi:2,Rj:3,pk:4,Sj:5,Xc:6,nk:7,ep:8,om:9,Tj:10,Uj:11,fh:12,Hl:13,El:14,Gl:15,Dl:16,Fl:17,Cl:18,yn:19,So:20,To:21,Ii:22}};Ho.T[Ho.T.Vj]="ALLOW_INTEREST_GROUPS";Ho.T[Ho.T.Oi]="SERVER_CONTAINER_URL";Ho.T[Ho.T.Rj]="ADS_DATA_REDACTION";Ho.T[Ho.T.pk]="CUSTOMER_LIFETIME_VALUE";Ho.T[Ho.T.Sj]="ALLOW_CUSTOM_SCRIPTS";Ho.T[Ho.T.Xc]="ANY_COOKIE_PARAMS";Ho.T[Ho.T.nk]="COOKIE_EXPIRES";Ho.T[Ho.T.ep]="LEGACY_ENHANCED_CONVERSION_JS_VARIABLE";Ho.T[Ho.T.om]="RESTRICTED_DATA_PROCESSING";Ho.T[Ho.T.Tj]="ALLOW_DISPLAY_FEATURES";
Ho.T[Ho.T.Uj]="ALLOW_GOOGLE_SIGNALS";Ho.T[Ho.T.fh]="GENERATED_TRANSACTION_ID";Ho.T[Ho.T.Hl]="FLOODLIGHT_COUNTING_METHOD_UNKNOWN";Ho.T[Ho.T.El]="FLOODLIGHT_COUNTING_METHOD_STANDARD";Ho.T[Ho.T.Gl]="FLOODLIGHT_COUNTING_METHOD_UNIQUE";Ho.T[Ho.T.Dl]="FLOODLIGHT_COUNTING_METHOD_PER_SESSION";Ho.T[Ho.T.Fl]="FLOODLIGHT_COUNTING_METHOD_TRANSACTIONS";Ho.T[Ho.T.Cl]="FLOODLIGHT_COUNTING_METHOD_ITEMS_SOLD";Ho.T[Ho.T.yn]="ADS_OGT_V1_USAGE";Ho.T[Ho.T.So]="FORM_INTERACTION_PERMISSION_DENIED";Ho.T[Ho.T.To]="FORM_SUBMIT_PERMISSION_DENIED";
Ho.T[Ho.T.Ii]="MICROTASK_NOT_SUPPORTED";var Io={},Jo=(Io[N.m.Th]=Ho.T.Vj,Io[N.m.sd]=Ho.T.Oi,Io[N.m.rc]=Ho.T.Oi,Io[N.m.Ha]=Ho.T.Rj,Io[N.m.cf]=Ho.T.pk,Io[N.m.Eg]=Ho.T.Sj,Io[N.m.zc]=Ho.T.Xc,Io[N.m.Va]=Ho.T.Xc,Io[N.m.qb]=Ho.T.Xc,Io[N.m.fd]=Ho.T.Xc,Io[N.m.Rb]=Ho.T.Xc,Io[N.m.Bb]=Ho.T.Xc,Io[N.m.rb]=Ho.T.nk,Io[N.m.Sb]=Ho.T.om,Io[N.m.Fg]=Ho.T.Tj,Io[N.m.Pb]=Ho.T.Uj,Io),Ko={},Lo=(Ko.unknown=Ho.T.Hl,Ko.standard=Ho.T.El,Ko.unique=Ho.T.Gl,Ko.per_session=Ho.T.Dl,Ko.transactions=Ho.T.Fl,Ko.items_sold=Ho.T.Cl,Ko);var nb=[];function Mo(a,b){b=b===void 0?!1:b;kb("GTAG_EVENT_FEATURE_CHANNEL",a);b&&(nb[a]=!0)}function No(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=m(Object.keys(Jo)),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)&&Mo(Jo[f],b)}};var Oo=function(a,b,c,d,e,f,g,h,l,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.U=d;this.H=e;this.R=f;this.M=g;this.eventMetadata=h;this.onSuccess=l;this.onFailure=n;this.isGtmEvent=p},Po=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 2:c.push(a.C);break;case 1:c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 4:c.push(a.C),c.push(a.U),c.push(a.H),c.push(a.R)}return c},Q=function(a,b,c,d){for(var e=m(Po(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Qo=function(a){for(var b={},c=Po(a,4),d=m(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=m(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Oo.prototype.getMergedValues=function(a,b,c){function d(n){sd(n)&&yb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Po(this,b);g.reverse();for(var h=m(g),l=h.next();!l.done;l=h.next())d(l.value[a]);return f?e:void 0};
var Ro=function(a){for(var b=[N.m.Ze,N.m.Ve,N.m.We,N.m.Xe,N.m.Ye,N.m.af,N.m.bf],c=Po(a,3),d=m(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,l=m(b),n=l.next();!n.done;n=l.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},So=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.U={};this.C={};this.M={};this.ja={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},To=function(a,
b){a.H=b;return a},Uo=function(a,b){a.U=b;return a},Vo=function(a,b){a.C=b;return a},Wo=function(a,b){a.M=b;return a},Xo=function(a,b){a.ja=b;return a},Yo=function(a,b){a.R=b;return a},Zo=function(a,b){a.eventMetadata=b||{};return a},$o=function(a,b){a.onSuccess=b;return a},ap=function(a,b){a.onFailure=b;return a},bp=function(a,b){a.isGtmEvent=b;return a},cp=function(a){return new Oo(a.eventId,a.priorityId,a.H,a.U,a.C,a.M,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var dp=new xb,ep={},fp={},ip={name:Dg(19),set:function(a,b){td(Nb(a,b),ep);gp()},get:function(a){return hp(a,2)},reset:function(){dp=new xb;ep={};gp()}};function hp(a,b){return b!=2?dp.get(a):jp(a)}function jp(a,b){var c=a.split(".");b=b||[];for(var d=ep,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function kp(a,b){fp.hasOwnProperty(a)||(dp.set(a,b),td(Nb(a,b),ep),gp())}
function lp(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=hp(c,1);if(Array.isArray(d)||sd(d))d=td(d,null);fp[c]=d}}function gp(a){yb(fp,function(b,c){dp.set(b,c);td(Nb(b),ep);td(Nb(b,c),ep);a&&delete fp[b]})}function mp(a,b){var c,d=(b===void 0?2:b)!==1?jp(a):dp.get(a);qd(d)==="array"||qd(d)==="object"?c=td(d,null):c=d;return c};var np={xn:Jg(3,0)},op=[],pp=!1;function qp(a){op.push(a)}var rp=void 0,sp={},tp=void 0,up=new function(){var a=5;np.xn>0&&(a=np.xn);this.H=a;this.C=0;this.M=[]},vp=1E3;
function wp(a,b){var c=rp;if(c===void 0)if(b)c=bo();else return"";for(var d=[gk("https://"+Dg(21)),"/a","?id="+Dg(5)],e=m(op),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Vc:!!a}),l=m(h),n=l.next();!n.done;n=l.next()){var p=m(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function xp(){if(Fg.ja&&(tp&&(w.clearTimeout(tp),tp=void 0),rp!==void 0&&yp)){var a=Jl(jl.Z.Kc);if(Fl(a))pp||(pp=!0,Hl(a,xp));else{var b;if(!(b=sp[rp])){var c=up;b=c.C<c.H?!1:Fb()-c.M[c.C%c.H]<1E3}if(b||vp--<=0)P(1),sp[rp]=!0;else{var d=up,e=d.C++%d.H;d.M[e]=Fb();var f=wp(!0);dl({destinationId:Dg(5),endpoint:56,eventId:rp},f);pp=yp=!1}}}}function zp(){if(lk&&Fg.ja){var a=wp(!0,!0);dl({destinationId:Dg(5),endpoint:56,eventId:rp},a)}}var yp=!1;
function Ap(a){sp[a]||(a!==rp&&(xp(),rp=a),yp=!0,tp||(tp=w.setTimeout(xp,500)),wp().length>=2022&&xp())}var Bp=vb();function Cp(){Bp=vb()}function Dp(){return[["v","3"],["t","t"],["pid",String(Bp)]]};var Ep={};function Fp(a,b,c){lk&&a!==void 0&&(Ep[a]=Ep[a]||[],Ep[a].push(c+b),Ap(a))}function Gp(a){var b=a.eventId,c=a.Vc,d=[],e=Ep[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Ep[b];return d};function Hp(a,b,c,d){var e=so(a,!0);e&&Ip.register(e,b,c,d)}function Jp(a,b,c,d){var e=so(c,d.isGtmEvent);e&&(Vi&&(d.deferrable=!0),Ip.push("event",[b,a],e,d))}function Kp(a,b,c,d){var e=so(c,d.isGtmEvent);e&&Ip.push("get",[a,b],e,d)}function Lp(a){var b=so(a,!0),c;b?c=Mp(Ip,b).M:c={};return c}function Np(a,b){var c=so(a,!0);c&&Op(Ip,c,b)}
var Pp=function(){this.C={};this.M={};this.R={};this.ja=null;this.H={};this.U=!1;this.status=1},Qp=function(a,b,c,d){this.H=Fb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Rp=function(){this.destinations={};this.C={};this.commands=[]},Mp=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Pp},Sp=function(a,b,c,d){if(d.C){var e=Mp(a,d.C),f=e.ja;if(f){var g=td(c,null),h=td(e.C[d.C.id],null),l=td(e.H,null),n=td(e.M,null),p=td(a.C,null),q={};if(lk)try{q=
td(ep,null)}catch(x){P(72)}var r=d.C.prefix,u=function(x){Fp(d.messageContext.eventId,r,x)},t=cp(bp(ap($o(Zo(Xo(Wo(Yo(Vo(Uo(To(new So(d.messageContext.eventId,d.messageContext.priorityId),g),h),l),n),p),q),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Fp(d.messageContext.eventId,
r,"1");var x=d.type,y=d.C.id;if(nk&&x==="config"){var z,D=(z=so(y))==null?void 0:z.ids;if(!(D&&D.length>1)){var E,M=Dc("google_tag_data",{});M.td||(M.td={});E=M.td;var G=td(t.R);td(t.C,G);var L=[],U;for(U in E)E.hasOwnProperty(U)&&Fo(E[U],G).length&&L.push(U);L.length&&(Do(y,L),kb("TAGGING",zo[A.readyState]||14));E[y]=G}}f(d.C.id,b,d.H,t)}catch(ia){Fp(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():Hl(e.wa,v)}}};
Rp.prototype.register=function(a,b,c,d){var e=Mp(this,a);e.status!==3&&(e.ja=b,e.status=3,e.wa=Jl(c),Op(this,a,d||{}),this.flush())};
Rp.prototype.push=function(a,b,c,d){c!==void 0&&(Mp(this,c).status===1&&(Mp(this,c).status=2,this.push("require",[{}],c,{})),Mp(this,c).U&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[R.A.Rf]||(d.eventMetadata[R.A.Rf]=[c.destinationId]),d.eventMetadata[R.A.Ni]||(d.eventMetadata[R.A.Ni]=[c.id]));this.commands.push(new Qp(a,c,b,d));d.deferrable||this.flush()};
Rp.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Oc:void 0,uh:void 0,Zi:void 0,aj:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Mp(this,g).U?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Mp(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];yb(h,function(t,v){td(Nb(t,v),b.C)});No(h,!0);break;case "config":var l=
Mp(this,g);e.Oc={};yb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.Oc)}}(e));var n=!!e.Oc[N.m.ud];delete e.Oc[N.m.ud];var p=g.destinationId===g.id;No(e.Oc,!0);n||(p?l.H={}:l.C[g.id]={});l.U&&n||Sp(this,N.m.ma,e.Oc,f);l.U=!0;p?td(e.Oc,l.H):(td(e.Oc,l.C[g.id]),P(70));d=!0;break;case "event":e.uh={};yb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.uh)}}(e));No(e.uh);Sp(this,f.args[1],e.uh,f);break;case "get":var q={},r=(q[N.m.lf]=f.args[0],q[N.m.kf]=f.args[1],q);Sp(this,N.m.nc,r,
f);break;case "container_config":e.Zi={};yb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.Zi)}}(e));Mp(this,g).H=e.Zi;break;case "destination_config":var u=Mp(this,g);e.aj={};yb(f.args[0],function(t){return function(v,x){td(Nb(v,x),t.aj)}}(e));u.C[g.id]||(u.C[g.id]={});u.C[g.id]=e.aj}this.commands.shift();Tp(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};
var Tp=function(a,b){if(b.type!=="require")if(b.C)for(var c=Mp(a,b.C).R[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.R)for(var g=f.R[b.type]||[],h=0;h<g.length;h++)g[h]()}},Op=function(a,b,c){var d=td(c,null);td(Mp(a,b).M,d);Mp(a,b).M=d},Ip=new Rp;function Up(a){var b=a.location.href;if(a===a.top)return{url:b,Gq:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1],g;f&&((g=b)==null?void 0:g.indexOf(f))===-1&&(c=!1,b=f)}return{url:b,Gq:c}}function Vp(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Jk(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}}function Wp(){for(var a=w,b=a;a&&a!=a.parent;)a=a.parent,Vp(a)&&(b=a);return b};var Xp=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Yp=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};function Zp(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)};var $p=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},aq=function(a){var b=w;if(b.top==b)return 0;if(a===void 0?0:a){var c=b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Vp(b.top)?1:2},bq=function(a){a=a===void 0?document:a;return a.createElement("img")};function cq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function dq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function eq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=bq(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=wc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}dq(e,"load",f);dq(e,"error",f)};cq(e,"load",f);cq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function fq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Zp(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});gq(c,b)}
function gq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else eq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var hq=function(){this.ja=this.ja;this.R=this.R};hq.prototype.ja=!1;hq.prototype.dispose=function(){this.ja||(this.ja=!0,this.M())};hq.prototype[ma.Symbol.dispose]=function(){this.dispose()};hq.prototype.addOnDisposeCallback=function(a,b){this.ja?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};hq.prototype.M=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function iq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var jq=function(a,b){b=b===void 0?{}:b;hq.call(this);this.C=null;this.wa={};this.Lc=0;this.U=null;this.H=a;var c;this.wb=(c=b.timeoutMs)!=null?c:500;var d;this.Ya=(d=b.Ds)!=null?d:!1};za(jq,hq);jq.prototype.M=function(){this.wa={};this.U&&(dq(this.H,"message",this.U),delete this.U);delete this.wa;delete this.H;delete this.C;hq.prototype.M.call(this)};var lq=function(a){return typeof a.H.__tcfapi==="function"||kq(a)!=null};
jq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ya},d=Yp(function(){return a(c)}),e=0;this.wb!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.wb));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=iq(c),c.internalBlockOnErrors=b.Ya,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{mq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};jq.prototype.removeEventListener=function(a){a&&a.listenerId&&mq(this,"removeEventListener",null,a.listenerId)};
var oq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var l;if(h===0)if(a.purpose&&a.vendor){var n=nq(a.vendor.consents,d===void 0?"755":d);l=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&nq(a.purpose.consents,b)}else l=!0;else l=h===1?a.purpose&&a.vendor?nq(a.purpose.legitimateInterests,
b)&&nq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return l},nq=function(a,b){return!(!a||!a[b])},mq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(kq(a)){pq(a);var g=++a.Lc;a.wa[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},kq=function(a){if(a.C)return a.C;a.C=$p(a.H,"__tcfapiLocator");return a.C},pq=function(a){if(!a.U){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.wa[d.callId](d.returnValue,d.success)}catch(e){}};a.U=b;cq(a.H,"message",b)}},qq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=iq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(fq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var rq={1:0,3:0,4:0,7:3,9:3,10:3};Jg(32,500);function sq(){return Xn("tcf",function(){return{}})}var tq=function(){return new jq(w,{timeoutMs:-1})};
function uq(){var a=sq(),b=tq();lq(b)&&!vq()&&!wq()&&P(124);if(!a.active&&lq(b)){vq()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,kl().active=!0,a.tcString="tcunavailable");Pn();try{b.addEventListener(function(c){if(c.internalErrorState!==0)xq(a),Qn([N.m.aa,N.m.Ka,N.m.W]),kl().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,wq()&&(a.active=!0),!yq(c)||vq()||wq()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in rq)rq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(yq(c)){var g={},h;for(h in rq)if(rq.hasOwnProperty(h))if(h==="1"){var l,n=c,p={iq:!0};p=p===void 0?{}:p;l=qq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.iq)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?oq(n,"1",0):!0:!1;g["1"]=l}else g[h]=oq(c,h,rq[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[N.m.aa]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Qn([N.m.aa,N.m.Ka,N.m.W]),kl().active=!0):(r[N.m.Ka]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[N.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Qn([N.m.W]),yn(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:zq()||""}))}}else Qn([N.m.aa,N.m.Ka,N.m.W])})}catch(c){xq(a),Qn([N.m.aa,N.m.Ka,N.m.W]),kl().active=!0}}}
function xq(a){a.type="e";a.tcString="tcunavailable"}function yq(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function vq(){return w.gtag_enable_tcf_support===!0}function wq(){return sq().enableAdvertiserConsentMode===!0}function zq(){var a=sq();if(a.active)return a.tcString}function Aq(){var a=sq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function Bq(a){if(!rq.hasOwnProperty(String(a)))return!0;var b=sq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Cq=[N.m.aa,N.m.ka,N.m.W,N.m.Ka],Dq={},Eq=(Dq[N.m.aa]=1,Dq[N.m.ka]=2,Dq);function Fq(a){if(a===void 0)return 0;switch(Q(a,N.m.Ob)){case void 0:return 1;case !1:return 3;default:return 2}}function Gq(){return(F(183)?Hg(16).split("~"):Hg(17).split("~")).indexOf(ym())!==-1&&zc.globalPrivacyControl===!0}function Hq(a){if(Gq())return!1;var b=Fq(a);if(b===3)return!1;switch(tl(N.m.Ka)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Iq(){return vl()||!sl(N.m.aa)||!sl(N.m.ka)}function Jq(){var a={},b;for(b in Eq)Eq.hasOwnProperty(b)&&(a[Eq[b]]=tl(b));return"G1"+jf(a[1]||0)+jf(a[2]||0)}var Kq={},Lq=(Kq[N.m.aa]=0,Kq[N.m.ka]=1,Kq[N.m.W]=2,Kq[N.m.Ka]=3,Kq);function Mq(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Nq(a){for(var b="1",c=0;c<Cq.length;c++){var d=b,e,f=Cq[c],g=rl.delegatedConsentTypes[f];e=g===void 0?0:Lq.hasOwnProperty(g)?12|Lq[g]:8;var h=kl();h.accessedAny=!0;var l=h.entries[f]||{};e=e<<2|Mq(l.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Mq(l.declare)<<4|Mq(l.default)<<2|Mq(l.update)])}var n=b,p=(Gq()?1:0)<<3,q=(vl()?1:0)<<2,r=Fq(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[rl.containerScopedDefaults.ad_storage<<4|rl.containerScopedDefaults.analytics_storage<<2|rl.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(rl.usedContainerScopedDefaults?1:0)<<2|rl.containerScopedDefaults.ad_personalization]}
function Oq(){if(!sl(N.m.W))return"-";for(var a=Object.keys(Qm),b={},c=m(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=rl.corePlatformServices[e]!==!1}for(var f="",g=m(a),h=g.next();!h.done;h=g.next()){var l=h.value;b[l]&&(f+=Qm[l])}(rl.usedCorePlatformServices?rl.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Pq(){return Am()||(vq()||wq())&&Aq()==="1"?"1":"0"}function Qq(){return(Am()?!0:!(!vq()&&!wq())&&Aq()==="1")||!sl(N.m.W)}
function Rq(){var a="0",b="0",c;var d=sq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=sq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;Am()&&(h|=1);Aq()==="1"&&(h|=2);vq()&&(h|=4);var l;var n=sq();l=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;l==="1"&&(h|=8);kl().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Sq(){return ym()==="US-CO"};var Tq={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Uq(a){a=a===void 0?{}:a;var b=Dg(5).split("-")[0].toUpperCase(),c,d={ctid:Dg(5),nn:Gg(15),qn:Dg(14),Iq:Cg(7)?2:1,rr:a.sn,canonicalId:Dg(6),gr:(c=Nj())==null?void 0:c.canonicalContainerId,ur:a.Qd===void 0?void 0:a.Qd?10:12};d.canonicalId!==a.Oa&&(d.Oa=a.Oa);var e=Kj();d.Oq=e?e.canonicalContainerId:void 0;Xi?(d.Ih=Tq[b],d.Ih||(d.Ih=0)):d.Ih=Yi?13:10;Fg.H?(d.Wm=0,d.Hp=2):d.Wm=Fg.C?1:3;var f={6:!1};Fg.M===2?f[7]=!0:Fg.M===1&&(f[2]=!0);if(Cc){var g=mj(sj(Cc),"host");g&&(f[8]=g.match(/^(www\.)?googletagmanager\.com$/)===
null)}d.Np=f;return mf(d,a.sh)};function Vq(a,b,c,d){var e,f=Number(a.Sc!=null?a.Sc:void 0);f!==0&&(e=new Date((b||Fb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,wc:d}};var Wq=["ad_storage","ad_user_data"];function Xq(a,b){if(!a)return kb("TAGGING",32),10;if(b===null||b===void 0||b==="")return kb("TAGGING",33),11;var c=Yq(!1);if(c.error!==0)return kb("TAGGING",34),c.error;if(!c.value)return kb("TAGGING",35),2;c.value[a]=b;var d=Zq(c);d!==0&&kb("TAGGING",36);return d}
function $q(a){if(!a)return kb("TAGGING",27),{error:10};var b=Yq();if(b.error!==0)return kb("TAGGING",29),b;if(!b.value)return kb("TAGGING",30),{error:2};if(!(a in b.value))return kb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(kb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Yq(a){a=a===void 0?!0:a;if(!sl(Wq))return kb("TAGGING",43),{error:3};try{if(!w.localStorage)return kb("TAGGING",44),{error:1}}catch(f){return kb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=w.localStorage.getItem("_gcl_ls")}catch(f){return kb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return kb("TAGGING",47),{error:12}}}catch(f){return kb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return kb("TAGGING",49),{error:4};
if(b.version!==1)return kb("TAGGING",50),{error:5};try{var e=ar(b);a&&e&&Zq({value:b,error:0})}catch(f){return kb("TAGGING",48),{error:8}}return{value:b,error:0}}
function ar(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,kb("TAGGING",54),!0}else{for(var c=!1,d=m(Object.keys(a)),e=d.next();!e.done;e=d.next())c=ar(a[e.value])||c;return c}return!1}
function Zq(a){if(a.error)return a.error;if(!a.value)return kb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return kb("TAGGING",52),6}try{w.localStorage.setItem("_gcl_ls",c)}catch(d){return kb("TAGGING",53),7}return 0};var br={ng:"value",fb:"conversionCount",Eh:1},cr={Bh:9,Hh:10,ng:"timeouts",fb:"timeouts",Eh:0},dr=[br,cr,{Bh:11,Hh:12,ng:"eopCount",fb:"endOfPageCount",Eh:0},{Bh:11,Hh:12,ng:"errors",fb:"errors",Eh:0}];function er(a){var b;b=b===void 0?1:b;if(!fr(a))return{};var c=gr(dr),d=c[a.fb];if(d===void 0||d===-1)return c;var e={},f=oa(Object,"assign").call(Object,{},c,(e[a.fb]=d+b,e));return hr(f)?f:c}
function gr(a){var b;a:{var c=$q("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=m(a),h=g.next();!h.done;h=g.next()){var l=h.value;if(e&&fr(l)){var n=e[l.ng];n===void 0||Number.isNaN(n)?f[l.fb]=-1:f[l.fb]=Number(n)}else f[l.fb]=-1}return f}
function ir(){for(var a=er(br),b=[],c=m(dr),d=c.next();!d.done;d=c.next()){var e=d.value,f=a[e.fb];if(f===void 0||f<e.Eh)break;b.push(f.toString())}return b.join("~")}function hr(a,b){b=b||{};for(var c=Fb(),d=Vq(b,c,!0),e={},f=m(dr),g=f.next();!g.done;g=f.next()){var h=g.value,l=a[h.fb];l!==void 0&&l!==-1&&(e[h.ng]=l)}e.creationTimeMs=c;return Xq("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function fr(a){return sl(["ad_storage","ad_user_data"])?!a.Hh||Xa(a.Hh):!1}
function jr(a){return sl(["ad_storage","ad_user_data"])?!a.Bh||Xa(a.Bh):!1};var kr={O:{np:0,Qj:1,zg:2,fk:3,Mh:4,dk:5,ek:6,gk:7,Nh:8,yl:9,xl:10,ri:11,zl:12,ah:13,Il:14,Pf:15,mp:16,ze:17,Si:18,Ti:19,Ui:20,Bm:21,Vi:22,Ph:23,qk:24}};kr.O[kr.O.np]="RESERVED_ZERO";kr.O[kr.O.Qj]="ADS_CONVERSION_HIT";kr.O[kr.O.zg]="CONTAINER_EXECUTE_START";kr.O[kr.O.fk]="CONTAINER_SETUP_END";kr.O[kr.O.Mh]="CONTAINER_SETUP_START";kr.O[kr.O.dk]="CONTAINER_BLOCKING_END";kr.O[kr.O.ek]="CONTAINER_EXECUTE_END";kr.O[kr.O.gk]="CONTAINER_YIELD_END";kr.O[kr.O.Nh]="CONTAINER_YIELD_START";kr.O[kr.O.yl]="EVENT_EXECUTE_END";
kr.O[kr.O.xl]="EVENT_EVALUATION_END";kr.O[kr.O.ri]="EVENT_EVALUATION_START";kr.O[kr.O.zl]="EVENT_SETUP_END";kr.O[kr.O.ah]="EVENT_SETUP_START";kr.O[kr.O.Il]="GA4_CONVERSION_HIT";kr.O[kr.O.Pf]="PAGE_LOAD";kr.O[kr.O.mp]="PAGEVIEW";kr.O[kr.O.ze]="SNIPPET_LOAD";kr.O[kr.O.Si]="TAG_CALLBACK_ERROR";kr.O[kr.O.Ti]="TAG_CALLBACK_FAILURE";kr.O[kr.O.Ui]="TAG_CALLBACK_SUCCESS";kr.O[kr.O.Bm]="TAG_EXECUTE_END";kr.O[kr.O.Vi]="TAG_EXECUTE_START";kr.O[kr.O.Ph]="CUSTOM_PERFORMANCE_START";kr.O[kr.O.qk]="CUSTOM_PERFORMANCE_END";var lr=[],mr={},nr={};function or(a){if(Xa(19)&&lr.includes(a)){var b;(b=hd())==null||b.mark(a+"-"+kr.O.Ph+"-"+(nr[a]||0))}}function pr(a){if(Xa(19)&&lr.includes(a)){var b=a+"-"+kr.O.qk+"-"+(nr[a]||0),c={start:a+"-"+kr.O.Ph+"-"+(nr[a]||0),end:b},d;(d=hd())==null||d.mark(b);var e,f,g=(f=(e=hd())==null?void 0:e.measure(b,c))==null?void 0:f.duration;g!==void 0&&(nr[a]=(nr[a]||0)+1,mr[a]=g+(mr[a]||0))}};var qr=["2","3"];function rr(a){return a.origin!=="null"};function sr(a,b){var c=Pl(Kl.X.Gi,Pa()).get(a);if(c&&(!c.expires||(typeof c.expires==="string"?(new Date(c.expires)).getTime():c.expires.getTime())>=Date.now())&&c.value!==void 0)return b?decodeURIComponent(c.value):c.value}function tr(a,b,c){var d=Pl(Kl.X.Gi,Pa());d.set(a,{expires:c,value:b});Nl(Kl.X.Gi,d)}var ur=0,vr=0;
function wr(a,b,c,d,e){try{or("3");var f;Xa(20)&&!e&&(f=sr(a,c));var g,h=(g=xr(function(l){return l===a},b,c,d)[a])!=null?g:[];f!==void 0&&(h.includes(f)?vr++:ur++);return h}finally{pr("3")}}function xr(a,b,c,d){var e;if(yr(d)){for(var f={},g=String(b||zr()).split(";"),h=0;h<g.length;h++){var l=g[h].split("="),n=l[0].trim();if(n&&a(n)){var p=l.slice(1).join("=").trim();p&&c&&(p=decodeURIComponent(p));var q=void 0,r=void 0;((q=f)[r=n]||(q[r]=[])).push(p)}}e=f}else e={};return e}
function Ar(a,b,c,d,e){if(yr(e)){var f=Br(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=Cr(f,function(g){return g.Xp},b);if(f.length===1)return f[0];f=Cr(f,function(g){return g.Qq},c);return f[0]}}}function Dr(a,b,c,d){var e=zr(),f=window;rr(f)&&(f.document.cookie=a);var g=zr();return e!==g||c!==void 0&&wr(b,g,!1,d,!0).indexOf(c)>=0}
function Er(a,b,c,d){function e(x,y,z){if(z==null)return delete h[y],x;h[y]=z;return x+"; "+y+"="+z}function f(x,y){if(y==null)return x;h[y]=!0;return x+"; "+y}if(!yr(c.wc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Fr(b),g=a+"="+b);var h={};Xa(20)&&tr(a,b,c.zj?new Date(Date.now()+Number(c.zj)*1E3):c.expires);g=e(g,"path",c.path);var l;c.expires instanceof Date?l=c.expires.toUTCString():c.expires!=null&&(l=""+c.expires);g=e(g,
"expires",l);g=e(g,"max-age",c.zj);g=e(g,"samesite",c.hr);c.secure&&(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Gr(),q=void 0,r=!1,u=0;u<p.length;++u){var t=p[u]!=="none"?p[u]:void 0,v=e(g,"domain",t);v=f(v,c.flags);try{d&&d(a,h)}catch(x){q=x;continue}r=!0;if(!Hr(t,c.path)&&Dr(v,a,b,c.wc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Hr(n,c.path)?1:Dr(g,a,b,c.wc)?0:1}
function Ir(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");or("2");var d=Er(a,b,c);pr("2");return d}function Cr(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],l=b(h);l===c?d.push(h):f===void 0||l<f?(e=[h],f=l):l===f&&e.push(h)}return d.length>0?d:e}
function Br(a,b,c){for(var d=[],e=wr(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var l=g.shift();if(l){var n=l.split("-");d.push({Pp:e[f],Qp:g.join("."),Xp:Number(n[0])||1,Qq:Number(n[1])||1})}}}return d}function Fr(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}var Jr=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Kr=/(^|\.)doubleclick\.net$/i;
function Hr(a,b){return a!==void 0&&(Kr.test(window.document.location.hostname)||b==="/"&&Jr.test(a))}function Lr(a){if(!a)return 1;var b=a;Xa(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Mr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}function Nr(a,b){var c=""+Lr(a),d=Mr(b);d>1&&(c+="-"+d);return c}
var zr=function(){return rr(window)?window.document.cookie:""},yr=function(a){return a&&Xa(7)?(Array.isArray(a)?a:[a]).every(function(b){return ul(b)&&sl(b)}):!0},Gr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;Kr.test(e)||Jr.test(e)||a.push("none");return a};function Or(a){var b=Math.round(Math.random()*2147483647);return a?String(b^ai(a)&2147483647):String(b)}function Pr(a){return[Or(a),Math.round(Fb()/1E3)].join(".")}function Qr(a,b,c,d,e){var f=Lr(b),g;return(g=Ar(a,f,Mr(c),d,e))==null?void 0:g.Qp};var Rr;function Sr(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Tr,d=Ur,e=Vr();if(!e.init){Qc(A,"mousedown",a);Qc(A,"keyup",a);Qc(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Wr(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Vr().decorators.push(f)}
function Xr(a,b,c){for(var d=Vr().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var l=g.domains,n=a,p=!!g.sameHost;if(l&&(p||n!==A.location.hostname))for(var q=0;q<l.length;q++)if(l[q]instanceof RegExp){if(l[q].test(n)){h=!0;break a}}else if(n.indexOf(l[q])>=0||p&&l[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Ib(e,g.callback())}}return e}
function Vr(){var a=Dc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Yr=/(.*?)\*(.*?)\*(.*)/,Zr=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,$r=/^(?:www\.|m\.|amp\.)+/,as=/([^?#]+)(\?[^#]*)?(#.*)?/;function bs(a){var b=as.exec(a);if(b)return{Dj:b[1],query:b[2],fragment:b[3]}}function cs(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function ds(a,b){var c=[zc.userAgent,(new Date).getTimezoneOffset(),zc.userLanguage||zc.language,Math.floor(Fb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Rr)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Rr=d;for(var l=4294967295,n=0;n<c.length;n++)l=l>>>8^Rr[(l^c.charCodeAt(n))&255];return((l^-1)>>>0).toString(36)}
function es(a){return function(b){var c=sj(w.location.href),d=c.search.replace("?",""),e=jj(d,"_gl",!1,!0)||"";b.query=fs(e)||{};var f=mj(c,"fragment"),g;var h=-1;if(Kb(f,"_gl="))h=4;else{var l=f.indexOf("&_gl=");l>0&&(h=l+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=fs(g||"")||{};a&&gs(c,d,f)}}function hs(a,b){var c=cs(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function gs(a,b,c){function d(g,h){var l=hs("_gl",g);l.length&&(l=h+l);return l}if(yc&&yc.replaceState){var e=cs("_gl");if(e.test(b)||e.test(c)){var f=mj(a,"path");b=d(b,"?");c=d(c,"#");yc.replaceState({},"",""+f+b+c)}}}function is(a,b){var c=es(!!b),d=Vr();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Ib(e,f.query),a&&Ib(e,f.fragment));return e}
var fs=function(a){try{var b=js(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=ib(d[e+1]);c[f]=g}kb("TAGGING",6);return c}}catch(h){kb("TAGGING",8)}};function js(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Yr.exec(d);if(f){c=f;break a}d=lj(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],l;a:{for(var n=g[2],p=0;p<b;++p)if(n===ds(h,p)){l=!0;break a}l=!1}if(l)return h;kb("TAGGING",7)}}}
function ks(a,b,c,d,e){function f(p){p=hs(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=bs(c);if(!g)return"";var h=g.query||"",l=g.fragment||"",n=a+"="+b;d?l.substring(1).length!==0&&e||(l="#"+f(l.substring(1))):h="?"+f(h.substring(1));return""+g.Dj+h+l}
function ls(a,b){function c(n,p,q){var r;a:{for(var u in n)if(n.hasOwnProperty(u)){r=!0;break a}r=!1}if(r){var t,v=[],x;for(x in n)if(n.hasOwnProperty(x)){var y=n[x];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(x),v.push(hb(String(y))))}var z=v.join("*");t=["1",ds(z),z].join("*");d?(Xa(3)||Xa(1)||!p)&&ms("_gl",t,a,p,q):ns("_gl",t,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Xr(b,1,d),f=Xr(b,2,d),g=Xr(b,4,d),h=Xr(b,3,d);c(e,!1,!1);c(f,!0,!1);Xa(1)&&c(g,!0,!0);for(var l in h)h.hasOwnProperty(l)&&
os(l,h[l],a)}function os(a,b,c){c.tagName.toLowerCase()==="a"?ns(a,b,c):c.tagName.toLowerCase()==="form"&&ms(a,b,c)}function ns(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Xa(4)||d)){var h=w.location.href,l=bs(c.href),n=bs(h);g=!(l&&n&&l.Dj===n.Dj&&l.query===n.query&&l.fragment)}f=g}if(f){var p=ks(a,b,c.href,d,e);oc.test(p)&&(c.href=p)}}
function ms(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=ks(a,b,f,d,e);oc.test(h)&&(c.action=h)}}else{for(var l=c.childNodes||[],n=!1,p=0;p<l.length;p++){var q=l[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Tr(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||ls(e,e.hostname)}}catch(g){}}function Ur(a){try{var b=a.getAttribute("action");if(b){var c=mj(sj(b),"host");ls(a,c)}}catch(d){}}function ps(a,b,c,d){Sr();var e=c==="fragment"?2:1;d=!!d;Wr(a,b,e,d,!1);e===2&&kb("TAGGING",23);d&&kb("TAGGING",24)}
function qs(a,b){Sr();Wr(a,[oj(w.location,"host",!0)],b,!0,!0)}function rs(){var a=A.location.hostname,b=Zr.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?lj(f[2])||"":lj(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace($r,""),l=e.replace($r,""),n;if(!(n=h===l)){var p="."+l;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ss(a,b){return a===!1?!1:a||b||rs()};var ts=["1"],us={},vs={};function ws(a,b){b=b===void 0?!0:b;var c=xs(a.prefix);if(us[c])ys(a);else if(zs(c,a.path,a.domain)){var d=vs[xs(a.prefix)]||{id:void 0,Dh:void 0};b&&As(a,d.id,d.Dh);ys(a)}else{var e=uj("auiddc");if(e)kb("TAGGING",17),us[c]=e;else if(b){var f=xs(a.prefix),g=Pr();Bs(f,g,a);zs(c,a.path,a.domain);ys(a,!0)}}}
function ys(a,b){if((b===void 0?0:b)&&fr(br)){var c=Yq(!1);c.error!==0?kb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Zq(c)!==0&&kb("TAGGING",41)):kb("TAGGING",40):kb("TAGGING",39)}if(jr(br)&&gr([br])[br.fb]===-1){for(var d={},e=(d[br.fb]=0,d),f=m(dr),g=f.next();!g.done;g=f.next()){var h=g.value;h!==br&&jr(h)&&(e[h.fb]=0)}hr(e,a)}}
function As(a,b,c){var d=xs(a.prefix),e=us[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Fb()/1E3)));Bs(d,h,a,g*1E3)}}}}function Bs(a,b,c,d){var e;e=["1",Nr(c.domain,c.path),b].join(".");var f=Vq(c,d);f.wc=Cs();Ir(a,e,f)}function zs(a,b,c){var d=Qr(a,b,c,ts,Cs());if(!d)return!1;Ds(a,d);return!0}
function Ds(a,b){var c=b.split(".");c.length===5?(us[a]=c.slice(0,2).join("."),vs[a]={id:c.slice(2,4).join("."),Dh:Number(c[4])||0}):c.length===3?vs[a]={id:c.slice(0,2).join("."),Dh:Number(c[2])||0}:us[a]=b}function xs(a){return(a||"_gcl")+"_au"}function Es(a){function b(){sl(c)&&a()}var c=Cs();yl(function(){b();sl(c)||zl(b,c)},c)}
function Fs(a){var b=is(!0),c=xs(a.prefix);Es(function(){var d=b[c];if(d){Ds(c,d);var e=Number(us[c].split(".")[1])*1E3;if(e){kb("TAGGING",16);var f=Vq(a,e);f.wc=Cs();var g=["1",Nr(a.domain,a.path),d].join(".");Ir(c,g,f)}}})}function Gs(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Qr(a,e.path,e.domain,ts,Cs());h&&(g[a]=h);return g};Es(function(){ps(f,b,c,d)})}function Cs(){return["ad_storage","ad_user_data"]};function Hs(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Nj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Is(a,b){var c=Hs(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Nj]||(d[c[e].Nj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Nj].push(g)}}return d};var Js={},Ks=(Js.k={fa:/^[\w-]+$/},Js.b={fa:/^[\w-]+$/,Gj:!0},Js.i={fa:/^[1-9]\d*$/},Js.h={fa:/^\d+$/},Js.t={fa:/^[1-9]\d*$/},Js.d={fa:/^[A-Za-z0-9_-]+$/},Js.j={fa:/^\d+$/},Js.u={fa:/^[1-9]\d*$/},Js.l={fa:/^[01]$/},Js.o={fa:/^[1-9]\d*$/},Js.g={fa:/^[01]$/},Js.s={fa:/^.+$/},Js);var Ls={},Ps=(Ls[5]={Jh:{2:Ms},wj:"2",th:["k","i","b","u"]},Ls[4]={Jh:{2:Ms,GCL:Ns},wj:"2",th:["k","i","b"]},Ls[2]={Jh:{GS2:Ms,GS1:Os},wj:"GS2",th:"sogtjlhd".split("")},Ls);function Qs(a,b,c){var d=Ps[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Jh[e];if(f)return f(a,b)}}}
function Ms(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(u){}var e={},f=Ps[b];if(f){for(var g=f.th,h=m(d.split("$")),l=h.next();!l.done;l=h.next()){var n=l.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Ks[p];r&&(r.Gj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(u){}}return e}}}function Rs(a,b,c){var d=Ps[b];if(d)return[d.wj,c||"1",Ss(a,b)].join(".")}
function Ss(a,b){var c=Ps[b];if(c){for(var d=[],e=m(c.th),f=e.next();!f.done;f=e.next()){var g=f.value,h=Ks[g];if(h){var l=a[g];if(l!==void 0)if(h.Gj&&Array.isArray(l))for(var n=m(l),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+l))}}return d.join("$")}}function Ns(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Os(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Ts=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Us(a,b,c){if(Ps[b]){for(var d=[],e=wr(a,void 0,void 0,Ts.get(b)),f=m(e),g=f.next();!g.done;g=f.next()){var h=Qs(g.value,b,c);h&&d.push(Vs(h))}return d}}
function Ws(a){var b=Xs;if(Ps[2]){for(var c={},d=xr(a,void 0,void 0,Ts.get(2)),e=Object.keys(d).sort(),f=m(e),g=f.next();!g.done;g=f.next())for(var h=g.value,l=m(d[h]),n=l.next();!n.done;n=l.next()){var p=Qs(n.value,2,b);p&&(c[h]||(c[h]=[]),c[h].push(Vs(p)))}return c}}function Ys(a,b,c,d,e){d=d||{};var f=Nr(d.domain,d.path),g=Rs(b,c,f);if(!g)return 1;var h=Vq(d,e,void 0,Ts.get(c));return Ir(a,g,h)}function Zs(a,b){var c=b.fa;return typeof c==="function"?c(a):c.test(a)}
function Vs(a){for(var b=m(Object.keys(a)),c=b.next(),d={};!c.done;d={Wf:void 0},c=b.next()){var e=c.value,f=a[e];d.Wf=Ks[e];d.Wf?d.Wf.Gj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Zs(h,g.Wf)}}(d)):void 0:typeof f==="string"&&Zs(f,d.Wf)||(a[e]=void 0):a[e]=void 0}return a};var $s=function(){this.value=0};$s.prototype.set=function(a){return this.value|=1<<a};var at=function(a,b){b<=0||(a.value|=1<<b-1)};$s.prototype.get=function(){return this.value};$s.prototype.clear=function(a){this.value&=~(1<<a)};$s.prototype.clearAll=function(){this.value=0};$s.prototype.equals=function(a){return this.value===a.value};function bt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function ct(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function dt(){var a=String,b=w.location.hostname,c=w.location.pathname,d=b=Tb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Tb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(ai((""+b+e).toLowerCase()))};var et={},ft=(et.gclid=!0,et.dclid=!0,et.gbraid=!0,et.wbraid=!0,et),gt=/^\w+$/,ht=/^[\w-]+$/,it={},jt=(it.aw="_aw",it.dc="_dc",it.gf="_gf",it.gp="_gp",it.gs="_gs",it.ha="_ha",it.ag="_ag",it.gb="_gb",it),kt=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,lt=/^www\.googleadservices\.com$/;function mt(){return["ad_storage","ad_user_data"]}function nt(a){return!Xa(7)||sl(a)}function ot(a,b){function c(){var d=nt(b);d&&a();return d}yl(function(){c()||zl(c,b)},b)}
function pt(a){return qt(a).map(function(b){return b.gclid})}function rt(a){return st(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function st(a){var b=tt(a.prefix),c=ut("gb",b),d=ut("ag",b);if(!d||!c)return[];var e=function(h){return function(l){l.type=h;return l}},f=qt(c).map(e("gb")),g=vt(d).map(e("ag"));return f.concat(g).sort(function(h,l){return l.timestamp-h.timestamp})}
function wt(a,b,c,d,e){var f=ub(a,function(g){return g.gclid===b});f?(f.timestamp<c&&(f.timestamp=c,f.Rc=e),f.labels=xt(f.labels||[],d||[])):a.push({version:"2",gclid:b,timestamp:c,labels:d,Rc:e})}function vt(a){for(var b=Us(a,5)||[],c=[],d=m(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=zt(f);h&&wt(c,g.k,h,g.b||[],f.u)}return c.sort(function(l,n){return n.timestamp-l.timestamp})}
function qt(a){for(var b=[],c=wr(a,A.cookie,void 0,mt()),d=m(c),e=d.next();!e.done;e=d.next()){var f=At(e.value);f!=null&&(f.Rc=void 0,f.Ba=new $s,f.ab=[1],Bt(b,f))}b.sort(function(g,h){return h.timestamp-g.timestamp});return Ct(b)}function Dt(a,b){for(var c=[],d=m(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=m(b),h=g.next();!h.done;h=g.next()){var l=h.value;c.includes(l)||c.push(l)}return c}
function Bt(a,b,c){c=c===void 0?!1:c;for(var d,e,f=m(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ba&&b.Ba&&h.Ba.equals(b.Ba)&&(e=h)}if(d){var l,n,p=(l=d.Ba)!=null?l:new $s,q=(n=b.Ba)!=null?n:new $s;p.value|=q.value;d.Ba=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Rc=b.Rc);d.labels=Dt(d.labels||[],b.labels||[]);d.ab=Dt(d.ab||[],b.ab||[])}else c&&e?oa(Object,"assign").call(Object,e,b):a.push(b)}
function Et(a){if(!a)return new $s;var b=new $s;if(a===1)return at(b,2),at(b,3),b;at(b,a);return b}
function Ft(){var a=$q("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(ht))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new $s;typeof e==="number"?g=Et(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ba:g,ab:[2]}}catch(h){return null}}
function Gt(){var a=$q("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(ht))return b;var f=new $s,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ba:f,ab:[2]});return b},[])}catch(b){return null}}
function Ht(a){for(var b=[],c=wr(a,A.cookie,void 0,mt()),d=m(c),e=d.next();!e.done;e=d.next()){var f=At(e.value);f!=null&&(f.Rc=void 0,f.Ba=new $s,f.ab=[1],Bt(b,f))}var g=Ft();g&&(g.Rc=void 0,g.ab=g.ab||[2],Bt(b,g));if(Xa(14)){var h=Gt();if(h)for(var l=m(h),n=l.next();!n.done;n=l.next()){var p=n.value;p.Rc=void 0;p.ab=p.ab||[2];Bt(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return Ct(b)}
function xt(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function tt(a){return a&&typeof a==="string"&&a.match(gt)?a:"_gcl"}function It(a,b){if(a){var c={value:a,Ba:new $s};at(c.Ba,b);return c}}
function Jt(a,b,c){var d=sj(a),e=mj(d,"query",!1,void 0,"gclsrc"),f=It(mj(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=It(jj(g,"gclid",!1),3));e||(e=jj(g,"gclsrc",!1))}return f&&(e===void 0||e==="aw"||e==="aw.ds"||Xa(18)&&e==="aw.dv")?[f]:[]}
function Kt(a,b){var c=sj(a),d=mj(c,"query",!1,void 0,"gclid"),e=mj(c,"query",!1,void 0,"gclsrc"),f=mj(c,"query",!1,void 0,"wbraid");f=Rb(f);var g=mj(c,"query",!1,void 0,"gbraid"),h=mj(c,"query",!1,void 0,"gad_source"),l=mj(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||jj(n,"gclid",!1);e=e||jj(n,"gclsrc",!1);f=f||jj(n,"wbraid",!1);g=g||jj(n,"gbraid",!1);h=h||jj(n,"gad_source",!1)}return Lt(d,e,l,f,g,h)}function Mt(){return Kt(w.location.href,!0)}
function Lt(a,b,c,d,e,f){var g={},h=function(l,n){g[n]||(g[n]=[]);g[n].push(l)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(ht))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "aw.dv":Xa(18)&&(h(a,"aw"),h(a,"dc"));break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&ht.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&ht.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&ht.test(f)&&(g.gad_source=
f,h(f,"gs"));return g}function Nt(a){for(var b=Mt(),c=!0,d=m(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Kt(w.document.referrer,!1),b.gad_source=void 0);Ot(b,!1,a)}
function Pt(a){Nt(a);var b=Jt(w.location.href,!0,!1);b.length||(b=Jt(w.document.referrer,!1,!0));a=a||{};Qt(a);if(b.length){var c=b[0],d=Fb(),e=Vq(a,d,!0),f=mt(),g=function(){nt(f)&&e.expires!==void 0&&Xq("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ba.get()},expires:Number(e.expires)})};yl(function(){g();nt(f)||zl(g,f)},f)}}
function Qt(a){var b;if(b=Xa(15)){var c=Rt();b=kt.test(c)||lt.test(c)||St()}if(b){var d;a:{for(var e=sj(w.location.href),f=kj(mj(e,"query")),g=m(Object.keys(f)),h=g.next();!h.done;h=g.next()){var l=h.value;if(!ft[l]){var n=f[l][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=bt(n),r;if(q)c:{var u=q;if(u&&u.length!==0){var t=0;try{for(var v=10;t<u.length&&!(v--<=0);){var x=ct(u,t);if(x===void 0)break;var y=m(x),z=y.next().value,D=y.next().value,E=z,M=D,G=E&7;if(E>>3===16382){if(G!==0)break;
var L=ct(u,M);if(L===void 0)break;r=m(L).next().value===1;break c}var U;d:{var ia=void 0,S=u,ca=M;switch(G){case 0:U=(ia=ct(S,ca))==null?void 0:ia[1];break d;case 1:U=ca+8;break d;case 2:var ta=ct(S,ca);if(ta===void 0)break;var ka=m(ta),ea=ka.next().value;U=ka.next().value+ea;break d;case 5:U=ca+4;break d}U=void 0}if(U===void 0||U>u.length||U<=t)break;t=U}}catch(la){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var X=d;X&&Tt(X,7,a)}}
function Tt(a,b,c){c=c||{};var d=Fb(),e=Vq(c,d,!0),f=mt(),g=function(){if(nt(f)&&e.expires!==void 0){var h=Gt()||[];Bt(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ba:Et(b)},!0);Xq("gcl_aw",h.map(function(l){return{value:{value:l.gclid,creationTimeMs:l.timestamp,linkDecorationSources:l.Ba?l.Ba.get():0},expires:Number(l.expires)}}))}};yl(function(){nt(f)?g():zl(g,f)},f)}
function Ot(a,b,c,d,e){c=c||{};e=e||[];var f=tt(c.prefix),g=d||Fb(),h=Math.round(g/1E3),l=mt(),n=!1,p=!1,q=function(){if(nt(l)){var r=Vq(c,g,!0);r.wc=l;for(var u=function(U,ia){var S=ut(U,f);S&&(Ir(S,ia,r),U!=="gb"&&(n=!0))},t=function(U){var ia=["GCL",h,U];e.length>0&&ia.push(e.join("."));return ia.join(".")},v=m(["aw","dc","gf","ha","gp"]),x=v.next();!x.done;x=v.next()){var y=x.value;a[y]&&u(y,t(a[y][0]))}if(!n&&a.gb){var z=a.gb[0],D=ut("gb",f);!b&&qt(D).some(function(U){return U.gclid===z&&U.labels&&
U.labels.length>0})||u("gb",t(z))}}if(!p&&a.gbraid&&nt("ad_storage")&&(p=!0,!n)){var E=a.gbraid,M=ut("ag",f);if(b||!vt(M).some(function(U){return U.gclid===E&&U.labels&&U.labels.length>0})){var G={},L=(G.k=E,G.i=""+h,G.b=e,G);Ys(M,L,5,c,g)}}Ut(a,f,g,c)};yl(function(){q();nt(l)||zl(q,l)},l)}
function Ut(a,b,c,d){if(a.gad_source!==void 0&&nt("ad_storage")){var e=gd();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=ut("gs",b);if(g){var h=Math.floor((Fb()-(fd()||0))/1E3),l,n=dt(),p={};l=(p.k=f,p.i=""+h,p.u=n,p);Ys(g,l,5,d,c)}}}}
function Vt(a,b){var c=is(!0);ot(function(){for(var d=tt(b.prefix),e=0;e<a.length;++e){var f=a[e];if(jt[f]!==void 0){var g=ut(f,d),h=c[g];if(h){var l=Math.min(Wt(h),Fb()),n;b:{for(var p=l,q=wr(g,A.cookie,void 0,mt()),r=0;r<q.length;++r)if(Wt(q[r])>p){n=!0;break b}n=!1}if(!n){var u=Vq(b,l,!0);u.wc=mt();Ir(g,h,u)}}}}Ot(Lt(c.gclid,c.gclsrc),!1,b)},mt())}
function Xt(a){var b=["ag"],c=is(!0),d=tt(a.prefix);ot(function(){for(var e=0;e<b.length;++e){var f=ut(b[e],d);if(f){var g=c[f];if(g){var h=Qs(g,5);if(h){var l=zt(h);l||(l=Fb());var n;a:{for(var p=l,q=Us(f,5),r=0;r<q.length;++r)if(zt(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(l/1E3);Ys(f,h,5,a,l)}}}}},["ad_storage"])}function ut(a,b){var c=jt[a];if(c!==void 0)return b+c}function Wt(a){return Yt(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function zt(a){return a?(Number(a.i)||0)*1E3:0}function At(a){var b=Yt(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Yt(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!ht.test(a[2])?[]:a}
function Zt(a,b,c,d,e){if(Array.isArray(b)&&rr(w)){var f=tt(e),g=function(){for(var h={},l=0;l<a.length;++l){var n=ut(a[l],f);if(n){var p=wr(n,A.cookie,void 0,mt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};ot(function(){ps(g,b,c,d)},mt())}}
function $t(a,b,c,d){if(Array.isArray(a)&&rr(w)){var e=["ag"],f=tt(d),g=function(){for(var h={},l=0;l<e.length;++l){var n=ut(e[l],f);if(!n)return{};var p=Us(n,5);if(p.length){var q=p.sort(function(r,u){return zt(u)-zt(r)})[0];h[n]=Rs(q,5)}}return h};ot(function(){ps(g,a,b,c)},["ad_storage"])}}function Ct(a){return a.filter(function(b){return ht.test(b.gclid)})}
function au(a,b){if(rr(w)){for(var c=tt(b.prefix),d={},e=0;e<a.length;e++)jt[a[e]]&&(d[a[e]]=jt[a[e]]);ot(function(){yb(d,function(f,g){var h=wr(c+g,A.cookie,void 0,mt());h.sort(function(u,t){return Wt(t)-Wt(u)});if(h.length){var l=h[0],n=Wt(l),p=Yt(l.split(".")).length!==0?l.split(".").slice(3):[],q={},r;r=Yt(l.split(".")).length!==0?l.split(".")[2]:void 0;q[f]=[r];Ot(q,!0,b,n,p)}})},mt())}}
function bu(a){var b=["ag"],c=["gbraid"];ot(function(){for(var d=tt(a.prefix),e=0;e<b.length;++e){var f=ut(b[e],d);if(!f)break;var g=Us(f,5);if(g.length){var h=g.sort(function(q,r){return zt(r)-zt(q)})[0],l=zt(h),n=h.b,p={};p[c[e]]=h.k;Ot(p,!0,a,l,n)}}},["ad_storage"])}function cu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function du(a){function b(h,l,n){n&&(h[l]=n)}if(vl()){var c=Mt(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:is(!1)._gs);if(cu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);qs(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);qs(function(){return g},1)}}}function St(){var a=sj(w.location.href);return mj(a,"query",!1,void 0,"gad_source")}
function eu(a){if(!Xa(1))return null;var b=is(!0).gad_source;if(b!=null)return w.location.hash="",b;if(Xa(2)){b=St();if(b!=null)return b;var c=Mt();if(cu(c,a))return"0"}return null}function fu(a){var b=eu(a);b!=null&&qs(function(){var c={};return c.gad_source=b,c},4)}function gu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function hu(a,b,c,d){var e=[];c=c||{};if(!nt(mt()))return e;var f=qt(a),g=gu(e,f,b);if(g.length&&!d)for(var h=m(g),l=h.next();!l.done;l=h.next()){var n=l.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Vq(c,p,!0);r.wc=mt();Ir(a,q,r)}return e}
function iu(a,b){var c=[];b=b||{};var d=st(b),e=gu(c,d,a);if(e.length)for(var f=m(e),g=f.next();!g.done;g=f.next()){var h=g.value,l=tt(b.prefix),n=ut(h.type,l);if(!n)break;var p=h,q=p.version,r=p.gclid,u=p.labels,t=p.timestamp,v=Math.round(t/1E3);if(h.type==="ag"){var x={},y=(x.k=r,x.i=""+v,x.b=(u||[]).concat([a]),x);Ys(n,y,5,b,t)}else if(h.type==="gb"){var z=[q,v,r].concat(u||[],[a]).join("."),D=Vq(b,t,!0);D.wc=mt();Ir(n,z,D)}}return c}
function ju(a,b){var c=tt(b),d=ut(a,c);if(!d)return 0;var e;e=a==="ag"?vt(d):qt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function ku(a){for(var b=0,c=m(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function lu(a){var b=Math.max(ju("aw",a),ku(nt(mt())?Is():{})),c=Math.max(ju("gb",a),ku(nt(mt())?Is("_gac_gb",!0):{}));c=Math.max(c,ju("ag",a));return c>b}
function Rt(){return A.referrer?mj(sj(A.referrer),"host"):""};function xu(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function yu(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function zu(){return["ad_storage","ad_user_data"]}function Au(a){if(F(38)&&!Ol(Kl.X.gm)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{xu(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Nl(Kl.X.gm,function(d){d.gclid&&Tt(d.gclid,5,a)}),yu(c)||P(178))})}catch(c){P(177)}};yl(function(){nt(zu())?b():zl(b,zu())},zu())}};var Bu=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function Cu(a){return a.data.action!=="gcl_transfer"?(P(173),!0):a.data.gadSource?a.data.gclid?!1:(P(181),!0):(P(180),!0)}
function Du(a,b){if(F(a)){if(Ol(Kl.X.we))return P(176),Kl.X.we;if(Ol(Kl.X.im))return P(170),Kl.X.we;var c=Wp();if(!c)P(171);else if(c.opener){var d=function(g){if(!Bu.includes(g.origin))P(172);else if(!Cu(g)){var h={gadSource:g.data.gadSource};F(229)&&(h.gclid=g.data.gclid);Nl(Kl.X.we,h);a===200&&g.data.gclid&&Tt(String(g.data.gclid),6,b);var l;(l=g.stopImmediatePropagation)==null||l.call(g);dq(c,"message",d)}};if(cq(c,"message",d)){Nl(Kl.X.im,!0);for(var e=m(Bu),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},
f.value);P(174);return Kl.X.we}P(175)}}};function Nu(a){var b=Q(a.D,N.m.Gc),c=Q(a.D,N.m.Fc);b&&!c?(a.eventName!==N.m.ma&&a.eventName!==N.m.Yd&&P(131),a.isAborted=!0):!b&&c&&(P(132),a.isAborted=!0)}
function Ou(a){var b=An(N.m.aa)?Wn.pscdl:"denied";b!=null&&W(a,N.m.Kg,b)}function Pu(a){var b=aq(!0);W(a,N.m.Ec,b)}function Qu(a){Sq()&&W(a,N.m.je,1)}function Ru(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&lj(a.substring(0,b))===void 0;)b--;return lj(a.substring(0,b))||""}function Su(a){Tu(a,po.Hf.Jn,Q(a.D,N.m.rb))}function Tu(a,b,c){Hu(a,N.m.Jc)||W(a,N.m.Jc,{});Hu(a,N.m.Jc)[b]=c}function Uu(a){V(a,R.A.Sf,jl.Z.Ga)}
function Vu(a){var b=a.D.getMergedValues(N.m.Dc);b&&a.mergeHitDataForKey(N.m.Dc,b)}function Wu(a,b){b=b===void 0?!1:b;var c=T(a,R.A.Rf),d=Xu(a,"custom_event_accept_rules",!1)&&!b;if(c){var e=c.indexOf(a.target.destinationId)>=0,f=!0;F(240)&&T(a,R.A.Rl)&&(f=T(a,R.A.Za)===Ij());e&&f?V(a,R.A.Kh,!0):(V(a,R.A.Kh,!1),d||(a.isAborted=!0));F(240)&&(a.hasBeenAccepted()?a.isAborted=!0:T(a,R.A.Kh)&&a.accept())}}
function Yu(a){nk&&(jm=!0,a.eventName===N.m.ma?pm(a.D,a.target.id):(T(a,R.A.Oe)||(mm[a.target.id]=!0),fo(T(a,R.A.Za))))}function Zu(a){};var $u=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),av=/^~?[\w-]+(?:\.~?[\w-]+)*$/,bv=/^\d+\.fls\.doubleclick\.net$/,cv=/;gac=([^;?]+)/,dv=/;gacgb=([^;?]+)/;
function ev(a,b){if(bv.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match($u)?lj(c[1])||"":""}for(var d=[],e=m(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],l=a[g],n=0;n<l.length;n++)h.push(l[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function fv(a,b,c){for(var d=nt(mt())?Is("_gac_gb",!0):{},e=[],f=!1,g=m(Object.keys(d)),h=g.next();!h.done;h=g.next()){var l=h.value,n=hu("_gac_gb_"+l,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(l+":"+n.join(","))}return{gq:f?e.join(";"):"",fq:ev(d,dv)}}function gv(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(av)?b[1]:void 0}
function hv(a){var b={},c,d,e;bv.test(A.location.host)&&(c=gv("gclgs"),d=gv("gclst"),e=gv("gcllp"));if(c&&d&&e)b.cg=c,b.yh=d,b.xh=e;else{var f=Fb(),g=vt((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),l=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Rc});h.length>0&&l.length>0&&n.length>0&&(b.cg=h.join("."),b.yh=l.join("."),b.xh=n.join("."))}return b}
function iv(a,b,c,d){d=d===void 0?!1:d;if(bv.test(A.location.host)){var e=gv(c);if(e){if(d){var f=new $s;at(f,2);at(f,3);return e.split(".").map(function(h){return{gclid:h,Ba:f,ab:[1]}})}return e.split(".").map(function(h){return{gclid:h,Ba:new $s,ab:[1]}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Ht(g):qt(g)}if(b==="wbraid")return qt((a||"_gcl")+"_gb");if(b==="braids")return st({prefix:a})}return[]}function jv(a){return bv.test(A.location.host)?!(gv("gclaw")||gv("gac")):lu(a)}
function kv(a,b,c){var d;d=c?iu(a,b):hu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function qv(){return Xn("dedupe_gclid",function(){return Pr()})};function wv(a,b,c,d){var e=Mc(),f;if(e===1)a:{var g=Dg(3);g=g.toLowerCase();for(var h="https://"+g,l="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(l)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==w.location.protocol?a:b)+c};
var Bv=function(a,b){if(a&&(rb(a)&&(a=so(a)),a)){var c=void 0,d=!1,e=Q(b,N.m.Io);if(e&&Array.isArray(e)){c=[];for(var f=0;f<e.length;f++){var g=so(e[f]);g&&(c.push(g),(a.id===g.id||a.id===a.destinationId&&a.destinationId===g.destinationId)&&(d=!0))}}if(!c||d){var h=Q(b,N.m.il),l;if(h){l=Array.isArray(h)?h:[h];var n=Q(b,N.m.bl),p=Q(b,N.m.fl),q=Q(b,N.m.jl),r=Tm(Q(b,N.m.Ho)),u=n||p,t=1;a.prefix!=="UA"||c||(t=5);for(var v=0;v<l.length;v++)if(v<t)if(c)xv(c,l[v],r,b,{vc:u,options:q});else if(a.prefix===
"AW"&&a.ids[uo[1]])F(155)?xv([a],l[v],r||"US",b,{vc:u,options:q}):yv(a.ids[uo[0]],a.ids[uo[1]],l[v],b,{vc:u,options:q});else if(a.prefix==="UA")if(F(155))xv([a],l[v],r||"US",b,{vc:u});else{var x=a.destinationId,y=l[v],z={vc:u};P(23);if(y){z=z||{};var D=zv(Av,z,x),E={};z.vc!==void 0?E.receiver=z.vc:E.replace=y;E.ga_wpid=x;E.destination=y;D(2,Eb(),E)}}}}}},xv=function(a,b,c,d,e){P(21);if(b&&c){e=e||{};for(var f={countryNameCode:c,destinationNumber:b,retrievalTime:Eb()},g=0;g<a.length;g++){var h=a[g];
Cv[h.id]||(h&&h.prefix==="AW"&&!f.adData&&h.ids.length>=2?(f.adData={ak:h.ids[uo[0]],cl:h.ids[uo[1]]},Dv(f.adData,d),Cv[h.id]=!0):h&&h.prefix==="UA"&&!f.gaData&&(f.gaData={gaWpid:h.destinationId},Cv[h.id]=!0))}(f.gaData||f.adData)&&zv(Ev,e,void 0,d)(e.vc,f,e.options)}},yv=function(a,b,c,d,e){P(22);if(c){e=e||{};var f=zv(Fv,e,a,d),g={ak:a,cl:b};e.vc===void 0&&(g.autoreplace=c);Dv(g,d);f(2,e.vc,g,c,0,Eb(),e.options)}},Dv=function(a,b){a.dma=Pq();Qq()&&(a.dmaCps=Oq());Hq(b)?a.npa="0":a.npa="1"},zv=function(a,
b,c,d){var e=w;if(e[a.functionName])return b.Cj&&Sc(b.Cj),e[a.functionName];var f=Gv();e[a.functionName]=f;if(a.additionalQueues)for(var g=0;g<a.additionalQueues.length;g++)e[a.additionalQueues[g]]=e[a.additionalQueues[g]]||Gv();a.idKey&&e[a.idKey]===void 0&&(e[a.idKey]=c);fl({destinationId:Dg(5),endpoint:0,eventId:d==null?void 0:d.eventId,priorityId:d==null?void 0:d.priorityId},wv("https://","http://",a.scriptUrl),b.Cj,b.Mq);return f},Gv=function(){function a(){a.q=a.q||[];a.q.push(arguments)}return a},
Fv={functionName:"_googWcmImpl",idKey:"_googWcmAk",scriptUrl:"www.gstatic.com/wcm/loader.js"},Av={functionName:"_gaPhoneImpl",idKey:"ga_wpid",scriptUrl:"www.gstatic.com/gaphone/loader.js"},Hv={En:Hg(2),tp:"5"},Ev={functionName:"_googCallTrackingImpl",additionalQueues:[Av.functionName,Fv.functionName],scriptUrl:"www.gstatic.com/call-tracking/call-tracking_"+(Hv.En||Hv.tp)+".js"},Cv={};var Iv=[N.m.aa,N.m.W];var Nv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,Ov=/^www.googleadservices.com$/;function Pv(a){a||(a=Qv());return a.Er?!1:a.uq||a.wq||a.yq||a.xq||a.De||a.wh||a.hq||a.Yb==="aw.ds"||F(235)&&a.Yb==="aw.dv"||a.lq?!0:!1}
function Qv(){var a={},b=is(!0);a.Er=!!b._up;var c=Mt(),d=ou();a.uq=c.aw!==void 0;a.wq=c.dc!==void 0;a.yq=c.wbraid!==void 0;a.xq=c.gbraid!==void 0;a.Yb=typeof c.gclsrc==="string"?c.gclsrc:void 0;a.De=d.De;a.wh=d.wh;var e=A.referrer?mj(sj(A.referrer),"host"):"";a.lq=Nv.test(e);a.hq=Ov.test(e);return a};var lg;function Wv(){var a=data.permissions||{};lg=new rg(Dg(5),a)};var Xv=Jg(57,5),Yv=Jg(58,50),Zv=vb();
var aw=function(a,b){a&&($v("sid",a.targetId,b),$v("cc",a.clientCount,b),$v("tl",a.totalLifeMs,b),$v("hc",a.heartbeatCount,b),$v("cl",a.clientLifeMs,b))},$v=function(a,b,c){b!=null&&c.push(a+"="+b)},bw=function(){var a=A.referrer;if(a){var b;return mj(sj(a),"host")===((b=w.location)==null?void 0:b.host)?1:2}return 0},cw="https://"+Dg(21)+"/a?",ew=function(){this.U=dw;this.M=0};ew.prototype.H=function(a,b,c,d){var e=bw(),f,g=[];f=w===w.top&&e!==0&&
b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&$v("si",a.kg,g);$v("m",0,g);$v("iss",f,g);$v("if",c,g);aw(b,g);d&&$v("fm",encodeURIComponent(d.substring(0,Yv)),g);this.R(g);};ew.prototype.C=function(a,b,c,d,e){var f=[];$v("m",1,f);$v("s",a,f);$v("po",bw(),f);b&&($v("st",b.state,f),$v("si",b.kg,f),$v("sm",b.sg,f));aw(c,f);$v("c",d,f);e&&$v("fm",encodeURIComponent(e.substring(0,Yv)),f);this.R(f);
};ew.prototype.R=function(a){a=a===void 0?[]:a;!lk||this.M>=Xv||($v("pid",Zv,a),$v("bc",++this.M,a),a.unshift("ctid="+Dg(5)+"&t=s"),this.U(""+cw+a.join("&")))};var fw=Number('')||500,gw=Number('')||5E3,hw=Number('')||20,iw=Number('')||5E3;function jw(a){return a.performance&&a.performance.now()||Date.now()}
var kw=function(a,b){var c=w,d;var e=function(f,g,h){h=h===void 0?{bn:function(){},dn:function(){},Zm:function(){},onFailure:function(){}}:h;this.xp=f;this.C=g;this.M=h;this.ja=this.wa=this.heartbeatCount=this.wp=0;this.nh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.kg=jw(this.C);this.sg=jw(this.C);this.U=10};e.prototype.init=function(){this.R(1);this.Ya()};e.prototype.getState=function(){return{state:this.state,
kg:Math.round(jw(this.C)-this.kg),sg:Math.round(jw(this.C)-this.sg)}};e.prototype.R=function(f){this.state!==f&&(this.state=f,this.sg=jw(this.C))};e.prototype.Am=function(){return String(this.wp++)};e.prototype.Ya=function(){var f=this;this.heartbeatCount++;this.wb({type:0,clientId:this.id,requestId:this.Am(),maxDelay:this.oh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ja++,g.isDead||f.ja>hw){var l=g.isDead&&g.failure.failureType;
f.U=l||10;f.R(4);f.vp();var n,p;(p=(n=f.M).Zm)==null||p.call(n,{failureType:l||10,data:g.failure.data})}else f.R(3),f.Fm();else{if(f.heartbeatCount>g.stats.heartbeatCount+hw){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.M).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var u=f.state;f.R(2);if(u!==2)if(f.nh){var t,v;(v=(t=f.M).dn)==null||v.call(t)}else{f.nh=!0;var x,y;(y=(x=f.M).bn)==null||y.call(x)}f.ja=0;f.yp();f.Fm()}}})};e.prototype.oh=function(){return this.state===2?
gw:fw};e.prototype.Fm=function(){var f=this;this.C.setTimeout(function(){f.Ya()},Math.max(0,this.oh()-(jw(this.C)-this.wa)))};e.prototype.Cp=function(f,g,h){var l=this;this.wb({type:1,clientId:this.id,requestId:this.Am(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,u={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},t,v;(v=(t=l.M).onFailure)==null||v.call(t,u);h(u)}})};e.prototype.wb=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.U},g(f);else{var l=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var u=h.H[n];u&&h.Of(u,7)},(p=f.maxDelay)!=null?p:iw),r={request:f,pn:g,kn:l,Kq:q};this.H[n]=r;l||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.wa=jw(this.C);f.kn=!1;this.xp(f.request)};e.prototype.yp=function(){for(var f=m(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.kn&&this.sendRequest(h)}};e.prototype.vp=function(){for(var f=
m(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Of(this.H[g.value],this.U)};e.prototype.Of=function(f,g){this.Lc(f);var h=f.request;h.failure={failureType:g};f.pn(h)};e.prototype.Lc=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Kq)};e.prototype.sq=function(f){this.wa=jw(this.C);var g=this.H[f.requestId];if(g)this.Lc(g),g.pn(f);else{var h,l;(l=(h=this.M).onFailure)==null||l.call(h,{failureType:14})}};d=new e(a,c,b);return d};var lw;
var mw=function(){lw||(lw=new ew);return lw},dw=function(a){Hl(Jl(jl.Z.Kc),function(){Pc(a)})},nw=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},ow=function(a){var b=a,c,d=Hg(11);d=Hg(10);c=d;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var e;try{e=new URL(a)}catch(f){return null}return e.protocol!==
"https:"?null:e},pw=function(a){var b=Ol(Kl.X.qm);return b&&b[a]},qw=function(a,b,c,d,e){var f=this;this.H=d;this.U=this.R=!1;this.ja=null;this.initTime=c;this.C=15;this.M=this.Sp(a);w.setTimeout(function(){f.initialize()},1E3);Sc(function(){f.Bq(a,b,e)})};k=qw.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),kg:this.initTime,sg:Math.round(Fb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.M.Cp(a,b,c)};k.getState=function(){return this.M.getState().state};
k.Bq=function(a,b,c){var d=w.location.origin,e=this,f=Nc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,l=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?nw(h):"",p;F(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Nc(l+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ja=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===
a.origin&&e.M.sq(r.data)});e.initialize()})};f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Sp=function(a){var b=this,c=kw(function(d){var e;(e=b.ja)==null||e.postMessage(d,a.origin)},{bn:function(){b.R=!0;b.H.H(c.getState(),c.stats)},dn:function(){},Zm:function(d){b.R?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,
d==null?void 0:d.data)):(b.C=(d==null?void 0:d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.U||this.M.init();this.U=!0};function rw(){var a=og(lg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function sw(a,b){var c=Math.round(Fb());b=b===void 0?!1:b;var d=w.location.origin;if(!d||!rw()||F(168))return;fj()&&!a&&(a=""+d+ej()+"/_/service_worker");var e=ow(a);if(e===null||pw(e.origin))return;if(!Ac()){mw().H(void 0,void 0,6);return}var f=new qw(e,!!a,c||Math.round(Fb()),mw(),b);Pl(Kl.X.qm,{})[e.origin]=f;}
var tw=function(a,b,c,d){var e;if((e=pw(a))==null||!e.delegate){var f=Ac()?16:6;mw().C(f,void 0,void 0,b.commandType);d({failureType:f});return}pw(a).delegate(b,c,d);};
function uw(a,b,c,d,e){var f=ow();if(f===null){d(Ac()?16:6);return}var g,h=(g=pw(f.origin))==null?void 0:g.initTime,l=Math.round(Fb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?l-h:void 0}};e&&(n.params.encryptionKeyString=e);tw(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function vw(a,b,c,d){var e=ow(a);if(e===null){d("_is_sw=f"+(Ac()?16:6)+"te");return}var f=b?1:0,g=Math.round(Fb()),h,l=(h=pw(e.origin))==null?void 0:h.initTime,n=l?g-l:void 0,p=!1;F(169)&&(p=!0);tw(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:w.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,u,t=(u=pw(e.origin))==
null?void 0:u.getState();t!==void 0&&(r+="s"+t);d(n?r+("t"+n):r+"te")});};function ww(a){if(F(10))return;var b=fj()||Fg.C||!!fk(a.D);F(245)&&(b=Fg.C||!!fk(a.D));if(b||F(168))return;sw(void 0,F(131));};function xw(){var a;a=a===void 0?document:a;var b;return!((b=a.featurePolicy)==null||!b.allowedFeatures().includes("attribution-reporting"))};var Jw=function(a){this.C=1;this.C>0||(this.C=1);this.onSuccess=a.D.onSuccess},Kw=function(a,b){return Qb(function(){a.C--;if(qb(a.onSuccess)&&a.C===0)a.onSuccess()},b>0?b:1)};function Nw(a,b){var c=!!fj();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?ej()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?F(90)&&Bm()?Lw():""+ej()+"/ag/g/c":Lw();case 16:return c?F(90)&&Bm()?Mw():""+ej()+"/ga/g/c":Mw();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
ej()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?ej()+"/d/pagead/form-data":F(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Ep+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?ej()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 66:return"https://www.google.com/pagead/uconversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 63:return"https://www.googleadservices.com/pagead/conversion";case 64:return c?ej()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 65:return"https://www.google.com/pagead/1p-conversion";case 22:return c?ej()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?ej()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";
case 23:return c?ej()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?ej()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return F(205)?"https://www.google.com/measurement/conversion/":c?ej()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?ej()+"/d/ccm/form-data":F(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:rc(a,"Unknown endpoint")}};function Pw(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var Qw="email sha256_email_address phone_number sha256_phone_number first_name last_name".split(" "),Rw="first_name sha256_first_name last_name sha256_last_name street sha256_street city region country postal_code".split(" ");function Sw(a,b){if(!b._tag_metadata){for(var c={},d=0,e=0;e<a.length;e++)d+=Tw(a[e],b,c)?1:0;d>0&&(b._tag_metadata=c)}}function Tw(a,b,c){var d=b[a];if(d===void 0)return!1;c[a]=Array.isArray(d)?d.map(function(){return{mode:"c"}}):{mode:"c"};return!0}
function Uw(a,b,c){function d(f,g){g=String(g).substring(0,100);e.push(""+f+encodeURIComponent(g))}if(!c)return"";var e=[];d("i",String(a));d("f",b);c.mode&&d("m",c.mode);c.isPreHashed&&d("p","1");c.rawLength&&d("r",String(c.rawLength));c.normalizedLength&&d("n",String(c.normalizedLength));c.location&&d("l",c.location);c.selector&&d("s",c.selector);return e.join(".")};function Vw(){this.blockSize=-1};function Ww(a,b){this.blockSize=-1;this.blockSize=64;this.M=Ga.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.H=0;this.C=[];this.ja=a;this.U=b;this.wa=Ga.Int32Array?new Int32Array(64):Array(64);Xw===void 0&&(Ga.Int32Array?Xw=new Int32Array(Yw):Xw=Yw);this.reset()}Ha(Ww,Vw);for(var Zw=[],$w=0;$w<63;$w++)Zw[$w]=0;var ax=[].concat(128,Zw);
Ww.prototype.reset=function(){this.R=this.H=0;var a;if(Ga.Int32Array)a=new Int32Array(this.U);else{var b=this.U,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var bx=function(a){for(var b=a.M,c=a.wa,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var l=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,u=a.C[5]|0,t=a.C[6]|0,v=a.C[7]|0,x=0;x<64;x++){var y=((l>>>2|l<<30)^(l>>>13|l<<19)^(l>>>22|l<<10))+(l&n^l&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&u^~r&t)+(Xw[x]|0)|0)+(c[x]|0)|0)|0;v=t;t=u;u=r;r=q+z|0;q=p;p=n;n=l;l=z+y|0}a.C[0]=a.C[0]+l|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+u|0;a.C[6]=a.C[6]+t|0;a.C[7]=a.C[7]+v|0};
Ww.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.M[d++]=a.charCodeAt(c++),d==this.blockSize&&(bx(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.M[d++]=g;d==this.blockSize&&(bx(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.R+=b};Ww.prototype.digest=function(){var a=[],b=this.R*8;this.H<56?this.update(ax,56-this.H):this.update(ax,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.M[c]=b&255,b/=256;bx(this);for(var d=0,e=0;e<this.ja;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Yw=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Xw;function cx(){Ww.call(this,8,dx)}Ha(cx,Ww);var dx=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var ex=/^[0-9A-Fa-f]{64}$/;function fx(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function gx(a){var b=w;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(ex.test(a))return Promise.resolve(a);try{var d=fx(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return hx(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function hx(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};function Qx(a,b){b&&yb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};function Rx(a,b){var c=Hu(a,N.m.Dc);if(c&&typeof c==="object")for(var d=m(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value,g=c[f];g!==void 0&&(g===null&&(g=""),b["gap."+f]=String(g))}};var ey={};ey.O=kr.O;var fy={ds:"L",pp:"S",zs:"Y",Gr:"B",Tr:"E",Xr:"I",ws:"TC",Wr:"HTC"},gy={pp:"S",Sr:"V",Jr:"E",vs:"tag"},hy={},iy=(hy[ey.O.Ti]="6",hy[ey.O.Ui]="5",hy[ey.O.Si]="7",hy);function jy(){function a(c,d){var e=ob(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var ky=!1;
function Dy(a){}function Ey(a){}
function Fy(){}function Gy(a){}
function Hy(a){}function Iy(a){}
function Jy(){}
function Ky(a,b){}
function Ly(a,b,c){}
function My(){};var Ny=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function Oy(a,b,c,d,e,f,g,h){var l=oa(Object,"assign").call(Object,{},Ny);c&&(l.body=c,l.method="POST");oa(Object,"assign").call(Object,l,e);h==null||Uk(h);w.fetch(b,l).then(function(n){h==null||Vk(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function u(){p.read().then(function(t){var v;v=t.done;var x=q.decode(t.value,{stream:!v});Py(d,x);v?(f==null||f(),r()):u()}).catch(function(){r()})}u()})}}).catch(function(){h==null||Vk(h);
g?g():F(128)&&(b+="&_z=retryFetch",c?cl(a,b,c):bl(a,b))})};var Qy=function(a){this.M=a;this.C=""},Ry=function(a,b){a.H=b;return a},Py=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=m(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(l){}e=void 0}Sy(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},Ty=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c={};Sy(a,(c[b.fallback_url_method]=
[b.fallback_url],c.options={},c))}}},Sy=function(a,b){b&&(Uy(b.send_pixel,b.options,a.M),Uy(b.create_iframe,b.options,a.R),Uy(b.fetch,b.options,a.H))};function Vy(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function Uy(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=sd(b)?b:{},f=m(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var Wy=function(a,b){this.Nq=a;this.timeoutMs=b;this.Ta=void 0},Uk=function(a){a.Ta||(a.Ta=setTimeout(function(){a.Nq();a.Ta=void 0},a.timeoutMs))},Vk=function(a){a.Ta&&(clearTimeout(a.Ta),a.Ta=void 0)};var Fz=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),Gz={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},Hz={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},Iz="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function Jz(){var a=hp("gtm.allowlist")||hp("gtm.whitelist");a&&P(9);Xi&&!F(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:F(212)&&(a=void 0);Fz.test(w.location&&w.location.hostname)&&(Xi?P(116):(P(117),Kz&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Jb(Cb(a),Gz),c=hp("gtm.blocklist")||hp("gtm.blacklist");c||(c=hp("tagTypeBlacklist"))&&P(3);c?P(8):c=[];Fz.test(w.location&&w.location.hostname)&&(c=Cb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));Cb(c).indexOf("google")>=0&&P(2);var d=c&&Jb(Cb(c),Hz),e={};return function(f){var g=f&&f[nf.Qa];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=cj[g]||[],l=!0;if(a){var n;if(n=l)a:{if(b.indexOf(g)<0){if(Xi&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){P(11);n=!1;break a}}else{n=!1;break a}}n=!0}l=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var u=wb(d,h||[]);u&&
P(10);q=u}}var t=!l||q;!t&&(h.indexOf("sandboxedScripts")===-1?0:Xi&&h.indexOf("cmpPartners")>=0?!Lz():b&&b.indexOf("sandboxedScripts")!==-1?0:wb(d,Iz))&&(t=!0);return e[g]=t}}function Lz(){var a=og(lg.C,Dg(5),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var Kz=!1;Kz=!0;F(218)&&(Kz=Bg(48,Kz));function Mz(a,b,c,d,e){if(!Rj(a)){d.loadExperiments=Pi();Tj(a,d,e);var f=Nz(a),g=function(){Bj().container[a]&&(Bj().container[a].state=3);Oz()},h={destinationId:a,endpoint:0};if(fj())fl(h,ej()+"/"+f,void 0,g);else{var l=Kb(a,"GTM-"),n=ek(),p=c?"/gtag/js":"/gtm.js",q=dk(b,p+f);if(!q){var r=Dg(3)+p;n&&Cc&&l&&(r=Cc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=wv("https://","http://",r+f)}fl(h,q,void 0,g)}}}function Oz(){Uj()||yb(Vj(),function(a,b){Pz(a,b.transportUrl,b.context);P(92)})}
function Pz(a,b,c,d){if(!Sj(a))if(c.loadExperiments||(c.loadExperiments=Pi()),Uj()){var e=Bj(),f=Aj(a);f?f.state=0:(f={state:0,transportUrl:b,context:c,parent:Mj()},F(269)?e.destinationArray[a]=[f]:e.destination[a]=f);Cj({ctid:a,isDestination:!0},d);P(91)}else{var g=Bj(),h=Aj(a);h?h.state=1:(h={context:c,state:1,parent:Mj()},F(269)?g.destinationArray[a]=[h]:g.destination[a]=h);Cj({ctid:a,isDestination:!0},d);var l={destinationId:a,endpoint:0};if(fj())fl(l,ej()+("/gtd"+Nz(a,!0)));else{var n="/gtag/destination"+
Nz(a,!0),p=dk(b,n);p||(p=wv("https://","http://",Dg(3)+n));fl(l,p)}}}function Nz(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a),d=Dg(19);d!=="dataLayer"&&(c+="&l="+d);if(!Kb(a,"GTM-")||b)c=F(130)?c+(fj()?"&sc=1":"&cx=c"):c+"&cx=c";var e=c,f,g={nn:Gg(15),qn:Dg(14)};f=mf(g);c=e+("&gtm="+f);ek()&&(c+="&sign="+Ri.Qi);var h=Fg.M;h===1?c+="&fps=fc":h===2&&(c+="&fps=fe");return c};var Qz=function(){this.H=0;this.C={}};Qz.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Je:c};return d};Qz.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var Sz=function(a,b){var c=[];yb(Rz.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Je===void 0||b.indexOf(e.Je)>=0)&&c.push(e.listener)});return c};function Tz(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:Dg(5)}};function Uz(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var Wz=function(a,b){this.C=!1;this.R=[];this.eventData={tags:[]};this.U=!1;this.H=this.M=0;Vz(this,a,b)},Xz=function(a,b,c,d){if(Ti.hasOwnProperty(b)||b==="__zone")return-1;var e={};sd(d)&&(e=td(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},Yz=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},Zz=function(a){if(!a.C){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.C=!0;a.R.length=0}},Vz=function(a,b,c){b!==void 0&&a.Uf(b);c&&w.setTimeout(function(){Zz(a)},
Number(c))};Wz.prototype.Uf=function(a){var b=this,c=Hb(function(){Sc(function(){a(Dg(5),b.eventData)})});this.C?c():this.R.push(c)};var $z=function(a){a.M++;return Hb(function(){a.H++;a.U&&a.H>=a.M&&Zz(a)})},aA=function(a){a.U=!0;a.H>=a.M&&Zz(a)};var bA={};function cA(){return w[dA()]}var eA=function(a){if(vl()){var b=cA();b(a+"require","linker");b(a+"linker:passthrough",!0)}},fA=function(a){var b=w;b.GoogleAnalyticsObject||(b.GoogleAnalyticsObject=a||"ga");var c=b.GoogleAnalyticsObject;if(b[c])b.hasOwnProperty(c);else{var d=function(){var e=Fa.apply(0,arguments);d.q=d.q||[];d.q.push(e)};d.l=Number(Eb());b[c]=d}return b[c]};
function dA(){return w.GoogleAnalyticsObject||"ga"}function gA(){var a=Dg(5);}
function hA(a,b){return function(){var c=cA(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),l=g.indexOf("&tid="+b)<0;l&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);l&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var nA=["es","1"],oA={},pA={};function qA(a,b){if(lk){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";oA[a]=[["e",c],["eid",a]];Ap(a)}}function rA(a){var b=a.eventId,c=a.Vc;if(!oA[b])return[];var d=[];pA[b]||d.push(nA);d.push.apply(d,Ba(oA[b]));c&&(pA[b]=!0);return d};var sA={},tA={},uA={};function vA(a,b,c,d){lk&&F(120)&&((d===void 0?0:d)?(uA[b]=uA[b]||0,++uA[b]):c!==void 0?(tA[a]=tA[a]||{},tA[a][b]=Math.round(c)):(sA[a]=sA[a]||{},sA[a][b]=(sA[a][b]||0)+1))}function wA(a){var b=a.eventId,c=a.Vc,d=sA[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete sA[b];return e.length?[["md",e.join(".")]]:[]}
function xA(a){var b=a.eventId,c=a.Vc,d=tA[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete tA[b];return e.length?[["mtd",e.join(".")]]:[]}function yA(){for(var a=[],b=m(Object.keys(uA)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+uA[d])}return a.length?[["mec",a.join(".")]]:[]};var zA={},AA={};function BA(a,b,c){if(lk&&b){var d=ik(b);zA[a]=zA[a]||[];zA[a].push(c+d);var e=b[nf.Qa];if(!e)throw Error("Error: No function name given for function call.");var f=(Pf[e]?"1":"2")+d;AA[a]=AA[a]||[];AA[a].push(f);Ap(a)}}function CA(a){var b=a.eventId,c=a.Vc,d=[],e=zA[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=AA[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete zA[b],delete AA[b]);return d};function DA(a,b,c){c=c===void 0?!1:c;EA().addRestriction(0,a,b,c)}function FA(a,b,c){c=c===void 0?!1:c;EA().addRestriction(1,a,b,c)}function GA(){var a=Ij();return EA().getRestrictions(1,a)}var HA=function(){this.container={};this.C={}},IA=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
HA.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=IA(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
HA.prototype.getRestrictions=function(a,b){var c=IA(this,b);if(a===0){var d,e;return[].concat(Ba((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),Ba((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(Ba((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),Ba((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
HA.prototype.getExternalRestrictions=function(a,b){var c=IA(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};HA.prototype.removeExternalRestrictions=function(a){var b=IA(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function EA(){return Xn("r",function(){return new HA})};function JA(a,b,c,d){var e=Nf[a],f=KA(a,b,c,d);if(!f)return null;var g=ag(e[nf.rm],c,[]);if(g&&g.length){var h=g[0];f=JA(h.index,{onSuccess:f,onFailure:h.Qm===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function KA(a,b,c,d){function e(){function x(){qm(3);var L=Fb()-G;Tz(1,a,Nf[a][nf.gh]);BA(c.id,f,"7");Yz(c.Nc,E,"exception",L);F(109)&&Ly(c,f,ey.O.Si);M||(M=!0,h())}if(f[nf.hp])h();else{var y=$f(f,c,[]),z=y[nf.Hn];if(z!=null)for(var D=0;D<z.length;D++)if(!An(z[D])){h();return}var E=Xz(c.Nc,String(f[nf.Qa]),Number(f[nf.qh]),y[nf.METADATA]),M=!1;y.vtp_gtmOnSuccess=function(){if(!M){M=!0;var L=Fb()-G;BA(c.id,Nf[a],"5");Yz(c.Nc,E,"success",L);F(109)&&Ly(c,f,ey.O.Ui);g()}};y.vtp_gtmOnFailure=function(){if(!M){M=
!0;var L=Fb()-G;BA(c.id,Nf[a],"6");Yz(c.Nc,E,"failure",L);F(109)&&Ly(c,f,ey.O.Ti);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);BA(c.id,f,"1");F(109)&&Ky(c,f);var G=Fb();try{bg(y,{event:c,index:a,type:1})}catch(L){x(L)}F(109)&&Ly(c,f,ey.O.Bm)}}var f=Nf[a],g=b.onSuccess,h=b.onFailure,l=b.terminate;if(c.isBlocked(f))return null;var n=ag(f[nf.Cm],c,[]);if(n&&n.length){var p=n[0],q=JA(p.index,{onSuccess:g,onFailure:h,terminate:l},c,d);if(!q)return null;
g=q;h=p.Qm===2?l:q}if(f[nf.hm]||f[nf.kp]){var r=f[nf.hm]?Of:c.xr,u=g,t=h;if(!r[a]){var v=LA(a,r,Hb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](u,t)}}return e}function LA(a,b,c){var d=[],e=[];b[a]=MA(d,e,c);return{onSuccess:function(){b[a]=NA;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=OA;for(var f=0;f<e.length;f++)e[f]()}}}function MA(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function NA(a){a()}function OA(a,b){b()};var RA=function(a,b){for(var c=[],d=0;d<Nf.length;d++)if(a[d]){var e=Nf[d];var f=$z(b.Nc);try{var g=JA(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[nf.Qa];if(!h)throw Error("Error: No function name given for function call.");var l=Pf[h];c.push({vn:d,priorityOverride:(l?l.priorityOverride||0:0)||Uz(e[nf.Qa],1)||0,execute:g})}else PA(d,b),f()}catch(p){f()}}c.sort(QA);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function SA(a,b){if(!Rz)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=Sz(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=$z(b);try{d[e](a,f)}catch(g){f()}}return!0}function QA(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.vn,h=b.vn;f=g>h?1:g<h?-1:0}return f}
function PA(a,b){if(lk){var c=function(d){var e=b.isBlocked(Nf[d])?"3":"4",f=ag(Nf[d][nf.rm],b,[]);f&&f.length&&c(f[0].index);BA(b.id,Nf[d],e);var g=ag(Nf[d][nf.Cm],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var TA=!1,Rz;function UA(){Rz||(Rz=new Qz);return Rz}
function VA(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(F(109)){}if(d==="gtm.js"){if(TA)return!1;TA=!0}var e=!1,f=GA(),g=td(a,null);if(!f.every(function(u){return u({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}qA(b,d);var h=a.eventCallback,l=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:WA(g,e),xr:[],logMacroError:function(u,t,v){P(6);qm(0);Tz(2,t,v)},cachedModelValues:XA(),Nc:new Wz(function(){if(F(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,
0))},l),originalEventData:g};F(120)&&lk&&(n.reportMacroDiscrepancy=vA);F(109)&&Hy(n.id);var p=gg(n);F(109)&&Iy(n.id);e&&(p=YA(p));F(109)&&Gy(b);var q=RA(p,n),r=SA(a,n.Nc);aA(n.Nc);d!=="gtm.js"&&d!=="gtm.sync"||gA();return ZA(p,q)||r}function XA(){var a={};a.event=mp("event",1);a.ecommerce=mp("ecommerce",1);a.gtm=mp("gtm");a.eventModel=mp("eventModel");return a}
function WA(a,b){var c=Jz();return function(d){if(c(d))return!0;var e=d&&d[nf.Qa];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Ij();f=EA().getRestrictions(0,g);var h=a;b&&(h=td(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var l=cj[e]||[],n=m(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:l,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function YA(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Nf[c][nf.Qa]);if(Si[d]||Nf[c][nf.lp]!==void 0||Uz(d,2))b[c]=!0}return b}function ZA(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Nf[c]&&!Ti[String(Nf[c][nf.Qa])])return!0;return!1};function $A(){UA().addListener("gtm.init",function(a,b){Fg.ja=!0;cm();b()})};var aB=!1,bB=0,cB=[];function dB(a){if(!aB){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){aB=!0;for(var e=0;e<cB.length;e++)Sc(cB[e])}cB.push=function(){for(var f=Fa.apply(0,arguments),g=0;g<f.length;g++)Sc(f[g]);return 0}}}function eB(){if(!aB&&bB<140){bB++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");dB()}catch(c){w.setTimeout(eB,50)}}}
function fB(){var a=w;aB=!1;bB=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")dB();else{Qc(A,"DOMContentLoaded",dB);Qc(A,"readystatechange",dB);if(A.createEventObject&&A.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&eB()}Qc(a,"load",dB)}}function gB(a){aB?a():cB.push(a)};function hB(a,b){return arguments.length===1?iB("set",a):iB("set",a,b)}function jB(a,b){return arguments.length===1?iB("config",a):iB("config",a,b)}function kB(a,b,c){c=c||{};c[N.m.rd]=a;return iB("event",b,c)}function iB(){return arguments};var lB=function(){this.messages=[];this.C=[]};lB.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=oa(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};lB.prototype.listen=function(a){this.C.push(a)};
lB.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};lB.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function mB(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[R.A.Za]=Dg(6);nB().enqueue(a,b,c)}function oB(){var a=pB;nB().listen(a)}
function nB(){return Xn("mb",function(){return new lB})};var qB={},rB={};function sB(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Fj:void 0,mj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Fj=so(g,b),e.Fj){var h=Gj();ub(h,function(r){return function(u){return r.Fj.destinationId===u}}(e))?c.push(g):d.push(g)}}else{var l=qB[g]||[];e.mj={};l.forEach(function(r){return function(u){r.mj[u]=!0}}(e));for(var n=Jj(),p=0;p<n.length;p++)if(e.mj[n[p]]){c=c.concat(Gj());break}var q=rB[g]||[];q.length&&(c=c.concat(q))}}return{yj:c,Lq:d}}
function tB(a){yb(qB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function uB(a){yb(rB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var vB=!1,wB=!1;function xB(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=td(b,null),b[N.m.hf]&&(d.eventCallback=b[N.m.hf]),b[N.m.Qg]&&(d.eventTimeout=b[N.m.Qg]));return d}function yB(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:bo()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function zB(a,b){var c=a&&a[N.m.rd];c===void 0&&(c=hp(N.m.rd,2),c===void 0&&(c="default"));if(rb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?rb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=sB(d,b.isGtmEvent),f=e.yj,g=e.Lq;if(g.length)for(var h=AB(a),l=0;l<g.length;l++){var n=so(g[l],b.isGtmEvent);if(n){var p=n.destinationId,q=void 0;((q=Aj(n.destinationId))==null?void 0:q.state)===0||Pz(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var r=f.concat(g);return{yj:to(f,b.isGtmEvent),
Fp:to(r,b.isGtmEvent)}}}var BB=void 0,CB=void 0;function DB(a,b,c){var d=td(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&P(136);var e=td(b,null);td(c,e);mB(jB(Jj()[0],e),a.eventId,d)}function AB(a){for(var b=m([N.m.sd,N.m.rc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Ip.C[d];if(e)return e}}
var EB={config:function(a,b){var c=yB(a,b);if(!(a.length<2)&&rb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!sd(a[2])||a.length>3)return;d=a[2]}var e=so(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Cg(7)){var l=Lj(Mj());if(Wj(l)){var n=l.parent,p=n.isDestination;h={Pq:Lj(n),Jq:p};break a}}h=void 0}var q=h;q&&(f=q.Pq,g=q.Jq);qA(c.eventId,"gtag.config");var r=e.destinationId,u=e.id!==r;if(u?Gj().indexOf(r)===-1:Jj().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[N.m.Gc]){var t=AB(d);if(u)Pz(r,t,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;BB?DB(b,v,BB):CB||(CB=td(v,null))}else Mz(r,t,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(P(128),g&&P(130),b.inheritParentConfig)){var x;var y=d;CB?(DB(b,CB,y),x=!1):(!y[N.m.ud]&&Cg(11)&&BB||(BB=td(y,null)),x=!0);x&&f.containers&&f.containers.join(",");return}nk&&(eo===1&&(Vl.mcc=!1),eo=2);if(Cg(11)&&!u&&!d[N.m.ud]){var z=wB;wB=!0;if(z)return}vB||P(43);if(!b.noTargetGroup)if(u){uB(e.id);
var D=e.id,E=d[N.m.Tg]||"default";E=String(E).split(",");for(var M=0;M<E.length;M++){var G=rB[E[M]]||[];rB[E[M]]=G;G.indexOf(D)<0&&G.push(D)}}else{tB(e.id);var L=e.id,U=d[N.m.Tg]||"default";U=U.toString().split(",");for(var ia=0;ia<U.length;ia++){var S=qB[U[ia]]||[];qB[U[ia]]=S;S.indexOf(L)<0&&S.push(L)}}delete d[N.m.Tg];var ca=b.eventMetadata||{};ca.hasOwnProperty(R.A.yd)||(ca[R.A.yd]=!b.fromContainerExecution);b.eventMetadata=ca;delete d[N.m.hf];for(var ta=u?[e.id]:Gj(),ka=0;ka<ta.length;ka++){var ea=
d,X=ta[ka],la=td(b,null),ya=so(X,la.isGtmEvent);ya&&Ip.push("config",[ea],ya,la)}}}}},consent:function(a,b){if(a.length===3){P(39);var c=yB(a,b),d=a[1],e={},f=Rm(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===N.m.yg?Array.isArray(h)?NaN:Number(h):g===N.m.hc?(Array.isArray(h)?h:[h]).map(Sm):Tm(h)}b.fromContainerExecution||(e[N.m.W]&&P(139),e[N.m.Ka]&&P(140));d==="default"?wn(e):d==="update"?yn(e,c):d==="declare"&&b.fromContainerExecution&&vn(e)}},container_config:function(a,b){if(F(240)&&
b.isGtmEvent&&a.length===3&&rb(a[1])&&sd(a[2])){var c=a[2],d=so(a[1],!0);if(d){var e=d.destinationId,f=td(b,null),g=so(e,f.isGtmEvent);g&&Ip.push("container_config",[c],g,f)}}},destination_config:function(a,b){if(F(240)&&b.isGtmEvent&&a.length===3&&rb(a[1])&&sd(a[2])){var c=a[2],d=so(a[1],!0);if(d){var e=d.destinationId,f=td(b,null),g=so(e,f.isGtmEvent);g&&Ip.push("destination_config",[c],g,f)}}},event:function(a,b){var c=a[1];if(!(a.length<2)&&rb(c)){var d=void 0;if(a.length>2){if(!sd(a[2])&&a[2]!==
void 0||a.length>3)return;d=a[2]}var e=xB(c,d),f=yB(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var l=zB(d,b);if(l){for(var n=l.yj,p=l.Fp,q=p.map(function(L){return L.id}),r=p.map(function(L){return L.destinationId}),u=n.map(function(L){return L.id}),t=m(Gj()),v=t.next();!v.done;v=t.next()){var x=v.value;r.indexOf(x)<0&&u.push(x)}qA(g,c);for(var y=m(u),z=y.next();!z.done;z=y.next()){var D=z.value,
E=td(b,null),M=td(d,null);delete M[N.m.hf];var G=E.eventMetadata||{};G.hasOwnProperty(R.A.yd)||(G[R.A.yd]=!E.fromContainerExecution);G[R.A.Ni]=q.slice();G[R.A.Rf]=r.slice();E.eventMetadata=G;Jp(c,M,D,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[N.m.rd]=q.join(","):delete e.eventModel[N.m.rd];vB||P(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[R.A.zm]&&(b.noGtmEvent=!0);e.eventModel[N.m.Fc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){P(53);if(a.length===
4&&rb(a[1])&&rb(a[2])&&qb(a[3])){var c=so(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){vB||P(43);var f=AB();if(ub(Gj(),function(h){return c.destinationId===h})){yB(a,b);var g={};td((g[N.m.lf]=d,g[N.m.kf]=e,g),null);Kp(d,function(h){Sc(function(){e(h)})},c.id,b)}else Pz(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){vB=!0;var c=yB(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),
f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&rb(a[1])&&qb(a[2])){if(mg(a[1],a[2]),P(74),a[1]==="all"){P(75);var b=!1;try{b=a[2](Dg(5),"unknown",{})}catch(c){}b||P(76)}}else P(73)},set:function(a,b){var c=void 0;a.length===2&&sd(a[1])?c=td(a[1],null):a.length===3&&rb(a[1])&&(c={},sd(a[2])||Array.isArray(a[2])?c[a[1]]=td(a[2],null):c[a[1]]=a[2]);if(c){var d=yB(a,b),e=d.eventId,f=d.priorityId;td(c,null);Dg(5);var g=td(c,null);Ip.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=
e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},FB={policy:!0};var HB=function(a){if(GB(a))return a;this.value=a};HB.prototype.getUntrustedMessageValue=function(){return this.value};var GB=function(a){return!a||qd(a)!=="object"||sd(a)?!1:"getUntrustedMessageValue"in a};HB.prototype.getUntrustedMessageValue=HB.prototype.getUntrustedMessageValue;var IB=!1,JB=[];function KB(){if(!IB){IB=!0;for(var a=0;a<JB.length;a++)Sc(JB[a])}}function LB(a){IB?Sc(a):JB.push(a)};var MB=0,NB={},OB=[],PB=[],QB=!1,RB=!1;function SB(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function TB(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return UB(a)}function VB(a,b){if(!sb(b)||b<0)b=0;var c=ao(),d=0,e=!1,f=void 0;f=w.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(w.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function WB(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(zb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function XB(){var a;if(PB.length)a=PB.shift();else if(OB.length)a=OB.shift();else return;var b;var c=a;if(QB||!WB(c.message))b=c;else{QB=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=bo(),f=bo(),c.message["gtm.uniqueEventId"]=bo());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},l={},n={message:(l.event="gtm.init",l["gtm.uniqueEventId"]=f,l),messageContext:{eventId:f}};OB.unshift(n,c);b=h}return b}
function YB(){for(var a=!1,b;!RB&&(b=XB());){RB=!0;delete ep.eventModel;gp();var c=b,d=c.message,e=c.messageContext;if(d==null)RB=!1;else{e.fromContainerExecution&&lp();try{if(qb(d))try{d.call(ip)}catch(M){}else if(Array.isArray(d)){if(rb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),l=hp(f.join("."),2);if(l!=null)try{l[g].apply(l,h)}catch(M){}}}else{var n=void 0;if(zb(d))a:{if(d.length&&rb(d[0])){var p=EB[d[0]];if(p&&(!e.fromContainerExecution||!FB[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,u=r._clear||e.overwriteModelFields,t=m(Object.keys(r)),v=t.next();!v.done;v=t.next()){var x=v.value;x!=="_clear"&&(u&&kp(x),kp(x,r[x]))}$i||($i=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=bo(),r["gtm.uniqueEventId"]=y,kp("gtm.uniqueEventId",y)),q=VA(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&gp(!0);var z=d["gtm.uniqueEventId"];if(typeof z==="number"){for(var D=NB[String(z)]||[],E=0;E<D.length;E++)PB.push(ZB(D[E]));D.length&&PB.sort(SB);
delete NB[String(z)];z>MB&&(MB=z)}RB=!1}}}return!a}
function $B(){if(F(109)){var a=!Fg.R;}var c=YB();if(F(109)){}try{var e=w[Dg(19)],f=Dg(5),g=e.hide;if(g&&g[f]!==void 0&&
g.end){g[f]=!1;var h=!0,l;for(l in g)if(g.hasOwnProperty(l)&&g[l]===!0){h=!1;break}h&&(g.end(),g.end=null)}}catch(n){Dg(5)}return c}function pB(a){if(MB<a.notBeforeEventId){var b=String(a.notBeforeEventId);NB[b]=NB[b]||[];NB[b].push(a)}else PB.push(ZB(a)),PB.sort(SB),Sc(function(){RB||YB()})}function ZB(a){return{message:a.message,messageContext:a.messageContext}}
function aC(){function a(f){var g={};if(GB(f)){var h=f;f=GB(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=Dc(Dg(19),[]),c=$n();c.pruned===!0&&P(83);NB=nB().get();oB();gB(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});LB(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Wn.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new HB(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});OB.push.apply(OB,h);var l=d.apply(b,f),n=Math.max(100,Jg(1,300));if(this.length>n)for(P(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof l!=="boolean"||l;return YB()&&p};var e=b.slice(0).map(function(f){return a(f)});OB.push.apply(OB,e);if(!Fg.R){if(F(109)){}Sc($B)}}
var UB=function(a){return w[Dg(19)].push(a)};function bC(a){UB(a)};function cC(){var a,b=sj(w.location.href);(a=b.hostname+b.pathname)&&Yl("dl",encodeURIComponent(a));var c;var d=Dg(5);if(d){var e=Cg(7)?1:0,f,g=Mj(),h=Lj(g),l=(f=h&&h.context)&&f.fromContainerExecution?1:0,n=f&&f.source||0,p=Dg(6);c=d+";"+p+";"+l+";"+n+";"+e}else c=void 0;var q=c;q&&Yl("tdp",q);var r=aq(!0);r!==void 0&&Yl("frm",String(r))};var dC={},eC=void 0;
function fC(){if(dn()||nk)Yl("csp",function(){return Object.keys(dC).map(function(a){return[a==="undefined"?"":a].concat(Ba(dC[a])).join(";")}).join("~")||void 0},!1),w.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){P(179);var b=al(a.effectiveDirective);if(b){var c;var d=Zk(b,a.blockedURI);c=d?Xk[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=
void 0}var h=e;if(h){for(var l=m(c),n=l.next();!n.done;n=l.next()){var p=n.value;if(!p.on){p.on=!0;if(F(59)){var q={eventId:p.eventId,priorityId:p.priorityId};if(dn()){var r=q,u={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(dn()){var t=kn("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});t.tagDiagnostics=u;cn(t)}}}gC(p.destinationId,p.endpoint)}}$k(b,a.blockedURI)}}}}})}
function gC(a,b){var c=String(a);if(dC.hasOwnProperty(c)){var d=dC[c],e=d.findIndex(function(f){return f>=b});if(e>=0&&d[e]===b)return;e<0&&(e=d.length);d.splice(e,0,b)}else dC[c]=[b];Zl("csp",!0);eC===void 0&&F(171)&&(eC=w.setTimeout(function(){var f;if(f=F(171))a:{try{if(Cc){f=new URL(Cc);break a}}catch(l){}f=void 0}if(f){var g=Vl.csp;Vl.csp=!0;Vl.seq=!1;var h=$l(!1);Vl.csp=g;Vl.seq=!0;Lc(""+Cc+(Cc.indexOf("?")>=0?"&":"?")+"is_td=1"+h)}eC=void 0},500))};var hC=void 0;function iC(){F(236)&&w.addEventListener("pageshow",function(a){a&&(Yl("bfc",function(){return hC?"1":"0"}),a.persisted?(hC=!0,Zl("bfc",!0),cm()):hC=!1)})};function jC(){var a;var b=Kj();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Yl("pcid",e)};var kC=/^(https?:)?\/\//;
function lC(){var a=Nj();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=hd())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=m(e),l=h.next();!l.done;l=h.next()){var n=l.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(kC,"")===d.replace(kC,""))){b=g;break a}}P(146)}else P(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Yl("rtg",String(a.canonicalContainerId)),Yl("slo",String(p)),Yl("hlo",a.htmlLoadOrder||"-1"),
Yl("lst",String(a.loadScriptType||"0")))}else P(144)};
function GC(){};var HC=function(){};HC.prototype.toString=function(){return"undefined"};var IC=new HC;function PC(){F(212)&&Xi&&(mg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),DA(Ij(),function(a){var b,c;b=a.entityId;c=a.securityGroups;var d="__"+b;return Uz(d,5)||!(!Pf[d]||!Pf[d][5])||c.includes("cmpPartners")}))};function QC(a,b){function c(g){var h=sj(g),l=mj(h,"protocol"),n=mj(h,"host",!0),p=mj(h,"port"),q=mj(h,"path").toLowerCase().replace(/\/$/,"");if(l===void 0||l==="http"&&p==="80"||l==="https"&&p==="443")l="web",p="default";return[l,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function RC(a){return SC(a)?1:0}
function SC(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=td(a,{});td({arg1:c[d],any_of:void 0},e);if(RC(e))return!0}return!1}switch(a["function"]){case "_cn":return fh(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<ah.length;g++){var h=ah[g];if(b[h]!=null){f=b[h](c);break a}}}catch(l){}f=!1}return f;case "_ew":return bh(b,c);case "_eq":return gh(b,c);case "_ge":return hh(b,c);case "_gt":return jh(b,c);case "_lc":return ch(b,c);case "_le":return ih(b,
c);case "_lt":return kh(b,c);case "_re":return eh(b,c,a.ignore_case);case "_sw":return lh(b,c);case "_um":return QC(b,c)}return!1};var TC=function(){this.C=this.gppString=void 0};TC.prototype.reset=function(){this.C=this.gppString=void 0};var UC=new TC;[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var VC=function(a,b,c,d){hq.call(this);this.nh=b;this.Of=c;this.Lc=d;this.wb=new Map;this.oh=0;this.wa=new Map;this.Ya=new Map;this.U=void 0;this.H=a};za(VC,hq);VC.prototype.M=function(){delete this.C;this.wb.clear();this.wa.clear();this.Ya.clear();this.U&&(dq(this.H,"message",this.U),delete this.U);delete this.H;delete this.Lc;hq.prototype.M.call(this)};
var WC=function(a){if(a.C)return a.C;a.Of&&a.Of(a.H)?a.C=a.H:a.C=$p(a.H,a.nh);var b;return(b=a.C)!=null?b:null},YC=function(a,b,c){if(WC(a))if(a.C===a.H){var d=a.wb.get(b);d&&d(a.C,c)}else{var e=a.wa.get(b);if(e&&e.xj){XC(a);var f=++a.oh;a.Ya.set(f,{Gh:e.Gh,Wp:e.Xm(c),persistent:b==="addEventListener"});a.C.postMessage(e.xj(c,f),"*")}}},XC=function(a){a.U||(a.U=function(b){try{var c;c=a.Lc?a.Lc(b):void 0;if(c){var d=c.Sq,e=a.Ya.get(d);if(e){e.persistent||a.Ya.delete(d);var f;(f=e.Gh)==null||f.call(e,
e.Wp,c.payload)}}}catch(g){}},cq(a.H,"message",a.U))};var ZC=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},$C=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},aD={Xm:function(a){return a.listener},xj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Gh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},bD={Xm:function(a){return a.listener},xj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Gh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function cD(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Sq:b.__gppReturn.callId}}
var dD=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;hq.call(this);this.caller=new VC(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},cD);this.caller.wb.set("addEventListener",ZC);this.caller.wa.set("addEventListener",aD);this.caller.wb.set("removeEventListener",$C);this.caller.wa.set("removeEventListener",bD);this.timeoutMs=c!=null?c:500};za(dD,hq);dD.prototype.M=function(){this.caller.dispose();hq.prototype.M.call(this)};
dD.prototype.addEventListener=function(a){var b=this,c=Yp(function(){a(eD,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);YC(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(l){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(fD,!0);return}a(gD,!0)}}})};
dD.prototype.removeEventListener=function(a){YC(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var gD={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},eD={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},fD={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function hD(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){UC.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");UC.C=d}}function iD(){try{var a=new dD(w,{timeoutMs:-1});WC(a.caller)&&a.addEventListener(hD)}catch(b){}};function jD(){var a=[["cv",Dg(1)],["rv",Dg(14)],["tc",Nf.filter(function(c){return c}).length]],b=Gg(15);b&&a.push(["x",b]);Yj()&&a.push(["tag_exp",Yj()]);return a};function kD(a){a.Vc&&(vr=ur=0);return[["cd",""+ur],["cd",""+vr]]};var lD={};function Lg(a){lD[a]=(lD[a]||0)+1}function mD(){for(var a=lD,b=[],c=m(Object.keys(a)),d=c.next();!d.done;d=c.next()){var e=d.value;b.push(e+"."+a[e])}return b.length===0?[]:[["bdm",b.join("~")]]};var nD={},oD={};function pD(a){var b=a.eventId,c=a.Vc,d=[],e=nD[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=oD[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete nD[b],delete oD[b]);return d};function qD(){return!1}function rD(){var a={};return function(b,c,d){}};function sD(){var a=tD;return function(b,c,d){var e=d&&d.event;uD(c);var f=Qh(b)?void 0:1,g=new cb;yb(c,function(r,u){var t=Id(u,void 0,f);t===void 0&&u!==void 0&&P(44);g.set(r,t)});a.Mb(eg());var h={Km:sg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Uf:e!==void 0?function(r){e.Nc.Uf(r)}:void 0,Jb:function(){return b},log:function(){},bq:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},ar:!!Uz(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(qD()){var l=rD(),n,p;h.nb={Mj:[],Vf:{},ac:function(r,u,t){u===1&&(n=r);u===7&&(p=t);l(r,u,t)},Fh:ki()};h.log=function(r){var u=Fa.apply(1,arguments);n&&l(n,4,{level:r,source:p,message:u})}}var q=df(a,h,[b,g]);a.Mb();q instanceof Ia&&(q.type==="return"?q=q.data:q=void 0);return B(q,void 0,f)}}function uD(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;qb(b)&&(a.gtmOnSuccess=function(){Sc(b)});qb(c)&&(a.gtmOnFailure=function(){Sc(c)})};function vD(a){}vD.K="internal.addAdsClickIds";function wD(a,b){var c=this;}wD.publicName="addConsentListener";var xD=!1;function yD(a){for(var b=0;b<a.length;++b)if(xD)try{a[b]()}catch(c){P(77)}else a[b]()}function zD(a,b,c){var d=this,e;return e}zD.K="internal.addDataLayerEventListener";function AD(a,b,c){}AD.publicName="addDocumentEventListener";function BD(a,b,c,d){}BD.publicName="addElementEventListener";function CD(a){return a.J.lb()};function DD(a){}DD.publicName="addEventCallback";
function SD(a){}SD.K="internal.addFormAbandonmentListener";function TD(a,b,c,d){}
TD.K="internal.addFormData";var UD={},VD=[],WD={},XD=0,YD=0;
function eE(a,b){}eE.K="internal.addFormInteractionListener";
function lE(a,b){}lE.K="internal.addFormSubmitListener";
function qE(a){}qE.K="internal.addGaSendListener";function rE(a){if(!a)return{};var b=a.bq;return Tz(b.type,b.index,b.name)}function sE(a){return a?{originatingEntity:rE(a)}:{}};
var uE=function(a,b,c){tE().updateZone(a,b,c)},wE=function(a,b,c,d,e,f){var g=tE();c=c&&Jb(c,vE);for(var h=g.createZone(a,c),l=0;l<b.length;l++){var n=String(b[l]);if(g.registerChild(n,Dg(5),h)){var p=n,q=a,r=d,u=e,t=f;if(Kb(p,"GTM-"))Mz(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=iB("js",Eb());Mz(p,void 0,!0,{source:1,fromContainerExecution:!0});var x={originatingEntity:u,inheritParentConfig:t};mB(v,q,x);mB(jB(p,r),q,x)}}}return h},tE=function(){return Xn("zones",function(){return new xE})},
yE={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},vE={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},xE=function(){this.C={};this.H={};this.M=0};k=xE.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.C[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.Ej],b))return!1;for(var e=0;e<c.vg.length;e++)if(this.H[c.vg[e]].Ee(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.C[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.vg.length;f++){var g=this.H[c.vg[f]];g.Ee(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.Ej],b);return function(l,n){n=n||[];if(!h(l,n))return!1;for(var p=0;p<e.length;++p)if(e[p].M(l,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.C[a[b]]};k.createZone=function(a,b){var c=String(++this.M);this.H[c]=new zE(a,b);return c};k.updateZone=function(a,
b,c){var d=this.H[a];d&&d.R(b,c)};k.registerChild=function(a,b,c){var d=this.C[a];if(!d&&Wn[a]||!d&&Rj(a)||d&&d.Ej!==b)return!1;if(d)return d.vg.push(c),!1;this.C[a]={Ej:b,vg:[c]};return!0};var zE=function(a,b){this.H=null;this.C=[{eventId:a,Ee:!0}];if(b){this.H={};for(var c=0;c<b.length;c++)this.H[b[c]]=!0}};zE.prototype.R=function(a,b){var c=this.C[this.C.length-1];a<=c.eventId||c.Ee!==b&&this.C.push({eventId:a,Ee:b})};zE.prototype.Ee=function(a){for(var b=this.C.length-1;b>=0;b--)if(this.C[b].eventId<=
a)return this.C[b].Ee;return!1};zE.prototype.M=function(a,b){b=b||[];if(!this.H||yE[a]||this.H[a])return!0;for(var c=0;c<b.length;++c)if(this.H[b[c]])return!0;return!1};function AE(a){var b=Wn.zones;return b?b.getIsAllowedFn(Jj(),a):function(){return!0}}function BE(){var a=Wn.zones;a&&a.unregisterChild(Jj())}
function CE(){FA(Ij(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Wn.zones;return c?c.isActive(Jj(),b):!0});DA(Ij(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return AE(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var DE=function(a,b){this.tagId=a;this.canonicalId=b};
function EE(a,b){var c=this;return a}EE.K="internal.loadGoogleTag";function FE(a){return new Ad("",function(b){var c=this.evaluate(b);if(c instanceof Ad)return new Ad("",function(){var d=Fa.apply(0,arguments),e=this,f=td(CD(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(l){return e.evaluate(l)}),h=this.J.jb();h.Od(f);return c.Kb.apply(c,[h].concat(Ba(g)))})})};function GE(a,b,c){var d=this;}GE.K="internal.addGoogleTagRestriction";var HE={},IE=[];
function PE(a,b){}
PE.K="internal.addHistoryChangeListener";function QE(a,b,c){}QE.publicName="addWindowEventListener";function RE(a,b){return!0}RE.publicName="aliasInWindow";function SE(a,b,c){}SE.K="internal.appendRemoteConfigParameter";function TE(a){var b;return b}
TE.publicName="callInWindow";function UE(a){}UE.publicName="callLater";function VE(a){}VE.K="callOnDomReady";function WE(a){}WE.K="callOnWindowLoad";function XE(a,b){var c;return c}XE.K="internal.computeGtmParameter";function YE(a,b){var c=this;}YE.K="internal.consentScheduleFirstTry";function ZE(a,b){var c=this;}ZE.K="internal.consentScheduleRetry";function $E(a){var b;return b}$E.K="internal.copyFromCrossContainerData";function aF(a,b){var c;var d=Id(c,this.J,Qh(CD(this).Jb())?2:1);d===void 0&&c!==void 0&&P(45);return d}aF.publicName="copyFromDataLayer";
function bF(a){var b=void 0;return b}bF.K="internal.copyFromDataLayerCache";function cF(a){var b;return b}cF.publicName="copyFromWindow";function dF(a){var b=void 0;return Id(b,this.J,1)}dF.K="internal.copyKeyFromWindow";var eF=function(a){return a===jl.Z.Ga&&Bl[a]===il.Ia.ue&&!An(N.m.aa)};var fF=function(){return"0"},gF=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];F(102)&&b.push("gbraid");return tj(a,b,"0")};var hF={},iF={},jF={},kF={},lF={},mF={},nF={},oF={},pF={},qF={},rF={},sF={},tF={},uF={},vF={},wF={},xF={},yF={},zF={},AF={},BF={},CF={},DF={},EF={},FF={},GF={},HF=(GF[N.m.La]=(hF[2]=[eF],hF),GF[N.m.xf]=(iF[2]=[eF],iF),GF[N.m.jf]=(jF[2]=[eF],jF),GF[N.m.nl]=(kF[2]=[eF],kF),GF[N.m.ol]=(lF[2]=[eF],lF),GF[N.m.pl]=(mF[2]=[eF],mF),GF[N.m.ql]=(nF[2]=[eF],nF),GF[N.m.rl]=(oF[2]=[eF],oF),GF[N.m.Eb]=(pF[2]=[eF],pF),GF[N.m.yf]=(qF[2]=[eF],qF),GF[N.m.zf]=(rF[2]=[eF],rF),GF[N.m.Af]=(sF[2]=[eF],sF),GF[N.m.Bf]=(tF[2]=
[eF],tF),GF[N.m.Cf]=(uF[2]=[eF],uF),GF[N.m.Df]=(vF[2]=[eF],vF),GF[N.m.Ef]=(wF[2]=[eF],wF),GF[N.m.Ff]=(xF[2]=[eF],xF),GF[N.m.ob]=(yF[1]=[eF],yF),GF[N.m.bd]=(zF[1]=[eF],zF),GF[N.m.hd]=(AF[1]=[eF],AF),GF[N.m.ce]=(BF[1]=[eF],BF),GF[N.m.Te]=(CF[1]=[function(a){return F(102)&&eF(a)}],CF),GF[N.m.jd]=(DF[1]=[eF],DF),GF[N.m.ya]=(EF[1]=[eF],EF),GF[N.m.Xa]=(FF[1]=[eF],FF),GF),IF={},JF=(IF[N.m.ob]=fF,IF[N.m.bd]=fF,IF[N.m.hd]=fF,IF[N.m.ce]=fF,IF[N.m.Te]=fF,IF[N.m.jd]=function(a){if(!sd(a))return{};var b=td(a,
null);delete b.match_id;return b},IF[N.m.ya]=gF,IF[N.m.Xa]=gF,IF),KF={},LF={},MF=(LF[R.A.Ra]=(KF[2]=[eF],KF),LF),NF={};var OF=function(a,b,c,d){this.C=a;this.M=b;this.R=c;this.U=d};OF.prototype.getValue=function(a){a=a===void 0?jl.Z.Gb:a;if(!this.M.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.U(this.C):this.C};OF.prototype.H=function(){return qd(this.C)==="array"||sd(this.C)?td(this.C,null):this.C};
var PF=function(){},QF=function(a,b){this.conditions=a;this.C=b},RF=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new OF(c,e,g,a.C[b]||PF)},SF,TF;var UF,VF=!1;function WF(){VF=!0;var a=!1;if(F(218)&&Bg(52,a,!1))UF=productSettings,productSettings=void 0;else{}UF=UF||{}}function XF(a){VF||WF();return UF[a]};var YF=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=m(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;V(this,g,d[g])}},Hu=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,T(a,R.A.Sf))},W=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(SF!=null||(SF=new QF(HF,JF)),e=RF(SF,b,c));d[b]=e};
YF.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return W(this,a,b),!0;if(!sd(c))return!1;W(this,a,oa(Object,"assign").call(Object,c,b));return!0};var ZF=function(a,b){b=b===void 0?{}:b;for(var c=m(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
YF.prototype.copyToHitData=function(a,b,c){var d=Q(this.D,a);d===void 0&&(d=b);if(rb(d)&&c!==void 0&&F(92))try{d=c(d)}catch(e){}d!==void 0&&W(this,a,d)};
var T=function(a,b){var c=a.metadata[b];if(b===R.A.Sf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,T(a,R.A.Sf))},V=function(a,b,c){var d=a.metadata,e;c===void 0?e=c:(TF!=null||(TF=new QF(MF,NF)),e=RF(TF,b,c));d[b]=e},$F=function(a,b){b=b===void 0?{}:b;for(var c=m(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},Xu=function(a,b,c){var d=XF(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c},aG=function(a){for(var b=new YF(a.target,a.eventName,a.D),c=ZF(a),d=m(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;W(b,f,c[f])}for(var g=$F(a),h=m(Object.keys(g)),l=h.next();!l.done;l=h.next()){var n=l.value;V(b,n,g[n])}b.isAborted=a.isAborted;return b},bG=function(a){var b=a.D,c=b.eventId,d=b.priorityId;return d?c+"_"+d:String(c)};
YF.prototype.accept=function(){var a=Pl(Kl.X.oi,{}),b=bG(this),c=this.target.destinationId;a[b]||(a[b]={});a[b][c]=Ij();var d=Kl.X.oi;if(Ll(d)){var e;(e=Ml(d))==null||e.notify()}};YF.prototype.hasBeenAccepted=function(a){var b=Ol(Kl.X.oi);if(!b)return!1;var c=b[bG(this)];return c?c[a!=null?a:this.target.destinationId]!==void 0:!1};function cG(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Hu(a,b)},setHitData:function(b,c){W(a,b,c)},setHitDataIfNotDefined:function(b,c){Hu(a,b)===void 0&&W(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return T(a,b)},setMetadata:function(b,c){V(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return Q(a.D,b)},kb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return sd(c)?a.mergeHitDataForKey(b,c):!1},accept:function(){a.accept()},hasBeenAccepted:function(b){return a.hasBeenAccepted(b)}}};function dG(a,b){var c;return c}dG.K="internal.copyPreHit";function eG(a,b){var c=null;return Id(c,this.J,2)}eG.publicName="createArgumentsQueue";function fG(a){return Id(function(c){var d=cA();if(typeof c==="function")d(function(){c(function(f,g,h){var l=
cA(),n=l&&l.getByName&&l.getByName(f);return(new w.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.J,1)}fG.K="internal.createGaCommandQueue";function gG(a){return Id(function(){if(!qb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.J,
Qh(CD(this).Jb())?2:1)}gG.publicName="createQueue";function hG(a,b){var c=null;return c}hG.K="internal.createRegex";function iG(a){}iG.K="internal.declareConsentState";function jG(a){var b="";return b}jG.K="internal.decodeUrlHtmlEntities";function kG(a,b,c){var d;return d}kG.K="internal.decorateUrlWithGaCookies";function lG(){}lG.K="internal.deferCustomEvents";function mG(a){return nG?A.querySelector(a):null}
function oG(a,b){if(!nG)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var pG=!1;
if(A.querySelectorAll)try{var qG=A.querySelectorAll(":root");qG&&qG.length==1&&qG[0]==A.documentElement&&(pG=!0)}catch(a){}var nG=pG;function rG(){var a=w.screen;return{width:a?a.width:0,height:a?a.height:0}}
function sG(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!w.getComputedStyle)return!0;var c=w.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=w.getComputedStyle(d,null))}return!1}
var NG=function(a){a=a||{hg:!0,ig:!0,Jj:void 0};a.Xb=a.Xb||{email:!0,phone:!1,address:!1};var b=BG(a),c=CG[b];if(c&&Fb()-c.timestamp<200)return c.result;var d=DG(),e=d.status,f=[],g,h,l=[];if(!F(33)){if(a.Xb&&a.Xb.email){var n=EG(d.elements);f=FG(n,a&&a.Xf);g=GG(f);n.length>10&&(e="3")}!a.Jj&&g&&(f=[g]);for(var p=0;p<f.length;p++)l.push(HG(f[p],!!a.hg,!!a.ig));l=l.slice(0,10)}else if(a.Xb){}g&&(h=HG(g,!!a.hg,!!a.ig));var M={elements:l,hn:h,status:e};CG[b]={timestamp:Fb(),result:M};
return M},OG=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},QG=function(a){var b=PG(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},PG=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():void 0}},HG=function(a,b,c){var d=a.element,
e={ra:a.ra,type:a.sa,tagName:d.tagName};b&&(e.querySelector=RG(d));c&&(e.isVisible=!sG(d));return e},BG=function(a){var b=!(a==null||!a.hg)+"."+!(a==null||!a.ig);a&&a.Xf&&a.Xf.length&&(b+="."+a.Xf.join("."));a&&a.Xb&&(b+="."+a.Xb.email+"."+a.Xb.phone+"."+a.Xb.address);return b},GG=function(a){if(a.length!==0){var b;b=SG(a,function(c){return!TG.test(c.ra)});b=SG(b,function(c){return c.element.tagName.toUpperCase()==="INPUT"});b=SG(b,function(c){return!sG(c.element)});return b[0]}},FG=function(a,b){b&&
b.length!==0||(b=[]);for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&oG(a[d].element,g)){e=!1;break}}a[d].sa===MG.Nb&&F(227)&&(TG.test(a[d].ra)||a[d].element.tagName.toUpperCase()==="A"&&a[d].element.hasAttribute("href")&&a[d].element.getAttribute("href").indexOf("mailto:")!==-1)&&(e=!1);e&&c.push(a[d])}return c},SG=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},RG=function(a){var b;if(a===A.body)b="body";else{var c;if(a.id)c="#"+
a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===a){e=g+1;break a}e=-1}else e=1}d=RG(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},EG=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(UG);if(f){var g=f[0],h;if(w.location){var l=oj(w.location,"host",!0);h=g.toLowerCase().indexOf(l)>=0}else h=
!1;h||b.push({element:d,ra:g,sa:MG.Nb})}}}return b},DG=function(){var a=[],b=A.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(VG.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(WG.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||F(33)&&XG.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},UG=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,
TG=/support|noreply/i,VG="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),WG=["BR"],YG=Jg(36,2),MG={Nb:"1",Ed:"2",vd:"3",Cd:"4",Ne:"5",Qf:"6",mh:"7",Ri:"8",Lh:"9",Li:"10"},CG={},XG=["INPUT","SELECT"],ZG=PG(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
function TH(a){var b;K(this,"detect_user_provided_data","auto");var c=B(a)||{},d=NG({hg:!!c.includeSelector,ig:!!c.includeVisibility,Xf:c.excludeElementSelectors,Xb:c.fieldFilters,Jj:!!c.selectMultipleElements});b=new cb;var e=new wd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(UH(f[g]));d.hn!==void 0&&b.set("preferredEmailElement",UH(d.hn));b.set("status",d.status);if(F(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(zc&&
zc.userAgent||"")){}return b}
var VH=function(a){switch(a){case MG.Nb:return"email";case MG.Ed:return"phone_number";case MG.vd:return"first_name";case MG.Cd:return"last_name";case MG.Ri:return"street";case MG.Lh:return"city";case MG.Li:return"region";case MG.Qf:return"postal_code";case MG.Ne:return"country"}},UH=function(a){var b=new cb;b.set("userData",a.ra);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(F(33)){}else switch(a.type){case MG.Nb:b.set("type","email")}return b};TH.K="internal.detectUserProvidedData";
function YH(a,b){return f}YH.K="internal.enableAutoEventOnClick";
function fI(a,b){return p}fI.K="internal.enableAutoEventOnElementVisibility";function gI(){}gI.K="internal.enableAutoEventOnError";var hI={},iI=[],jI={},kI=0,lI=0;
function rI(a,b){var c=this;return d}rI.K="internal.enableAutoEventOnFormInteraction";
function wI(a,b){var c=this;return f}wI.K="internal.enableAutoEventOnFormSubmit";
function BI(){var a=this;}BI.K="internal.enableAutoEventOnGaSend";var CI={},DI=[];
function KI(a,b){var c=this;return f}KI.K="internal.enableAutoEventOnHistoryChange";var LI=["http://","https://","javascript:","file://"];
function PI(a,b){var c=this;return h}PI.K="internal.enableAutoEventOnLinkClick";var QI,RI;
function bJ(a,b){var c=this;return d}bJ.K="internal.enableAutoEventOnScroll";function cJ(a){return function(){if(a.limit&&a.Bj>=a.limit)a.Ch&&w.clearInterval(a.Ch);else{a.Bj++;var b=Fb();UB({event:a.eventName,"gtm.timerId":a.Ch,"gtm.timerEventNumber":a.Bj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.un,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.un,"gtm.triggers":a.Cr})}}}
function dJ(a,b){
return f}dJ.K="internal.enableAutoEventOnTimer";var tc=Da(["data-gtm-yt-inspected-"]),fJ=["www.youtube.com","www.youtube-nocookie.com"],gJ,hJ=!1;
function rJ(a,b){var c=this;return e}rJ.K="internal.enableAutoEventOnYouTubeActivity";hJ=!1;function sJ(a,b){if(!J(a)||!wh(b))throw I(this.getName(),["string","Object|undefined"],arguments);var c=b?B(b):{},d=a,e=!1;return e}sJ.K="internal.evaluateBooleanExpression";var tJ;function uJ(a){var b=!1;return b}uJ.K="internal.evaluateMatchingRules";var FJ="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function GJ(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function HJ(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=oa(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function IJ(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function JJ(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function KJ(a){if(!JJ(a))return null;var b=GJ(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(FJ).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};var RJ=function(a){var b=QJ[a.target.destinationId];if(!a.isAborted&&b)for(var c=cG(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},SJ=function(a,b){var c=QJ[a];c||(c=QJ[a]=[]);c.push(b)},QJ={};function UJ(){var a=w.__uspapi;if(qb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function $J(a){var b=ob("GTAG_EVENT_FEATURE_CHANNEL");b&&(W(a,N.m.qf,b),mb())};function JK(){return Bq(7)&&Bq(9)&&Bq(10)};
var NK=function(a,b){if(!b.isGtmEvent){var c=Q(b,N.m.lf),d=Q(b,N.m.kf),e=Q(b,c);if(e===void 0){var f=void 0;KK.hasOwnProperty(c)?f=KK[c]:LK.hasOwnProperty(c)&&(f=LK[c]);f===1&&(f=MK(c));rb(f)?cA()(function(){var g,h,l,n=(l=(g=cA())==null?void 0:(h=g.getByName)==null?void 0:h.call(g,a))==null?void 0:l.get(f);d(n)}):d(void 0)}else d(e)}},OK=function(a,b){var c=a[N.m.ld],d=b+".",e=a[N.m.na]||"",f=c===void 0?!!a.use_anchor:c==="fragment",g=!!a[N.m.Hc];e=String(e).replace(/\s+/g,"").split(",");var h=cA();
h(d+"require","linker");h(d+"linker:autoLink",e,f,g)},SK=function(a,b,c){if(!c.isGtmEvent||!PK[a]){var d=!An(N.m.ka),e=function(f){var g="gtm"+String(bo()),h,l=cA(),n=QK(b,"",c),p,q=n.createOnlyFields._useUp;if(c.isGtmEvent||RK(b,n.createOnlyFields)){c.isGtmEvent&&(h=n.createOnlyFields,n.gtmTrackerName&&(h.name=g));l(function(){var u,t=l==null?void 0:(u=l.getByName)==null?void 0:u.call(l,b);t&&(p=t.get("clientId"));if(!c.isGtmEvent){var v;l==null||(v=l.remove)==null||v.call(l,b)}});l("create",a,c.isGtmEvent?
h:n.createOnlyFields);d&&An(N.m.ka)&&(d=!1,l(function(){var u,t,v=(u=cA())==null?void 0:(t=u.getByName)==null?void 0:t.call(u,c.isGtmEvent?g:b);!v||v.get("clientId")==p&&q||(c.isGtmEvent?(n.fieldsToSet["&gcu"]="1",n.fieldsToSet["&sst.gcut"]=Om[f]):(n.fieldsToSend["&gcu"]="1",n.fieldsToSend["&sst.gcut"]=Om[f]),v.set(n.fieldsToSet),
c.isGtmEvent?v.send("pageview"):v.send("pageview",n.fieldsToSend))}));c.isGtmEvent&&l(function(){var u;l==null||(u=l.remove)==null||u.call(l,g)})}};Cn(function(){return void e(N.m.ka)},N.m.ka);Cn(function(){return void e(N.m.aa)},N.m.aa);Cn(function(){return void e(N.m.W)},N.m.W);c.isGtmEvent&&(PK[a]=!0)}},TK=function(a,b){ek()&&b&&(a[N.m.qc]=b)},bL=function(a,b,c){function d(){var ca=Fa.apply(0,arguments);ca[0]=x?x+"."+ca[0]:""+ca[0];t.apply(window,ca)}function e(ca){function ta(xa,Va){for(var $a=
0;Va&&$a<Va.length;$a++)d(xa,Va[$a])}var ka=c.isGtmEvent,ea=ka?UK(y):VK(b,c);if(ea){var X={};TK(X,ca);d("require","ec","ec.js",X);ka&&ea.dj&&d("set","&cu",ea.dj);var la=ea.action;if(ka||la==="impressions")if(ta("ec:addImpression",ea.Vm),!ka)return;if(la==="promo_click"||la==="promo_view"||ka&&ea.rg){var ya=ea.rg;ta("ec:addPromo",ya);if(ya&&ya.length>0&&la==="promo_click"){ka?d("ec:setAction",la,ea.Wb):d("ec:setAction",la);return}if(!ka)return}la!=="promo_view"&&la!=="impressions"&&(ta("ec:addProduct",
ea.Md),d("ec:setAction",la,ea.Wb))}}function f(ca){if(ca){var ta={};if(sd(ca))for(var ka in WK)WK.hasOwnProperty(ka)&&XK(WK[ka],ka,ca[ka],ta);TK(ta,E);d("require","linkid",ta)}}function g(){var ca=Q(c,N.m.Fo);if(ca){var ta=Dg(19);d("require",ca,{dataLayer:ta});d("require","render")}}function h(){var ca=Q(c,N.m.df);t(function(){if(!c.isGtmEvent&&sd(ca)){var ta=y.fieldsToSend,ka,ea,X=(ka=v())==null?void 0:(ea=ka.getByName)==null?void 0:ea.call(ka,x),la;for(la in ca)if(ca[la]!=null&&/^(dimension|metric)\d+$/.test(la)){var ya=
void 0,xa=(ya=X)==null?void 0:ya.get(MK(ca[la]));YK(ta,la,xa)}}})}function l(ca,ta,ka){ka&&(ta=String(ta));y.fieldsToSend[ca]=ta}function n(){if(y.displayfeatures){var ca="_dc_gtm_"+p.replace(/[^A-Za-z0-9-]/g,"");d("require","displayfeatures",void 0,{cookieName:ca})}}var p=a,q=so(a),r=c.eventMetadata[R.A.Rf];if(!(q&&r&&r.indexOf(q.destinationId)<0)){nk&&(jm=!0,b===N.m.ma?pm(c,a):(c.eventMetadata[R.A.Oe]||(mm[a]=!0),fo(c.eventMetadata[R.A.Za])));var u,t=c.isGtmEvent?fA(Q(c,"gaFunctionName")):fA();
if(qb(t)){var v=cA,x;x=c.isGtmEvent?Q(c,"name")||Q(c,"gtmTrackerName"):"gtag_"+p.split("-").join("_");var y=QK(x,b,c);!c.isGtmEvent&&RK(x,y.createOnlyFields)&&(t(function(){var ca,ta;v()&&((ca=v())==null||(ta=ca.remove)==null||ta.call(ca,x))}),ZK[x]=!1);t("create",p,y.createOnlyFields);var z=c.isGtmEvent&&y.fieldsToSet[N.m.qc];if(!c.isGtmEvent&&y.createOnlyFields[N.m.qc]||z){var D=dk(c.isGtmEvent?y.fieldsToSet[N.m.qc]:y.createOnlyFields[N.m.qc],"/analytics.js");D&&(u=D)}var E=c.isGtmEvent?y.fieldsToSet[N.m.qc]:
y.createOnlyFields[N.m.qc];if(E){var M=c.isGtmEvent?y.fieldsToSet[N.m.Rg]:y.createOnlyFields[N.m.Rg];M&&!ZK[x]&&(ZK[x]=!0,t(hA(x,M)))}c.isGtmEvent?y.enableRecaptcha&&d("require","recaptcha","recaptcha.js"):(h(),f(y.linkAttribution));var G=y[N.m.Wa];G&&G[N.m.na]&&OK(G,x);d("set",y.fieldsToSet);if(c.isGtmEvent){if(y.enableLinkId){var L={};TK(L,E);d("require","linkid","linkid.js",L)}SK(p,x,c)}if(b===N.m.Zc)if(c.isGtmEvent){n();if(y.remarketingLists){var U="_dc_gtm_"+p.replace(/[^A-Za-z0-9-]/g,"");d("require",
"adfeatures",{cookieName:U})}e(E);d("send","pageview");y.createOnlyFields._useUp&&eA(x+".")}else g(),d("send","pageview",y.fieldsToSend);else b===N.m.ma?(g(),Bv(p,c),Q(c,N.m.Db)&&(du(["aw","dc"]),eA(x+".")),fu(["aw","dc"]),y.sendPageView!=0&&d("send","pageview",y.fieldsToSend),SK(p,x,c)):b===N.m.nc?NK(x,c):b==="screen_view"?d("send","screenview",y.fieldsToSend):b==="timing_complete"?(y.fieldsToSend.hitType="timing",l("timingCategory",y.eventCategory,!0),c.isGtmEvent?l("timingVar",y.timingVar,!0):
l("timingVar",y.name,!0),l("timingValue",Ab(y.value)),y.eventLabel!==void 0&&l("timingLabel",y.eventLabel,!0),d("send",y.fieldsToSend)):b==="exception"?d("send","exception",y.fieldsToSend):b===""&&c.isGtmEvent||(b==="track_social"&&c.isGtmEvent?(y.fieldsToSend.hitType="social",l("socialNetwork",y.socialNetwork,!0),l("socialAction",y.socialAction,!0),l("socialTarget",y.socialTarget,!0)):((c.isGtmEvent||$K[b])&&e(E),c.isGtmEvent&&n(),y.fieldsToSend.hitType="event",l("eventCategory",y.eventCategory,
!0),l("eventAction",y.eventAction||b,!0),y.eventLabel!==void 0&&l("eventLabel",y.eventLabel,!0),y.value!==void 0&&l("eventValue",Ab(y.value))),d("send",y.fieldsToSend));var ia=u&&!c.eventMetadata[R.A.ym];if(!aL&&(!c.isGtmEvent||ia)){aL=!0;var S=function(){c.onFailure()};Lc(u||"https://www.google-analytics.com/analytics.js",function(){var ca;((ca=v())==null?0:ca.loaded)||S()},S)}}else Sc(c.onFailure)}},cL=function(a,b,c,d){On(function(){bL(a,b,d)},[N.m.ka,N.m.aa])},RK=function(a,b){var c=dL[a];dL[a]=
td(b,null);if(!c)return!1;for(var d in b)if(b.hasOwnProperty(d)&&b[d]!==c[d])return!0;for(var e in c)if(c.hasOwnProperty(e)&&c[e]!==b[e])return!0;return!1},VK=function(a,b){function c(t){return{id:d(N.m.Pa),affiliation:d(N.m.Hk),revenue:d(N.m.Ma),tax:d(N.m.Jk),shipping:d(N.m.ee),coupon:d(N.m.Ik),list:d(N.m.di)||d(N.m.ef)||t}}for(var d=function(t){return Q(b,t)},e=d(N.m.Ca),f,g=0;e&&g<e.length&&!(f=e[g][N.m.di]||e[g][N.m.ef]);g++);var h=d(N.m.df);if(sd(h))for(var l=0;e&&l<e.length;++l){var n=e[l],
p;for(p in h)h.hasOwnProperty(p)&&/^(dimension|metric)\d+$/.test(p)&&h[p]!=null&&YK(n,p,n[h[p]])}var q=null,r=d(N.m.uo);if(a===N.m.zb||a===N.m.Xd)q={action:a,Wb:c(),Md:eL(e)};else if(a===N.m.Ud)q={action:"add",Wb:c(),Md:eL(e)};else if(a===N.m.Vd)q={action:"remove",Wb:c(),Md:eL(e)};else if(a===N.m.mc)q={action:"detail",Wb:c(f),Md:eL(e)};else if(a===N.m.jc)q={action:"impressions",Vm:eL(e)};else if(a===N.m.kc)q={action:"promo_view",rg:eL(r)||eL(e)};else if(a==="select_content"&&r&&r.length>0||a===N.m.yc)q=
{action:"promo_click",rg:eL(r)||eL(e)};else if(a==="select_content"||a===N.m.Wd)q={action:"click",Wb:{list:d(N.m.di)||d(N.m.ef)||f},Md:eL(e)};else if(a===N.m.Yc||a==="checkout_progress"){var u={step:a===N.m.Yc?1:d(N.m.bi),option:d(N.m.Mg)};q={action:"checkout",Md:eL(e),Wb:td(c(),u)}}else a==="set_checkout_option"&&(q={action:"checkout_option",Wb:{step:d(N.m.bi),option:d(N.m.Mg)}});q&&(q.dj=d(N.m.sb));return q},UK=function(a){var b=a.gtmEcommerceData;if(!b)return null;var c={};b.currencyCode&&(c.dj=
b.currencyCode);if(b.impressions){c.action="impressions";var d=b.impressions;c.Vm=b.translateIfKeyEquals==="impressions"?eL(d):d}if(b.promoView){c.action="promo_view";var e=b.promoView.promotions;c.rg=b.translateIfKeyEquals==="promoView"?eL(e):e}if(b.promoClick){var f=b.promoClick;c.action="promo_click";var g=f.promotions;c.rg=b.translateIfKeyEquals==="promoClick"?eL(g):g;c.Wb=f.actionField;return c}for(var h in b)if(b[h]!==void 0&&h!=="translateIfKeyEquals"&&h!=="impressions"&&h!=="promoView"&&h!==
"promoClick"&&h!=="currencyCode"){c.action=h;var l=b[h].products;c.Md=b.translateIfKeyEquals==="products"?eL(l):l;c.Wb=b[h].actionField;break}return Object.keys(c).length?c:null},eL=function(a){function b(e){function f(h,l){for(var n=0;n<l.length;n++){var p=l[n];if(e[p]){g[h]=e[p];break}}}var g=td(e,null);f("id",["id","item_id","promotion_id"]);f("name",["name","item_name","promotion_name"]);f("brand",["brand","item_brand"]);f("variant",["variant","item_variant"]);f("list",["list_name","item_list_name"]);
f("position",["list_position","creative_slot","index"]);(function(){if(e.category)g.category=e.category;else{for(var h="",l=0;l<fL.length;l++)e[fL[l]]!==void 0&&(h&&(h+="/"),h+=e[fL[l]]);h&&(g.category=h)}})();f("listPosition",["list_position"]);f("creative",["creative_name"]);f("list",["list_name"]);f("position",["list_position","creative_slot"]);return g}for(var c=[],d=0;a&&d<a.length;d++)a[d]&&sd(a[d])&&c.push(b(a[d]));return c.length?c:void 0},QK=function(a,b,c){var d=function(S){return Q(c,S)},
e={},f={},g={},h={},l=gL(d(N.m.Ao));!c.isGtmEvent&&l&&YK(f,"exp",l);g["&gtm"]=Uq({Oa:c.eventMetadata[R.A.Za],sh:!0});c.isGtmEvent||(g._no_slc=!0);vl()&&(h._cs=hL);var n=d(N.m.df);if(!c.isGtmEvent&&sd(n))for(var p in n)if(n.hasOwnProperty(p)&&/^(dimension|metric)\d+$/.test(p)&&n[p]!=null){var q=d(String(n[p]));q!==void 0&&YK(f,p,q)}for(var r=!c.isGtmEvent,u=Qo(c),t=0;t<u.length;++t){var v=u[t];if(c.isGtmEvent){var x=d(v);iL.hasOwnProperty(v)?e[v]=x:jL.hasOwnProperty(v)?h[v]=x:g[v]=x}else{var y=void 0;
v!==N.m.Ea?y=d(v):y=c.getMergedValues(v);if(kL.hasOwnProperty(v))XK(kL[v],v,y,e);else if(lL.hasOwnProperty(v))XK(lL[v],v,y,g);else if(LK.hasOwnProperty(v))XK(LK[v],v,y,f);else if(KK.hasOwnProperty(v))XK(KK[v],v,y,h);else if(/^(dimension|metric|content_group)\d+$/.test(v))XK(1,v,y,f);else if(v===N.m.Ea){if(!mL){var z=Pb(y);z&&(f["&did"]=z)}var D=void 0,E=void 0;b===N.m.ma?D=Pb(c.getMergedValues(v),"."):(D=Pb(c.getMergedValues(v,1),"."),E=Pb(c.getMergedValues(v,2),"."));D&&(f["&gdid"]=D);E&&(f["&edid"]=
E)}else v===N.m.Va&&u.indexOf(N.m.fd)<0&&(h.cookieName=String(y)+"_ga");F(153)&&nL[v]&&(c.H.hasOwnProperty(v)||b===N.m.ma&&c.C.hasOwnProperty(v))&&(r=!1)}}F(153)&&r&&(f["&jsscut"]="1");d(N.m.Fg)!==!1&&d(N.m.Pb)!==!1&&JK()||(g.allowAdFeatures=!1);g.allowAdPersonalizationSignals=Hq(c);!c.isGtmEvent&&d(N.m.Db)&&(h._useUp=!0);if(c.isGtmEvent){h.name=h.name||e.gtmTrackerName;var M=g.hitCallback;g.hitCallback=function(){qb(M)&&M();c.onSuccess()}}else{YK(h,"cookieDomain","auto");YK(g,"forceSSL",!0);YK(e,
"eventCategory",oL(b));pL[b]&&YK(f,"nonInteraction",!0);b==="login"||b==="sign_up"||b==="share"?YK(e,"eventLabel",d(N.m.Yk)):b==="search"||b==="view_search_results"?YK(e,"eventLabel",d(N.m.No)):b==="select_content"&&YK(e,"eventLabel",d(N.m.qo));var G=e[N.m.Wa]||{},L=G[N.m.tf];L||L!=0&&G[N.m.na]?h.allowLinker=!0:L===!1&&YK(h,"useAmpClientId",!1);f.hitCallback=c.onSuccess;h.name=a}Iq()&&(g["&gcs"]=Jq());g["&gcd"]=Nq(c);vl()&&(An(N.m.ka)||(h.storage="none"),An([N.m.aa,N.m.W])||(g.allowAdFeatures=!1,
h.storeGac=!1));Qq()&&(g["&dma_cps"]=Oq());g["&dma"]=Pq();lq(tq())&&(g["&tcfd"]=Rq());Yj()&&(g["&tag_exp"]=Yj());var U=fk(c)||d(N.m.qc),ia=d(N.m.Rg);U&&(c.isGtmEvent||(h[N.m.qc]=U),h._cd2l=!0);ia&&!c.isGtmEvent&&(h[N.m.Rg]=ia);e.fieldsToSend=f;e.fieldsToSet=g;e.createOnlyFields=h;return e},hL=function(a){return An(a)},gL=function(a){if(Array.isArray(a)){for(var b=[],c=0;c<a.length;c++){var d=a[c];if(d!=null){var e=d.id,f=d.variant;e!=null&&f!=null&&b.push(String(e)+"."+String(f))}}return b.length>
0?b.join("!"):void 0}},YK=function(a,b,c){a.hasOwnProperty(b)||(a[b]=c)},oL=function(a){var b="general";qL[a]?b="ecommerce":rL[a]?b="engagement":a==="exception"&&(b="error");return b},MK=function(a){return a&&rb(a)?a.replace(/(_[a-z])/g,function(b){return b[1].toUpperCase()}):a},XK=function(a,b,c,d){if(c!==void 0)if(sL[b]&&(c=Bb(c)),b!=="anonymize_ip"||c||(c=void 0),a===1)d[MK(b)]=c;else if(rb(a))d[a]=c;else for(var e in a)a.hasOwnProperty(e)&&c[e]!==void 0&&(d[a[e]]=c[e])},mL=!1;var aL=!1,ZK={},PK={},tL={},nL=(tL[N.m.Ob]=1,tL[N.m.Pb]=1,tL[N.m.qb]=1,tL[N.m.rb]=1,tL[N.m.Bb]=1,tL[N.m.fd]=1,tL[N.m.Rb]=1,tL[N.m.Va]=1,tL[N.m.zc]=1,tL[N.m.al]=1,tL[N.m.ya]=1,tL[N.m.vf]=1,tL[N.m.Xa]=1,tL[N.m.Cb]=1,tL),uL={},KK=(uL.client_storage="storage",uL.sample_rate=1,uL.site_speed_sample_rate=1,uL.store_gac=1,uL.use_amp_client_id=1,uL[N.m.Qb]=1,uL[N.m.pb]="storeGac",uL[N.m.qb]=1,uL[N.m.rb]=1,uL[N.m.Bb]=1,uL[N.m.fd]=1,uL[N.m.Rb]=1,uL[N.m.zc]=
1,uL),vL={},jL=(vL._cs=1,vL._useUp=1,vL.allowAnchor=1,vL.allowLinker=1,vL.alwaysSendReferrer=1,vL.clientId=1,vL.cookieDomain=1,vL.cookieExpires=1,vL.cookieFlags=1,vL.cookieName=1,vL.cookiePath=1,vL.cookieUpdate=1,vL.legacyCookieDomain=1,vL.legacyHistoryImport=1,vL.name=1,vL.sampleRate=1,vL.siteSpeedSampleRate=1,vL.storage=1,vL.storeGac=1,vL.useAmpClientId=1,vL._cd2l=1,vL),lL={anonymize_ip:1},wL={},LK=(wL.campaign={content:"campaignContent",id:"campaignId",medium:"campaignMedium",name:"campaignName",
source:"campaignSource",term:"campaignKeyword"},wL.app_id=1,wL.app_installer_id=1,wL.app_name=1,wL.app_version=1,wL.description="exDescription",wL.fatal="exFatal",wL.language=1,wL.page_hostname="hostname",wL.transport_type="transport",wL[N.m.sb]="currencyCode",wL[N.m.Vg]=1,wL[N.m.ya]="location",wL[N.m.vf]="page",wL[N.m.Xa]="referrer",wL[N.m.Cb]="title",wL[N.m.ji]=1,wL[N.m.La]=1,wL),xL={},kL=(xL.content_id=1,xL.event_action=1,xL.event_category=1,xL.event_label=1,xL.link_attribution=1,xL.name=1,xL[N.m.Wa]=
1,xL[N.m.Yk]=1,xL[N.m.pd]=1,xL[N.m.Ma]=1,xL),iL={displayfeatures:1,enableLinkId:1,enableRecaptcha:1,eventAction:1,eventCategory:1,eventLabel:1,gaFunctionName:1,gtmEcommerceData:1,gtmTrackerName:1,linker:1,remarketingLists:1,socialAction:1,socialNetwork:1,socialTarget:1,timingVar:1,value:1},fL=["item_category","item_category2","item_category3","item_category4","item_category5"],yL={},WK=(yL.levels=1,yL[N.m.rb]="duration",yL[N.m.fd]=1,yL),zL={},sL=(zL.anonymize_ip=1,zL.fatal=1,zL.send_page_view=1,zL.store_gac=
1,zL.use_amp_client_id=1,zL[N.m.pb]=1,zL[N.m.Vg]=1,zL),AL={},$K=(AL.checkout_progress=1,AL.select_content=1,AL.set_checkout_option=1,AL[N.m.Ud]=1,AL[N.m.Vd]=1,AL[N.m.Yc]=1,AL[N.m.Wd]=1,AL[N.m.jc]=1,AL[N.m.yc]=1,AL[N.m.kc]=1,AL[N.m.zb]=1,AL[N.m.Xd]=1,AL[N.m.mc]=1,AL),BL={},qL=(BL.checkout_progress=1,BL.set_checkout_option=1,BL[N.m.wk]=1,BL[N.m.xk]=1,BL[N.m.Ud]=1,BL[N.m.Vd]=1,BL[N.m.yk]=1,BL[N.m.Yc]=1,BL[N.m.zb]=1,BL[N.m.Xd]=1,BL[N.m.zk]=1,BL),CL={},rL=(CL.generate_lead=1,CL.login=1,CL.search=1,CL.select_content=
1,CL.share=1,CL.sign_up=1,CL.view_search_results=1,CL[N.m.Wd]=1,CL[N.m.jc]=1,CL[N.m.yc]=1,CL[N.m.kc]=1,CL[N.m.mc]=1,CL),DL={},pL=(DL.view_search_results=1,DL[N.m.jc]=1,DL[N.m.kc]=1,DL[N.m.mc]=1,DL),dL={};function EL(a,b,c,d){}EL.K="internal.executeEventProcessor";function FL(a){var b;return Id(b,this.J,1)}FL.K="internal.executeJavascriptString";function GL(a){var b;return b};function HL(a){var b="";return b}HL.K="internal.generateClientId";function IL(a){var b={};return Id(b)}IL.K="internal.getAdsCookieWritingOptions";function JL(a,b){var c=!1;return c}JL.K="internal.getAllowAdPersonalization";function KL(){var a;return a}KL.K="internal.getAndResetEventUsage";function LL(a,b){b=b===void 0?!0:b;var c;return c}LL.K="internal.getAuid";var ML=null;function NL(){var a=new cb;return a}NL.publicName="getContainerVersion";function OL(a,b){b=b===void 0?!0:b;var c;return c}OL.publicName="getCookieValues";function PL(){var a="";return a}PL.K="internal.getCorePlatformServicesParam";function QL(){return xm()}QL.K="internal.getCountryCode";function RL(){var a=[];a=Gj();return Id(a)}RL.K="internal.getDestinationIds";function SL(a){var b=new cb;return b}SL.K="internal.getDeveloperIds";function TL(a){var b;return b}TL.K="internal.getEcsidCookieValue";function UL(a,b){var c=null;return c}UL.K="internal.getElementAttribute";function VL(a){var b=null;return b}VL.K="internal.getElementById";function WL(a){var b="";return b}WL.K="internal.getElementInnerText";function XL(a,b){var c=null;return Id(c)}XL.K="internal.getElementProperty";function YL(a){var b;return b}YL.K="internal.getElementValue";function ZL(a){var b=0;return b}ZL.K="internal.getElementVisibilityRatio";function $L(a){var b=null;return b}$L.K="internal.getElementsByCssSelector";
function aM(a){var b;if(!J(a))throw I(this.getName(),["string"],arguments);K(this,"read_event_data",a);var c;a:{var d=a,e=CD(this).originalEventData;if(e){for(var f=e,g={},h={},l={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),u=0;u<r.length;u++){for(var t=r[u].split("."),v=0;v<t.length;v++)n.push(t[v]),v!==t.length-1&&n.push(l);u!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var x=[],y="",z=m(n),D=z.next();!D.done;D=
z.next()){var E=D.value;E===l?(x.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&x.push(y);for(var M=m(x),G=M.next();!G.done;G=M.next()){if(f==null){c=void 0;break a}f=f[G.value]}c=f}else c=void 0}b=Id(c,this.J,1);return b}aM.K="internal.getEventData";function bM(a){var b=null;if(!J(a))throw I(this.getName(),["string"],arguments);K(this,"read_dom_elements","css",a);try{var c=mG(a);c&&(b=new Fd(c))}catch(d){return null}return b}bM.K="internal.getFirstElementByCssSelector";var cM={};cM.disableUserDataWithoutCcd=F(223);cM.enableDecodeUri=F(92);cM.enableGaAdsConversions=F(122);cM.enableGaAdsConversionsClientId=F(121);cM.enableOverrideAdsCps=F(170);cM.enableUrlDecodeEventUsage=F(139);function dM(){return Id(cM)}dM.K="internal.getFlags";function eM(){var a;return a}eM.K="internal.getGsaExperimentId";function fM(){return new Fd(IC)}fM.K="internal.getHtmlId";function gM(a){var b;return b}gM.K="internal.getIframingState";function hM(a,b){var c={};return Id(c)}hM.K="internal.getLinkerValueFromLocation";function iM(){var a=new cb;return a}iM.K="internal.getPrivacyStrings";function jM(a,b){var c;if(!J(a)||!J(b))throw I(this.getName(),["string","string"],arguments);var d=XF(a)||{};c=Id(d[b],this.J);return c}jM.K="internal.getProductSettingsParameter";function kM(a,b){var c;return c}kM.publicName="getQueryParameters";function lM(a,b){var c;return c}lM.publicName="getReferrerQueryParameters";function mM(a){var b="";return b}mM.publicName="getReferrerUrl";function nM(){return ym()}nM.K="internal.getRegionCode";function oM(a,b){var c;return c}oM.K="internal.getRemoteConfigParameter";function pM(){var a=new cb;a.set("width",0);a.set("height",0);return a}pM.K="internal.getScreenDimensions";function qM(){var a="";return a}qM.K="internal.getTopSameDomainUrl";function rM(){var a="";return a}rM.K="internal.getTopWindowUrl";function sM(a){var b="";return b}sM.publicName="getUrl";function tM(){K(this,"get_user_agent");return zc.userAgent}tM.K="internal.getUserAgent";function uM(){var a;return a?Id(LJ(a)):a}uM.K="internal.getUserAgentClientHints";function BM(){var a=w;return a.gaGlobal=a.gaGlobal||{}}function CM(){var a=BM();a.hid=a.hid||vb();return a.hid}function DM(a,b){var c=BM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};function aN(a){(Uv(a)||fj())&&W(a,N.m.tl,ym()||xm());!Uv(a)&&fj()&&W(a,N.m.zi,"::")}function bN(a){if(fj()&&!Uv(a)&&(Bm()||W(a,N.m.Xk,!0),F(78))){Su(a);Tu(a,po.Hf.Ln,Um(Q(a.D,N.m.Va)));var b=po.Hf.Mn;var c=Q(a.D,N.m.zc);Tu(a,b,c===!0?1:c===!1?0:void 0);Tu(a,po.Hf.Kn,Um(Q(a.D,N.m.Bb)));Tu(a,po.Hf.In,Nr(Tm(Q(a.D,N.m.qb)),Tm(Q(a.D,N.m.Rb))))}};var wN={AW:Kl.X.zn,G:Kl.X.Vo,DC:Kl.X.Ro};function xN(a){var b=lx(a);return""+ai(b.map(function(c){return c.value}).join("!"))}function yN(a){var b=so(a);return b&&wN[b.prefix]}function zN(a,b){var c=a[b];c&&(c.clearTimerId&&w.clearTimeout(c.clearTimerId),c.clearTimerId=w.setTimeout(function(){delete a[b]},36E5))};var eO=function(a){for(var b={},c=String(dO.cookie).split(";"),d=0;d<c.length;d++){var e=c[d].split("="),f=e[0].trim();if(f&&a(f)){var g=e.slice(1).join("=").trim();g&&(g=decodeURIComponent(g));var h=void 0,l=void 0;((h=b)[l=f]||(h[l]=[])).push(g)}}return b};var fO=window,dO=document,gO=function(a){var b=fO._gaUserPrefs;if(b&&b.ioo&&b.ioo()||dO.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&fO["ga-disable-"+a]===!0)return!0;try{var c=fO.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(f){}for(var d=eO(function(f){return f==="AMP_TOKEN"}).AMP_TOKEN||[],e=0;e<d.length;e++)if(d[e]=="$OPT_OUT")return!0;return dO.getElementById("__gaOptOutExtension")?!0:!1};var hO="gclid dclid gclsrc wbraid gbraid gad_source gad_campaignid utm_source utm_medium utm_campaign utm_term utm_content utm_id".split(" ");function iO(){var a=A.location,b,c=a==null?void 0:(b=a.search)==null?void 0:b.replace("?",""),d;if(c){for(var e=[],f=kj(c,!0),g=m(hO),h=g.next();!h.done;h=g.next()){var l=h.value,n=f[l];if(n)for(var p=0;p<n.length;p++){var q=n[p];q!==void 0&&e.push({name:l,value:q})}}d=e}else d=[];return d};
function tO(a){yb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[N.m.Ub]||{};yb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function ZO(a,b){}function $O(a,b){var c=function(){};return c}
function aP(a,b,c){}var bP=H.P.sk,cP=H.P.tk;var dP=$O;function eP(a,b){if(F(240)){var c=Gj();c&&c.indexOf(b)>-1&&(a[R.A.Rl]=!0)}}var fP=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function gP(a,b,c){var d=this;}gP.K="internal.gtagConfig";function hP(a,b,c){var d=this;}hP.K="internal.gtagDestinationConfig";
function jP(a,b){}
jP.publicName="gtagSet";function kP(){var a={};return a};function lP(a){}lP.K="internal.initializeServiceWorker";function mP(a,b){}mP.publicName="injectHiddenIframe";var nP=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function oP(a,b,c,d,e){}oP.K="internal.injectHtml";var sP={};
function uP(a,b,c,d){}var vP={dl:1,id:1},wP={};
function xP(a,b,c,d){}F(160)?xP.publicName="injectScript":uP.publicName="injectScript";xP.K="internal.injectScript";function yP(){var a=!1;a=!!rm["5"];return a}yP.K="internal.isAutoPiiEligible";function zP(a){var b=!0;return b}zP.publicName="isConsentGranted";function AP(a){var b=!1;return b}AP.K="internal.isDebugMode";function BP(){return Am()}BP.K="internal.isDmaRegion";function CP(a){var b=!1;return b}CP.K="internal.isEntityInfrastructure";function DP(a){var b=!1;if(!Gh(a))throw I(this.getName(),["number"],[a]);b=F(a);return b}DP.K="internal.isFeatureEnabled";function EP(){var a=!1;return a}EP.K="internal.isFpfe";function FP(){var a=!1;return a}FP.K="internal.isGcpConversion";function GP(){var a=!1;return a}GP.K="internal.isLandingPage";function HP(){var a=!1;return a}HP.K="internal.isOgt";function IP(){var a;return a}IP.K="internal.isSafariPcmEligibleBrowser";function JP(){var a=fi(function(b){CD(this).log("error",b)});a.publicName="JSON";return a};function KP(a){var b=void 0;return Id(b)}KP.K="internal.legacyParseUrl";function LP(){return!1}
var MP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function NP(){}NP.publicName="logToConsole";function OP(a,b){}OP.K="internal.mergeRemoteConfig";function PP(a,b,c){c=c===void 0?!0:c;var d=[];return Id(d)}PP.K="internal.parseCookieValuesFromString";function QP(a){var b=void 0;return b}QP.publicName="parseUrl";function RP(a){}RP.K="internal.processAsNewEvent";function SP(a,b,c){var d;return d}SP.K="internal.pushToDataLayer";function TP(a){var b=Fa.apply(1,arguments),c=!1;if(!J(a))throw I(this.getName(),["string"],arguments);for(var d=[this,a],e=m(b),f=e.next();!f.done;f=e.next())d.push(B(f.value,this.J,1));try{K.apply(null,d),c=!0}catch(g){return!1}return c}TP.publicName="queryPermission";function UP(a){var b=this;}UP.K="internal.queueAdsTransmission";function VP(a){var b=void 0;return b}VP.publicName="readAnalyticsStorage";function WP(){var a="";return a}WP.publicName="readCharacterSet";function XP(){return Dg(19)}XP.K="internal.readDataLayerName";function YP(){var a="";return a}YP.publicName="readTitle";function ZP(a,b){var c=this;if(!J(a)||!yh(b))throw I(this.getName(),["string","function"],arguments);SJ(a,function(d){b.invoke(c.J,Id(d,c.J,1))});}ZP.K="internal.registerCcdCallback";function $P(a,b){if(!J(a)||!vh(b)&&!xh(b))throw I(this.getName(),["string","Object|undefined"],arguments);var c=CD(this);if(Qh(c.Jb()))return!1;var d,e,f=so(a,!0);if(!f)return!1;switch(f.prefix){case "AW":d=lK;e=jl.Z.Ga;break;case "DC":d=BK;e=jl.Z.Ga;break;case "GF":d=GK;e=jl.Z.Gb;break;case "HA":d=IK;e=jl.Z.Gb;break;case "UA":d=cL;e=jl.Z.Gb;break;case "MC":d=$O(f,c.eventId);e=jl.Z.xc;break;case "G":return ZO(a,c.eventId),!0;default:return!1}Hp(a,
d,e,B(b,this.J,1));return!0}$P.K="internal.registerDestination";var aQ=["config","event","get","set"];function bQ(a,b,c){}bQ.K="internal.registerGtagCommandListener";function cQ(a,b){var c=!1;return c}cQ.K="internal.removeDataLayerEventListener";function dQ(a,b){}
dQ.K="internal.removeFormData";function eQ(){}eQ.publicName="resetDataLayer";function fQ(a,b,c){var d=void 0;return d}fQ.K="internal.scrubUrlParams";function gQ(a){}gQ.K="internal.sendAdsHit";function hQ(a,b,c,d){}hQ.K="internal.sendGtagEvent";function iQ(a,b,c){}iQ.publicName="sendPixel";function jQ(a,b){}jQ.K="internal.setAnchorHref";function kQ(a){}kQ.K="internal.setContainerConsentDefaults";function lQ(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}lQ.publicName="setCookie";function mQ(a){}mQ.K="internal.setCorePlatformServices";function nQ(a,b){}nQ.K="internal.setDataLayerValue";function oQ(a){}oQ.publicName="setDefaultConsentState";function pQ(a,b){}pQ.K="internal.setDelegatedConsentType";function qQ(a,b){}qQ.K="internal.setFormAction";function rQ(a,b,c){c=c===void 0?!1:c;}rQ.K="internal.setInCrossContainerData";function sQ(a,b,c){return!1}sQ.publicName="setInWindow";function tQ(a,b,c){}tQ.K="internal.setProductSettingsParameter";function uQ(a,b,c){if(!J(a)||!J(b)||arguments.length!==3)throw I(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Lp(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!sd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=B(c,this.J,1);}uQ.K="internal.setRemoteConfigParameter";function vQ(a,b){}vQ.K="internal.setTransmissionMode";function wQ(a,b,c,d){var e=this;}wQ.publicName="sha256";function xQ(a,b,c){}
xQ.K="internal.sortRemoteConfigParameters";function yQ(a){}yQ.K="internal.storeAdsBraidLabels";function zQ(a,b){var c=void 0;return c}zQ.K="internal.subscribeToCrossContainerData";function AQ(a){}AQ.K="internal.taskSendAdsHits";var BQ={},CQ={};BQ.getItem=function(a){var b=null;return b};BQ.setItem=function(a,b){};
BQ.removeItem=function(a){};BQ.clear=function(){};BQ.publicName="templateStorage";function DQ(a,b){var c=!1;return c}DQ.K="internal.testRegex";function EQ(a){var b;return b};function FQ(a,b){}FQ.K="internal.trackUsage";function GQ(a,b){var c;return c}GQ.K="internal.unsubscribeFromCrossContainerData";function HQ(a){}HQ.publicName="updateConsentState";function IQ(a){var b=!1;return b}IQ.K="internal.userDataNeedsEncryption";var JQ;function KQ(a,b,c){JQ=JQ||new qi;JQ.add(a,b,c)}function LQ(a,b){var c=JQ=JQ||new qi;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=qb(b)?Jh(a,b):Kh(a,b)}
function MQ(){return function(a){var b;var c=JQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.J.lb();if(e){var f=!1,g=e.Jb();if(g){Qh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function NQ(){var a=function(c){return void LQ(c.K,c)},b=function(c){return void KQ(c.publicName,c)};b(wD);b(DD);b(RE);b(TE);b(UE);b(aF);b(cF);b(eG);b(JP());b(gG);b(NL);b(OL);b(kM);b(lM);b(mM);b(sM);b(jP);b(mP);b(zP);b(NP);b(QP);b(TP);b(WP);b(YP);b(iQ);b(lQ);b(oQ);b(sQ);b(wQ);b(BQ);b(HQ);KQ("Math",Oh());KQ("Object",oi);KQ("TestHelper",si());KQ("assertApi",Lh);KQ("assertThat",Mh);KQ("decodeUri",Rh);KQ("decodeUriComponent",Th);KQ("encodeUri",Uh);KQ("encodeUriComponent",Vh);KQ("fail",$h);KQ("generateRandom",
ci);KQ("getTimestamp",di);KQ("getTimestampMillis",di);KQ("getType",ei);KQ("makeInteger",gi);KQ("makeNumber",hi);KQ("makeString",ii);KQ("makeTableMap",ji);KQ("mock",mi);KQ("mockObject",ni);KQ("fromBase64",GL,!("atob"in w));KQ("localStorage",MP,!LP());KQ("toBase64",EQ,!("btoa"in w));a(vD);a(zD);a(TD);a(eE);a(lE);a(qE);a(GE);a(PE);a(SE);a(VE);a(WE);a(XE);a(YE);a(ZE);a($E);a(bF);a(dF);a(dG);a(fG);a(hG);a(iG);a(jG);a(kG);a(lG);a(TH);a(YH);a(fI);a(gI);a(rI);a(wI);a(BI);a(KI);a(PI);a(bJ);a(dJ);a(rJ);a(sJ);
a(uJ);a(EL);a(FL);a(HL);a(IL);a(JL);a(KL);a(LL);a(QL);a(RL);a(SL);a(TL);a(UL);a(VL);a(WL);a(XL);a(YL);a(ZL);a($L);a(aM);a(bM);a(dM);a(eM);a(fM);a(gM);a(hM);a(iM);a(jM);a(nM);a(oM);a(pM);a(qM);a(rM);a(uM);a(gP);a(hP);a(lP);a(oP);a(xP);a(yP);a(AP);a(BP);a(CP);a(DP);a(EP);a(FP);a(GP);a(HP);a(IP);a(KP);a(EE);a(OP);a(PP);a(RP);a(SP);a(UP);a(XP);a(ZP);a($P);a(bQ);a(cQ);a(dQ);a(fQ);a(gQ);a(hQ);a(jQ);a(kQ);a(mQ);a(nQ);a(pQ);a(qQ);a(rQ);a(tQ);a(uQ);a(vQ);a(xQ);a(yQ);a(zQ);a(AQ);a(DQ);a(FQ);a(GQ);a(IQ);LQ("internal.IframingStateSchema",
kP());LQ("internal.quickHash",bi);
F(104)&&a(PL);F(160)?b(xP):b(uP);F(177)&&b(VP);return MQ()};var tD;
function OQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;tD=new bf;PQ();Jf=sD();var e=tD,f=NQ(),g=new Bd("require",f);g.Sa();e.C.C.set("require",g);Ya.set("require",g);for(var h=[],l=0;l<c.length;l++){var n=c[l];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[l]&&d[l].length&&dg(n,d[l]);try{tD.execute(n),F(120)&&lk&&n[0]===50&&h.push(n[1])}catch(r){}}F(120)&&(Wf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");cj[q]=["sandboxedScripts"]}QQ(b)}function PQ(){tD.Uc(function(a,b,c){Wn.SANDBOXED_JS_SEMAPHORE=Wn.SANDBOXED_JS_SEMAPHORE||0;Wn.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Wn.SANDBOXED_JS_SEMAPHORE--}})}function QQ(a){a&&yb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");cj[e]=cj[e]||[];cj[e].push(b)}})};function RQ(a){mB(hB("developer_id."+a,!0),0,{})};var SQ=Array.isArray;function TQ(a,b){return td(a,b||null)}function Y(a){return window.encodeURIComponent(a)}function UQ(a,b,c){Pc(a,b,c)}
function VQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=mj(sj(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function WQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function XQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=WQ(b,"parameter","parameterValue");e&&(c=TQ(e,c))}return c}function YQ(a,b,c){return a===void 0||a===c?b:a}function ZQ(){try{if(!F(243))return null;var a=[],b;a:{try{b=!!mG('script[data-requiremodule^="mage/"]');break a}catch(g){}b=!1}b&&a.push("ac");var c;a:{try{c=!!mG('script[src^="//assets.squarespace.com/"]');break a}catch(g){}c=!1}c&&a.push("sqs");var d;a:{try{d=!!mG('script[id="d-js-core"]');break a}catch(g){}d=!1}d&&a.push("dud");var e;a:{try{e=!!mG('script[src*="woocommerce"],link[href*="woocommerce"],[class|="woocommerce"]');break a}catch(g){}e=!1}e&&a.push("woo");var f;a:{try{f=!!mG('meta[content*="fourthwall"],script[src*="fourthwall"],link[href*="fourthwall"]');
break a}catch(g){}f=!1}f&&a.push("fw");if(a.length>0)return{plf:a.join(".")}}catch(g){}return null};function $Q(a,b,c){return Lc(a,b,c,void 0)}function aR(a,b){return hp(a,b||2)}function bR(a,b){w[a]=b}function cR(a,b,c){var d=w;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}var dR={};var Z={securityGroups:{}};

Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!rb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&$g(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},V:a}})}();







Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.F="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},V:a}})}();








Z.securityGroups.read_dom_elements=["google"],function(){function a(b,c,d){return{type:c,value:d}}(function(b){Z.__read_dom_elements=b;Z.__read_dom_elements.F="read_dom_elements";Z.__read_dom_elements.isVendorTemplate=!0;Z.__read_dom_elements.priorityOverride=0;Z.__read_dom_elements.isInfrastructure=!1;Z.__read_dom_elements["5"]=!1})(function(b){var c=b.vtp_allowedElementIds||"none",d=b.vtp_allowedCssSelectors||"none",e=b.vtp_elementIds||[],f=b.vtp_cssSelectors||[],g=b.vtp_createPermissionError;return{assert:function(h,
l,n){switch(l){case "id":if(c==="none")break;if(c==="any"||e.indexOf(n)>-1)return;break;case "css":if(d==="none")break;if(d==="any"||f.indexOf(n)>-1)return;break;default:throw g(h,{},"Unknown selector type "+l+".");}throw g(h,{},"Prohibited selector value "+n+" for selector type "+l+".");},V:a}})}();



Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=kB(String(b.streamId),d,c);mB(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.F="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get["5"]=!1;


Z.securityGroups.zone=[],function(){var a={},b=function(d){for(var e=0;e<d.length;e++)if(!d[e])return!1;return!0},c=function(d){var e=b(d.vtp_boundaries||[]);if(d.vtp_gtmTagId in a)uE(a[d.vtp_gtmTagId],d.vtp_gtmEventId,e);else if(e){var f=d.vtp_childContainers.map(function(n){return n.publicId}),g=d.vtp_enableTypeRestrictions?d.vtp_whitelistedTypes.map(function(n){return n.typeId}):null,h={};var l=wE(d.vtp_gtmEventId,f,g,h,Tz(1,d.vtp_gtmEntityIndex,d.vtp_gtmEntityName),!!d.vtp_inheritParentConfig);a[d.vtp_gtmTagId]=l}Sc(d.vtp_gtmOnSuccess)};Z.__zone=c;Z.__zone.F="zone";Z.__zone.isVendorTemplate=!0;Z.__zone.priorityOverride=0;Z.__zone.isInfrastructure=
!1;Z.__zone["5"]=!0}();

var Zn={dataLayer:ip,callback:function(a){bj.hasOwnProperty(a)&&qb(bj[a])&&bj[a]();delete bj[a]},bootstrap:0};
function eR(){Yn();Pj();Oz();Ib(cj,Z.securityGroups);var a=Lj(Mj()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;jn(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||P(142);Vf={Op:jg}}var fR=!1;F(218)&&(fR=Bg(47,fR));
function um(){try{if(fR||!Xj()){Qi();F(218)&&(Fg.C=Bg(50,Fg.C));
F(218)&&(Fg.R=Bg(51,Fg.R));if(F(109)){}Wa[7]=!0;var a=Xn("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});rn(a);Vn();iD();uq();co();if(Qj()){Dg(5);BE();EA().removeExternalRestrictions(Ij());}else{oo();Tf();Pf=Z;Qf=RC;Wv();OQ();eR();PC();sm||(rm=wm(),rm["0"]&&Pl(Kl.X.se,JSON.stringify(rm)));Rn();aC();fB();IB=!1;A.readyState==="complete"?KB():Qc(w,"load",KB);$A();lk&&(qp(Dp),w.setInterval(Cp,864E5),qp(jD),qp(rA),qp(jy),qp(Gp),qp(pD),qp(CA),F(120)&&(qp(wA),qp(xA),qp(yA)),
lD={},qp(mD),Kg(),F(261)&&qp(kD));nk&&(gm(),Go(),cC(),lC(),jC(),Yl("bt",String(Fg.H?2:Fg.C?1:0)),Yl("ct",String(Fg.H?0:Fg.C?1:3)),fC(),iC());GC();qm(1);CE();aj=Fb();Zn.bootstrap=aj;Fg.R&&$B();F(109)&&Fy();F(134)&&(typeof w.name==="string"&&Kb(w.name,"web-pixel-sandbox-CUSTOM")&&id()?RQ("dMDg0Yz"):w.Shopify&&(RQ("dN2ZkMj"),id()&&RQ("dNTU0Yz")))}}}catch(b){qm(4),
zp()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Wm(n)&&(l=h.Bl)}function c(){l&&Cc?g(l):a()}if(!w[Dg(37)]){var d=!1;if(A.referrer){var e=sj(A.referrer);d=oj(e,"host")===Dg(38)}if(!d){var f=wr(Dg(39));d=!(!f.length||!f[0].length)}d&&(w[Dg(37)]=!0,Lc(Dg(40)))}var g=function(t){var v="GTM",x="GTM";Xi&&(v="OGT",x="GTAG");var y=Dg(23),z=w[y];z||(z=[],w[y]=z,Lc("https://"+Dg(3)+"/debug/bootstrap?id="+Dg(5)+"&src="+x+"&cond="+String(t)+"&gtm="+Uq()));var D={messageType:"CONTAINER_STARTING",
data:{scriptSource:Cc,containerProduct:v,debug:!1,id:Dg(5),targetRef:{ctid:Dg(5),isDestination:Fj(),canonicalId:Dg(6)},aliases:Jj(),destinations:Gj()}};D.data.resume=function(){a()};Cg(2)&&(D.data.initialPublish=!0);z.push(D)},h={Yo:1,Ql:2,km:3,mk:4,Bl:5};h[h.Yo]="GTM_DEBUG_LEGACY_PARAM";h[h.Ql]="GTM_DEBUG_PARAM";h[h.km]="REFERRER";h[h.mk]="COOKIE";h[h.Bl]="EXTENSION_PARAM";var l=void 0,n=void 0,p=mj(w.location,"query",!1,void 0,"gtm_debug");Wm(p)&&(l=h.Ql);if(!l&&A.referrer){var q=sj(A.referrer);
oj(q,"host")===Dg(24)&&(l=h.km)}if(!l){var r=wr("__TAG_ASSISTANT");r.length&&r[0].length&&(l=h.mk)}l||b();if(!l&&Vm(n)){var u=!1;Qc(A,"TADebugSignal",function(){u||(u=!0,b(),c())},!1);w.setTimeout(function(){u||(u=!0,b(),c())},200)}else c()})(function(){!fR||wm()["0"]?um():tm()});

})()


!function(a){function l(t,e){return"function"==typeof t?t.call(e):t}function h(t,e){this.$element=a(t),this.options=e,this.enabled=!0,this.fixTitle()}h.prototype={show:function(){var t=this.getTitle();if(t&&this.enabled){var e,i=this.tip(),s=(i.find(".tipsy-inner")[this.options.html?"html":"text"](t),i[0].className="tipsy",i.remove().css({top:0,left:0,visibility:"hidden",display:"block"}).prependTo(document.body),a.extend({},this.$element.offset(),{width:this.$element[0].offsetWidth,height:this.$element[0].offsetHeight})),n=i[0].offsetWidth,o=i[0].offsetHeight,t=l(this.options.gravity,this.$element[0]);switch(t.charAt(0)){case"n":e={top:s.top+s.height+this.options.offset,left:s.left+s.width/2-n/2};break;case"s":e={top:s.top-o-this.options.offset,left:s.left+s.width/2-n/2};break;case"e":e={top:s.top+s.height/2-o/2,left:s.left-n-this.options.offset};break;case"w":(extra_gap=0)<a("body").css("position").length&&"relative"==a("body").css("position")&&a("#wpadminbar").length&&(extra_gap=a("#wpadminbar").height()),e={top:s.top+s.height/2-o/2-extra_gap,left:s.left+s.width+this.options.offset}}2==t.length&&("w"==t.charAt(1)?e.left=s.left+s.width/2-15:e.left=s.left+s.width/2-n+15),i.css(e).addClass("tipsy-"+t),i.find(".tipsy-arrow")[0].className="tipsy-arrow tipsy-arrow-"+t.charAt(0),this.options.className&&i.addClass(l(this.options.className,this.$element[0])),this.options.fade?i.stop().css({opacity:0,display:"block",visibility:"visible"}).animate({opacity:this.options.opacity}):i.css({visibility:"visible",opacity:this.options.opacity})}},hide:function(){this.options.fade?this.tip().stop().fadeOut(function(){a(this).remove()}):this.tip().remove()},fixTitle:function(){var t=this.$element;!t.attr("title")&&"string"==typeof t.attr("original-title")||t.attr("original-title",t.attr("title")||"").removeAttr("title")},getTitle:function(){var t,e=this.$element,i=this.options;return this.fixTitle(),"string"==typeof(i=this.options).title?t=e.attr("title"==i.title?"original-title":i.title):"function"==typeof i.title&&(t=i.title.call(e[0])),(t=(""+t).replace(/(^\s*|\s*$)/,""))||i.fallback},tip:function(){return this.$tip||(this.$tip=a('<div class="tipsy"></div>').html('<div class="tipsy-arrow"></div><div class="tipsy-inner"></div>'),this.$tip.data("tipsy-pointee",this.$element[0])),this.$tip},validate:function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},toggleEnabled:function(){this.enabled=!this.enabled}},a.fn.tipsy=function(i){var t,e;return!0===i?this.data("tipsy"):("string"==typeof i?(t=this.data("tipsy"))&&t[i]():((i=a.extend({},a.fn.tipsy.defaults,i)).on||this.each(function(){s(this)}),"manual"!=i.trigger&&(t="hover"==i.trigger?"mouseenter":"focus",e="hover"==i.trigger?"mouseleave":"blur",i.on?a(document).on(t,this.selector,n).on(e,this.selector,o):this.on(t,n).on(e,o))),this);function s(t){var e=a.data(t,"tipsy");return e||(e=new h(t,a.fn.tipsy.elementOptions(t,i)),a.data(t,"tipsy",e)),e}function n(){var t=s(this);t.hoverState="in",0==i.delayIn?t.show():(t.fixTitle(),setTimeout(function(){"in"==t.hoverState&&t.show()},i.delayIn))}function o(){var t=s(this);t.hoverState="out",0==i.delayOut?t.hide():setTimeout(function(){"out"==t.hoverState&&t.hide()},i.delayOut)}},a.fn.tipsy.defaults={className:null,delayIn:0,delayOut:0,fade:!1,fallback:"",gravity:"n",html:!1,live:!1,offset:0,opacity:.8,title:"title",trigger:"hover"},a.fn.tipsy.revalidate=function(){a(".tipsy").each(function(){var t=a.data(this,"tipsy-pointee");t&&function(t){for(;t=t.parentNode;)if(t==document)return 1}(t)||a(this).remove()})},a.fn.tipsy.elementOptions=function(t,e){return a.metadata?a.extend({},e,a(t).metadata()):e},a.fn.tipsy.autoNS=function(){return a(this).offset().top>a(document).scrollTop()+a(window).height()/2?"s":"n"},a.fn.tipsy.autoWE=function(){return a(this).offset().left>a(document).scrollLeft()+a(window).width()/2?"e":"w"},a.fn.tipsy.autoBounds=function(n,o){return function(){var t={ns:o[0],ew:1<o.length&&o[1]},e=a(document).scrollTop()+n,i=a(document).scrollLeft()+n,s=a(this);return s.offset().top<e&&(t.ns="n"),s.offset().left<i&&(t.ew="w"),a(window).width()+a(document).scrollLeft()-s.offset().left<n&&(t.ew="e"),a(window).height()+a(document).scrollTop()-s.offset().top<n&&(t.ns="s"),t.ns+(t.ew||"")}}}(jQuery);
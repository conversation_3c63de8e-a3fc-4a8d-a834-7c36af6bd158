<!DOCTYPE html>
<html lang="fr" class="translated-ltr"><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <!-- base href="https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/" -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="profile" href="https://gmpg.org/xfn/11">
  <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
  <style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style><!-- This site is optimized with the Yoast SEO Premium plugin v25.4 (Yoast SEO v25.4) - https://yoast.com/wordpress/plugins/seo/ -->
  <title>Création d'agents d'IA coordonnés avec LangGraph&nbsp;: tutoriel pratique | CodeCut</title>
  <meta name="description" content="Learn how to orchestrate LangGraph agents with LangChain and Tavily to build a debate‑style investment committee AI.">
  <link rel="canonical" href="https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/">
  <meta property="og:locale" content="en_US">
  <meta property="og:type" content="article">
  <meta property="og:title" content="Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial">
  <meta property="og:description" content="Learn how to orchestrate LangGraph agents with LangChain and Tavily to build a debate‑style investment committee AI.">
  <meta property="og:url" content="https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/">
  <meta property="og:site_name" content="CodeCut">
  <meta property="article:published_time" content="2025-06-11T00:19:14+00:00">
  <meta property="article:modified_time" content="2025-09-27T13:27:25+00:00">
  <meta property="og:image" content="https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png">
  <meta property="og:image:width" content="931">
  <meta property="og:image:height" content="488">
  <meta property="og:image:type" content="image/png">
  <meta name="author" content="Khuyen Tran, Bex Tuychiev">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:creator" content="@KhuyenTran16">
  <meta name="twitter:site" content="@KhuyenTran16">
  <meta name="twitter:label1" content="Written by">
  <meta name="twitter:data1" content="Khuyen Tran, Bex Tuychiev">
  <meta name="twitter:label2" content="Est. reading time">
  <meta name="twitter:data2" content="14 minutes">
  <script type="text/javascript" async="" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/analytics.js"></script><script type="text/javascript" async="" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/js.txt"></script><script type="application/ld+json" class="yoast-schema-graph">{"@context":"https://schema.org","@graph":[{"@type":"Article","@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#article","isPartOf":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/"},"author":{"name":"Khuyen Tran","@id":"https://codecut.ai/#/schema/person/0c1c7b36aab1f535b34cecc99c946014"},"headline":"Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial","datePublished":"2025-06-11T00:19:14+00:00","dateModified":"2025-09-27T13:27:25+00:00","mainEntityOfPage":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/"},"wordCount":1478,"commentCount":0,"publisher":{"@id":"https://codecut.ai/#organization"},"image":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#primaryimage"},"thumbnailUrl":"https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png","articleSection":["Blog","LLM"],"inLanguage":"en-US","potentialAction":[{"@type":"CommentAction","name":"Comment","target":["https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#respond"]}]},{"@type":"WebPage","@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/","url":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/","name":"Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial | CodeCut","isPartOf":{"@id":"https://codecut.ai/#website"},"primaryImageOfPage":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#primaryimage"},"image":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#primaryimage"},"thumbnailUrl":"https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png","datePublished":"2025-06-11T00:19:14+00:00","dateModified":"2025-09-27T13:27:25+00:00","description":"Learn how to orchestrate LangGraph agents with LangChain and Tavily to build a debate‑style investment committee AI.","breadcrumb":{"@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#breadcrumb"},"inLanguage":"en-US","potentialAction":[{"@type":"ReadAction","target":["https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/"]}]},{"@type":"ImageObject","inLanguage":"en-US","@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#primaryimage","url":"https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png","contentUrl":"https://codecut.ai/wp-content/uploads/2025/06/langraph-featured-image.png","width":931,"height":488},{"@type":"BreadcrumbList","@id":"https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/#breadcrumb","itemListElement":[{"@type":"ListItem","position":1,"name":"Home","item":"https://codecut.ai/"},{"@type":"ListItem","position":2,"name":"Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial"}]},{"@type":"WebSite","@id":"https://codecut.ai/#website","url":"https://codecut.ai/","name":"CodeCut","description":"Productivity for Data Scientists","publisher":{"@id":"https://codecut.ai/#organization"},"potentialAction":[{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://codecut.ai/?s={search_term_string}"},"query-input":{"@type":"PropertyValueSpecification","valueRequired":true,"valueName":"search_term_string"}}],"inLanguage":"en-US"},{"@type":"Organization","@id":"https://codecut.ai/#organization","name":"CodeCut","url":"https://codecut.ai/","logo":{"@type":"ImageObject","inLanguage":"en-US","@id":"https://codecut.ai/#/schema/logo/image/","url":"https://codecut.ai/wp-content/uploads/2025/04/logo_1200x675_no_crop.png","contentUrl":"https://codecut.ai/wp-content/uploads/2025/04/logo_1200x675_no_crop.png","width":1200,"height":675,"caption":"CodeCut"},"image":{"@id":"https://codecut.ai/#/schema/logo/image/"},"sameAs":["https://x.com/KhuyenTran16","https://www.linkedin.com/in/khuyen-tran-1401/"],"description":"CodeCut is a platform dedicated to helping data professionals write cleaner, faster, and more efficient code. We provide hands-on tutorials, tool comparisons, and best practices across Python, data science, and modern workflow automation.","legalName":"Codecut Technologies LLC","foundingDate":"2024-08-06","numberOfEmployees":{"@type":"QuantitativeValue","minValue":"1","maxValue":"10"}},{"@type":"Person","@id":"https://codecut.ai/#/schema/person/0c1c7b36aab1f535b34cecc99c946014","name":"Khuyen Tran","description":"Khuyen Tran creates accessible data science content that reaches over 100,000 monthly readers through my 180+ Towards Data Science articles and 800+ daily CodeCut tips covering Python, data workflows, and ML engineering. Her mission is to make open-source tools approachable through clear tutorials, documentation, and videos while helping data scientists adopt engineering best practices from version control to deployment."}]}</script><!-- / Yoast SEO Premium plugin. -->
  <link rel="dns-prefetch" href="https://www.googletagmanager.com/">
  <link rel="dns-prefetch" href="https://fonts.googleapis.com/">
  <script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/codecut.ai\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.2"}};
/*! This file is auto-generated */
!function(s,n){var o,i,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),a=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===a[t]})}function u(e,t){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);for(var n=e.getImageData(16,16,1,1),a=0;a<n.data.length;a++)if(0!==n.data[a])return!1;return!0}function f(e,t,n,a){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\udde8\ud83c\uddf6","\ud83c\udde8\u200b\ud83c\uddf6")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!a(e,"\ud83e\udedf")}return!1}function g(e,t,n,a){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):s.createElement("canvas"),o=r.getContext("2d",{willReadFrequently:!0}),i=(o.textBaseline="top",o.font="600 32px Arial",{});return e.forEach(function(e){i[e]=t(o,e,n,a)}),i}function t(e){var t=s.createElement("script");t.src=e,t.defer=!0,s.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",i=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){s.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+g.toString()+"("+[JSON.stringify(i),f.toString(),p.toString(),u.toString()].join(",")+"));",a=new Blob([e],{type:"text/javascript"}),r=new Worker(URL.createObjectURL(a),{name:"wpTestEmojiSupports"});return void(r.onmessage=function(e){c(n=e.data),r.terminate(),t(n)})}catch(e){}c(n=g(i,f,p,u))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);
</script>
  <link rel="stylesheet" id="woosb-blocks-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/blocks.css" media="all">
  <link rel="stylesheet" id="astra-theme-css-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/style.min_002.css" media="all">
  <style id="astra-theme-css-inline-css">
:root{--ast-post-nav-space:0;--ast-container-default-xlg-padding:6.67em;--ast-container-default-lg-padding:5.67em;--ast-container-default-slg-padding:4.34em;--ast-container-default-md-padding:3.34em;--ast-container-default-sm-padding:6.67em;--ast-container-default-xs-padding:2.4em;--ast-container-default-xxs-padding:1.4em;--ast-code-block-background:#EEEEEE;--ast-comment-inputs-background:#FAFAFA;--ast-normal-container-width:1230px;--ast-narrow-container-width:750px;--ast-blog-title-font-weight:normal;--ast-blog-meta-weight:inherit;--ast-global-color-primary:var(--ast-global-color-5);--ast-global-color-secondary:var(--ast-global-color-4);--ast-global-color-alternate-background:var(--ast-global-color-7);--ast-global-color-subtle-background:var(--ast-global-color-6);--ast-bg-style-guide:#F8FAFC;--ast-shadow-style-guide:0px 0px 4px 0 #00000057;--ast-global-dark-bg-style:#fff;--ast-global-dark-lfs:#fbfbfb;--ast-widget-bg-color:#fafafa;--ast-wc-container-head-bg-color:#fbfbfb;--ast-title-layout-bg:#eeeeee;--ast-search-border-color:#e7e7e7;--ast-lifter-hover-bg:#e6e6e6;--ast-gallery-block-color:#000;--srfm-color-input-label:var(--ast-global-color-2);}html{font-size:112.5%;}a,.page-title{color:var(--ast-global-color-0);}a:hover,a:focus{color:var(--ast-global-color-1);}body,button,input,select,textarea,.ast-button,.ast-custom-button{font-family:'Comfortaa',display;font-weight:400;font-size:18px;font-size:1rem;line-height:var(--ast-body-line-height,2em);}blockquote{color:var(--ast-global-color-3);}p,.entry-content p{margin-bottom:1.4em;}h1,h2,h3,h4,h5,h6,.entry-content :where(h1,h2,h3,h4,h5,h6),.site-title,.site-title a{font-weight:500;line-height:2em;}.ast-site-identity .site-title a{color:var(--ast-global-color-2);}.site-title{font-size:35px;font-size:1.9444444444444rem;display:none;}.site-header .site-description{font-size:15px;font-size:0.83333333333333rem;display:none;}.entry-title{font-size:26px;font-size:1.4444444444444rem;}.archive .ast-article-post .ast-article-inner,.blog .ast-article-post .ast-article-inner,.archive .ast-article-post .ast-article-inner:hover,.blog .ast-article-post .ast-article-inner:hover{overflow:hidden;}h1,.entry-content :where(h1){font-size:35px;font-size:1.9444444444444rem;font-weight:700;font-family:'Comfortaa',display;line-height:2.5em;}h2,.entry-content :where(h2){font-size:32px;font-size:1.7777777777778rem;font-weight:500;font-family:'Comfortaa',display;line-height:2.5em;}h3,.entry-content :where(h3){font-size:22px;font-size:1.2222222222222rem;font-weight:400;font-family:'Comfortaa',display;line-height:2.5em;}h4,.entry-content :where(h4){font-size:20px;font-size:1.1111111111111rem;line-height:3.5em;font-weight:400;font-family:'Comfortaa',display;text-decoration:initial;}h5,.entry-content :where(h5){font-size:18px;font-size:1rem;line-height:1.2em;}h6,.entry-content :where(h6){font-size:16px;font-size:0.88888888888889rem;line-height:1.25em;}::selection{background-color:var(--ast-global-color-0);color:#000000;}body,h1,h2,h3,h4,h5,h6,.entry-title a,.entry-content :where(h1,h2,h3,h4,h5,h6){color:var(--ast-global-color-3);}.tagcloud a:hover,.tagcloud a:focus,.tagcloud a.current-item{color:#000000;border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);}input:focus,input[type="text"]:focus,input[type="email"]:focus,input[type="url"]:focus,input[type="password"]:focus,input[type="reset"]:focus,input[type="search"]:focus,textarea:focus{border-color:var(--ast-global-color-0);}input[type="radio"]:checked,input[type=reset],input[type="checkbox"]:checked,input[type="checkbox"]:hover:checked,input[type="checkbox"]:focus:checked,input[type=range]::-webkit-slider-thumb{border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);box-shadow:none;}.site-footer a:hover + .post-count,.site-footer a:focus + .post-count{background:var(--ast-global-color-0);border-color:var(--ast-global-color-0);}.single .nav-links .nav-previous,.single .nav-links .nav-next{color:var(--ast-global-color-0);}.entry-meta,.entry-meta *{line-height:1.45;color:var(--ast-global-color-0);}.entry-meta a:not(.ast-button):hover,.entry-meta a:not(.ast-button):hover *,.entry-meta a:not(.ast-button):focus,.entry-meta a:not(.ast-button):focus *,.page-links > .page-link,.page-links .page-link:hover,.post-navigation a:hover{color:var(--ast-global-color-1);}#cat option,.secondary .calendar_wrap thead a,.secondary .calendar_wrap thead a:visited{color:var(--ast-global-color-0);}.secondary .calendar_wrap #today,.ast-progress-val span{background:var(--ast-global-color-0);}.secondary a:hover + .post-count,.secondary a:focus + .post-count{background:var(--ast-global-color-0);border-color:var(--ast-global-color-0);}.calendar_wrap #today > a{color:#000000;}.page-links .page-link,.single .post-navigation a{color:var(--ast-global-color-0);}.ast-search-menu-icon .search-form button.search-submit{padding:0 4px;}.ast-search-menu-icon form.search-form{padding-right:0;}.ast-header-search .ast-search-menu-icon.ast-dropdown-active .search-form,.ast-header-search .ast-search-menu-icon.ast-dropdown-active .search-field:focus{transition:all 0.2s;}.search-form input.search-field:focus{outline:none;}.wp-block-latest-posts > li > a{color:var(--ast-global-color-5);}.widget-title,.widget .wp-block-heading{font-size:25px;font-size:1.3888888888889rem;color:var(--ast-global-color-3);}.ast-search-menu-icon.slide-search a:focus-visible:focus-visible,.astra-search-icon:focus-visible,#close:focus-visible,a:focus-visible,.ast-menu-toggle:focus-visible,.site .skip-link:focus-visible,.wp-block-loginout input:focus-visible,.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper,.ast-header-navigation-arrow:focus-visible,.woocommerce .wc-proceed-to-checkout > .checkout-button:focus-visible,.woocommerce .woocommerce-MyAccount-navigation ul li a:focus-visible,.ast-orders-table__row .ast-orders-table__cell:focus-visible,.woocommerce .woocommerce-order-details .order-again > .button:focus-visible,.woocommerce .woocommerce-message a.button.wc-forward:focus-visible,.woocommerce #minus_qty:focus-visible,.woocommerce #plus_qty:focus-visible,a#ast-apply-coupon:focus-visible,.woocommerce .woocommerce-info a:focus-visible,.woocommerce .astra-shop-summary-wrap a:focus-visible,.woocommerce a.wc-forward:focus-visible,#ast-apply-coupon:focus-visible,.woocommerce-js .woocommerce-mini-cart-item a.remove:focus-visible,#close:focus-visible,.button.search-submit:focus-visible,#search_submit:focus,.normal-search:focus-visible,.ast-header-account-wrap:focus-visible,.woocommerce .ast-on-card-button.ast-quick-view-trigger:focus,.astra-cart-drawer-close:focus,.ast-single-variation:focus,.ast-woocommerce-product-gallery__image:focus,.ast-button:focus,.woocommerce-product-gallery--with-images [data-controls="prev"]:focus-visible,.woocommerce-product-gallery--with-images [data-controls="next"]:focus-visible{outline-style:dotted;outline-color:inherit;outline-width:thin;}input:focus,input[type="text"]:focus,input[type="email"]:focus,input[type="url"]:focus,input[type="password"]:focus,input[type="reset"]:focus,input[type="search"]:focus,input[type="number"]:focus,textarea:focus,.wp-block-search__input:focus,[data-section="section-header-mobile-trigger"] .ast-button-wrap .ast-mobile-menu-trigger-minimal:focus,.ast-mobile-popup-drawer.active .menu-toggle-close:focus,.woocommerce-ordering select.orderby:focus,#ast-scroll-top:focus,#coupon_code:focus,.woocommerce-page #comment:focus,.woocommerce #reviews #respond input#submit:focus,.woocommerce a.add_to_cart_button:focus,.woocommerce .button.single_add_to_cart_button:focus,.woocommerce .woocommerce-cart-form button:focus,.woocommerce .woocommerce-cart-form__cart-item .quantity .qty:focus,.woocommerce .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper .woocommerce-input-wrapper > .input-text:focus,.woocommerce #order_comments:focus,.woocommerce #place_order:focus,.woocommerce .woocommerce-address-fields .woocommerce-address-fields__field-wrapper .woocommerce-input-wrapper > .input-text:focus,.woocommerce .woocommerce-MyAccount-content form button:focus,.woocommerce .woocommerce-MyAccount-content .woocommerce-EditAccountForm .woocommerce-form-row .woocommerce-Input.input-text:focus,.woocommerce .ast-woocommerce-container .woocommerce-pagination ul.page-numbers li a:focus,body #content .woocommerce form .form-row .select2-container--default .select2-selection--single:focus,#ast-coupon-code:focus,.woocommerce.woocommerce-js .quantity input[type=number]:focus,.woocommerce-js .woocommerce-mini-cart-item .quantity input[type=number]:focus,.woocommerce p#ast-coupon-trigger:focus{border-style:dotted;border-color:inherit;border-width:thin;}input{outline:none;}.woocommerce-js input[type=text]:focus,.woocommerce-js input[type=email]:focus,.woocommerce-js textarea:focus,input[type=number]:focus,.comments-area textarea#comment:focus,.comments-area textarea#comment:active,.comments-area .ast-comment-formwrap input[type="text"]:focus,.comments-area .ast-comment-formwrap input[type="text"]:active{outline-style:unset;outline-color:inherit;outline-width:thin;}.main-header-menu .menu-link,.ast-header-custom-item a{color:var(--ast-global-color-3);}.main-header-menu .menu-item:hover > .menu-link,.main-header-menu .menu-item:hover > .ast-menu-toggle,.main-header-menu .ast-masthead-custom-menu-items a:hover,.main-header-menu .menu-item.focus > .menu-link,.main-header-menu .menu-item.focus > .ast-menu-toggle,.main-header-menu .current-menu-item > .menu-link,.main-header-menu .current-menu-ancestor > .menu-link,.main-header-menu .current-menu-item > .ast-menu-toggle,.main-header-menu .current-menu-ancestor > .ast-menu-toggle{color:var(--ast-global-color-0);}.header-main-layout-3 .ast-main-header-bar-alignment{margin-right:auto;}.header-main-layout-2 .site-header-section-left .ast-site-identity{text-align:left;}.ast-logo-title-inline .site-logo-img{padding-right:1em;}.site-logo-img img{ transition:all 0.2s linear;}body .ast-oembed-container *{position:absolute;top:0;width:100%;height:100%;left:0;}body .wp-block-embed-pocket-casts .ast-oembed-container *{position:unset;}.ast-header-break-point .ast-mobile-menu-buttons-minimal.menu-toggle{background:transparent;color:var(--ast-global-color-0);}.ast-header-break-point .ast-mobile-menu-buttons-outline.menu-toggle{background:transparent;border:1px solid var(--ast-global-color-0);color:var(--ast-global-color-0);}.ast-header-break-point .ast-mobile-menu-buttons-fill.menu-toggle{background:var(--ast-global-color-0);}.ast-single-post-featured-section + article {margin-top: 2em;}.site-content .ast-single-post-featured-section img {width: 100%;overflow: hidden;object-fit: cover;}.site > .ast-single-related-posts-container {margin-top: 0;}@media (min-width: 922px) {.ast-desktop .ast-container--narrow {max-width: var(--ast-narrow-container-width);margin: 0 auto;}}.ast-page-builder-template .hentry {margin: 0;}.ast-page-builder-template .site-content > .ast-container {max-width: 100%;padding: 0;}.ast-page-builder-template .site .site-content #primary {padding: 0;margin: 0;}.ast-page-builder-template .no-results {text-align: center;margin: 4em auto;}.ast-page-builder-template .ast-pagination {padding: 2em;}.ast-page-builder-template .entry-header.ast-no-title.ast-no-thumbnail {margin-top: 0;}.ast-page-builder-template .entry-header.ast-header-without-markup {margin-top: 0;margin-bottom: 0;}.ast-page-builder-template .entry-header.ast-no-title.ast-no-meta {margin-bottom: 0;}.ast-page-builder-template.single .post-navigation {padding-bottom: 2em;}.ast-page-builder-template.single-post .site-content > .ast-container {max-width: 100%;}.ast-page-builder-template .entry-header {margin-top: 4em;margin-left: auto;margin-right: auto;padding-left: 20px;padding-right: 20px;}.single.ast-page-builder-template .entry-header {padding-left: 20px;padding-right: 20px;}.ast-page-builder-template .ast-archive-description {margin: 4em auto 0;padding-left: 20px;padding-right: 20px;}.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide {margin-left: 0;margin-right: 0;}.footer-adv .footer-adv-overlay{border-top-style:solid;border-top-color:#7a7a7a;}@media( max-width: 420px ) {.single .nav-links .nav-previous,.single .nav-links .nav-next {width: 100%;text-align: center;}}.wp-block-buttons.aligncenter{justify-content:center;}@media (max-width:921px){.ast-theme-transparent-header #primary,.ast-theme-transparent-header #secondary{padding:0;}}@media (max-width:921px){.ast-plain-container.ast-no-sidebar #primary{padding:0;}}.ast-plain-container.ast-no-sidebar #primary{margin-top:0;margin-bottom:0;}.wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button,.ast-outline-button,.wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button{border-top-width:2px;border-right-width:2px;border-bottom-width:2px;border-left-width:2px;font-family:inherit;font-weight:inherit;line-height:1em;}.wp-block-button .wp-block-button__link.wp-element-button.is-style-outline:not(.has-background),.wp-block-button.is-style-outline>.wp-block-button__link.wp-element-button:not(.has-background),.ast-outline-button{background-color:transparent;}.entry-content[data-ast-blocks-layout] > figure{margin-bottom:1em;}.elementor-widget-container .elementor-loop-container .e-loop-item[data-elementor-type="loop-item"]{width:100%;}.review-rating{display:flex;align-items:center;order:2;}@media (max-width:921px){.ast-left-sidebar #content > .ast-container{display:flex;flex-direction:column-reverse;width:100%;}.ast-separate-container .ast-article-post,.ast-separate-container .ast-article-single{padding:1.5em 2.14em;}.ast-author-box img.avatar{margin:20px 0 0 0;}}@media (min-width:922px){.ast-separate-container.ast-right-sidebar #primary,.ast-separate-container.ast-left-sidebar #primary{border:0;}.search-no-results.ast-separate-container #primary{margin-bottom:4em;}}.elementor-widget-button .elementor-button{border-style:solid;text-decoration:none;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;}body .elementor-button.elementor-size-sm,body .elementor-button.elementor-size-xs,body .elementor-button.elementor-size-md,body .elementor-button.elementor-size-lg,body .elementor-button.elementor-size-xl,body .elementor-button{padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;}.elementor-widget-button .elementor-button{border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);}.elementor-widget-button .elementor-button:hover,.elementor-widget-button .elementor-button:focus{color:#000000;background-color:var(--ast-global-color-1);border-color:var(--ast-global-color-1);}.wp-block-button .wp-block-button__link ,.elementor-widget-button .elementor-button,.elementor-widget-button .elementor-button:visited{color:#000000;}.elementor-widget-button .elementor-button{line-height:1em;}.wp-block-button .wp-block-button__link:hover,.wp-block-button .wp-block-button__link:focus{color:#000000;background-color:var(--ast-global-color-1);border-color:var(--ast-global-color-1);}.elementor-widget-heading h1.elementor-heading-title{line-height:2.5em;}.elementor-widget-heading h2.elementor-heading-title{line-height:2.5em;}.elementor-widget-heading h3.elementor-heading-title{line-height:2.5em;}.elementor-widget-heading h4.elementor-heading-title{line-height:3.5em;}.elementor-widget-heading h5.elementor-heading-title{line-height:1.2em;}.elementor-widget-heading h6.elementor-heading-title{line-height:1.25em;}.wp-block-button .wp-block-button__link,.wp-block-search .wp-block-search__button,body .wp-block-file .wp-block-file__button{border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);color:#000000;font-family:inherit;font-weight:inherit;line-height:1em;padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;}.menu-toggle,button,.ast-button,.ast-custom-button,.button,input#submit,input[type="button"],input[type="submit"],input[type="reset"],form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button,body .wp-block-file .wp-block-file__button{border-style:solid;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;color:#000000;border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;font-family:inherit;font-weight:inherit;line-height:1em;}button:focus,.menu-toggle:hover,button:hover,.ast-button:hover,.ast-custom-button:hover .button:hover,.ast-custom-button:hover ,input[type=reset]:hover,input[type=reset]:focus,input#submit:hover,input#submit:focus,input[type="button"]:hover,input[type="button"]:focus,input[type="submit"]:hover,input[type="submit"]:focus,form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button:hover,form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button:focus,body .wp-block-file .wp-block-file__button:hover,body .wp-block-file .wp-block-file__button:focus{color:#000000;background-color:var(--ast-global-color-1);border-color:var(--ast-global-color-1);}@media (max-width:921px){.ast-mobile-header-stack .main-header-bar .ast-search-menu-icon{display:inline-block;}.ast-header-break-point.ast-header-custom-item-outside .ast-mobile-header-stack .main-header-bar .ast-search-icon{margin:0;}.ast-comment-avatar-wrap img{max-width:2.5em;}.ast-comment-meta{padding:0 1.8888em 1.3333em;}.ast-separate-container .ast-comment-list li.depth-1{padding:1.5em 2.14em;}.ast-separate-container .comment-respond{padding:2em 2.14em;}}@media (min-width:544px){.ast-container{max-width:100%;}}@media (max-width:544px){.ast-separate-container .ast-article-post,.ast-separate-container .ast-article-single,.ast-separate-container .comments-title,.ast-separate-container .ast-archive-description{padding:1.5em 1em;}.ast-separate-container #content .ast-container{padding-left:0.54em;padding-right:0.54em;}.ast-separate-container .ast-comment-list .bypostauthor{padding:.5em;}.ast-search-menu-icon.ast-dropdown-active .search-field{width:170px;}.site-branding img,.site-header .site-logo-img .custom-logo-link img{max-width:100%;}} #ast-mobile-header .ast-site-header-cart-li a{pointer-events:none;}body,.ast-separate-container{background-color:#2f2d2e;background-image:none;}@media (max-width:921px){.widget-title{font-size:25px;font-size:1.3888888888889rem;}body,button,input,select,textarea,.ast-button,.ast-custom-button{font-size:18px;font-size:1rem;}#secondary,#secondary button,#secondary input,#secondary select,#secondary textarea{font-size:18px;font-size:1rem;}.site-title{display:none;}.site-header .site-description{display:none;}h1,.entry-content :where(h1){font-size:30px;}h2,.entry-content :where(h2){font-size:25px;}h3,.entry-content :where(h3){font-size:20px;}}@media (max-width:544px){.site-title{display:none;}.site-header .site-description{display:none;}h1,.entry-content :where(h1){font-size:30px;}h2,.entry-content :where(h2){font-size:25px;}h3,.entry-content :where(h3){font-size:20px;}}@media (max-width:544px){html{font-size:102.6%;}}@media (min-width:922px){.ast-container{max-width:1270px;}}@font-face {font-family: "Astra";src: url(https://codecut.ai/wp-content/themes/astra/assets/fonts/astra.woff) format("woff"),url(https://codecut.ai/wp-content/themes/astra/assets/fonts/astra.ttf) format("truetype"),url(https://codecut.ai/wp-content/themes/astra/assets/fonts/astra.svg#astra) format("svg");font-weight: normal;font-style: normal;font-display: fallback;}@media (max-width:10px) {.main-header-bar .main-header-bar-navigation{display:none;}}.ast-desktop .main-header-menu.submenu-with-border .sub-menu,.ast-desktop .main-header-menu.submenu-with-border .astra-full-megamenu-wrapper{border-color:var(--ast-global-color-0);}.ast-desktop .main-header-menu.submenu-with-border .sub-menu{border-top-width:2px;border-right-width:2px;border-left-width:2px;border-bottom-width:2px;border-style:solid;}.ast-desktop .main-header-menu.submenu-with-border .sub-menu .sub-menu{top:-2px;}.ast-desktop .main-header-menu.submenu-with-border .sub-menu .menu-link,.ast-desktop .main-header-menu.submenu-with-border .children .menu-link{border-bottom-width:1px;border-style:solid;border-color:#eaeaea;}@media (min-width:922px){.main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu:hover > .sub-menu,.main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu.focus > .sub-menu{margin-left:-4px;}}.ast-small-footer{border-top-style:solid;border-top-width:1px;border-top-color:#7a7a7a;}.ast-small-footer-wrap{text-align:center;}.site .comments-area{padding-bottom:3em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .main-header-bar-navigation .ast-search-icon {display: none;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-form {padding: 0;display: block;overflow: hidden;}.ast-header-break-point .ast-header-custom-item .widget:last-child {margin-bottom: 1em;}.ast-header-custom-item .widget {margin: 0.5em;display: inline-block;vertical-align: middle;}.ast-header-custom-item .widget p {margin-bottom: 0;}.ast-header-custom-item .widget li {width: auto;}.ast-header-custom-item-inside .button-custom-menu-item .menu-link {display: none;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item .ast-custom-button-link {display: none;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item .menu-link {display: block;}.ast-header-break-point.ast-header-custom-item-outside .main-header-bar .ast-search-icon {margin-right: 1em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-field,.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon.ast-inline-search .search-field {width: 100%;padding-right: 5.5em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-submit {display: block;position: absolute;height: 100%;top: 0;right: 0;padding: 0 1em;border-radius: 0;}.ast-header-break-point .ast-header-custom-item .ast-masthead-custom-menu-items {padding-left: 20px;padding-right: 20px;margin-bottom: 1em;margin-top: 1em;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item {padding-left: 0;padding-right: 0;margin-top: 0;margin-bottom: 0;}.astra-icon-down_arrow::after {content: "\e900";font-family: Astra;}.astra-icon-close::after {content: "\e5cd";font-family: Astra;}.astra-icon-drag_handle::after {content: "\e25d";font-family: Astra;}.astra-icon-format_align_justify::after {content: "\e235";font-family: Astra;}.astra-icon-menu::after {content: "\e5d2";font-family: Astra;}.astra-icon-reorder::after {content: "\e8fe";font-family: Astra;}.astra-icon-search::after {content: "\e8b6";font-family: Astra;}.astra-icon-zoom_in::after {content: "\e56b";font-family: Astra;}.astra-icon-check-circle::after {content: "\e901";font-family: Astra;}.astra-icon-shopping-cart::after {content: "\f07a";font-family: Astra;}.astra-icon-shopping-bag::after {content: "\f290";font-family: Astra;}.astra-icon-shopping-basket::after {content: "\f291";font-family: Astra;}.astra-icon-circle-o::after {content: "\e903";font-family: Astra;}.astra-icon-certificate::after {content: "\e902";font-family: Astra;}.wp-block-file {display: flex;align-items: center;flex-wrap: wrap;justify-content: space-between;}.wp-block-pullquote {border: none;}.wp-block-pullquote blockquote::before {content: "\201D";font-family: "Helvetica",sans-serif;display: flex;transform: rotate( 180deg );font-size: 6rem;font-style: normal;line-height: 1;font-weight: bold;align-items: center;justify-content: center;}.has-text-align-right > blockquote::before {justify-content: flex-start;}.has-text-align-left > blockquote::before {justify-content: flex-end;}figure.wp-block-pullquote.is-style-solid-color blockquote {max-width: 100%;text-align: inherit;}:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 3em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 3em;--wp--custom--ast-container-width: 1230px;--wp--custom--ast-content-width-size: 910px;--wp--custom--ast-wide-width-size: 1230px;}.ast-narrow-container {--wp--custom--ast-content-width-size: 750px;--wp--custom--ast-wide-width-size: 750px;}@media(max-width: 921px) {:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 2em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 2em;}}@media(max-width: 544px) {:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 1.5em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 1.5em;}}.entry-content > .wp-block-group,.entry-content > .wp-block-cover,.entry-content > .wp-block-columns {padding-top: var(--wp--custom--ast-default-block-top-padding);padding-right: var(--wp--custom--ast-default-block-right-padding);padding-bottom: var(--wp--custom--ast-default-block-bottom-padding);padding-left: var(--wp--custom--ast-default-block-left-padding);}.ast-plain-container.ast-no-sidebar .entry-content .alignfull,.ast-page-builder-template .ast-no-sidebar .entry-content .alignfull {margin-left: calc( -50vw + 50%);margin-right: calc( -50vw + 50%);max-width: 100vw;width: 100vw;}.ast-plain-container.ast-no-sidebar .entry-content .alignfull .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .alignfull .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .alignfull .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .alignfull .alignwide,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide .alignwide,.ast-plain-container.ast-no-sidebar .entry-content .wp-block-column .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-column .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .wp-block-column .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-column .alignwide {margin-left: auto;margin-right: auto;width: 100%;}[data-ast-blocks-layout] .wp-block-separator:not(.is-style-dots) {height: 0;}[data-ast-blocks-layout] .wp-block-separator {margin: 20px auto;}[data-ast-blocks-layout] .wp-block-separator:not(.is-style-wide):not(.is-style-dots) {max-width: 100px;}[data-ast-blocks-layout] .wp-block-separator.has-background {padding: 0;}.entry-content[data-ast-blocks-layout] > * {max-width: var(--wp--custom--ast-content-width-size);margin-left: auto;margin-right: auto;}.entry-content[data-ast-blocks-layout] > .alignwide,.entry-content[data-ast-blocks-layout] .wp-block-cover__inner-container,.entry-content[data-ast-blocks-layout] > p {max-width: var(--wp--custom--ast-wide-width-size);}.entry-content[data-ast-blocks-layout] .alignfull {max-width: none;}.entry-content .wp-block-columns {margin-bottom: 0;}blockquote {margin: 1.5em;border-color: rgba(0,0,0,0.05);}.wp-block-quote:not(.has-text-align-right):not(.has-text-align-center) {border-left: 5px solid rgba(0,0,0,0.05);}.has-text-align-right > blockquote,blockquote.has-text-align-right {border-right: 5px solid rgba(0,0,0,0.05);}.has-text-align-left > blockquote,blockquote.has-text-align-left {border-left: 5px solid rgba(0,0,0,0.05);}.wp-block-site-tagline,.wp-block-latest-posts .read-more {margin-top: 15px;}.wp-block-loginout p label {display: block;}.wp-block-loginout p:not(.login-remember):not(.login-submit) input {width: 100%;}.wp-block-loginout input:focus {border-color: transparent;}.wp-block-loginout input:focus {outline: thin dotted;}.entry-content .wp-block-media-text .wp-block-media-text__content {padding: 0 0 0 8%;}.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {padding: 0 8% 0 0;}.entry-content .wp-block-media-text.has-background .wp-block-media-text__content {padding: 8%;}.entry-content .wp-block-cover:not([class*="background-color"]):not(.has-text-color.has-link-color) .wp-block-cover__inner-container,.entry-content .wp-block-cover:not([class*="background-color"]) .wp-block-cover-image-text,.entry-content .wp-block-cover:not([class*="background-color"]) .wp-block-cover-text,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover__inner-container,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover-image-text,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover-text {color: var(--ast-global-color-primary,var(--ast-global-color-5));}.wp-block-loginout .login-remember input {width: 1.1rem;height: 1.1rem;margin: 0 5px 4px 0;vertical-align: middle;}.wp-block-latest-posts > li > *:first-child,.wp-block-latest-posts:not(.is-grid) > li:first-child {margin-top: 0;}.entry-content > .wp-block-buttons,.entry-content > .wp-block-uagb-buttons {margin-bottom: 1.5em;}.wp-block-latest-posts > li > a {font-size: 28px;}.wp-block-latest-posts > li > *,.wp-block-latest-posts:not(.is-grid) > li {margin-top: 15px;margin-bottom: 15px;}.wp-block-latest-posts .wp-block-latest-posts__post-date,.wp-block-latest-posts .wp-block-latest-posts__post-author {font-size: 15px;}@media (max-width:544px){.wp-block-columns .wp-block-column:not(:last-child){margin-bottom:20px;}.wp-block-latest-posts{margin:0;}}@media( max-width: 600px ) {.entry-content .wp-block-media-text .wp-block-media-text__content,.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {padding: 8% 0 0;}.entry-content .wp-block-media-text.has-background .wp-block-media-text__content {padding: 8%;}}.ast-narrow-container .site-content .wp-block-uagb-image--align-full .wp-block-uagb-image__figure {max-width: 100%;margin-left: auto;margin-right: auto;}:root .has-ast-global-color-0-color{color:var(--ast-global-color-0);}:root .has-ast-global-color-0-background-color{background-color:var(--ast-global-color-0);}:root .wp-block-button .has-ast-global-color-0-color{color:var(--ast-global-color-0);}:root .wp-block-button .has-ast-global-color-0-background-color{background-color:var(--ast-global-color-0);}:root .has-ast-global-color-1-color{color:var(--ast-global-color-1);}:root .has-ast-global-color-1-background-color{background-color:var(--ast-global-color-1);}:root .wp-block-button .has-ast-global-color-1-color{color:var(--ast-global-color-1);}:root .wp-block-button .has-ast-global-color-1-background-color{background-color:var(--ast-global-color-1);}:root .has-ast-global-color-2-color{color:var(--ast-global-color-2);}:root .has-ast-global-color-2-background-color{background-color:var(--ast-global-color-2);}:root .wp-block-button .has-ast-global-color-2-color{color:var(--ast-global-color-2);}:root .wp-block-button .has-ast-global-color-2-background-color{background-color:var(--ast-global-color-2);}:root .has-ast-global-color-3-color{color:var(--ast-global-color-3);}:root .has-ast-global-color-3-background-color{background-color:var(--ast-global-color-3);}:root .wp-block-button .has-ast-global-color-3-color{color:var(--ast-global-color-3);}:root .wp-block-button .has-ast-global-color-3-background-color{background-color:var(--ast-global-color-3);}:root .has-ast-global-color-4-color{color:var(--ast-global-color-4);}:root .has-ast-global-color-4-background-color{background-color:var(--ast-global-color-4);}:root .wp-block-button .has-ast-global-color-4-color{color:var(--ast-global-color-4);}:root .wp-block-button .has-ast-global-color-4-background-color{background-color:var(--ast-global-color-4);}:root .has-ast-global-color-5-color{color:var(--ast-global-color-5);}:root .has-ast-global-color-5-background-color{background-color:var(--ast-global-color-5);}:root .wp-block-button .has-ast-global-color-5-color{color:var(--ast-global-color-5);}:root .wp-block-button .has-ast-global-color-5-background-color{background-color:var(--ast-global-color-5);}:root .has-ast-global-color-6-color{color:var(--ast-global-color-6);}:root .has-ast-global-color-6-background-color{background-color:var(--ast-global-color-6);}:root .wp-block-button .has-ast-global-color-6-color{color:var(--ast-global-color-6);}:root .wp-block-button .has-ast-global-color-6-background-color{background-color:var(--ast-global-color-6);}:root .has-ast-global-color-7-color{color:var(--ast-global-color-7);}:root .has-ast-global-color-7-background-color{background-color:var(--ast-global-color-7);}:root .wp-block-button .has-ast-global-color-7-color{color:var(--ast-global-color-7);}:root .wp-block-button .has-ast-global-color-7-background-color{background-color:var(--ast-global-color-7);}:root .has-ast-global-color-8-color{color:var(--ast-global-color-8);}:root .has-ast-global-color-8-background-color{background-color:var(--ast-global-color-8);}:root .wp-block-button .has-ast-global-color-8-color{color:var(--ast-global-color-8);}:root .wp-block-button .has-ast-global-color-8-background-color{background-color:var(--ast-global-color-8);}:root{--ast-global-color-0:#72befa;--ast-global-color-1:#e583b6;--ast-global-color-2:#1e293b;--ast-global-color-3:#c9c8c8;--ast-global-color-4:#ffffff;--ast-global-color-5:#ffffff;--ast-global-color-6:#2f2d2e;--ast-global-color-7:#2f2d2e;--ast-global-color-8:#94a3b8;}:root {--ast-border-color : #dddddd;}#masthead .ast-container,.ast-header-breadcrumb .ast-container{max-width:100%;padding-left:35px;padding-right:35px;}@media (max-width:10px){#masthead .ast-container,.ast-header-breadcrumb .ast-container{padding-left:20px;padding-right:20px;}}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .main-header-bar-navigation .ast-search-icon {display: none;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-form {padding: 0;display: block;overflow: hidden;}.ast-header-break-point .ast-header-custom-item .widget:last-child {margin-bottom: 1em;}.ast-header-custom-item .widget {margin: 0.5em;display: inline-block;vertical-align: middle;}.ast-header-custom-item .widget p {margin-bottom: 0;}.ast-header-custom-item .widget li {width: auto;}.ast-header-custom-item-inside .button-custom-menu-item .menu-link {display: none;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item .ast-custom-button-link {display: none;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item .menu-link {display: block;}.ast-header-break-point.ast-header-custom-item-outside .main-header-bar .ast-search-icon {margin-right: 1em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-field,.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon.ast-inline-search .search-field {width: 100%;padding-right: 5.5em;}.ast-header-break-point.ast-header-custom-item-inside .main-header-bar .ast-search-menu-icon .search-submit {display: block;position: absolute;height: 100%;top: 0;right: 0;padding: 0 1em;border-radius: 0;}.ast-header-break-point .ast-header-custom-item .ast-masthead-custom-menu-items {padding-left: 20px;padding-right: 20px;margin-bottom: 1em;margin-top: 1em;}.ast-header-custom-item-inside.ast-header-break-point .button-custom-menu-item {padding-left: 0;padding-right: 0;margin-top: 0;margin-bottom: 0;}.astra-icon-down_arrow::after {content: "\e900";font-family: Astra;}.astra-icon-close::after {content: "\e5cd";font-family: Astra;}.astra-icon-drag_handle::after {content: "\e25d";font-family: Astra;}.astra-icon-format_align_justify::after {content: "\e235";font-family: Astra;}.astra-icon-menu::after {content: "\e5d2";font-family: Astra;}.astra-icon-reorder::after {content: "\e8fe";font-family: Astra;}.astra-icon-search::after {content: "\e8b6";font-family: Astra;}.astra-icon-zoom_in::after {content: "\e56b";font-family: Astra;}.astra-icon-check-circle::after {content: "\e901";font-family: Astra;}.astra-icon-shopping-cart::after {content: "\f07a";font-family: Astra;}.astra-icon-shopping-bag::after {content: "\f290";font-family: Astra;}.astra-icon-shopping-basket::after {content: "\f291";font-family: Astra;}.astra-icon-circle-o::after {content: "\e903";font-family: Astra;}.astra-icon-certificate::after {content: "\e902";font-family: Astra;}.wp-block-file {display: flex;align-items: center;flex-wrap: wrap;justify-content: space-between;}.wp-block-pullquote {border: none;}.wp-block-pullquote blockquote::before {content: "\201D";font-family: "Helvetica",sans-serif;display: flex;transform: rotate( 180deg );font-size: 6rem;font-style: normal;line-height: 1;font-weight: bold;align-items: center;justify-content: center;}.has-text-align-right > blockquote::before {justify-content: flex-start;}.has-text-align-left > blockquote::before {justify-content: flex-end;}figure.wp-block-pullquote.is-style-solid-color blockquote {max-width: 100%;text-align: inherit;}:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 3em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 3em;--wp--custom--ast-container-width: 1230px;--wp--custom--ast-content-width-size: 910px;--wp--custom--ast-wide-width-size: 1230px;}.ast-narrow-container {--wp--custom--ast-content-width-size: 750px;--wp--custom--ast-wide-width-size: 750px;}@media(max-width: 921px) {:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 2em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 2em;}}@media(max-width: 544px) {:root {--wp--custom--ast-default-block-top-padding: 3em;--wp--custom--ast-default-block-right-padding: 1.5em;--wp--custom--ast-default-block-bottom-padding: 3em;--wp--custom--ast-default-block-left-padding: 1.5em;}}.entry-content > .wp-block-group,.entry-content > .wp-block-cover,.entry-content > .wp-block-columns {padding-top: var(--wp--custom--ast-default-block-top-padding);padding-right: var(--wp--custom--ast-default-block-right-padding);padding-bottom: var(--wp--custom--ast-default-block-bottom-padding);padding-left: var(--wp--custom--ast-default-block-left-padding);}.ast-plain-container.ast-no-sidebar .entry-content .alignfull,.ast-page-builder-template .ast-no-sidebar .entry-content .alignfull {margin-left: calc( -50vw + 50%);margin-right: calc( -50vw + 50%);max-width: 100vw;width: 100vw;}.ast-plain-container.ast-no-sidebar .entry-content .alignfull .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .alignfull .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .alignfull .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .alignfull .alignwide,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .alignwide .alignwide,.ast-plain-container.ast-no-sidebar .entry-content .wp-block-column .alignfull,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-column .alignfull,.ast-plain-container.ast-no-sidebar .entry-content .wp-block-column .alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-column .alignwide {margin-left: auto;margin-right: auto;width: 100%;}[data-ast-blocks-layout] .wp-block-separator:not(.is-style-dots) {height: 0;}[data-ast-blocks-layout] .wp-block-separator {margin: 20px auto;}[data-ast-blocks-layout] .wp-block-separator:not(.is-style-wide):not(.is-style-dots) {max-width: 100px;}[data-ast-blocks-layout] .wp-block-separator.has-background {padding: 0;}.entry-content[data-ast-blocks-layout] > * {max-width: var(--wp--custom--ast-content-width-size);margin-left: auto;margin-right: auto;}.entry-content[data-ast-blocks-layout] > .alignwide,.entry-content[data-ast-blocks-layout] .wp-block-cover__inner-container,.entry-content[data-ast-blocks-layout] > p {max-width: var(--wp--custom--ast-wide-width-size);}.entry-content[data-ast-blocks-layout] .alignfull {max-width: none;}.entry-content .wp-block-columns {margin-bottom: 0;}blockquote {margin: 1.5em;border-color: rgba(0,0,0,0.05);}.wp-block-quote:not(.has-text-align-right):not(.has-text-align-center) {border-left: 5px solid rgba(0,0,0,0.05);}.has-text-align-right > blockquote,blockquote.has-text-align-right {border-right: 5px solid rgba(0,0,0,0.05);}.has-text-align-left > blockquote,blockquote.has-text-align-left {border-left: 5px solid rgba(0,0,0,0.05);}.wp-block-site-tagline,.wp-block-latest-posts .read-more {margin-top: 15px;}.wp-block-loginout p label {display: block;}.wp-block-loginout p:not(.login-remember):not(.login-submit) input {width: 100%;}.wp-block-loginout input:focus {border-color: transparent;}.wp-block-loginout input:focus {outline: thin dotted;}.entry-content .wp-block-media-text .wp-block-media-text__content {padding: 0 0 0 8%;}.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {padding: 0 8% 0 0;}.entry-content .wp-block-media-text.has-background .wp-block-media-text__content {padding: 8%;}.entry-content .wp-block-cover:not([class*="background-color"]):not(.has-text-color.has-link-color) .wp-block-cover__inner-container,.entry-content .wp-block-cover:not([class*="background-color"]) .wp-block-cover-image-text,.entry-content .wp-block-cover:not([class*="background-color"]) .wp-block-cover-text,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover__inner-container,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover-image-text,.entry-content .wp-block-cover-image:not([class*="background-color"]) .wp-block-cover-text {color: var(--ast-global-color-primary,var(--ast-global-color-5));}.wp-block-loginout .login-remember input {width: 1.1rem;height: 1.1rem;margin: 0 5px 4px 0;vertical-align: middle;}.wp-block-latest-posts > li > *:first-child,.wp-block-latest-posts:not(.is-grid) > li:first-child {margin-top: 0;}.entry-content > .wp-block-buttons,.entry-content > .wp-block-uagb-buttons {margin-bottom: 1.5em;}.wp-block-latest-posts > li > a {font-size: 28px;}.wp-block-latest-posts > li > *,.wp-block-latest-posts:not(.is-grid) > li {margin-top: 15px;margin-bottom: 15px;}.wp-block-latest-posts .wp-block-latest-posts__post-date,.wp-block-latest-posts .wp-block-latest-posts__post-author {font-size: 15px;}@media (max-width:544px){.wp-block-columns .wp-block-column:not(:last-child){margin-bottom:20px;}.wp-block-latest-posts{margin:0;}}@media( max-width: 600px ) {.entry-content .wp-block-media-text .wp-block-media-text__content,.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {padding: 8% 0 0;}.entry-content .wp-block-media-text.has-background .wp-block-media-text__content {padding: 8%;}}.ast-narrow-container .site-content .wp-block-uagb-image--align-full .wp-block-uagb-image__figure {max-width: 100%;margin-left: auto;margin-right: auto;}:root .has-ast-global-color-0-color{color:var(--ast-global-color-0);}:root .has-ast-global-color-0-background-color{background-color:var(--ast-global-color-0);}:root .wp-block-button .has-ast-global-color-0-color{color:var(--ast-global-color-0);}:root .wp-block-button .has-ast-global-color-0-background-color{background-color:var(--ast-global-color-0);}:root .has-ast-global-color-1-color{color:var(--ast-global-color-1);}:root .has-ast-global-color-1-background-color{background-color:var(--ast-global-color-1);}:root .wp-block-button .has-ast-global-color-1-color{color:var(--ast-global-color-1);}:root .wp-block-button .has-ast-global-color-1-background-color{background-color:var(--ast-global-color-1);}:root .has-ast-global-color-2-color{color:var(--ast-global-color-2);}:root .has-ast-global-color-2-background-color{background-color:var(--ast-global-color-2);}:root .wp-block-button .has-ast-global-color-2-color{color:var(--ast-global-color-2);}:root .wp-block-button .has-ast-global-color-2-background-color{background-color:var(--ast-global-color-2);}:root .has-ast-global-color-3-color{color:var(--ast-global-color-3);}:root .has-ast-global-color-3-background-color{background-color:var(--ast-global-color-3);}:root .wp-block-button .has-ast-global-color-3-color{color:var(--ast-global-color-3);}:root .wp-block-button .has-ast-global-color-3-background-color{background-color:var(--ast-global-color-3);}:root .has-ast-global-color-4-color{color:var(--ast-global-color-4);}:root .has-ast-global-color-4-background-color{background-color:var(--ast-global-color-4);}:root .wp-block-button .has-ast-global-color-4-color{color:var(--ast-global-color-4);}:root .wp-block-button .has-ast-global-color-4-background-color{background-color:var(--ast-global-color-4);}:root .has-ast-global-color-5-color{color:var(--ast-global-color-5);}:root .has-ast-global-color-5-background-color{background-color:var(--ast-global-color-5);}:root .wp-block-button .has-ast-global-color-5-color{color:var(--ast-global-color-5);}:root .wp-block-button .has-ast-global-color-5-background-color{background-color:var(--ast-global-color-5);}:root .has-ast-global-color-6-color{color:var(--ast-global-color-6);}:root .has-ast-global-color-6-background-color{background-color:var(--ast-global-color-6);}:root .wp-block-button .has-ast-global-color-6-color{color:var(--ast-global-color-6);}:root .wp-block-button .has-ast-global-color-6-background-color{background-color:var(--ast-global-color-6);}:root .has-ast-global-color-7-color{color:var(--ast-global-color-7);}:root .has-ast-global-color-7-background-color{background-color:var(--ast-global-color-7);}:root .wp-block-button .has-ast-global-color-7-color{color:var(--ast-global-color-7);}:root .wp-block-button .has-ast-global-color-7-background-color{background-color:var(--ast-global-color-7);}:root .has-ast-global-color-8-color{color:var(--ast-global-color-8);}:root .has-ast-global-color-8-background-color{background-color:var(--ast-global-color-8);}:root .wp-block-button .has-ast-global-color-8-color{color:var(--ast-global-color-8);}:root .wp-block-button .has-ast-global-color-8-background-color{background-color:var(--ast-global-color-8);}:root{--ast-global-color-0:#72befa;--ast-global-color-1:#e583b6;--ast-global-color-2:#1e293b;--ast-global-color-3:#c9c8c8;--ast-global-color-4:#ffffff;--ast-global-color-5:#ffffff;--ast-global-color-6:#2f2d2e;--ast-global-color-7:#2f2d2e;--ast-global-color-8:#94a3b8;}:root {--ast-border-color : #dddddd;}#masthead .ast-container,.ast-header-breadcrumb .ast-container{max-width:100%;padding-left:35px;padding-right:35px;}@media (max-width:10px){#masthead .ast-container,.ast-header-breadcrumb .ast-container{padding-left:20px;padding-right:20px;}}.ast-single-entry-banner {-js-display: flex;display: flex;flex-direction: column;justify-content: center;text-align: center;position: relative;background: var(--ast-title-layout-bg);}.ast-single-entry-banner[data-banner-layout="layout-1"] {max-width: 1230px;background: inherit;padding: 20px 0;}.ast-single-entry-banner[data-banner-width-type="custom"] {margin: 0 auto;width: 100%;}.ast-single-entry-banner + .site-content .entry-header {margin-bottom: 0;}.site .ast-author-avatar {--ast-author-avatar-size: ;}a.ast-underline-text {text-decoration: underline;}.ast-container > .ast-terms-link {position: relative;display: block;}a.ast-button.ast-badge-tax {padding: 4px 8px;border-radius: 3px;font-size: inherit;}header.entry-header{text-align:left;}header.entry-header > *:not(:last-child){margin-bottom:10px;}@media (max-width:921px){header.entry-header{text-align:left;}}@media (max-width:544px){header.entry-header{text-align:left;}}.ast-archive-entry-banner {-js-display: flex;display: flex;flex-direction: column;justify-content: center;text-align: center;position: relative;background: var(--ast-title-layout-bg);}.ast-archive-entry-banner[data-banner-width-type="custom"] {margin: 0 auto;width: 100%;}.ast-archive-entry-banner[data-banner-layout="layout-1"] {background: inherit;padding: 20px 0;text-align: left;}body.archive .ast-archive-description{max-width:1230px;width:100%;text-align:left;padding-top:3em;padding-right:3em;padding-bottom:3em;padding-left:3em;}body.archive .ast-archive-description .ast-archive-title,body.archive .ast-archive-description .ast-archive-title *{font-size:40px;font-size:2.2222222222222rem;}body.archive .ast-archive-description > *:not(:last-child){margin-bottom:10px;}@media (max-width:921px){body.archive .ast-archive-description{text-align:left;}}@media (max-width:544px){body.archive .ast-archive-description{text-align:left;}}.ast-breadcrumbs .trail-browse,.ast-breadcrumbs .trail-items,.ast-breadcrumbs .trail-items li{display:inline-block;margin:0;padding:0;border:none;background:inherit;text-indent:0;text-decoration:none;}.ast-breadcrumbs .trail-browse{font-size:inherit;font-style:inherit;font-weight:inherit;color:inherit;}.ast-breadcrumbs .trail-items{list-style:none;}.trail-items li::after{padding:0 0.3em;content:"\00bb";}.trail-items li:last-of-type::after{display:none;}h1,h2,h3,h4,h5,h6,.entry-content :where(h1,h2,h3,h4,h5,h6){color:var(--ast-global-color-5);}.elementor-posts-container [CLASS*="ast-width-"]{width:100%;}.elementor-template-full-width .ast-container{display:block;}.elementor-screen-only,.screen-reader-text,.screen-reader-text span,.ui-helper-hidden-accessible{top:0 !important;}@media (max-width:544px){.elementor-element .elementor-wc-products .woocommerce[class*="columns-"] ul.products li.product{width:auto;margin:0;}.elementor-element .woocommerce .woocommerce-result-count{float:none;}}.ast-header-break-point .main-header-bar{border-bottom-width:1px;}@media (min-width:922px){.main-header-bar{border-bottom-width:1px;}}.main-header-menu .menu-item, #astra-footer-menu .menu-item, .main-header-bar .ast-masthead-custom-menu-items{-js-display:flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;}.main-header-menu > .menu-item > .menu-link, #astra-footer-menu > .menu-item > .menu-link{height:100%;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-js-display:flex;display:flex;}.ast-primary-menu-disabled .main-header-bar .ast-masthead-custom-menu-items{flex:unset;}.main-header-menu .sub-menu .menu-item.menu-item-has-children > .menu-link:after{position:absolute;right:1em;top:50%;transform:translate(0,-50%) rotate(270deg);}.ast-header-break-point .main-header-bar .main-header-bar-navigation .page_item_has_children > .ast-menu-toggle::before, .ast-header-break-point .main-header-bar .main-header-bar-navigation .menu-item-has-children > .ast-menu-toggle::before, .ast-mobile-popup-drawer .main-header-bar-navigation .menu-item-has-children>.ast-menu-toggle::before, .ast-header-break-point .ast-mobile-header-wrap .main-header-bar-navigation .menu-item-has-children > .ast-menu-toggle::before{font-weight:bold;content:"\e900";font-family:Astra;text-decoration:inherit;display:inline-block;}.ast-header-break-point .main-navigation ul.sub-menu .menu-item .menu-link:before{content:"\e900";font-family:Astra;font-size:.65em;text-decoration:inherit;display:inline-block;transform:translate(0, -2px) rotateZ(270deg);margin-right:5px;}.widget_search .search-form:after{font-family:Astra;font-size:1.2em;font-weight:normal;content:"\e8b6";position:absolute;top:50%;right:15px;transform:translate(0, -50%);}.astra-search-icon::before{content:"\e8b6";font-family:Astra;font-style:normal;font-weight:normal;text-decoration:inherit;text-align:center;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;z-index:3;}.main-header-bar .main-header-bar-navigation .page_item_has_children > a:after, .main-header-bar .main-header-bar-navigation .menu-item-has-children > a:after, .menu-item-has-children .ast-header-navigation-arrow:after{content:"\e900";display:inline-block;font-family:Astra;font-size:.6rem;font-weight:bold;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;margin-left:10px;line-height:normal;}.menu-item-has-children .sub-menu .ast-header-navigation-arrow:after{margin-left:0;}.ast-mobile-popup-drawer .main-header-bar-navigation .ast-submenu-expanded>.ast-menu-toggle::before{transform:rotateX(180deg);}.ast-header-break-point .main-header-bar-navigation .menu-item-has-children > .menu-link:after{display:none;}@media (min-width:922px){.ast-builder-menu .main-navigation > ul > li:last-child a{margin-right:0;}}.ast-separate-container .ast-article-inner{background-color:transparent;background-image:none;}.ast-separate-container .ast-article-post{background-color:#2f2d2e;background-image:none;}@media (max-width:921px){.ast-separate-container .ast-article-post{background-color:var(--ast-global-color-5);background-image:none;}}@media (max-width:544px){.ast-separate-container .ast-article-post{background-color:var(--ast-global-color-5);background-image:none;}}.ast-separate-container .ast-article-single:not(.ast-related-post), .woocommerce.ast-separate-container .ast-woocommerce-container, .ast-separate-container .error-404, .ast-separate-container .no-results, .single.ast-separate-container  .ast-author-meta, .ast-separate-container .related-posts-title-wrapper, .ast-separate-container .comments-count-wrapper, .ast-box-layout.ast-plain-container .site-content, .ast-padded-layout.ast-plain-container .site-content, .ast-separate-container .ast-archive-description, .ast-separate-container .comments-area .comment-respond, .ast-separate-container .comments-area .ast-comment-list li, .ast-separate-container .comments-area .comments-title{background-color:#2f2d2e;background-image:none;}@media (max-width:921px){.ast-separate-container .ast-article-single:not(.ast-related-post), .woocommerce.ast-separate-container .ast-woocommerce-container, .ast-separate-container .error-404, .ast-separate-container .no-results, .single.ast-separate-container  .ast-author-meta, .ast-separate-container .related-posts-title-wrapper, .ast-separate-container .comments-count-wrapper, .ast-box-layout.ast-plain-container .site-content, .ast-padded-layout.ast-plain-container .site-content, .ast-separate-container .ast-archive-description{background-color:var(--ast-global-color-5);background-image:none;}}@media (max-width:544px){.ast-separate-container .ast-article-single:not(.ast-related-post), .woocommerce.ast-separate-container .ast-woocommerce-container, .ast-separate-container .error-404, .ast-separate-container .no-results, .single.ast-separate-container  .ast-author-meta, .ast-separate-container .related-posts-title-wrapper, .ast-separate-container .comments-count-wrapper, .ast-box-layout.ast-plain-container .site-content, .ast-padded-layout.ast-plain-container .site-content, .ast-separate-container .ast-archive-description{background-color:var(--ast-global-color-5);background-image:none;}}.ast-separate-container.ast-two-container #secondary .widget{background-color:#2f2d2e;background-image:none;}@media (max-width:921px){.ast-separate-container.ast-two-container #secondary .widget{background-color:var(--ast-global-color-5);background-image:none;}}@media (max-width:544px){.ast-separate-container.ast-two-container #secondary .widget{background-color:var(--ast-global-color-5);background-image:none;}}
		#ast-scroll-top {
			display: none;
			position: fixed;
			text-align: center;
			cursor: pointer;
			z-index: 99;
			width: 2.1em;
			height: 2.1em;
			line-height: 2.1;
			color: #ffffff;
			border-radius: 2px;
			content: "";
			outline: inherit;
		}
		@media (min-width: 769px) {
			#ast-scroll-top {
				content: "769";
			}
		}
		#ast-scroll-top .ast-icon.icon-arrow svg {
			margin-left: 0px;
			vertical-align: middle;
			transform: translate(0, -20%) rotate(180deg);
			width: 1.6em;
		}
		.ast-scroll-to-top-right {
			right: 30px;
			bottom: 30px;
		}
		.ast-scroll-to-top-left {
			left: 30px;
			bottom: 30px;
		}
	#ast-scroll-top{background-color:#e583b6;font-size:15px;}.ast-scroll-top-icon::before{content:"\e900";font-family:Astra;text-decoration:inherit;}.ast-scroll-top-icon{transform:rotate(180deg);}@media (max-width:921px){#ast-scroll-top .ast-icon.icon-arrow svg{width:1em;}}:root{--e-global-color-astglobalcolor0:#72befa;--e-global-color-astglobalcolor1:#e583b6;--e-global-color-astglobalcolor2:#1e293b;--e-global-color-astglobalcolor3:#c9c8c8;--e-global-color-astglobalcolor4:#ffffff;--e-global-color-astglobalcolor5:#ffffff;--e-global-color-astglobalcolor6:#2f2d2e;--e-global-color-astglobalcolor7:#2f2d2e;--e-global-color-astglobalcolor8:#94a3b8;}.comment-reply-title{font-size:29px;font-size:1.6111111111111rem;}.ast-comment-meta{line-height:1.666666667;color:var(--ast-global-color-0);font-size:15px;font-size:0.83333333333333rem;}.ast-comment-list #cancel-comment-reply-link{font-size:18px;font-size:1rem;}.comments-count-wrapper {padding: 2em 0;}.comments-count-wrapper .comments-title {font-weight: normal;word-wrap: break-word;}.ast-comment-list {margin: 0;word-wrap: break-word;padding-bottom: 0.5em;list-style: none;}.site-content article .comments-area {border-top: 1px solid var(--ast-single-post-border,var(--ast-border-color));}.ast-comment-list li {list-style: none;}.ast-comment-list li.depth-1 .ast-comment,.ast-comment-list li.depth-2 .ast-comment {border-bottom: 1px solid #eeeeee;}.ast-comment-list .comment-respond {padding: 1em 0;border-bottom: 1px solid #dddddd;}.ast-comment-list .comment-respond .comment-reply-title {margin-top: 0;padding-top: 0;}.ast-comment-list .comment-respond p {margin-bottom: .5em;}.ast-comment-list .ast-comment-edit-reply-wrap {-js-display: flex;display: flex;justify-content: flex-end;}.ast-comment-list .ast-edit-link {flex: 1;}.ast-comment-list .comment-awaiting-moderation {margin-bottom: 0;}.ast-comment {padding: 1em 0 ;}.ast-comment-avatar-wrap img {border-radius: 50%;}.ast-comment-content {clear: both;}.ast-comment-cite-wrap {text-align: left;}.ast-comment-cite-wrap cite {font-style: normal;}.comment-reply-title {padding-top: 1em;font-weight: normal;line-height: 1.65;}.ast-comment-meta {margin-bottom: 0.5em;}.comments-area {border-top: 1px solid #eeeeee;margin-top: 2em;}.comments-area .comment-form-comment {width: 100%;border: none;margin: 0;padding: 0;}.comments-area .comment-notes,.comments-area .comment-textarea,.comments-area .form-allowed-tags {margin-bottom: 1.5em;}.comments-area .form-submit {margin-bottom: 0;}.comments-area textarea#comment,.comments-area .ast-comment-formwrap input[type="text"] {width: 100%;border-radius: 0;vertical-align: middle;margin-bottom: 10px;}.comments-area .no-comments {margin-top: 0.5em;margin-bottom: 0.5em;}.comments-area p.logged-in-as {margin-bottom: 1em;}.ast-separate-container .comments-count-wrapper {background-color: #fff;padding: 2em 6.67em 0;}@media (max-width: 1200px) {.ast-separate-container .comments-count-wrapper {padding: 2em 3.34em;}}.ast-separate-container .comments-area {border-top: 0;}.ast-separate-container .ast-comment-list {padding-bottom: 0;}.ast-separate-container .ast-comment-list li {background-color: #fff;}.ast-separate-container .ast-comment-list li.depth-1 .children li {padding-bottom: 0;padding-top: 0;margin-bottom: 0;}.ast-separate-container .ast-comment-list li.depth-1 .ast-comment,.ast-separate-container .ast-comment-list li.depth-2 .ast-comment {border-bottom: 0;}.ast-separate-container .ast-comment-list .comment-respond {padding-top: 0;padding-bottom: 1em;background-color: transparent;}.ast-separate-container .ast-comment-list .pingback p {margin-bottom: 0;}.ast-separate-container .ast-comment-list .bypostauthor {padding: 2em;margin-bottom: 1em;}.ast-separate-container .ast-comment-list .bypostauthor li {background: transparent;margin-bottom: 0;padding: 0 0 0 2em;}.ast-separate-container .comment-reply-title {padding-top: 0;}.comment-content a {word-wrap: break-word;}.comment-form-legend {margin-bottom: unset;padding: 0 0.5em;}.ast-separate-container .ast-comment-list li.depth-1 {padding: 4em 6.67em;margin-bottom: 2em;}@media (max-width: 1200px) {.ast-separate-container .ast-comment-list li.depth-1 {padding: 3em 3.34em;}}.ast-separate-container .comment-respond {background-color: #fff;padding: 4em 6.67em;border-bottom: 0;}@media (max-width: 1200px) {.ast-separate-container .comment-respond {padding: 3em 2.34em;}}.ast-comment-list .children {margin-left: 2em;}@media (max-width: 992px) {.ast-comment-list .children {margin-left: 1em;}}.ast-comment-list #cancel-comment-reply-link {white-space: nowrap;font-size: 15px;font-size: 1rem;margin-left: 1em;}.ast-comment-avatar-wrap {float: left;clear: right;margin-right: 1.33333em;}.ast-comment-meta-wrap {float: left;clear: right;padding: 0 0 1.33333em;}.ast-comment-time .timendate,.ast-comment-time .reply {margin-right: 0.5em;}.comments-area #wp-comment-cookies-consent {margin-right: 10px;}.ast-page-builder-template .comments-area {padding-left: 20px;padding-right: 20px;margin-top: 0;margin-bottom: 2em;}.ast-separate-container .ast-comment-list .bypostauthor .bypostauthor {background: transparent;margin-bottom: 0;padding-right: 0;padding-bottom: 0;padding-top: 0;}@media (min-width:922px){.ast-separate-container .ast-comment-list li .comment-respond{padding-left:2.66666em;padding-right:2.66666em;}}@media (max-width:544px){.ast-separate-container .comments-count-wrapper{padding:1.5em 1em;}.ast-separate-container .ast-comment-list li.depth-1{padding:1.5em 1em;margin-bottom:1.5em;}.ast-separate-container .ast-comment-list .bypostauthor{padding:.5em;}.ast-separate-container .comment-respond{padding:1.5em 1em;}.ast-separate-container .ast-comment-list .bypostauthor li{padding:0 0 0 .5em;}.ast-comment-list .children{margin-left:0.66666em;}}@media (max-width:921px){.ast-comment-avatar-wrap img{max-width:2.5em;}.comments-area{margin-top:1.5em;}.ast-separate-container .comments-count-wrapper{padding:2em 2.14em;}.ast-separate-container .ast-comment-list li.depth-1{padding:1.5em 2.14em;}.ast-separate-container .comment-respond{padding:2em 2.14em;}.ast-comment-meta{font-size:15px;font-size:0.83333333333333rem;}.comment-reply-title{font-size:29px;font-size:1.6111111111111rem;}.ast-comment-list #cancel-comment-reply-link{font-size:18px;font-size:1rem;}}@media (max-width:921px){.ast-comment-avatar-wrap{margin-right:0.5em;}}
</style>
  <link rel="stylesheet" id="astra-google-fonts-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/css.css" media="all">
  <link rel="stylesheet" id="astra-menu-animation-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/menu-animation.min.css" media="all">
  <link rel="stylesheet" id="dashicons-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/dashicons.min.css" media="all">
  <link rel="stylesheet" id="menu-icons-extra-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/extra.min.css" media="all">
  <style id="wp-emoji-styles-inline-css">

	img.wp-smiley, img.emoji {
		display: inline !important;
		border: none !important;
		box-shadow: none !important;
		height: 1em !important;
		width: 1em !important;
		margin: 0 0.07em !important;
		vertical-align: -0.1em !important;
		background: none !important;
		padding: 0 !important;
	}
</style>
  <link rel="stylesheet" id="wp-block-library-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/style.min.css" media="all">
  <link rel="stylesheet" id="prismatic-blocks-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/styles-blocks.css" media="all">
  <link rel="stylesheet" id="ht-youtube-embed-css-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/ht-youtube-embed.css" media="all">
  <link rel="stylesheet" id="cr-frontend-css-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/frontend_002.css" media="all">
  <link rel="stylesheet" id="cr-badges-css-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/badges.css" media="all">
  <link rel="stylesheet" id="ab_test_styles-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/experiment-frontend.css" media="all">
  <style id="global-styles-inline-css">
:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--color--ast-global-color-0: var(--ast-global-color-0);--wp--preset--color--ast-global-color-1: var(--ast-global-color-1);--wp--preset--color--ast-global-color-2: var(--ast-global-color-2);--wp--preset--color--ast-global-color-3: var(--ast-global-color-3);--wp--preset--color--ast-global-color-4: var(--ast-global-color-4);--wp--preset--color--ast-global-color-5: var(--ast-global-color-5);--wp--preset--color--ast-global-color-6: var(--ast-global-color-6);--wp--preset--color--ast-global-color-7: var(--ast-global-color-7);--wp--preset--color--ast-global-color-8: var(--ast-global-color-8);--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:root { --wp--style--global--content-size: var(--wp--custom--ast-content-width-size);--wp--style--global--wide-size: var(--wp--custom--ast-wide-width-size); }:where(body) { margin: 0; }.wp-site-blocks > .alignleft { float: left; margin-right: 2em; }.wp-site-blocks > .alignright { float: right; margin-left: 2em; }.wp-site-blocks > .aligncenter { justify-content: center; margin-left: auto; margin-right: auto; }:where(.wp-site-blocks) > * { margin-block-start: 24px; margin-block-end: 0; }:where(.wp-site-blocks) > :first-child { margin-block-start: 0; }:where(.wp-site-blocks) > :last-child { margin-block-end: 0; }:root { --wp--style--block-gap: 24px; }:root :where(.is-layout-flow) > :first-child{margin-block-start: 0;}:root :where(.is-layout-flow) > :last-child{margin-block-end: 0;}:root :where(.is-layout-flow) > *{margin-block-start: 24px;margin-block-end: 0;}:root :where(.is-layout-constrained) > :first-child{margin-block-start: 0;}:root :where(.is-layout-constrained) > :last-child{margin-block-end: 0;}:root :where(.is-layout-constrained) > *{margin-block-start: 24px;margin-block-end: 0;}:root :where(.is-layout-flex){gap: 24px;}:root :where(.is-layout-grid){gap: 24px;}.is-layout-flow > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}.is-layout-flow > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}.is-layout-flow > .aligncenter{margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}.is-layout-constrained > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}.is-layout-constrained > .aligncenter{margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull)){max-width: var(--wp--style--global--content-size);margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > .alignwide{max-width: var(--wp--style--global--wide-size);}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}body{padding-top: 0px;padding-right: 0px;padding-bottom: 0px;padding-left: 0px;}a:where(:not(.wp-element-button)){text-decoration: none;}:root :where(.wp-element-button, .wp-block-button__link){background-color: #32373c;border-width: 0;color: #fff;font-family: inherit;font-size: inherit;line-height: inherit;padding: calc(0.667em + 2px) calc(1.333em + 2px);text-decoration: none;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-ast-global-color-0-color{color: var(--wp--preset--color--ast-global-color-0) !important;}.has-ast-global-color-1-color{color: var(--wp--preset--color--ast-global-color-1) !important;}.has-ast-global-color-2-color{color: var(--wp--preset--color--ast-global-color-2) !important;}.has-ast-global-color-3-color{color: var(--wp--preset--color--ast-global-color-3) !important;}.has-ast-global-color-4-color{color: var(--wp--preset--color--ast-global-color-4) !important;}.has-ast-global-color-5-color{color: var(--wp--preset--color--ast-global-color-5) !important;}.has-ast-global-color-6-color{color: var(--wp--preset--color--ast-global-color-6) !important;}.has-ast-global-color-7-color{color: var(--wp--preset--color--ast-global-color-7) !important;}.has-ast-global-color-8-color{color: var(--wp--preset--color--ast-global-color-8) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-ast-global-color-0-background-color{background-color: var(--wp--preset--color--ast-global-color-0) !important;}.has-ast-global-color-1-background-color{background-color: var(--wp--preset--color--ast-global-color-1) !important;}.has-ast-global-color-2-background-color{background-color: var(--wp--preset--color--ast-global-color-2) !important;}.has-ast-global-color-3-background-color{background-color: var(--wp--preset--color--ast-global-color-3) !important;}.has-ast-global-color-4-background-color{background-color: var(--wp--preset--color--ast-global-color-4) !important;}.has-ast-global-color-5-background-color{background-color: var(--wp--preset--color--ast-global-color-5) !important;}.has-ast-global-color-6-background-color{background-color: var(--wp--preset--color--ast-global-color-6) !important;}.has-ast-global-color-7-background-color{background-color: var(--wp--preset--color--ast-global-color-7) !important;}.has-ast-global-color-8-background-color{background-color: var(--wp--preset--color--ast-global-color-8) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-ast-global-color-0-border-color{border-color: var(--wp--preset--color--ast-global-color-0) !important;}.has-ast-global-color-1-border-color{border-color: var(--wp--preset--color--ast-global-color-1) !important;}.has-ast-global-color-2-border-color{border-color: var(--wp--preset--color--ast-global-color-2) !important;}.has-ast-global-color-3-border-color{border-color: var(--wp--preset--color--ast-global-color-3) !important;}.has-ast-global-color-4-border-color{border-color: var(--wp--preset--color--ast-global-color-4) !important;}.has-ast-global-color-5-border-color{border-color: var(--wp--preset--color--ast-global-color-5) !important;}.has-ast-global-color-6-border-color{border-color: var(--wp--preset--color--ast-global-color-6) !important;}.has-ast-global-color-7-border-color{border-color: var(--wp--preset--color--ast-global-color-7) !important;}.has-ast-global-color-8-border-color{border-color: var(--wp--preset--color--ast-global-color-8) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}
</style>
  <link rel="stylesheet" id="prismatic-prism-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/theme-okaidia.css" media="all">
  <link rel="stylesheet" id="prismatic-plugin-styles-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/plugin-styles.css" media="all">
  <link rel="stylesheet" id="woocommerce-layout-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/woocommerce-layout.min.css" media="all">
  <link rel="stylesheet" id="woocommerce-smallscreen-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/woocommerce-smallscreen.min.css" media="only screen and (max-width: 921px)">
  <link rel="stylesheet" id="woocommerce-general-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/woocommerce.min.css" media="all">
  <style id="woocommerce-general-inline-css">

					.woocommerce .woocommerce-result-count, .woocommerce-page .woocommerce-result-count {
						float: left;
					}

					.woocommerce .woocommerce-ordering {
						float: right;
						margin-bottom: 2.5em;
					}
				
					.woocommerce-js a.button, .woocommerce button.button, .woocommerce input.button, .woocommerce #respond input#submit {
						font-size: 100%;
						line-height: 1;
						text-decoration: none;
						overflow: visible;
						padding: 0.5em 0.75em;
						font-weight: 700;
						border-radius: 3px;
						color: $secondarytext;
						background-color: $secondary;
						border: 0;
					}
					.woocommerce-js a.button:hover, .woocommerce button.button:hover, .woocommerce input.button:hover, .woocommerce #respond input#submit:hover {
						background-color: #dad8da;
						background-image: none;
						color: #515151;
					}
				#customer_details h3:not(.elementor-widget-woocommerce-checkout-page h3){font-size:1.2rem;padding:20px 0 14px;margin:0 0 20px;border-bottom:1px solid var(--ast-border-color);font-weight:700;}form #order_review_heading:not(.elementor-widget-woocommerce-checkout-page #order_review_heading){border-width:2px 2px 0 2px;border-style:solid;font-size:1.2rem;margin:0;padding:1.5em 1.5em 1em;border-color:var(--ast-border-color);font-weight:700;}.woocommerce-Address h3, .cart-collaterals h2{font-size:1.2rem;padding:.7em 1em;}.woocommerce-cart .cart-collaterals .cart_totals>h2{font-weight:700;}form #order_review:not(.elementor-widget-woocommerce-checkout-page #order_review){padding:0 2em;border-width:0 2px 2px;border-style:solid;border-color:var(--ast-border-color);}ul#shipping_method li:not(.elementor-widget-woocommerce-cart #shipping_method li){margin:0;padding:0.25em 0 0.25em 22px;text-indent:-22px;list-style:none outside;}.woocommerce span.onsale, .wc-block-grid__product .wc-block-grid__product-onsale{background-color:var(--ast-global-color-0);color:#000000;}.woocommerce-message, .woocommerce-info{border-top-color:var(--ast-global-color-0);}.woocommerce-message::before,.woocommerce-info::before{color:var(--ast-global-color-0);}.woocommerce ul.products li.product .price, .woocommerce div.product p.price, .woocommerce div.product span.price, .widget_layered_nav_filters ul li.chosen a, .woocommerce-page ul.products li.product .ast-woo-product-category, .wc-layered-nav-rating a{color:var(--ast-global-color-3);}.woocommerce nav.woocommerce-pagination ul,.woocommerce nav.woocommerce-pagination ul li{border-color:var(--ast-global-color-0);}.woocommerce nav.woocommerce-pagination ul li a:focus, .woocommerce nav.woocommerce-pagination ul li a:hover, .woocommerce nav.woocommerce-pagination ul li span.current{background:var(--ast-global-color-0);color:#000000;}.woocommerce-MyAccount-navigation-link.is-active a{color:var(--ast-global-color-1);}.woocommerce .widget_price_filter .ui-slider .ui-slider-range, .woocommerce .widget_price_filter .ui-slider .ui-slider-handle{background-color:var(--ast-global-color-0);}.woocommerce .star-rating, .woocommerce .comment-form-rating .stars a, .woocommerce .star-rating::before{color:var(--ast-global-color-3);}.woocommerce div.product .woocommerce-tabs ul.tabs li.active:before,  .woocommerce div.ast-product-tabs-layout-vertical .woocommerce-tabs ul.tabs li:hover::before{background:var(--ast-global-color-0);}.ast-site-header-cart a{color:var(--ast-global-color-3);}.ast-site-header-cart a:focus, .ast-site-header-cart a:hover, .ast-site-header-cart .current-menu-item a{color:var(--ast-global-color-0);}.ast-cart-menu-wrap .count, .ast-cart-menu-wrap .count:after{border-color:var(--ast-global-color-0);color:var(--ast-global-color-0);}.ast-cart-menu-wrap:hover .count{color:#000000;background-color:var(--ast-global-color-0);}.ast-site-header-cart .widget_shopping_cart .total .woocommerce-Price-amount{color:var(--ast-global-color-0);}.woocommerce a.remove:hover, .ast-woocommerce-cart-menu .main-header-menu .woocommerce-custom-menu-item .menu-item:hover > .menu-link.remove:hover{color:var(--ast-global-color-0);border-color:var(--ast-global-color-0);background-color:#ffffff;}.ast-site-header-cart .widget_shopping_cart .buttons .button.checkout, .woocommerce .widget_shopping_cart .woocommerce-mini-cart__buttons .checkout.wc-forward{color:#000000;border-color:var(--ast-global-color-1);background-color:var(--ast-global-color-1);}.site-header .ast-site-header-cart-data .button.wc-forward, .site-header .ast-site-header-cart-data .button.wc-forward:hover{color:#000000;}.below-header-user-select .ast-site-header-cart .widget, .ast-above-header-section .ast-site-header-cart .widget a, .below-header-user-select .ast-site-header-cart .widget_shopping_cart a{color:var(--ast-global-color-3);}.below-header-user-select .ast-site-header-cart .widget_shopping_cart a:hover, .ast-above-header-section .ast-site-header-cart .widget_shopping_cart a:hover, .below-header-user-select .ast-site-header-cart .widget_shopping_cart a.remove:hover, .ast-above-header-section .ast-site-header-cart .widget_shopping_cart a.remove:hover{color:var(--ast-global-color-0);}.woocommerce .woocommerce-cart-form button[name="update_cart"]:disabled{color:#000000;}.woocommerce #content table.cart .button[name="apply_coupon"], .woocommerce-page #content table.cart .button[name="apply_coupon"]{padding:10px 40px;}.woocommerce table.cart td.actions .button, .woocommerce #content table.cart td.actions .button, .woocommerce-page table.cart td.actions .button, .woocommerce-page #content table.cart td.actions .button{line-height:1;border-width:1px;border-style:solid;}.woocommerce ul.products li.product .button, .woocommerce-page ul.products li.product .button{line-height:1.3;}.woocommerce-js a.button, .woocommerce button.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce-js a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled], .woocommerce input.button:disabled:hover, .woocommerce input.button:disabled[disabled]:hover, .woocommerce #respond input#submit, .woocommerce button.button.alt.disabled, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link, .wc-block-grid__product-onsale{color:#000000;border-color:var(--ast-global-color-0);background-color:var(--ast-global-color-0);}.woocommerce-js a.button:hover, .woocommerce button.button:hover, .woocommerce .woocommerce-message a.button:hover,.woocommerce #respond input#submit:hover,.woocommerce #respond input#submit.alt:hover, .woocommerce-js a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover, .woocommerce input.button:hover, .woocommerce button.button.alt.disabled:hover, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link:hover{color:#000000;border-color:var(--ast-global-color-1);background-color:var(--ast-global-color-1);}.woocommerce-js a.button, .woocommerce button.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce-js a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce-cart table.cart td.actions .button, .woocommerce form.checkout_coupon .button, .woocommerce #respond input#submit, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link{padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;}.woocommerce ul.products li.product a, .woocommerce-js a.button:hover, .woocommerce button.button:hover, .woocommerce input.button:hover, .woocommerce #respond input#submit:hover{text-decoration:none;}.woocommerce .up-sells h2, .woocommerce .related.products h2, .woocommerce .woocommerce-tabs h2{font-size:1.5rem;}.woocommerce h2, .woocommerce-account h2{font-size:1.625rem;}.woocommerce ul.product-categories > li ul li:before{content:"\e900";padding:0 5px 0 5px;display:inline-block;font-family:Astra;transform:rotate(-90deg);font-size:0.7rem;}.ast-site-header-cart i.astra-icon:before{font-family:Astra;}.ast-icon-shopping-cart:before{content:"\f07a";}.ast-icon-shopping-bag:before{content:"\f290";}.ast-icon-shopping-basket:before{content:"\f291";}.ast-icon-shopping-cart svg{height:.82em;}.ast-icon-shopping-bag svg{height:1em;width:1em;}.ast-icon-shopping-basket svg{height:1.15em;width:1.2em;}.ast-site-header-cart.ast-menu-cart-outline .ast-addon-cart-wrap, .ast-site-header-cart.ast-menu-cart-fill .ast-addon-cart-wrap {line-height:1;}.ast-site-header-cart.ast-menu-cart-fill i.astra-icon{ font-size:1.1em;}li.woocommerce-custom-menu-item .ast-site-header-cart i.astra-icon:after{ padding-left:2px;}.ast-hfb-header .ast-addon-cart-wrap{ padding:0.4em;}.ast-header-break-point.ast-header-custom-item-outside .ast-woo-header-cart-info-wrap{ display:none;}.ast-site-header-cart i.astra-icon:after{ background:var(--ast-global-color-0);}@media (min-width:545px) and (max-width:921px){.woocommerce.tablet-columns-6 ul.products li.product, .woocommerce-page.tablet-columns-6 ul.products li.product{width:calc(16.66% - 16.66px);}.woocommerce.tablet-columns-5 ul.products li.product, .woocommerce-page.tablet-columns-5 ul.products li.product{width:calc(20% - 16px);}.woocommerce.tablet-columns-4 ul.products li.product, .woocommerce-page.tablet-columns-4 ul.products li.product{width:calc(25% - 15px);}.woocommerce.tablet-columns-3 ul.products li.product, .woocommerce-page.tablet-columns-3 ul.products li.product{width:calc(33.33% - 14px);}.woocommerce.tablet-columns-2 ul.products li.product, .woocommerce-page.tablet-columns-2 ul.products li.product{width:calc(50% - 10px);}.woocommerce.tablet-columns-1 ul.products li.product, .woocommerce-page.tablet-columns-1 ul.products li.product{width:100%;}.woocommerce div.product .related.products ul.products li.product{width:calc(33.33% - 14px);}}@media (min-width:545px) and (max-width:921px){.woocommerce[class*="columns-"].columns-3 > ul.products li.product, .woocommerce[class*="columns-"].columns-4 > ul.products li.product, .woocommerce[class*="columns-"].columns-5 > ul.products li.product, .woocommerce[class*="columns-"].columns-6 > ul.products li.product{width:calc(33.33% - 14px);margin-right:20px;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(3n){margin-right:0;clear:right;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(3n+1){clear:left;}.woocommerce[class*="columns-"] ul.products li.product:nth-child(n), .woocommerce-page[class*="columns-"] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce.tablet-columns-2 ul.products li.product:nth-child(2n), .woocommerce-page.tablet-columns-2 ul.products li.product:nth-child(2n), .woocommerce.tablet-columns-3 ul.products li.product:nth-child(3n), .woocommerce-page.tablet-columns-3 ul.products li.product:nth-child(3n), .woocommerce.tablet-columns-4 ul.products li.product:nth-child(4n), .woocommerce-page.tablet-columns-4 ul.products li.product:nth-child(4n), .woocommerce.tablet-columns-5 ul.products li.product:nth-child(5n), .woocommerce-page.tablet-columns-5 ul.products li.product:nth-child(5n), .woocommerce.tablet-columns-6 ul.products li.product:nth-child(6n), .woocommerce-page.tablet-columns-6 ul.products li.product:nth-child(6n){margin-right:0;clear:right;}.woocommerce.tablet-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce-page.tablet-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce.tablet-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce-page.tablet-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce.tablet-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce-page.tablet-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce.tablet-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce-page.tablet-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce.tablet-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce-page.tablet-columns-6 ul.products li.product:nth-child(6n+1){clear:left;}.woocommerce div.product .related.products ul.products li.product:nth-child(3n), .woocommerce-page.tablet-columns-1 .site-main ul.products li.product{margin-right:0;clear:right;}.woocommerce div.product .related.products ul.products li.product:nth-child(3n+1){clear:left;}}@media (min-width:922px){.woocommerce form.checkout_coupon{width:50%;}.woocommerce #reviews #comments{float:left;}.woocommerce #reviews #review_form_wrapper{float:right;}}@media (max-width:921px){.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack.ast-no-menu-items .ast-site-header-cart, .ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack.ast-no-menu-items .ast-site-header-cart{padding-right:0;padding-left:0;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .main-header-bar{text-align:center;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .ast-site-header-cart, .ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .ast-mobile-menu-buttons{display:inline-block;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-2.ast-mobile-header-inline .site-branding{flex:auto;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack .site-branding{flex:0 0 100%;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack .main-header-container{display:flex;justify-content:center;}.woocommerce-cart .woocommerce-shipping-calculator .button{width:100%;}.woocommerce div.product div.images, .woocommerce div.product div.summary, .woocommerce #content div.product div.images, .woocommerce #content div.product div.summary, .woocommerce-page div.product div.images, .woocommerce-page div.product div.summary, .woocommerce-page #content div.product div.images, .woocommerce-page #content div.product div.summary{float:none;width:100%;}.woocommerce-cart table.cart td.actions .ast-return-to-shop{display:block;text-align:center;margin-top:1em;}}@media (max-width:544px){.ast-separate-container .ast-woocommerce-container{padding:.54em 1em 1.33333em;}.woocommerce-message, .woocommerce-error, .woocommerce-info{display:flex;flex-wrap:wrap;}.woocommerce-message a.button, .woocommerce-error a.button, .woocommerce-info a.button{order:1;margin-top:.5em;}.woocommerce .woocommerce-ordering, .woocommerce-page .woocommerce-ordering{float:none;margin-bottom:2em;}.woocommerce table.cart td.actions .button, .woocommerce #content table.cart td.actions .button, .woocommerce-page table.cart td.actions .button, .woocommerce-page #content table.cart td.actions .button{padding-left:1em;padding-right:1em;}.woocommerce #content table.cart .button, .woocommerce-page #content table.cart .button{width:100%;}.woocommerce #content table.cart td.actions .coupon, .woocommerce-page #content table.cart td.actions .coupon{float:none;}.woocommerce #content table.cart td.actions .coupon .button, .woocommerce-page #content table.cart td.actions .coupon .button{flex:1;}.woocommerce #content div.product .woocommerce-tabs ul.tabs li a, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li a{display:block;}.woocommerce ul.products a.button, .woocommerce-page ul.products a.button{padding:0.5em 0.75em;}.woocommerce div.product .related.products ul.products li.product, .woocommerce.mobile-columns-2 ul.products li.product, .woocommerce-page.mobile-columns-2 ul.products li.product{width:calc(50% - 10px);}.woocommerce.mobile-columns-6 ul.products li.product, .woocommerce-page.mobile-columns-6 ul.products li.product{width:calc(16.66% - 16.66px);}.woocommerce.mobile-columns-5 ul.products li.product, .woocommerce-page.mobile-columns-5 ul.products li.product{width:calc(20% - 16px);}.woocommerce.mobile-columns-4 ul.products li.product, .woocommerce-page.mobile-columns-4 ul.products li.product{width:calc(25% - 15px);}.woocommerce.mobile-columns-3 ul.products li.product, .woocommerce-page.mobile-columns-3 ul.products li.product{width:calc(33.33% - 14px);}.woocommerce.mobile-columns-1 ul.products li.product, .woocommerce-page.mobile-columns-1 ul.products li.product{width:100%;}}@media (max-width:544px){.woocommerce ul.products a.button.loading::after, .woocommerce-page ul.products a.button.loading::after{display:inline-block;margin-left:5px;position:initial;}.woocommerce.mobile-columns-1 .site-main ul.products li.product:nth-child(n), .woocommerce-page.mobile-columns-1 .site-main ul.products li.product:nth-child(n){margin-right:0;}.woocommerce #content div.product .woocommerce-tabs ul.tabs li, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li{display:block;margin-right:0;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product, .woocommerce[class*="columns-"].columns-4 > ul.products li.product, .woocommerce[class*="columns-"].columns-5 > ul.products li.product, .woocommerce[class*="columns-"].columns-6 > ul.products li.product{width:calc(50% - 10px);margin-right:20px;}.woocommerce[class*="columns-"] ul.products li.product:nth-child(n), .woocommerce-page[class*="columns-"] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce-page[class*=columns-].columns-3>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-4>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-5>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-6>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-3>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-4>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-5>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-6>ul.products li.product:nth-child(2n){margin-right:0;clear:right;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(2n+1){clear:left;}.woocommerce-page[class*=columns-] ul.products li.product:nth-child(n), .woocommerce[class*=columns-] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce.mobile-columns-6 ul.products li.product:nth-child(6n), .woocommerce-page.mobile-columns-6 ul.products li.product:nth-child(6n), .woocommerce.mobile-columns-5 ul.products li.product:nth-child(5n), .woocommerce-page.mobile-columns-5 ul.products li.product:nth-child(5n), .woocommerce.mobile-columns-4 ul.products li.product:nth-child(4n), .woocommerce-page.mobile-columns-4 ul.products li.product:nth-child(4n), .woocommerce.mobile-columns-3 ul.products li.product:nth-child(3n), .woocommerce-page.mobile-columns-3 ul.products li.product:nth-child(3n), .woocommerce.mobile-columns-2 ul.products li.product:nth-child(2n), .woocommerce-page.mobile-columns-2 ul.products li.product:nth-child(2n), .woocommerce div.product .related.products ul.products li.product:nth-child(2n){margin-right:0;clear:right;}.woocommerce.mobile-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce-page.mobile-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce.mobile-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce-page.mobile-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce.mobile-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce-page.mobile-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce.mobile-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce-page.mobile-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce.mobile-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce-page.mobile-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce div.product .related.products ul.products li.product:nth-child(2n+1){clear:left;}}@media (min-width:922px){.woocommerce #content .ast-woocommerce-container div.product div.images, .woocommerce .ast-woocommerce-container div.product div.images, .woocommerce-page #content .ast-woocommerce-container div.product div.images, .woocommerce-page .ast-woocommerce-container div.product div.images{width:50%;}.woocommerce #content .ast-woocommerce-container div.product div.summary, .woocommerce .ast-woocommerce-container div.product div.summary, .woocommerce-page #content .ast-woocommerce-container div.product div.summary, .woocommerce-page .ast-woocommerce-container div.product div.summary{width:46%;}.woocommerce.woocommerce-checkout form #customer_details.col2-set .col-1, .woocommerce.woocommerce-checkout form #customer_details.col2-set .col-2, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set .col-1, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set .col-2{float:none;width:auto;}}.woocommerce-js a.button , .woocommerce button.button.alt ,.woocommerce-page table.cart td.actions .button, .woocommerce-page #content table.cart td.actions .button , .woocommerce-js a.button.alt ,.woocommerce .woocommerce-message a.button , .ast-site-header-cart .widget_shopping_cart .buttons .button.checkout, .woocommerce button.button.alt.disabled , .wc-block-grid__products .wc-block-grid__product .wp-block-button__link {border:solid;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;border-color:var(--ast-global-color-0);}.woocommerce-js a.button:hover , .woocommerce button.button.alt:hover , .woocommerce-page table.cart td.actions .button:hover, .woocommerce-page #content table.cart td.actions .button:hover, .woocommerce-js a.button.alt:hover ,.woocommerce .woocommerce-message a.button:hover , .ast-site-header-cart .widget_shopping_cart .buttons .button.checkout:hover , .woocommerce button.button.alt.disabled:hover , .wc-block-grid__products .wc-block-grid__product .wp-block-button__link:hover{border-color:var(--ast-global-color-1);}.widget_product_search button{flex:0 0 auto;padding:10px 20px;}@media (min-width:922px){.woocommerce.woocommerce-checkout form #customer_details.col2-set, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set{width:55%;float:left;margin-right:4.347826087%;}.woocommerce.woocommerce-checkout form #order_review, .woocommerce.woocommerce-checkout form #order_review_heading, .woocommerce-page.woocommerce-checkout form #order_review, .woocommerce-page.woocommerce-checkout form #order_review_heading{width:40%;float:right;margin-right:0;clear:right;}}select, .select2-container .select2-selection--single{background-image:url("data:image/svg+xml,%3Csvg class='ast-arrow-svg' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' x='0px' y='0px' width='26px' height='16.043px' fill='%23c9c8c8' viewBox='57 35.171 26 16.043' enable-background='new 57 35.171 26 16.043' xml:space='preserve' %3E%3Cpath d='M57.5,38.193l12.5,12.5l12.5-12.5l-2.5-2.5l-10,10l-10-10L57.5,38.193z'%3E%3C/path%3E%3C/svg%3E");background-size:.8em;background-repeat:no-repeat;background-position-x:calc( 100% - 10px );background-position-y:center;-webkit-appearance:none;-moz-appearance:none;padding-right:2em;}
						.woocommerce ul.products li.product.desktop-align-left, .woocommerce-page ul.products li.product.desktop-align-left {
							text-align: left;
						}
						.woocommerce ul.products li.product.desktop-align-left .star-rating,
						.woocommerce ul.products li.product.desktop-align-left .button,
						.woocommerce-page ul.products li.product.desktop-align-left .star-rating,
						.woocommerce-page ul.products li.product.desktop-align-left .button {
							margin-left: 0;
							margin-right: 0;
						}
					@media(max-width: 921px){
						.woocommerce ul.products li.product.tablet-align-left, .woocommerce-page ul.products li.product.tablet-align-left {
							text-align: left;
						}
						.woocommerce ul.products li.product.tablet-align-left .star-rating,
						.woocommerce ul.products li.product.tablet-align-left .button,
						.woocommerce-page ul.products li.product.tablet-align-left .star-rating,
						.woocommerce-page ul.products li.product.tablet-align-left .button {
							margin-left: 0;
							margin-right: 0;
						}
					}@media(max-width: 544px){
						.woocommerce ul.products li.product.mobile-align-left, .woocommerce-page ul.products li.product.mobile-align-left {
							text-align: left;
						}
						.woocommerce ul.products li.product.mobile-align-left .star-rating,
						.woocommerce ul.products li.product.mobile-align-left .button,
						.woocommerce-page ul.products li.product.mobile-align-left .star-rating,
						.woocommerce-page ul.products li.product.mobile-align-left .button {
							margin-left: 0;
							margin-right: 0;
						}
					}.ast-woo-active-filter-widget .wc-block-active-filters{display:flex;align-items:self-start;justify-content:space-between;}.ast-woo-active-filter-widget .wc-block-active-filters__clear-all{flex:none;margin-top:2px;}.woocommerce.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #customer_details.col2-set, .woocommerce-page.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #customer_details.col2-set{width:100%;}.woocommerce.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #order_review, .woocommerce.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #order_review_heading, .woocommerce-page.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #order_review, .woocommerce-page.woocommerce-checkout .elementor-widget-woocommerce-checkout-page #order_review_heading{width:100%;float:inherit;}.elementor-widget-woocommerce-checkout-page .select2-container .select2-selection--single, .elementor-widget-woocommerce-cart .select2-container .select2-selection--single{padding:0;}.elementor-widget-woocommerce-checkout-page .woocommerce form .woocommerce-additional-fields, .elementor-widget-woocommerce-checkout-page .woocommerce form .shipping_address, .elementor-widget-woocommerce-my-account .woocommerce-MyAccount-navigation-link, .elementor-widget-woocommerce-cart .woocommerce a.remove{border:none;}.elementor-widget-woocommerce-cart .cart-collaterals .cart_totals > h2{background-color:inherit;border-bottom:0px;margin:0px;}.elementor-widget-woocommerce-cart .cart-collaterals .cart_totals{padding:0;border-color:inherit;border-radius:0;margin-bottom:0px;border-width:0px;}.elementor-widget-woocommerce-cart .woocommerce-cart-form .e-apply-coupon{line-height:initial;}.elementor-widget-woocommerce-my-account .woocommerce-MyAccount-content .woocommerce-Address-title h3{margin-bottom:var(--myaccount-section-title-spacing, 0px);}.elementor-widget-woocommerce-my-account .woocommerce-Addresses .woocommerce-Address-title, .elementor-widget-woocommerce-my-account table.shop_table thead, .elementor-widget-woocommerce-my-account .woocommerce-page table.shop_table thead, .elementor-widget-woocommerce-cart table.shop_table thead{background:inherit;}.elementor-widget-woocommerce-cart .e-apply-coupon, .elementor-widget-woocommerce-cart #coupon_code, .elementor-widget-woocommerce-checkout-page .e-apply-coupon, .elementor-widget-woocommerce-checkout-page #coupon_code{height:100%;}.elementor-widget-woocommerce-cart td.product-name dl.variation dt{font-weight:inherit;}.elementor-element.elementor-widget-woocommerce-checkout-page .e-checkout__container #customer_details .col-1{margin-bottom:0;}
</style>
  <style id="woocommerce-inline-inline-css">
.woocommerce form .form-row .required { visibility: visible; }
</style>
  <link rel="stylesheet" id="ivory-search-styles-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/ivory-search.min.css" media="all">
  <link rel="stylesheet" id="woocommerce-pre-orders-main-css-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/main.css" media="all">
  <link rel="stylesheet" id="brands-styles-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/brands.css" media="all">
  <link rel="stylesheet" id="woosb-frontend-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/frontend.css" media="all">
  <link rel="stylesheet" id="xoo-wsc-fonts-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/xoo-wsc-fonts.css" media="all">
  <link rel="stylesheet" id="xoo-wsc-style-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/xoo-wsc-style.css" media="all">
  <style id="xoo-wsc-style-inline-css">




.xoo-wsc-ft-buttons-cont a.xoo-wsc-ft-btn, .xoo-wsc-container .xoo-wsc-btn {
	background-color: #000000;
	color: #ffffff;
	border: 2px solid #ffffff;
	padding: 10px 20px;
}

.xoo-wsc-ft-buttons-cont a.xoo-wsc-ft-btn:hover, .xoo-wsc-container .xoo-wsc-btn:hover {
	background-color: #ffffff;
	color: #000000;
	border: 2px solid #000000;
}

 

.xoo-wsc-footer{
	background-color: #ffffff;
	color: #000000;
	padding: 10px 20px;
	box-shadow: 0 -1px 10px #0000001a;
}

.xoo-wsc-footer, .xoo-wsc-footer a, .xoo-wsc-footer .amount{
	font-size: 18px;
}

.xoo-wsc-btn .amount{
	color: #ffffff}

.xoo-wsc-btn:hover .amount{
	color: #000000;
}

.xoo-wsc-ft-buttons-cont{
	grid-template-columns: auto;
}

.xoo-wsc-basket{
	bottom: 12px;
	right: 1px;
	background-color: #ffffff;
	color: #000000;
	box-shadow: 0px 0px 15px 2px #0000001a;
	border-radius: 50%;
	display: none;
	width: 60px;
	height: 60px;
}


.xoo-wsc-bki{
	font-size: 30px}

.xoo-wsc-items-count{
	top: -12px;
	left: -12px;
}

.xoo-wsc-items-count{
	background-color: #000000;
	color: #ffffff;
}

.xoo-wsc-container, .xoo-wsc-slider{
	max-width: 365px;
	right: -365px;
	top: 0;bottom: 0;
	bottom: 0;
	font-family: }


.xoo-wsc-cart-active .xoo-wsc-container, .xoo-wsc-slider-active .xoo-wsc-slider{
	right: 0;
}


.xoo-wsc-cart-active .xoo-wsc-basket{
	right: 365px;
}

.xoo-wsc-slider{
	right: -365px;
}

span.xoo-wsch-close {
    font-size: 16px;
    right: 10px;
}

.xoo-wsch-top{
	justify-content: center;
}

.xoo-wsch-text{
	font-size: 20px;
}

.xoo-wsc-header{
	color: #000000;
	background-color: #ffffff;
	border-bottom: 2px solid #eee;
}


.xoo-wsc-body{
	background-color: #f8f9fa;
}

.xoo-wsc-products:not(.xoo-wsc-pattern-card), .xoo-wsc-products:not(.xoo-wsc-pattern-card) span.amount, .xoo-wsc-products:not(.xoo-wsc-pattern-card) a{
	font-size: 16px;
	color: #000000;
}

.xoo-wsc-products:not(.xoo-wsc-pattern-card) .xoo-wsc-product{
	padding: 10px 15px;
	margin: 10px 15px;
	border-radius: 5px;
	box-shadow: 0 2px 2px #00000005;
	background-color: #ffffff;
}

.xoo-wsc-sum-col{
	justify-content: center;
}


/** Shortcode **/
.xoo-wsc-sc-count{
	background-color: #000000;
	color: #ffffff;
}

.xoo-wsc-sc-bki{
	font-size: 28px;
	color: #000000;
}
.xoo-wsc-sc-cont{
	color: #000000;
}

.added_to_cart{
	display: none!important;
}

.xoo-wsc-product dl.variation {
	display: block;
}


.xoo-wsc-product-cont{
	padding: 10px 10px;
}

.xoo-wsc-products:not(.xoo-wsc-pattern-card) .xoo-wsc-img-col{
	width: 28%;
}

.xoo-wsc-pattern-card .xoo-wsc-img-col img{
	max-width: 100%;
	height: auto;
}

.xoo-wsc-products:not(.xoo-wsc-pattern-card) .xoo-wsc-sum-col{
	width: 72%;
}

.xoo-wsc-pattern-card .xoo-wsc-product-cont{
	width: 50% 
}

@media only screen and (max-width: 600px) {
	.xoo-wsc-pattern-card .xoo-wsc-product-cont  {
		width: 50%;
	}
}


.xoo-wsc-pattern-card .xoo-wsc-product{
	border: 0;
	box-shadow: 0px 10px 15px -12px #0000001a;
}


.xoo-wsc-sm-front{
	background-color: #eee;
}
.xoo-wsc-pattern-card, .xoo-wsc-sm-front{
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
}
.xoo-wsc-pattern-card, .xoo-wsc-img-col img, .xoo-wsc-img-col, .xoo-wsc-sm-back-cont{
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;
}
.xoo-wsc-sm-back{
	background-color: #fff;
}
.xoo-wsc-pattern-card, .xoo-wsc-pattern-card a, .xoo-wsc-pattern-card .amount{
	font-size: 16px;
}

.xoo-wsc-sm-front, .xoo-wsc-sm-front a, .xoo-wsc-sm-front .amount{
	color: #000;
}

.xoo-wsc-sm-back, .xoo-wsc-sm-back a, .xoo-wsc-sm-back .amount{
	color: #000;
}


.magictime {
    animation-duration: 0.5s;
}


</style>
  <link rel="stylesheet" id="dflip-style-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/dflip.min.css" media="all">
  <link rel="stylesheet" id="wpdreams-asl-basic-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/style.basic.css" media="all">
  <link rel="stylesheet" id="wpdreams-asl-instance-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/style-curvy-black.css" media="all">
  <link rel="stylesheet" id="elementor-frontend-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/custom-frontend.min.css" media="all">
  <link rel="stylesheet" id="widget-image-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-image.min.css" media="all">
  <link rel="stylesheet" id="widget-nav-menu-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/custom-pro-widget-nav-menu.min.css" media="all">
  <link rel="stylesheet" id="widget-heading-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-heading.min.css" media="all">
  <link rel="stylesheet" id="widget-social-icons-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-social-icons.min.css" media="all">
  <link rel="stylesheet" id="e-apple-webkit-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/custom-apple-webkit.min.css" media="all">
  <link rel="stylesheet" id="widget-divider-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-divider.min.css" media="all">
  <link rel="stylesheet" id="widget-post-info-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-post-info.min.css" media="all">
  <link rel="stylesheet" id="widget-icon-list-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/custom-widget-icon-list.min.css" media="all">
  <link rel="stylesheet" id="elementor-icons-shared-0-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/fontawesome.min.css" media="all">
  <link rel="stylesheet" id="elementor-icons-fa-regular-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/regular.min.css" media="all">
  <link rel="stylesheet" id="elementor-icons-fa-solid-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/solid.min.css" media="all">
  <link rel="stylesheet" id="widget-posts-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-posts.min.css" media="all">
  <link rel="stylesheet" id="widget-form-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-form.min.css" media="all">
  <link rel="stylesheet" id="e-popup-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/popup.min.css" media="all">
  <link rel="stylesheet" id="elementor-icons-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/elementor-icons.min.css" media="all">
  <link rel="stylesheet" id="elementor-post-887-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/post-887.css" media="all">
  <link rel="stylesheet" id="elementor-post-7716-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/post-7716.css" media="all">
  <link rel="stylesheet" id="elementor-post-7720-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/post-7720.css" media="all">
  <link rel="stylesheet" id="elementor-post-7888-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/post-7888.css" media="all">
  <link rel="stylesheet" id="chart-builder-admin-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/admin.css" media="all">
  <link rel="stylesheet" id="elementor-post-11778-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/post-11778.css" media="all">
  <link rel="stylesheet" id="elementor-post-11767-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/post-11767.css" media="all">
  <link rel="stylesheet" id="simple-favorites-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/favorites.css" media="all">
  <link rel="stylesheet" id="astra-child-theme-css-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/style.css" media="all">
  <link rel="stylesheet" id="ekit-widget-styles-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-styles.css" media="all">
  <link rel="stylesheet" id="ekit-responsive-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/responsive.css" media="all">
  <link rel="stylesheet" id="um_modal-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-modal.min.css" media="all">
  <link rel="stylesheet" id="um_ui-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/jquery-ui.min.css" media="all">
  <link rel="stylesheet" id="um_tipsy-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/tipsy.min.css" media="all">
  <link rel="stylesheet" id="um_raty-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-raty.min.css" media="all">
  <link rel="stylesheet" id="select2-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/select2.min.css" media="all">
  <link rel="stylesheet" id="um_fileupload-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-fileupload.min.css" media="all">
  <link rel="stylesheet" id="um_confirm-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-confirm.min.css" media="all">
  <link rel="stylesheet" id="um_datetime-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/default.min.css" media="all">
  <link rel="stylesheet" id="um_datetime_date-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/default.date.min.css" media="all">
  <link rel="stylesheet" id="um_datetime_time-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/default.time.min.css" media="all">
  <link rel="stylesheet" id="um_fonticons_ii-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/fonticons-ii.min.css" media="all">
  <link rel="stylesheet" id="um_fonticons_fa-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/fonticons-fa.min.css" media="all">
  <link rel="stylesheet" id="um_fontawesome-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-fontawesome.min.css" media="all">
  <link rel="stylesheet" id="um_common-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/common.min.css" media="all">
  <link rel="stylesheet" id="um_responsive-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-responsive.min.css" media="all">
  <link rel="stylesheet" id="um_styles-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-styles.min.css" media="all">
  <link rel="stylesheet" id="um_crop-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/cropper.min.css" media="all">
  <link rel="stylesheet" id="um_profile-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-profile.min.css" media="all">
  <link rel="stylesheet" id="um_account-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-account.min.css" media="all">
  <link rel="stylesheet" id="um_misc-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-misc.min.css" media="all">
  <link rel="stylesheet" id="um_default_css-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-old-default.min.css" media="all">
  <link rel="stylesheet" id="ecs-styles-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/ecs-style.css" media="all">
  <link rel="stylesheet" id="elementor-icons-fa-brands-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/brands.min.css" media="all"><!--[if IE]>
<script src="https://codecut.ai/wp-content/themes/astra/assets/js/minified/flexibility.min.js?ver=4.11.12" id="astra-flexibility-js"></script>
<script id="astra-flexibility-js-after">
flexibility(document.documentElement);
</script>
<![endif]-->
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/jquery.min.js" id="jquery-core-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/jquery-migrate.min.js" id="jquery-migrate-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/jquery.blockUI.min.js" id="jquery-blockui-js" defer="defer" data-wp-strategy="defer"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/js.cookie.min.js" id="js-cookie-js" defer="defer" data-wp-strategy="defer"></script>
  <script id="woocommerce-js-extra">
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","i18n_password_show":"Show password","i18n_password_hide":"Hide password"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/woocommerce.min.js" id="woocommerce-js" defer="defer" data-wp-strategy="defer"></script>
  <script id="favorites-js-extra">
var favorites_data = {"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","nonce":"e6fb9b2a6a","favorite":"Favorite <i class=\"sf-icon-star-empty\"><\/i>","favorited":"Favorited <i class=\"sf-icon-star-full\"><\/i>","includecount":"","indicate_loading":"","loading_text":"Loading","loading_image":"","loading_image_active":"","loading_image_preload":"","cache_enabled":"1","button_options":{"button_type":"custom","custom_colors":false,"box_shadow":false,"include_count":false,"default":{"background_default":false,"border_default":false,"text_default":false,"icon_default":false,"count_default":false},"active":{"background_active":false,"border_active":false,"text_active":false,"icon_active":false,"count_active":false}},"authentication_modal_content":"<p>Please login to add favorites.<\/p>\n<p><a href=\"#\" data-favorites-modal-close>Dismiss this notice<\/a><\/p>\n","authentication_redirect":"1","dev_mode":"","logged_in":"","user_id":"0","authentication_redirect_url":"https:\/\/codecut.ai\/login\/"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/favorites.min.js" id="favorites-js"></script><!-- Google tag (gtag.js) snippet added by Site Kit --> <!-- Google Analytics snippet added by Site Kit -->
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/js_003.txt" id="google_gtagjs-js" async=""></script>
  <script id="google_gtagjs-js-after">
window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}
gtag("set","linker",{"domains":["codecut.ai"]});
gtag("js", new Date());
gtag("set", "developer_id.dZTNiMT", true);
gtag("config", "GT-T5MH98FN", {"googlesitekit_post_type":"post"});
</script>
  <script id="ecs_ajax_load-js-extra">
var ecs_ajax_params = {"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","posts":"{\"page\":0,\"name\":\"building-multi-agent-ai-langgraph-tutorial\",\"error\":\"\",\"m\":\"\",\"p\":0,\"post_parent\":\"\",\"subpost\":\"\",\"subpost_id\":\"\",\"attachment\":\"\",\"attachment_id\":0,\"pagename\":\"\",\"page_id\":0,\"second\":\"\",\"minute\":\"\",\"hour\":\"\",\"day\":0,\"monthnum\":0,\"year\":0,\"w\":0,\"category_name\":\"\",\"tag\":\"\",\"cat\":\"\",\"tag_id\":\"\",\"author\":\"\",\"author_name\":\"\",\"feed\":\"\",\"tb\":\"\",\"paged\":0,\"meta_key\":\"\",\"meta_value\":\"\",\"preview\":\"\",\"s\":\"\",\"sentence\":\"\",\"title\":\"\",\"fields\":\"all\",\"menu_order\":\"\",\"embed\":\"\",\"category__in\":[],\"category__not_in\":[],\"category__and\":[],\"post__in\":[],\"post__not_in\":[],\"post_name__in\":[],\"tag__in\":[],\"tag__not_in\":[],\"tag__and\":[],\"tag_slug__in\":[],\"tag_slug__and\":[],\"post_parent__in\":[],\"post_parent__not_in\":[],\"author__in\":[],\"author__not_in\":[],\"search_columns\":[],\"ignore_sticky_posts\":false,\"suppress_filters\":false,\"cache_results\":true,\"update_post_term_cache\":true,\"update_menu_item_cache\":false,\"lazy_load_term_meta\":true,\"update_post_meta_cache\":true,\"post_type\":\"\",\"posts_per_page\":10,\"nopaging\":false,\"comments_per_page\":\"20\",\"no_found_rows\":false,\"order\":\"DESC\"}"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/ecs_ajax_pagination.js" id="ecs_ajax_load-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-gdpr.min.js" id="um-gdpr-js"></script>
  <script id="wc-cart-fragments-js-extra">
var wc_cart_fragments_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","cart_hash_key":"wc_cart_hash_1cfdc3c5b2ef0c45f5c31af69a18eca7","fragment_name":"wc_fragments_1cfdc3c5b2ef0c45f5c31af69a18eca7","request_timeout":"5000"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/cart-fragments.min.js" id="wc-cart-fragments-js" defer="defer" data-wp-strategy="defer"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/ecs.js" id="ecs-script-js"></script>
  <script data-cfasync="false" id="abst_ajax">var bt_ajaxurl = 'https://codecut.ai/wp-admin/admin-ajax.php';var bt_adminurl = 'https://codecut.ai/wp-admin/';var bt_pluginurl = 'https://codecut.ai/wp-content/plugins/bt-bb-ab/';var bt_homeurl = 'https://codecut.ai';</script>
  <script data-cfasync="false" id="abst_variables">var bt_ajaxurl = 'https://codecut.ai/wp-admin/admin-ajax.php';var bt_experiments = {};
bt_experiments["16639"] = {"name":"Home Page Newsletter With Artcles","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["14112"] = {"name":"Test TextHero","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["13414"] = {"name":"Pricing Test","target_percentage":"100","url_query":"","conversion_page":"selector","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"#book-now","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["13229"] = {"name":"Header on Home Page","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["13221"] = {"name":"Daily Tips Menu","target_percentage":"100","url_query":"","conversion_page":"13137","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"ab_test","is_current_user_track":true,"full_page_default_page":"false","page_variations":[],"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"Browse Tool","target_option_device_size":"all"};bt_experiments["12329"] = {"name":"Testimonial","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"full_page","is_current_user_track":true,"full_page_default_page":"11846","page_variations":{"12316":"https:\/\/codecut.ai\/"},"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"};bt_experiments["11840"] = {"name":"Form in home page","target_percentage":"100","url_query":"","conversion_page":"10209","conversion_url":"","conversion_link_pattern":"","conversion_time":"0","conversion_style":true,"conversion_selector":"","conversion_text":"","goals":[],"test_type":"full_page","is_current_user_track":true,"full_page_default_page":"7749","page_variations":{"11846":"https:\/\/codecut.ai\/home-without-testimonial\/"},"use_order_value":"","magic_definition":"","css_test_variations":"2","test_status":"complete","test_winner":"","target_option_device_size":"all"}; bt_conversion_vars = [];</script>
  <style>[bt-variation="Browse Tool"][bt-eid="13221"]{display:inherit !important;} .page-id-11846,.postid-11846{display:none;} .11846{display:none;} .page-id-7749,.postid-7749{display:none;} .7749{display:none;}</style>
  <style id="absthide">
/* Default hidden styles for all variations */
[bt-variation]:not(.bt-show-variation),
[data-bt-variation]:not(.bt-show-variation),
[class*='ab-var-'] {
    opacity: 0 !important;
    display: none !important;
}

/* First hidden element uses display: inherit */
[bt-variation]:not(.bt-show-variation):first-of-type,
[data-bt-variation]:not(.bt-show-variation):first-of-type,
[class*='ab-var-']:first-of-type {
    display: inherit !important; /* Ensure it still occupies layout space */
}

/* When the body has the ab-test-setup-complete class, revert to fully hidden */
body.ab-test-setup-complete [bt-variation]:not(.bt-show-variation),
body.ab-test-setup-complete [data-bt-variation]:not(.bt-show-variation),
body.ab-test-setup-complete [class*='ab-var-'] {
    display: none !important;
    opacity: 1 !important; /* Reset opacity just in case */
    visibility: visible !important; /* Reset visibility */
}

/* Don't apply variation hiding when Beaver Builder is active */
body.fl-builder-edit [bt-variation]:not(.bt-show-variation),
body.fl-builder-edit [data-bt-variation]:not(.bt-show-variation),
body.fl-builder-edit [class*='ab-var-'] {
    display: inherit !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure variations are visible in , Bakery  and Bricks builders */
#breakdance_canvas [bt-eid], #editor [data-bt-eid], body[data-builder-window='iframe'] .brx-body [bt-eid],  .vc_editor .vc_element [class*='ab-var-'] {
    display: inherit !important; /* Retain inherited display type */
    opacity: 1 !important; /* Fully visible */
    visibility: visible !important; /* Ensure it's interactable */
}
</style>
  <style>[bt_hidden=true] { display: none !important; visibility: hidden !important; height: 0 !important; } </style>
  <script data-cfasync="false">
          var conversion_details = [];
          var current_page = [15293];
        </script>
  <meta name="generator" content="Site Kit by Google 1.162.1"><!-- Global site tag (gtag.js) - Google Analytics -->
  <script async="" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/js_002.txt"></script>
  <script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-185909341-1');
</script>
  <script type="text/javascript" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/klaviyo.js"></script>
  <noscript>
   <style>.woocommerce-product-gallery{ opacity: 1 !important; }</style>
  </noscript>
  <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
  <link rel="preload" as="style" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/css_002.css">
  <link rel="stylesheet" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/css_002.css" media="all">
  <meta name="generator" content="Elementor 3.32.3; features: additional_custom_breakpoints; settings: css_print_method-external, google_font-enabled, font_display-auto">
  <style>
				.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
				.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
					background-image: none !important;
				}
				@media screen and (max-height: 1024px) {
					.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
					.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
						background-image: none !important;
					}
				}
				@media screen and (max-height: 640px) {
					.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
					.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
						background-image: none !important;
					}
				}
			</style>
  <script>document.addEventListener("DOMContentLoaded", function() { var enlacesConImagen = document.querySelectorAll('a img\[alt="Seraphinite Accelerator"\]'); enlacesConImagen.forEach(function(imagen) { var enlace = imagen.closest('a'); if (enlace) { enlace.style.display = "none"; } }); });</script>
  <style>
				            
					div[id*='ajaxsearchlitesettings'].searchsettings .asl_option_inner label {
						font-size: 0px !important;
						color: rgba(0, 0, 0, 0);
					}
					div[id*='ajaxsearchlitesettings'].searchsettings .asl_option_inner label:after {
						font-size: 11px !important;
						position: absolute;
						top: 0;
						left: 0;
						z-index: 1;
					}
					.asl_w_container {
						width: 100%;
						margin: 0px 0px 0px 0px;
						min-width: 200px;
					}
					div[id*='ajaxsearchlite'].asl_m {
						width: 100%;
					}
					div[id*='ajaxsearchliteres'].wpdreams_asl_results div.resdrg span.highlighted {
						font-weight: bold;
						color: rgba(217, 49, 43, 1);
						background-color: rgba(238, 238, 238, 1);
					}
					div[id*='ajaxsearchliteres'].wpdreams_asl_results .results img.asl_image {
						width: 70px;
						height: 70px;
						object-fit: cover;
					}
					div[id*='ajaxsearchlite'].asl_r .results {
						max-height: none;
					}
					div[id*='ajaxsearchlite'].asl_r {
						position: absolute;
					}
				
							.asl_w, .asl_w * {font-family:"Comforta" !important;}
							.asl_m input[type=search]::placeholder{font-family:"Comforta" !important;}
							.asl_m input[type=search]::-webkit-input-placeholder{font-family:"Comforta" !important;}
							.asl_m input[type=search]::-moz-placeholder{font-family:"Comforta" !important;}
							.asl_m input[type=search]:-ms-input-placeholder{font-family:"Comforta" !important;}
						
						div.asl_r.asl_w.vertical .results .item::after {
							display: block;
							position: absolute;
							bottom: 0;
							content: '';
							height: 1px;
							width: 100%;
							background: #D8D8D8;
						}
						div.asl_r.asl_w.vertical .results .item.asl_last_item::after {
							display: none;
						}
					
						@media only screen and (max-width: 640px) {
							.asl_w_container {
								width: 60% !important;
							}
						}
								            </style>
  <link rel="icon" href="https://codecut.ai/wp-content/uploads/2023/12/cropped-icon-1_icon-1-1-32x32.png" sizes="32x32">
  <link rel="icon" href="https://codecut.ai/wp-content/uploads/2023/12/cropped-icon-1_icon-1-1-192x192.png" sizes="192x192">
  <link rel="apple-touch-icon" href="https://codecut.ai/wp-content/uploads/2023/12/cropped-icon-1_icon-1-1-180x180.png">
  <meta name="msapplication-TileImage" content="https://codecut.ai/wp-content/uploads/2023/12/cropped-icon-1_icon-1-1-270x270.png">
  <style id="wp-custom-css">
			
.elementor-shortcode{
color: #ffffff;
font-family: 'Comfortaa', sans-serif;
font-size:25px;
font-weight: 600;
}
.comments-area p.logged-in-as {
    margin-bottom: 1em;
    color: #ffffff !important;
}

p.comment-form-mailpoet {
    color: #ffffff !important;
}

ul.wc-block-components-express-payment__event-buttons.wc-ppcp-sm__container {
    display: none;
}
.wc-block-components-express-payment-continue-rule.wc-block-components-express-payment-continue-rule--cart {
    display: none;
}
a.components-button.wc-block-components-button.wp-element-button.wc-block-cart__submit-button.contained {
    background-color: #72befa;
    color: black;
}
h2.woocommerce-order-details__title {
    color: #2F2D2E;
}

h2.woocommerce-column__title
{
	color: #2F2D2E;
}
div.wpforms-container-full button[type=submit]:focus:after
{
	border:none !important;
}
.wpforms-container .wpforms-field
{
	padding:8px 0px !important;
}
div.wpforms-container-full .wpforms-confirmation-container-full
{
	background-color:#72b7ee !important;
	border:none !important;
	text-align:center !important;
}
.wp-block-image img {
    margin-bottom: 20px;
    margin-bottom: 20px; 
}
/* Center images in single blog posts */
.single-post .entry-content img,
.single-post .elementor-widget-theme-post-content img {
    display: block;
    margin: 20px auto;
}
h1.product_title.entry-title {
    color: white !important;
    font-size: 40px;
}
ul.tabs.wc-tabs {
    display: none;
}
.ast-woocommerce-container
 {
    margin-top: 50px !important;
}
.woocommerce-js div.product form.cart .button.single_add_to_cart_button
{
    background: linear-gradient(271deg, rgba(114, 190, 250, 1) 0%, rgba(229, 131, 182, 1) 100%) !important;
	border-radius: 50px;
	font-weight:400;
	padding: 15px 20px 15px 20px;
}

.woocommerce-cart .cart-collaterals .cart_totals>h2
{
	color:#000;
}
.woocommerce-page.woocommerce-checkout #payment div.payment_box{
	background-color:#2a2a2a !important;
}
.woocommerce-checkout #payment div.payment_box::before
{
	display:none !important;
}
fieldset#wc-stripe-upe-form
{
	border:1px solid #6a6a6a1a !important;
}
p.form-row.woocommerce-SavedPaymentMethods-saveNew label
{
	color:#fff;
}		</style>
  <meta http-equiv="X-Translated-By" content="Google">
  <meta http-equiv="X-Translated-To" content="fr">
  <script type="text/javascript" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/m=corsproxy" data-sourceurl="https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/"></script>
  <link href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/css2.css" rel="stylesheet">
  <script type="text/javascript" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/m=phishing_protection" data-phishing-protection-enabled="false" data-forms-warning-enabled="true" data-source-url="https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/"></script>
  <meta name="robots" content="none">
 <script async="" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/lazy.min.js"></script><link type="text/css" rel="stylesheet" charset="UTF-8" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/m=el_main_css.css"><script type="text/javascript" charset="UTF-8" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/m=el_main"></script><script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/wp-emoji-release.min.js" defer="defer"></script></head>
 <body data-rsssl="1" itemtype="https://schema.org/Blog" itemscope="" class="wp-singular post-template-default single single-post postid-15293 single-format-standard wp-custom-logo wp-theme-astra wp-child-theme-astra-child theme-astra bt-hidevars stk--is-astra-theme woocommerce-js astra ast-desktop ast-page-builder-template ast-no-sidebar astra-4.11.12 ast-header-custom-item-inside ast-full-width-primary-header group-blog ast-blog-single-style-1 ast-single-post ast-inherit-site-logo-transparent elementor-page-7888 ast-normal-title-enabled elementor-default elementor-kit-887 ab-test-setup-complete df-ios e--ua-firefox e--ua-safari ast-mouse-clicked" style="margin-top: 56px;" data-elementor-device-mode="desktop"><iframe style="border: medium; border-radius: 0px; box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px; height: 56px; left: 0px; right: auto; position: fixed; top: 0px; transition: none; width: 100%; z-index: 2147483646;" title="Navigation avec Google Traduction" frameborder="0" id="gt-nvframe" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/websitetranslationui.htm"></iframe>
  <script type="text/javascript" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/m=navigationui" data-environment="prod" data-proxy-url="https://codecut-ai.translate.goog" data-proxy-full-url="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" data-source-url="https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/" data-source-language="en" data-target-language="fr" data-display-language="fr" data-detected-source-language="" data-is-source-untranslated="false" data-source-untranslated-url="https://translate.google.com/website?sl=en&amp;tl=fr&amp;hl=fr&amp;client=webapp&amp;u=https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/&amp;anno=2" data-client="webapp"></script><a class="skip-link screen-reader-text" href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#content" title="Passer au contenu"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Passer au contenu</font></font></a>
  <div class="hfeed site" id="page">
   <header data-elementor-type="header" data-elementor-id="7716" class="elementor elementor-7716 elementor-location-header" data-elementor-post-type="elementor_library">
    <div class="elementor-element elementor-element-b75b880 e-flex e-con-boxed e-con e-parent e-lazyloaded" data-id="b75b880" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
     <div class="e-con-inner">
      <div class="elementor-element elementor-element-42bc3e7 e-con-full e-flex e-con e-child" data-id="42bc3e7" data-element_type="container">
       <div class="elementor-element elementor-element-ea0461e elementor-widget elementor-widget-image" data-id="ea0461e" data-element_type="widget" data-widget_type="image.default">
        <div class="elementor-widget-container"><a href="https://codecut-ai.translate.goog/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"> <img fetchpriority="high" width="1052" height="394" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/logo_compressed-1052x394.webp" class="attachment-medium size-medium wp-image-12564" alt="" srcset="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/logo_compressed-1052x394.png 1052w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/logo_compressed-768x288.png 768w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/logo_compressed-600x225.png 600w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/logo_compressed.png 1319w" sizes="(max-width: 1052px) 100vw, 1052px"> </a>
        </div>
       </div>
      </div>
      <div class="elementor-element elementor-element-3c72f79 e-con-full e-flex e-con e-child" data-id="3c72f79" data-element_type="container">
       <div class="elementor-element elementor-element-9762762 elementor-nav-menu__align-center elementor-nav-menu--stretch elementor-widget-mobile__width-auto elementor-widget-tablet__width-inherit elementor-widget-widescreen__width-initial elementor-widget-laptop__width-inherit elementor-nav-menu--dropdown-tablet elementor-nav-menu__text-align-aside elementor-nav-menu--toggle elementor-nav-menu--burger elementor-widget elementor-widget-nav-menu" data-id="9762762" data-element_type="widget" data-settings="{&quot;full_width&quot;:&quot;stretch&quot;,&quot;submenu_icon&quot;:{&quot;value&quot;:&quot;&lt;i class=\&quot;fas fa-angle-down\&quot; aria-hidden=\&quot;true\&quot;&gt;&lt;\/i&gt;&quot;,&quot;library&quot;:&quot;fa-solid&quot;},&quot;layout&quot;:&quot;horizontal&quot;,&quot;toggle&quot;:&quot;burger&quot;}" data-widget_type="nav-menu.default">
        <div class="elementor-widget-container">
         <nav aria-label="Menu" class="elementor-nav-menu--main elementor-nav-menu__container elementor-nav-menu--layout-horizontal e--pointer-none">
          <ul id="menu-1-9762762" class="elementor-nav-menu" data-smartmenus-id="1759234620221489">
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-17593"><a href="https://codecut-ai.translate.goog/newsletter/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Archives des newsletters</font></font></a></li>
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-14736"><a href="https://codecut-ai.translate.goog/blog/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Blog</font></font></a></li>
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-16107"><a href="https://codecut-ai.translate.goog/production-ready-data-science/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Livre</font></font></a></li>
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-14322"><a href="https://codecut-ai.translate.goog/sponsor/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Parrainer</font></font></a></li>
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-12010"><a aria-expanded="false" href="https://codecut-ai.translate.goog/user-2/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link has-submenu" id="sm-1759234620221489-1" aria-haspopup="true" aria-controls="sm-1759234620221489-2"><i class="_mi _before dashicons dashicons-admin-users" aria-hidden="true" style="font-size:1.3em;"></i><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Compte</font></font></span><span class="sub-arrow"><i class="fas fa-angle-down" aria-hidden="true"></i></span></a>
            <ul class="sub-menu elementor-nav-menu--dropdown" id="sm-1759234620221489-2" role="group" aria-hidden="true" aria-labelledby="sm-1759234620221489-1" aria-expanded="false">
             <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10295"><a href="https://codecut-ai.translate.goog/login-2/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-sub-item menu-link"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Se connecter</font></font></a></li>
            </ul></li>
          </ul>
         </nav>
         <div class="elementor-menu-toggle" role="button" tabindex="0" aria-label="Menu Basculer" aria-expanded="false" style=""><i aria-hidden="true" role="presentation" class="elementor-menu-toggle__icon--open eicon-menu-bar"></i><i aria-hidden="true" role="presentation" class="elementor-menu-toggle__icon--close eicon-close"></i>
         </div>
         <nav class="elementor-nav-menu--dropdown elementor-nav-menu__container" aria-hidden="true" style="width: 1903px; left: 0px; top: 38px; --menu-height: 0;">
          <ul id="menu-2-9762762" class="elementor-nav-menu" data-smartmenus-id="17592346202257322">
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-17593"><a href="https://codecut-ai.translate.goog/newsletter/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link" tabindex="-1"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Archives des newsletters</font></font></a></li>
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-14736"><a href="https://codecut-ai.translate.goog/blog/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link" tabindex="-1"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Blog</font></font></a></li>
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-16107"><a href="https://codecut-ai.translate.goog/production-ready-data-science/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link" tabindex="-1"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Livre</font></font></a></li>
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-14322"><a href="https://codecut-ai.translate.goog/sponsor/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link" tabindex="-1"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Parrainer</font></font></a></li>
           <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-12010"><a aria-expanded="false" href="https://codecut-ai.translate.goog/user-2/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-item menu-link has-submenu" tabindex="-1" id="sm-17592346202257322-1" aria-haspopup="true" aria-controls="sm-17592346202257322-2"><i class="_mi _before dashicons dashicons-admin-users" aria-hidden="true" style="font-size:1.3em;"></i><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Compte</font></font></span><span class="sub-arrow"><i class="fas fa-angle-down" aria-hidden="true"></i></span></a>
            <ul class="sub-menu elementor-nav-menu--dropdown" id="sm-17592346202257322-2" role="group" aria-hidden="true" aria-labelledby="sm-17592346202257322-1" aria-expanded="false">
             <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10295"><a href="https://codecut-ai.translate.goog/login-2/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" class="elementor-sub-item menu-link" tabindex="-1"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Se connecter</font></font></a></li>
            </ul></li>
          </ul>
         </nav>
        </div>
       </div>
      </div>
      <div class="elementor-element elementor-element-863881f e-con-full e-flex e-con e-child" data-id="863881f" data-element_type="container">
       <div class="elementor-element elementor-element-2212832 elementor-widget elementor-widget-shortcode" data-id="2212832" data-element_type="widget" data-widget_type="shortcode.default">
        <div class="elementor-widget-container">
         <div class="elementor-shortcode">
          <div class="asl_w_container asl_w_container_1" data-id="1" data-instance="1">
           <div id="ajaxsearchlite1" data-id="1" data-instance="1" class="asl_w asl_m asl_m_1 asl_m_1_1">
            <div class="probox">
             <div class="prosettings" style="display:none;" data-opened="0">
              <div class="innericon">
               <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="22" height="22" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><polygon transform="rotate(90 256 256)" points="142.332,104.886 197.48,50 402.5,256 197.48,462 142.332,407.113 292.727,256 "></polygon>
               </svg>
              </div>
             </div>
             <div class="proinput">
              <form role="search" action="#" autocomplete="off" aria-label="Formulaire de recherche"><input aria-label="Entrée de recherche" type="search" class="orig" tabindex="0" name="phrase" placeholder="Recherchez ici.." autocomplete="off"> <input aria-label="Recherche par saisie semi-automatique" type="text" class="autocomplete" tabindex="-1" name="phrase" autocomplete="off" disabled="disabled"> <font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><input type="submit" value="Démarrer la recherche" style="width:0; height: 0; visibility: hidden;"></font></font></font></font></font></font>
              </form>
             </div><button class="promagnifier" tabindex="0" aria-label="Loupe de recherche"> <span class="innericon" style="display:block;">
               <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="22" height="22" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><path d="M460.355,421.59L353.844,315.078c20.041-27.553,31.885-61.437,31.885-98.037
						C385.729,124.934,310.793,50,218.686,50C126.58,50,51.645,124.934,51.645,217.041c0,92.106,74.936,167.041,167.041,167.041
						c34.912,0,67.352-10.773,94.184-29.158L419.945,462L460.355,421.59z M100.631,217.041c0-65.096,52.959-118.056,118.055-118.056
						c65.098,0,118.057,52.959,118.057,118.056c0,65.096-52.959,118.056-118.057,118.056C153.59,335.097,100.631,282.137,100.631,217.041
						z"></path>
               </svg></span> </button>
             <div class="proloading">
              <div class="asl_loader">
               <div class="asl_loader-inner asl_simple-circle"></div>
              </div>
             </div>
             <div class="proclose">
              <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="12" height="12" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><polygon points="438.393,374.595 319.757,255.977 438.378,137.348 374.595,73.607 255.995,192.225 137.375,73.622 73.607,137.352 192.246,255.983 73.622,374.625 137.352,438.393 256.002,319.734 374.652,438.378 "></polygon>
              </svg>
             </div>
            </div>
           </div>
           <div class="asl_data_container" style="display:none !important;">
            <div class="asl_init_data wpdreams_asl_data_ct" style="display:none !important;" id="asl_init_id_1" data-asl-id="1" data-asl-instance="1" data-asldata="ew0KCSJob21ldXJsIjogImh0dHBzOi8vY29kZWN1dC5haS8iLA0KCSJyZXN1bHRzdHlwZSI6ICJ2ZXJ0aWNhbCIsDQoJInJlc3VsdHNwb3NpdGlvbiI6ICJob3ZlciIsDQoJIml0ZW1zY291bnQiOiA0LA0KCSJjaGFyY291bnQiOiAgMCwNCgkiaGlnaGxpZ2h0IjogMCwNCgkiaGlnaGxpZ2h0d2hvbGV3b3JkcyI6IDEsDQoJInNpbmdsZUhpZ2hsaWdodCI6IDAsDQoJInNjcm9sbFRvUmVzdWx0cyI6IHsNCgkJImVuYWJsZWQiOiAwLA0KCQkib2Zmc2V0IjogMA0KCX0sDQoJInJlc3VsdGFyZWFjbGlja2FibGUiOiAxLA0KCSJhdXRvY29tcGxldGUiOiB7DQoJCSJlbmFibGVkIiA6IDEsDQoJCSJsYW5nIiA6ICJlbiIsDQoJCSJ0cmlnZ2VyX2NoYXJjb3VudCIgOiAwDQoJfSwNCgkibW9iaWxlIjogew0KCQkibWVudV9zZWxlY3RvciI6ICIjbWVudS10b2dnbGUiDQoJfSwNCgkidHJpZ2dlciI6IHsNCgkJImNsaWNrIjogInJlc3VsdHNfcGFnZSIsDQoJCSJjbGlja19sb2NhdGlvbiI6ICJzYW1lIiwNCgkJInVwZGF0ZV9ocmVmIjogMCwNCgkJInJldHVybiI6ICJyZXN1bHRzX3BhZ2UiLA0KCQkicmV0dXJuX2xvY2F0aW9uIjogInNhbWUiLA0KCQkiZmFjZXQiOiAxLA0KCQkidHlwZSI6IDEsDQoJCSJyZWRpcmVjdF91cmwiOiAiP3M9e3BocmFzZX0iLA0KCQkiZGVsYXkiOiAzMDANCgl9LA0KICAgICJhbmltYXRpb25zIjogew0KICAgICAgICAicGMiOiB7DQogICAgICAgICAgICAic2V0dGluZ3MiOiB7DQogICAgICAgICAgICAgICAgImFuaW0iIDogImZhZGVkcm9wIiwNCiAgICAgICAgICAgICAgICAiZHVyIiAgOiAzMDANCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAicmVzdWx0cyIgOiB7DQoJCQkJImFuaW0iIDogImZhZGVkcm9wIiwNCgkJCQkiZHVyIiAgOiAzMDANCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAiaXRlbXMiIDogInZvaWRhbmltIg0KICAgICAgICB9LA0KICAgICAgICAibW9iIjogew0KICAgICAgICAgICAgInNldHRpbmdzIjogew0KICAgICAgICAgICAgICAgICJhbmltIiA6ICJmYWRlZHJvcCIsDQogICAgICAgICAgICAgICAgImR1ciIgIDogMzAwDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgInJlc3VsdHMiIDogew0KCQkJCSJhbmltIiA6ICJmYWRlZHJvcCIsDQoJCQkJImR1ciIgIDogMzAwDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgIml0ZW1zIiA6ICJ2b2lkYW5pbSINCiAgICAgICAgfQ0KICAgIH0sDQoJImF1dG9wIjogew0KCQkic3RhdGUiOiAiZGlzYWJsZWQiLA0KCQkicGhyYXNlIjogIiIsDQoJCSJjb3VudCI6IDEJfSwNCiAgICAicmVzUGFnZSI6IHsNCiAgICAgICAgInVzZUFqYXgiOiAwLA0KICAgICAgICAic2VsZWN0b3IiOiAiI21haW4iLA0KICAgICAgICAidHJpZ2dlcl90eXBlIjogMSwNCiAgICAgICAgInRyaWdnZXJfZmFjZXQiOiAxLA0KICAgICAgICAidHJpZ2dlcl9tYWduaWZpZXIiOiAwLA0KICAgICAgICAidHJpZ2dlcl9yZXR1cm4iOiAwICAgIH0sDQoJInJlc3VsdHNTbmFwVG8iOiAibGVmdCIsDQogICAgInJlc3VsdHMiOiB7DQogICAgICAgICJ3aWR0aCI6ICJhdXRvIiwNCiAgICAgICAgIndpZHRoX3RhYmxldCI6ICJhdXRvIiwNCiAgICAgICAgIndpZHRoX3Bob25lIjogImF1dG8iDQogICAgfSwNCgkic2V0dGluZ3NpbWFnZXBvcyI6ICJyaWdodCIsDQoJImNsb3NlT25Eb2NDbGljayI6IDEsDQoJIm92ZXJyaWRld3BkZWZhdWx0IjogMSwNCgkib3ZlcnJpZGVfbWV0aG9kIjogImdldCINCn0NCg=="></div>
            <div id="asl_hidden_data">
             <svg style="position:absolute" height="0" width="0"><filter id="aslblur">
               <feGaussianBlur in="SourceGraphic" stdDeviation="4"></feGaussianBlur>
              </filter>
             </svg>
             <svg style="position:absolute" height="0" width="0"><filter id="no_aslblur"></filter>
             </svg>
            </div>
           </div>
           <div id="ajaxsearchliteres1" class="vertical wpdreams_asl_results asl_w asl_r asl_r_1 asl_r_1_1">
            <div class="results">
             <div class="resdrg">
             </div>
            </div>
           </div>
           <div id="__original__ajaxsearchlitesettings1" data-id="1" class="searchsettings wpdreams_asl_settings asl_w asl_s asl_s_1" style="position: absolute;">
            <form name="options" aria-label="Formulaire de paramètres de recherche" autocomplete="off"><input type="hidden" name="filters_changed" style="display:none;" value="0"> <input type="hidden" name="filters_initial" style="display:none;" value="1">
             <div class="asl_option_inner hiddend"><input type="hidden" name="qtranslate_lang" id="qtranslate_lang1" value="0">
             </div>
             <fieldset class="asl_sett_scroll"><legend style="display: none;"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Sélecteurs génériques</font></font></legend>
              <div class="asl_option" tabindex="0">
               <div class="asl_option_inner"><input type="checkbox" value="exact" aria-label="Correspondances exactes uniquement" name="asl_gen[]">
                <div class="asl_option_checkbox"></div>
               </div>
               <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                Correspondances exactes uniquement
               </font></font></div>
              </div>
              <div class="asl_option" tabindex="0">
               <div class="asl_option_inner"><input type="checkbox" value="title" aria-label="Rechercher dans le titre" name="asl_gen[]" checked="checked">
                <div class="asl_option_checkbox"></div>
               </div>
               <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                Rechercher dans le titre
               </font></font></div>
              </div>
              <div class="asl_option" tabindex="0">
               <div class="asl_option_inner"><input type="checkbox" value="content" aria-label="Rechercher dans le contenu" name="asl_gen[]" checked="checked">
                <div class="asl_option_checkbox"></div>
               </div>
               <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                Rechercher dans le contenu
               </font></font></div>
              </div>
              <div class="asl_option_inner hiddend"><input type="checkbox" value="excerpt" aria-label="Rechercher dans l'extrait" name="asl_gen[]" checked="checked">
               <div class="asl_option_checkbox"></div>
              </div>
             </fieldset>
             <fieldset class="asl_sett_scroll"><legend style="display: none;"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Sélecteurs de type de publication</font></font></legend>
              <div class="asl_option_inner hiddend"><input type="checkbox" value="post" aria-label="Option cachée, veuillez l'ignorer" name="customset[]" checked="checked">
              </div>
              <div class="asl_option_inner hiddend"><input type="checkbox" value="page" aria-label="Option cachée, veuillez l'ignorer" name="customset[]" checked="checked">
              </div>
             </fieldset>
             <fieldset><legend><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Filtrer par catégories</font></font></legend>
              <div class="categoryfilter asl_sett_scroll">
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="362" aria-label="À propos de l'article" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 À propos de l'article
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="448" aria-label="Analyser les données" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Analyser les données
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="502" aria-label="Archive" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Archive
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="505" aria-label="Meilleures pratiques" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Meilleures pratiques
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="475" aria-label="De meilleurs résultats" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 De meilleurs résultats
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="235" aria-label="Blog" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Blog
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="462" aria-label="Optimisation du code" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Optimisation du code
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="460" aria-label="Qualité du code" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Qualité du code
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="468" aria-label="Ligne de commande" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Ligne de commande
                </font></font></div>
               </div>
               <div class="asl_option hiddend" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="1" aria-label="Conseils quotidiens" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Conseils quotidiens
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="472" aria-label="Tableau de bord" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Tableau de bord
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="447" aria-label="Analyse et manipulation des données" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Analyse et manipulation des données
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="501" aria-label="Ingénieur de données" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Ingénieur de données
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="508" aria-label="Visualisation des données" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Visualisation des données
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="499" aria-label="Cadre de données" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Cadre de données
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="492" aria-label="Lac Delta" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Lac Delta
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="489" aria-label="DevOps" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 DevOps
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="493" aria-label="DuckDB" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 DuckDB
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="469" aria-label="Gestion de l'environnement" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Gestion de l'environnement
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="457" aria-label="Ingénieur en fonctionnalités" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Ingénieur en fonctionnalités
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="470" aria-label="Git" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Git
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="467" aria-label="Carnet Jupyter" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Carnet Jupyter
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="500" aria-label="Master en droit (LLM)" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Master en droit (LLM)
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="459" aria-label="Outils LLM" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Outils LLM
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="509" aria-label="Apprentissage automatique" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Apprentissage automatique
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="455" aria-label="Apprentissage automatique et IA" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Apprentissage automatique et IA
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="456" aria-label="Outils d'apprentissage automatique" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Outils d'apprentissage automatique
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="449" aria-label="Gérer les données" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Gérer les données
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="498" aria-label="MLOps" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 MLOps
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="458" aria-label="Traitement du langage naturel" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Traitement du langage naturel
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="636" aria-label="Archives des newsletters" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Archives des newsletters
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="452" aria-label="NumPy" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 NumPy
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="450" aria-label="Pandas" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Pandas
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="491" aria-label="Polaires" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Polaires
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="453" aria-label="PySpark" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 PySpark
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="464" aria-label="Aides Python" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Aides Python
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="463" aria-label="Conseils Python" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Conseils Python
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="503" aria-label="Utilitaires Python" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Utilitaires Python
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="488" aria-label="Récupérer des données" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Récupérer des données
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="454" aria-label="SQL" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 SQL
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="465" aria-label="Essai" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Essai
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="461" aria-label="Séries chronologiques" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Séries chronologiques
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="466" aria-label="Outils" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Outils
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="473" aria-label="Visualisation" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Visualisation
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="471" aria-label="Visualisation et rapports" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Visualisation et rapports
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="474" aria-label="Flux de travail et automatisation" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Flux de travail et automatisation
                </font></font></div>
               </div>
               <div class="asl_option" tabindex="0">
                <div class="asl_option_inner"><input type="checkbox" value="476" aria-label="Automatisation des flux de travail" name="categoryset[]" checked="checked">
                 <div class="asl_option_checkbox"></div>
                </div>
                <div class="asl_option_label"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
                 Automatisation des flux de travail
                </font></font></div>
               </div>
              </div>
             </fieldset>
            </form>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </header>
   <div id="content" class="site-content">
    <div class="ast-container">
     <div id="primary" class="content-area primary">
      <main id="main" class="site-main">
       <div data-elementor-type="single-post" data-elementor-id="7888" class="elementor elementor-7888 elementor-location-single post-15293 post type-post status-publish format-standard has-post-thumbnail hentry category-blog category-llm ast-article-single" data-elementor-post-type="elementor_library">
        <div class="elementor-element elementor-element-93cf45a e-flex e-con-boxed e-con e-parent e-lazyloaded" data-id="93cf45a" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
         <div class="e-con-inner">
          <div class="elementor-element elementor-element-12dc192 elementor-widget elementor-widget-theme-post-title elementor-page-title elementor-widget-heading" data-id="12dc192" data-element_type="widget" data-widget_type="theme-post-title.default">
           <div class="elementor-widget-container">
            <h1 class="elementor-heading-title elementor-size-default"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Créer des agents d'IA coordonnés avec LangGraph&nbsp;: tutoriel pratique</font></font></h1>
           </div>
          </div>
          <div class="elementor-element elementor-element-31795fb elementor-widget elementor-widget-woocommerce-breadcrumb" data-id="31795fb" data-element_type="widget" data-widget_type="woocommerce-breadcrumb.default">
           <div class="elementor-widget-container">
            <nav class="woocommerce-breadcrumb" aria-label="Fil d'Ariane">
             <a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Accueil</font></font></a><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> &nbsp;/&nbsp; </font></font><a href="https://codecut-ai.translate.goog/blog/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Blog</font></font></a><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> &nbsp;/ Création d'agents d'IA coordonnés avec LangGraph&nbsp;: tutoriel pratique
            </font></font></nav>
           </div>
          </div>
          <div class="elementor-element elementor-element-405e543 elementor-align-center elementor-widget elementor-widget-post-info" data-id="405e543" data-element_type="widget" data-widget_type="post-info.default">
           <div class="elementor-widget-container">
            <ul class="elementor-inline-items elementor-icon-list-items elementor-post-info">
             <li class="elementor-icon-list-item elementor-repeater-item-54fd367 elementor-inline-item" itemprop="datePublished"><a href="https://codecut-ai.translate.goog/2025/06/10/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"> <span class="elementor-icon-list-icon"> <i aria-hidden="true" class="fas fa-calendar"></i> </span> <span class="elementor-icon-list-text elementor-post-info__item elementor-post-info__item--type-date"> <time><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">10 juin 2025</font></font></time> </span> </a></li>
            </ul>
           </div>
          </div>
         </div>
        </div>
        <div class="elementor-element elementor-element-914b2e5 e-con-full e-flex e-con e-parent e-lazyloaded" data-id="914b2e5" data-element_type="container">
         <div class="elementor-element elementor-element-a18a40b e-con-full e-flex e-con e-child" data-id="a18a40b" data-element_type="container">
          <div class="elementor-element elementor-element-77a40af elementor-widget elementor-widget-theme-post-featured-image elementor-widget-image" data-id="77a40af" data-element_type="widget" data-widget_type="theme-post-featured-image.default">
           <div class="elementor-widget-container"><img width="931" height="488" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/langraph-featured-image.webp" class="attachment-full size-full wp-image-15294" alt="" srcset="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/langraph-featured-image.png 931w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/langraph-featured-image-768x403.png 768w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/langraph-featured-image-600x315.png 600w" sizes="(max-width: 931px) 100vw, 931px">
           </div>
          </div>
          <div class="elementor-element elementor-element-a7637e2 elementor-widget elementor-widget-theme-post-title elementor-page-title elementor-widget-heading" data-id="a7637e2" data-element_type="widget" data-widget_type="theme-post-title.default">
           <div class="elementor-widget-container">
            <h1 class="elementor-heading-title elementor-size-large"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Créer des agents d'IA coordonnés avec LangGraph&nbsp;: tutoriel pratique</font></font></h1>
           </div>
          </div>
          <div class="elementor-element elementor-element-c2ad409 elementor-widget-widescreen__width-initial elementor-widget elementor-widget-theme-post-content" data-id="c2ad409" data-element_type="widget" data-widget_type="theme-post-content.default">
           <div class="elementor-widget-container"><!--begin code -->
            <div class="pp-multiple-authors-boxes-wrapper pp-multiple-authors-wrapper pp-multiple-authors-layout-inline multiple-authors-target-the-content box-post-id-14892 box-instance-id-1 ppma_boxes_14892" data-post_id="14892" data-instance_id="1" data-additional_class="pp-multiple-authors-layout-inline.multiple-authors-target-the-content" data-original_class="pp-multiple-authors-boxes-wrapper pp-multiple-authors-wrapper box-post-id-14892 box-instance-id-1"><span class="ppma-layout-prefix"></span>
             <div class="ppma-author-category-wrap"><span class="ppma-category-group ppma-category-group-1 category-index-0">
               <ul class="pp-multiple-authors-boxes-ul author-ul-0">
                <li class="pp-multiple-authors-boxes-li author_index_0 author_khuyentran1476 has-avatar">
                 <div class="pp-author-boxes-avatar">
                  <div class="avatar-image"><img alt="Photo de Khuyen Tran" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/khuyen_headshot.webp" srcset="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/khuyen_headshot.png 1x" class="multiple_authors_guest_author_avatar avatar" height="100" width="100">
                  </div>
                  <h2 class="pp-author-boxes-name multiple-authors-name"><a href="https://codecut-ai.translate.goog/author/khuyentran1476/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" rel="author" title="Khuyen Tran" class="author url fn"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Khuyen Tran</font></font></a></h2>
                 </div>
                 <div class="pp-author-boxes-avatar-details">
                 </div></li>
                <li class="pp-multiple-authors-boxes-li author_index_1 author_bexgboost has-avatar">
                 <div class="pp-author-boxes-avatar">
                  <div class="avatar-image"><img alt="Photo de Bex Tuychiev" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/Bex-Tuychiev-1.webp" srcset="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/Bex-Tuychiev-1.png 1x" class="multiple_authors_guest_author_avatar avatar" height="100" width="100">
                  </div>
                  <h2 class="pp-author-boxes-name multiple-authors-name"><a href="https://codecut-ai.translate.goog/author/bexgboost/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" rel="author" title="Bex Tuychiev" class="author url fn"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Bex Tuychiev</font></font></a></h2>
                 </div>
                 <div class="pp-author-boxes-avatar-details">
                 </div></li>
               </ul></span>
             </div><span class="ppma-layout-suffix"></span>
            </div><!--end code -->
            <h2><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Table des matières</font></font></h2>
            <ul>
             <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#introduction"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Introduction</font></font></a></li>
             <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#getting-started-with-langgraph"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Premiers pas avec LangGraph</font></font></a>
              <ul>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#environment-setup"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Configuration de l'environnement</font></font></a></li>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#creating-agents-in-langgraph"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Création d'agents dans LangGraph</font></font></a></li>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#creating-a-supervisor-multi-agent-system"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Création d'un système multi-agent de supervision</font></font></a></li>
              </ul></li>
             <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#building-the-investment-board-of-agents"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Création du conseil d'administration des agents d'investissement</font></font></a>
              <ul>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#high-level-application-overview"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Présentation de l'application de haut niveau</font></font></a></li>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#setting-up-configuration"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Configuration de la configuration</font></font></a></li>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#defining-tools"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Définition des outils</font></font></a></li>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#creating-the-agents"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Création des agents</font></font></a></li>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#formatting-the-output"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Formatage de la sortie</font></font></a></li>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#adding-a-terminal-based-chatbot-interface"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Ajout d'une interface de chatbot basée sur un terminal</font></font></a></li>
               <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#testing-and-running-the-system"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Tester et exécuter le système</font></font></a></li>
              </ul></li>
             <li><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#final-thoughts"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Réflexions finales</font></font></a></li>
            </ul>
            <h2 id="introduction"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Introduction</font></font></h2>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Avez-
vous déjà remarqué comment un seul chatbot ne peut analyser les 
problèmes que sous un seul angle à la fois au lieu de peser plusieurs 
points de vue comme le font les humains ?</font></font></p>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Pour démontrer cela, voyons comment un seul agent analyserait la santé financière d’Apple&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">from</span> langchain_openai <span class="token keyword">import</span> ChatOpenAI
<span class="token keyword">from</span> langchain<span class="token punctuation">.</span>prompts <span class="token keyword">import</span> ChatPromptTemplate<font></font>
<font></font>
<span class="token comment"># Single agent approach</span>
single_agent <span class="token operator">=</span> ChatOpenAI<span class="token punctuation">(</span>model<span class="token operator">=</span><span class="token string">"gpt-4"</span><span class="token punctuation">)</span>
prompt <span class="token operator">=</span> ChatPromptTemplate<span class="token punctuation">.</span>from_messages<span class="token punctuation">(</span><span class="token punctuation">[</span>
    <span class="token punctuation">(</span><span class="token string">"system"</span><span class="token punctuation">,</span> <span class="token string">"You are a financial analyst. Analyze the company's health from all angles."</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    <span class="token punctuation">(</span><span class="token string">"user"</span><span class="token punctuation">,</span> <span class="token string">"Analyze Apple's financial health considering growth, risks, and market position."</span><span class="token punctuation">)</span>
<span class="token punctuation">]</span><span class="token punctuation">)</span><font></font>
<font></font>
<span class="token comment"># The agent tries to be both optimistic and pessimistic at once</span>
response <span class="token operator">=</span> single_agent<span class="token punctuation">.</span>invoke<span class="token punctuation">(</span>prompt<span class="token punctuation">.</span>format_messages<span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
<span class="token keyword">print</span><span class="token punctuation">(</span>response<span class="token punctuation">.</span>content<span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Le résultat pourrait ressembler à ceci&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-text" tabindex="0"><code class="language-text">Apple demonstrates a healthy financial status with consistent growth and a strong market position. Its innovative product pipeline, growing services segment, high-profit margins and robust balance sheets substantiate its stability.
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Texte brut</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Cette réponse pose plusieurs problèmes :</font></font></p>
            <ol>
             <li><strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Biais de confirmation</font></font></strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">&nbsp;:
 La réponse tend à être unilatérale (dans ce cas, excessivement 
positive) car il n'y a pas de perspective contrebalançante. C'est comme 
n'avoir qu'une seule voix dans un débat.</font></font></li>
             <li><strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Aucune prise de décision</font></font></strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class=""> : le robot unique fournit uniquement une analyse sans prendre de décisions ou de recommandations concrètes.</font></font></li>
            </ol>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Cette
 perspective limitée rend particulièrement difficile la réalisation de 
tâches complexes comme l’analyse d’investissement, qui nécessite 
d’équilibrer différents facteurs et perspectives.</font></font></p>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">LangGraph
 résout ce problème en fournissant un cadre pour la construction de 
systèmes multi-agents coordonnés avec des agents spécialisés travaillant
 ensemble via une communication structurée.</font></font></p>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Voici à quoi ressemblerait la même analyse avec un système multi-agents&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-text" tabindex="0"><code class="language-text">Key bull arguments centered on Apple's strong financials (record Q1 2024 profits and cash, robust Services and ecosystem growth), operational resilience, and the potential for future innovation.<font></font>
<font></font>
The bears countered with Apple's slowing annual growth, overreliance on iPhone, regulatory headwinds, China risks, and concerns about high valuation and lack of breakthrough innovation.<font></font>
<font></font>
The chairman ultimately decided on a HOLD/RESEARCH MORE position, balancing Apple's financial strength and stability against meaningful macro, regulatory, and growth risks.<font></font>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Texte brut</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Cet exemple illustre le processus de débat structuré entre agents spécialisés :</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">L'agent haussier commence par présenter son dossier d'investissement</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">L'agent de l'ours répond en soulignant les principaux risques</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Le président évalue soigneusement les deux points de vue et prend une décision finale</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Cette
 approche multi-agents produit une analyse plus approfondie et 
équilibrée par rapport à un agent unique essayant de prendre en compte 
tous les angles à la fois.</font></font></p>
            <h2><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Points clés à retenir</font></font></h2>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Voici ce que vous apprendrez :</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Construire
 des systèmes multi-agents coordonnés qui débattent à partir de 
perspectives différentes au lieu de réponses biaisées uniques</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Créez des agents spécialisés avec des outils et des invites distincts pour l'analyse des investissements haussiers/baissiers</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Mettre
 en œuvre des flux de travail de supervision qui acheminent les 
conversations entre les agents dans des séquences structurées</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Déployer des comités d'investissement prêts à la production avec intégration des données de marché en temps réel</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Évoluez au-delà de la finance vers tout domaine nécessitant des points de vue multiples et une prise de décision structurée</font></font></li>
            </ul>
            <div class="klaviyo-form-VWXSdu" style="margin: 20px;"></div>
            <h2 id="getting-started-with-langgraph"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Premiers pas avec LangGraph</font></font></h2>
            <h3 id="environment-setup"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Configuration de l'environnement</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Tout d’abord, vous devez configurer votre environnement avec les packages suivants&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-bash" tabindex="0"><code class="language-bash">pip <span class="token function">install</span> langgraph langgraph-supervisor langchain langchain-core langchain-tavily langchain-openai python-dotenv
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Frapper</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Ces packages sont nécessaires à notre système de comité d’investissement :</font></font></p>
            <ul>
             <li><code>langgraph</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">:Cadre de base pour la construction de systèmes multi-agents avec gestion d'état</font></font></li>
             <li><code>langgraph-supervisor</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">: Fournit le modèle de superviseur pour la coordination des agents</font></font></li>
             <li><code>langchain</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">et </font></font><code>langchain-core</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">: Composants fondamentaux pour les applications LLM</font></font></li>
             <li><code>langchain-tavily</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">: Intégration avec l'API de recherche Tavily pour les études de marché</font></font></li>
             <li><code>langchain-openai</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">: Intégrations de modèles OpenAI</font></font></li>
             <li><code>python-dotenv</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">: Gestion des variables d'environnement pour les clés API</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Ensuite, créez un </font></font><code>.env</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">fichier et remplissez-le avec vos clés API d’ </font></font><a href="https://translate.google.com/website?sl=en&amp;tl=fr&amp;hl=fr&amp;client=webapp&amp;u=https://platform.openai.com/"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">OpenAI</font></font></a><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> et </font></font><a href="https://translate.google.com/website?sl=en&amp;tl=fr&amp;hl=fr&amp;client=webapp&amp;u=https://www.tavily.com/"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">de Tavily</font></font></a><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> (une API de moteur de recherche).</font></font></p>
            <div class="code-toolbar"><pre class="language-bash" tabindex="0"><code class="language-bash"><span class="token assign-left variable">TAVILY_API_KEY</span><span class="token operator">=</span><span class="token string">'your-api-key-goes-here'</span>
<span class="token assign-left variable">OPENAI_API_KEY</span><span class="token operator">=</span><span class="token string">'your-api-key-goes-here'</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Frapper</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Voyons maintenant comment créer votre premier agent dans LangGraph.</font></font></p>
            <blockquote>
             <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Notez qu’à l’avenir, </font></font><a href="https://codecut-ai.translate.goog/private-ai-workflows-langchain-ollama/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">une familiarité avec les bases de LangChain</font></font></a><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> sera utile.</font></font></p>
            </blockquote>
            <h3 id="creating-agents-in-langgraph"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Création d'agents dans LangGraph</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">LangGraph
 simplifie grandement la création de votre premier agent. Voyons comment
 créer un assistant polyvalent avec fonction de recherche web en 
quelques lignes de code.</font></font></p>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Tout d’abord, nous chargeons nos variables d’environnement contenant des clés API à l’aide de </font></font><code>load_dotenv()</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">et importons les composants nécessaires&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">from</span> dotenv <span class="token keyword">import</span> load_dotenv<font></font>
<font></font>
<span class="token keyword">from</span> langgraph<span class="token punctuation">.</span>prebuilt <span class="token keyword">import</span> create_react_agent
<span class="token keyword">from</span> langchain_openai <span class="token keyword">import</span> ChatOpenAI
<span class="token keyword">from</span> langchain_tavily <span class="token keyword">import</span> TavilySearch<font></font>
<font></font>
load_dotenv<span class="token punctuation">(</span><span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Ensuite, initialisez l’outil de recherche avec </font></font><code>TavilySearch(max_results=3)</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">, qui renverra les trois premiers résultats de recherche pour n’importe quelle requête.</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python">web_search <span class="token operator">=</span> TavilySearch<span class="token punctuation">(</span>max_results<span class="token operator">=</span><span class="token number">3</span><span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Créez l'agent à l'aide de </font></font><code>create_react_agent()</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">,
 qui implémente le modèle ReAct, un cadre qui permet aux agents de 
raisonner sur les actions, de les exécuter avec des outils, d'observer 
les résultats et de planifier les étapes suivantes en conséquence.</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python">agent <span class="token operator">=</span> create_react_agent<span class="token punctuation">(</span>
    model<span class="token operator">=</span>ChatOpenAI<span class="token punctuation">(</span>model<span class="token operator">=</span><span class="token string">"gpt-4o"</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    tools<span class="token operator">=</span><span class="token punctuation">[</span>web_search<span class="token punctuation">]</span><span class="token punctuation">,</span>
    prompt<span class="token operator">=</span><span class="token string">"You are a helpful assistant that can search the web for information and summarize the results to enhance your output."</span><span class="token punctuation">,</span>
<span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Nous configurons l'agent avec les trois composants principaux&nbsp;:</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Un modèle de langage (GPT-4o dans ce cas)</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Une liste d'outils permettant à l'agent de se connecter à des outils et API externes comme un moteur de recherche</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Une invite système qui définit le rôle et le comportement de l'agent</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Enfin, nous invoquons l'agent avec une question sur l'activité boursière d'aujourd'hui :</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python">response <span class="token operator">=</span> agent<span class="token punctuation">.</span>invoke<span class="token punctuation">(</span>
    <span class="token punctuation">{</span>
        <span class="token string">"messages"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
            <span class="token punctuation">{</span>
                <span class="token string">"role"</span><span class="token punctuation">:</span> <span class="token string">"user"</span><span class="token punctuation">,</span>
                <span class="token string">"content"</span><span class="token punctuation">:</span> <span class="token string">"Find the open and close prices of Apple's stocks for June 1, 2025."</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">]</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">)</span><font></font>
<font></font>
<span class="token keyword">print</span><span class="token punctuation">(</span>response<span class="token punctuation">[</span><span class="token string">'messages'</span><span class="token punctuation">]</span><span class="token punctuation">[</span><span class="token operator">-</span><span class="token number">1</span><span class="token punctuation">]</span><span class="token punctuation">.</span>content<span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <div class="code-toolbar"><pre class="language-text" tabindex="0"><code class="language-text">June 1, 2025, was a Sunday, so financial markets were closed. The next trading day was June 2, 2025. On June 2, 2025, Apple's (AAPL) stock:<font></font>
<font></font>
- Opened at: $200.28<font></font>
- Closed at: $201.70<font></font>
<font></font>
You can verify this information on trusted sources such as Yahoo Finance and Macrotrends:<font></font>
- Source: Yahoo Finance (historical data page)<font></font>
- Source: Macrotrends Apple stock history<font></font>
<font></font>
If you need data for the trading day immediately before June 2, 2025, let me know!<font></font>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Texte brut</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">La sortie démontre le modèle ReAct en action&nbsp;:</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">L'agent raisonne sur les informations dont il a besoin (cours des actions à une date précise)</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Exécute la tâche à l'aide de l'outil de recherche Web pour trouver les informations</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Observe les résultats et raisonne à nouveau sur leur validité</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Planifie les prochaines étapes en conséquence (dans ce cas, en fournissant les données pour le prochain jour de bourse)</font></font></li>
            </ul>
            <h3 id="creating-a-supervisor-multi-agent-system"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Création d'un système multi-agent de supervision</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Un superviseur est un agent spécial qui gère les flux de travail entre plusieurs agents. Il est responsable de&nbsp;:</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Acheminement du flux de travail entre les agents</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Gérer l'historique des conversations</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">S'assurer que les agents travaillent ensemble pour atteindre l'objectif</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Créons un système multi-agents de supervision avec trois agents&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">from</span> langgraph_supervisor <span class="token keyword">import</span> create_supervisor<font></font>
<font></font>
<span class="token comment"># Define domain-specific agents with their own tools and prompts</span>
agent1 <span class="token operator">=</span> create_react_agent<span class="token punctuation">(</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">)</span>
agent2 <span class="token operator">=</span> create_react_agent<span class="token punctuation">(</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">)</span>
agent3 <span class="token operator">=</span> create_react_agent<span class="token punctuation">(</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">)</span><font></font>
<font></font>
<span class="token comment"># Create a memory checkpointer to persist conversation history for the supervisor</span>
memory <span class="token operator">=</span> MemorySaver<span class="token punctuation">(</span><span class="token punctuation">)</span><font></font>
<font></font>
supervisor <span class="token operator">=</span> create_supervisor<span class="token punctuation">(</span>
    model<span class="token operator">=</span>ChatOpenAI<span class="token punctuation">(</span>model<span class="token operator">=</span><span class="token string">"o3"</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
    agents<span class="token operator">=</span><span class="token punctuation">[</span>agent1<span class="token punctuation">,</span> agent2<span class="token punctuation">,</span> agent3<span class="token punctuation">]</span><span class="token punctuation">,</span>
    prompt<span class="token operator">=</span><span class="token string">"Detailed system prompt instructing the model how to route the workflow."</span><span class="token punctuation">,</span>
<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token builtin">compile</span><span class="token punctuation">(</span>checkpointer<span class="token operator">=</span>memory<span class="token punctuation">)</span><font></font>
<font></font>
<span class="token comment"># Call the supervisor with a user query</span>
response <span class="token operator">=</span> supervisor<span class="token punctuation">.</span>invoke<span class="token punctuation">(</span><span class="token punctuation">{</span>
    <span class="token string">"messages"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
        <span class="token punctuation">{</span>
            <span class="token string">"role"</span><span class="token punctuation">:</span> <span class="token string">"user"</span><span class="token punctuation">,</span>
            <span class="token string">"content"</span><span class="token punctuation">:</span> <span class="token string">"Your question here..."</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">]</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Le
 système multi-agents du superviseur suit un workflow structuré pour 
traiter les requêtes des utilisateurs. Voici son fonctionnement&nbsp;:</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">L'utilisateur soumet une requête au superviseur</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Le superviseur oriente la requête vers les agents appropriés en fonction de l'invite du système</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Les agents analysent les informations de manière indépendante et renvoient leurs conclusions au superviseur</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Le superviseur regroupe les résultats et génère une réponse finale</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">La réponse finale est renvoyée à l'utilisateur</font></font></li>
            </ul>
            <p><img decoding="async" alt="Diagramme de flux de travail multi-agent montrant la requête de l'utilisateur acheminée via le superviseur vers plusieurs agents et inversement" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/langraph_2.webp"></p>
            <h2 id="building-the-investment-board-of-agents"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Création du conseil d'administration des agents d'investissement</font></font></h2>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Maintenant
 que nous avons couvert les bases de LangGraph, passons à l'exploration 
de l'application du conseil d'investissement des agents.</font></font></p>
            <h3 id="high-level-application-overview"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Présentation de l'application de haut niveau</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Voici un aperçu de haut niveau de l’application&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-text" tabindex="0"><code class="language-text">investment-committee/<font></font>
├── src/<font></font>
│   ├── __init__.py          # Package marker<font></font>
│   ├── config.py            # Prompts and model configuration<font></font>
│   ├── tools.py             # Agent tools organized by function<font></font>
│   ├── utils.py             # Utility functions for display<font></font>
│   └── agents.py            # Agent and supervisor creation<font></font>
├── main.py                  # Command-line interface<font></font>
├── requirements.txt         # Python dependencies<font></font>
├── .env.example            # Environment variables template<font></font>
└── README.md               # Documentation<font></font>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Texte brut</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Dans cette structure :</font></font></p>
            <ul>
             <li><code>src/config.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">contient la configuration du système</font></font></li>
             <li><code>src/tools.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">contient les outils pour les agents</font></font></li>
             <li><code>src/utils.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">contient les fonctions utilitaires pour l'application</font></font></li>
             <li><code>src/agents.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">contient la création de l'agent et du superviseur</font></font></li>
             <li><code>main.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">est le point d'entrée de l'interface de ligne de commande</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Examinons chacun de ces fichiers plus en détail.</font></font></p>
            <h3 id="setting-up-configuration"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Configuration de la configuration</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Ce </font></font><code>config.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">fichier
 centralise toute la configuration système en un seul emplacement, 
facilitant ainsi la personnalisation et la maintenance de l'application.
 Il contient deux principaux types de configuration&nbsp;:</font></font></p>
            <ul>
             <li><strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Configuration du modèle</font></font></strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">&nbsp;: spécifie le modèle de langue à utiliser pour tous les agents, qui est actuellement défini sur </font></font><code>"openai:gpt-4"</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">.</font></font></li>
             <li><strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Invites de l'agent</font></font></strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> : définissez la personnalité et le comportement de chaque agent grâce à des invites système détaillées.</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Voici l’invite du superviseur&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python">SUPERVISOR_PROMPT <span class="token operator">=</span> <span class="token punctuation">(</span>
    <span class="token string">"You are a SIMPLE ROUTER with one final summary task.\n\n"</span>
    <span class="token string">"MANDATORY WORKFLOW (follow exactly):\n"</span>
    <span class="token string">"1. bull_agent: Make initial bullish case\n"</span>
    <span class="token string">"2. bear_agent: Make initial bearish case\n"</span>
    <span class="token string">"3. bull_agent: Counter the bear's specific arguments\n"</span>
    <span class="token string">"4. bear_agent: Counter the bull's specific arguments\n"</span>
    <span class="token string">"5. chairman_agent: Make final investment decision\n"</span>
    <span class="token string">"6. YOU: Summarize the debate outcome\n\n"</span>
    <span class="token string">"RULES:\n"</span>
    <span class="token string">"- DO NOT summarize until AFTER chairman makes decision\n"</span>
    <span class="token string">"- ALWAYS end with chairman_agent making the decision first\n"</span>
    <span class="token string">"- Route agents in the exact order above\n"</span>
    <span class="token string">"- After chairman decides, provide a brief summary of:\n"</span>
    <span class="token string">"  • Key bull arguments\n"</span>
    <span class="token string">"  • Key bear arguments  \n"</span>
    <span class="token string">"  • Chairman's final decision and reasoning\n"</span>
    <span class="token string">"- Keep summary concise (3-4 sentences max)"</span>
<span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <h3 id="defining-tools"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Définition des outils</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Les
 outils sont des fonctions permettant aux agents d'interagir avec des 
sources de données externes et d'effectuer des tâches spécifiques. Ce 
système utilise six outils&nbsp;:</font></font></p>
            <ul>
             <li><code>find_positive_news</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">:Recherche des nouvelles et des développements positifs sur une action</font></font></li>
             <li><code>calculate_growth_potential</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">:Calcule les mesures de croissance de base et les indicateurs haussiers</font></font></li>
             <li><code>find_negative_news</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">: Recherche des nouvelles négatives et des risques concernant une action</font></font></li>
             <li><code>assess_market_risks</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">:Évalue les risques globaux du marché et les indicateurs baissiers</font></font></li>
             <li><code>get_current_market_sentiment</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">:Obtient le sentiment général du marché et les performances récentes</font></font></li>
             <li><code>make_investment_decision</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">:Fait une recommandation d'investissement finale basée sur des arguments haussiers et baissiers</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Chaque
 outil utilise l'API de recherche Tavily pour collecter des informations
 sur le marché en temps réel, garantissant que les agents basent leurs 
arguments sur des données actuelles plutôt que sur des informations de 
formation obsolètes.</font></font></p>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Voici le code de l' </font></font><code>find_positive_news</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">outil :</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">def</span> <span class="token function">find_positive_news</span><span class="token punctuation">(</span>stock_symbol<span class="token punctuation">:</span> <span class="token builtin">str</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""Search for positive news and developments about a stock"""</span>
    query <span class="token operator">=</span> <span class="token string-interpolation"><span class="token string">f"</span><span class="token interpolation"><span class="token punctuation">{</span>stock_symbol<span class="token punctuation">}</span></span><span class="token string"> stock positive news earnings growth revenue profit upgrade"</span></span>
    keywords <span class="token operator">=</span> <span class="token punctuation">[</span><span class="token string">"profit"</span><span class="token punctuation">,</span> <span class="token string">"growth"</span><span class="token punctuation">,</span> <span class="token string">"upgrade"</span><span class="token punctuation">,</span> <span class="token string">"beat"</span><span class="token punctuation">,</span> <span class="token string">"strong"</span><span class="token punctuation">,</span> <span class="token string">"positive"</span><span class="token punctuation">,</span> <span class="token string">"bullish"</span><span class="token punctuation">]</span>
    prefix <span class="token operator">=</span> <span class="token string">"<img draggable="false" role="img" class="emoji" alt="🐂" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f402.svg"> POSITIVE SIGNALS"</span>
    default <span class="token operator">=</span> <span class="token string">"Limited positive news found, but that could mean it's undervalued!"</span>
    <span class="token keyword">return</span> search_and_extract_signals<span class="token punctuation">(</span>stock_symbol<span class="token punctuation">,</span> query<span class="token punctuation">,</span> keywords<span class="token punctuation">,</span> prefix<span class="token punctuation">,</span> default<span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Afficher le code complet des outils dans le fichier </font></font><a href="https://translate.google.com/website?sl=en&amp;tl=fr&amp;hl=fr&amp;client=webapp&amp;u=https://github.com/CodeCutTech/langraph-demo/blob/main/src/tools.py"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">tools.py</font></font></a><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> .</font></font></p>
            <h3 id="creating-the-agents"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Création des agents</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Les
 agents sont les composants centraux du système multi-agents. Ils sont 
chargés d'analyser les informations et de prendre des décisions.</font></font></p>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Le </font></font><code>agents.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">fichier crée les agents et le superviseur. Il existe trois agents spécialisés&nbsp;:</font></font></p>
            <ol>
             <li><strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Agent haussier</font></font></strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> : Un analyste optimiste qui recherche des indicateurs positifs et un potentiel de croissance</font></font></li>
             <li><strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Agent baissier</font></font></strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> : Un analyste pessimiste qui identifie les risques et les signaux négatifs</font></font></li>
             <li><strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Agent président</font></font></strong><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"> : Un décideur neutre qui pèse les deux côtés et fait des recommandations d'investissement finales</font></font></li>
            </ol>
            <p><img decoding="async" alt="Diagramme du comité d'investissement montrant les agents haussiers, baissiers et présidents avec leurs rôles dans le processus d'analyse" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/langraph_1.webp"></p>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Chaque agent est créé avec la </font></font><code>create_react_agent</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">fonction avec les paramètres suivants&nbsp;:</font></font></p>
            <ul>
             <li><code>model</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">:Le modèle de langage à utiliser</font></font></li>
             <li><code>tools</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">:Les outils à utiliser</font></font></li>
             <li><code>prompt</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">: L'invite système à utiliser</font></font></li>
             <li><code>name</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">:Le nom de l'agent</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Voici le code pour les trois agents :</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">def</span> <span class="token function">create_bull_agent</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""Create the bull (optimistic) investment agent"""</span>
    <span class="token keyword">return</span> create_react_agent<span class="token punctuation">(</span>
        model<span class="token operator">=</span>MODEL_NAME<span class="token punctuation">,</span>
        tools<span class="token operator">=</span><span class="token punctuation">[</span>find_positive_news<span class="token punctuation">,</span> calculate_growth_potential<span class="token punctuation">]</span><span class="token punctuation">,</span>
        prompt<span class="token operator">=</span>BULL_AGENT_PROMPT<span class="token punctuation">,</span>
        name<span class="token operator">=</span><span class="token string">"bull_agent"</span><span class="token punctuation">,</span>
    <span class="token punctuation">)</span><font></font>
<font></font>
<font></font>
<span class="token keyword">def</span> <span class="token function">create_bear_agent</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""Create the bear (pessimistic) investment agent"""</span>
    <span class="token keyword">return</span> create_react_agent<span class="token punctuation">(</span>
        model<span class="token operator">=</span>MODEL_NAME<span class="token punctuation">,</span>
        tools<span class="token operator">=</span><span class="token punctuation">[</span>find_negative_news<span class="token punctuation">,</span> assess_market_risks<span class="token punctuation">]</span><span class="token punctuation">,</span>
        prompt<span class="token operator">=</span>BEAR_AGENT_PROMPT<span class="token punctuation">,</span>
        name<span class="token operator">=</span><span class="token string">"bear_agent"</span><span class="token punctuation">,</span>
    <span class="token punctuation">)</span><font></font>
<font></font>
<font></font>
<span class="token keyword">def</span> <span class="token function">create_chairman_agent</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""Create the chairman (decision maker) agent"""</span>
    <span class="token keyword">return</span> create_react_agent<span class="token punctuation">(</span>
        model<span class="token operator">=</span>MODEL_NAME<span class="token punctuation">,</span>
        tools<span class="token operator">=</span><span class="token punctuation">[</span>get_current_market_sentiment<span class="token punctuation">,</span> make_investment_decision<span class="token punctuation">]</span><span class="token punctuation">,</span>
        prompt<span class="token operator">=</span>CHAIRMAN_AGENT_PROMPT<span class="token punctuation">,</span>
        name<span class="token operator">=</span><span class="token string">"chairman_agent"</span><span class="token punctuation">,</span>
    <span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Le superviseur regroupe tous les agents sous une gestion coordonnée des flux de travail. Voici son code&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">def</span> <span class="token function">create_investment_supervisor</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""Create the supervisor that manages the investment committee"""</span>
    bull_agent <span class="token operator">=</span> create_bull_agent<span class="token punctuation">(</span><span class="token punctuation">)</span>
    bear_agent <span class="token operator">=</span> create_bear_agent<span class="token punctuation">(</span><span class="token punctuation">)</span>
    chairman_agent <span class="token operator">=</span> create_chairman_agent<span class="token punctuation">(</span><span class="token punctuation">)</span><font></font>
<font></font>
    supervisor <span class="token operator">=</span> create_supervisor<span class="token punctuation">(</span>
        model<span class="token operator">=</span>init_chat_model<span class="token punctuation">(</span>MODEL_NAME<span class="token punctuation">)</span><span class="token punctuation">,</span>
        agents<span class="token operator">=</span><span class="token punctuation">[</span>bull_agent<span class="token punctuation">,</span> bear_agent<span class="token punctuation">,</span> chairman_agent<span class="token punctuation">]</span><span class="token punctuation">,</span>
        prompt<span class="token operator">=</span>SUPERVISOR_PROMPT<span class="token punctuation">,</span>
        add_handoff_back_messages<span class="token operator">=</span><span class="token boolean">True</span><span class="token punctuation">,</span>
        output_mode<span class="token operator">=</span><span class="token string">"full_history"</span><span class="token punctuation">,</span>
    <span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token builtin">compile</span><span class="token punctuation">(</span><span class="token punctuation">)</span><font></font>
<font></font>
    <span class="token keyword">return</span> supervisor
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Dans ce code :</font></font></p>
            <ul>
             <li><code>add_handoff_back_messages=True</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">garantit
 que les agents peuvent faire référence aux arguments précédents des 
autres, créant ainsi un véritable débat conversationnel plutôt qu'une 
analyse isolée.</font></font></li>
             <li><code>output_mode="full_history"</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">fournit
 un contexte de conversation complet, vous permettant de voir l'ensemble
 du processus de raisonnement plutôt que simplement les décisions 
finales.</font></font></li>
            </ul>
            <h3 id="formatting-the-output"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Formatage de la sortie</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Pour formater la sortie, nous utilisons la </font></font><code>pretty_print_messages</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">fonction in </font></font><code>utils.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">. Elle prend les mises à jour du flux de conversation et les formate en sortie lisible&nbsp;:</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Identifier quel agent parle</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Traitement des mises à jour du flux de travail du superviseur et des interactions des agents</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Conversion des objets de message dans un format lisible par l'homme</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Cela
 nous permet de voir clairement le débat de va-et-vient entre les agents
 lorsqu’ils analysent les opportunités d’investissement.</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">while</span> <span class="token boolean">True</span><span class="token punctuation">:</span>
    <span class="token keyword">try</span><span class="token punctuation">:</span>
        stock_input <span class="token operator">=</span> <span class="token builtin">input</span><span class="token punctuation">(</span><span class="token string">"Enter stock symbol (or 'quit' to exit): "</span><span class="token punctuation">)</span><span class="token punctuation">.</span>strip<span class="token punctuation">(</span><span class="token punctuation">)</span><font></font>
<font></font>
        <span class="token keyword">if</span> stock_input<span class="token punctuation">.</span>lower<span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword">in</span> <span class="token punctuation">[</span><span class="token string">"quit"</span><span class="token punctuation">,</span> <span class="token string">"exit"</span><span class="token punctuation">,</span> <span class="token string">"q"</span><span class="token punctuation">]</span><span class="token punctuation">:</span>
            <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"\n<img draggable="false" role="img" class="emoji" alt="👋" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f44b.svg"> Goodbye! Happy investing!"</span><span class="token punctuation">)</span>
            <span class="token keyword">break</span><font></font>
<font></font>
        <span class="token keyword">if</span> <span class="token keyword">not</span> stock_input<span class="token punctuation">:</span>
            <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"Please enter a valid stock symbol."</span><span class="token punctuation">)</span>
            <span class="token keyword">continue</span><font></font>
<font></font>
        analyze_stock<span class="token punctuation">(</span>supervisor<span class="token punctuation">,</span> stock_input<span class="token punctuation">)</span><font></font>
<font></font>
    <span class="token keyword">except</span> KeyboardInterrupt<span class="token punctuation">:</span>
        <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"\n\n<img draggable="false" role="img" class="emoji" alt="👋" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f44b.svg"> Goodbye! Happy investing!"</span><span class="token punctuation">)</span>
        sys<span class="token punctuation">.</span>exit<span class="token punctuation">(</span><span class="token number">0</span><span class="token punctuation">)</span>
    <span class="token keyword">except</span> Exception <span class="token keyword">as</span> e<span class="token punctuation">:</span>
        <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string-interpolation"><span class="token string">f"\n<img draggable="false" role="img" class="emoji" alt="❌" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/274c.svg"> Error: </span><span class="token interpolation"><span class="token punctuation">{</span>e<span class="token punctuation">}</span></span><span class="token string">"</span></span><span class="token punctuation">)</span>
        <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"Please try again with a different stock symbol."</span><span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <h3 id="adding-a-terminal-based-chatbot-interface"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Ajout d'une interface de chatbot basée sur un terminal</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Ensuite,
 configurons une interface de ligne de commande interactive qui rend le 
système de comité d’investissement accessible aux utilisateurs.</font></font></p>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Dans le </font></font><code>main.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">fichier, nous configurons le message de bienvenue qui sera affiché lors de l'initialisation de l'application :</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">def</span> <span class="token function">print_welcome</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""Print welcome message and system description"""</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"<img draggable="false" role="img" class="emoji" alt="💼" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f4bc.svg"> INVESTMENT COMMITTEE SYSTEM"</span><span class="token punctuation">)</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"="</span> <span class="token operator">*</span> <span class="token number">50</span><span class="token punctuation">)</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"<img draggable="false" role="img" class="emoji" alt="🐂" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f402.svg"> Bull Agent: Finds reasons to BUY"</span><span class="token punctuation">)</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"<img draggable="false" role="img" class="emoji" alt="🐻" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f43b.svg"> Bear Agent: Finds reasons to AVOID"</span><span class="token punctuation">)</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"<img draggable="false" role="img" class="emoji" alt="🎯" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f3af.svg"> Chairman: Makes final decision"</span><span class="token punctuation">)</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"="</span> <span class="token operator">*</span> <span class="token number">50</span><span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Ensuite, créez la </font></font><code>analyze_stock</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">fonction qui gère le modèle d’interaction principal&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-python" tabindex="0"><code class="language-python"><span class="token keyword">def</span> <span class="token function">analyze_stock</span><span class="token punctuation">(</span>supervisor<span class="token punctuation">,</span> stock_symbol<span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token triple-quoted-string string">"""Analyze a stock using the investment committee"""</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string-interpolation"><span class="token string">f"\n<img draggable="false" role="img" class="emoji" alt="📈" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f4c8.svg"> ANALYZING: </span><span class="token interpolation"><span class="token punctuation">{</span>stock_symbol<span class="token punctuation">.</span>upper<span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">}</span></span><span class="token string">"</span></span><span class="token punctuation">)</span>
    <span class="token keyword">print</span><span class="token punctuation">(</span><span class="token string">"-"</span> <span class="token operator">*</span> <span class="token number">30</span><span class="token punctuation">)</span><font></font>
<font></font>
    user_query <span class="token operator">=</span> <span class="token string-interpolation"><span class="token string">f"Should I invest in </span><span class="token interpolation"><span class="token punctuation">{</span>stock_symbol<span class="token punctuation">}</span></span><span class="token string"> stock? I want to hear both bullish and bearish arguments before making a decision."</span></span><font></font>
<font></font>
    <span class="token keyword">for</span> chunk <span class="token keyword">in</span> supervisor<span class="token punctuation">.</span>stream<span class="token punctuation">(</span>
        <span class="token punctuation">{</span>
            <span class="token string">"messages"</span><span class="token punctuation">:</span> <span class="token punctuation">[</span>
                <span class="token punctuation">{</span>
                    <span class="token string">"role"</span><span class="token punctuation">:</span> <span class="token string">"user"</span><span class="token punctuation">,</span>
                    <span class="token string">"content"</span><span class="token punctuation">:</span> user_query<span class="token punctuation">,</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">]</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">)</span><span class="token punctuation">:</span>
        pretty_print_messages<span class="token punctuation">(</span>chunk<span class="token punctuation">,</span> last_message<span class="token operator">=</span><span class="token boolean">True</span><span class="token punctuation">)</span>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Python</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">La fonction:</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Prend le symbole boursier et construit une requête en langage naturel demandant des conseils en investissement</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Permet </font></font><code>supervisor.stream()</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">d'obtenir des mises à jour en temps réel de chaque agent pendant qu'il analyse le stock</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Affiche les réponses des agents de manière incrémentielle </font></font><code>pretty_print_messages()</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">pour montrer le processus d'analyse</font></font></li>
            </ul>
            <h3 id="testing-and-running-the-system"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Tester et exécuter le système</font></font></h3>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Nous sommes maintenant prêts à tester et exécuter le système&nbsp;! Exécutez le </font></font><code>main.py</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">script pour démarrer l'application&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-bash" tabindex="0"><code class="language-bash">python main.py
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Frapper</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Vous serez invité à saisir un symbole boursier. Essayons avec</font></font><code>NVDA</code><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-text" tabindex="0"><code class="language-text"><img draggable="false" role="img" class="emoji" alt="💼" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f4bc.svg"> INVESTMENT COMMITTEE SYSTEM<font></font>
==================================================<font></font>
<img draggable="false" role="img" class="emoji" alt="🐂" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f402.svg"> Bull Agent: Finds reasons to BUY
<img draggable="false" role="img" class="emoji" alt="🐻" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f43b.svg"> Bear Agent: Finds reasons to AVOID
<img draggable="false" role="img" class="emoji" alt="🎯" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f3af.svg"> Chairman: Makes final decision<font></font>
==================================================<font></font>
<img draggable="false" role="img" class="emoji" alt="🔄" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f504.svg"> Initializing investment committee...
<img draggable="false" role="img" class="emoji" alt="✅" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/2705.svg"> Committee ready!<font></font>
<font></font>
Enter stock symbol (or 'quit' to exit): NVDA<font></font>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Texte brut</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Le résultat ressemblera à ceci&nbsp;:</font></font></p>
            <div class="code-toolbar"><pre class="language-text" tabindex="0"><code class="language-text"><img draggable="false" role="img" class="emoji" alt="📈" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/1f4c8.svg"> ANALYZING: NVDA<font></font>
------------------------------<font></font>
...<font></font>
<font></font>
================================== Ai Message ==================================<font></font>
Name: supervisor<font></font>
<font></font>
**Summary:**<font></font>
The bullish case for NVDA centers on historic revenue and profit growth, AI market dominance, ecosystem lock-in, and robust financial momentum. The bearish side warns of bubble-like valuation, heavy customer concentration, rising competition, geopolitical risks, and the potential for hardware cycle reversals. The chairman ultimately recommends a HOLD or further research, acknowledging both the powerful growth story and the elevated risks, making it prudent to wait for more clarity or a better entry point.<font></font>
</code></pre><div class="toolbar"><div class="toolbar-item"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Texte brut</font></font></span></div><div class="toolbar-item"><button class="copy-to-clipboard-button" type="button" data-copy-state="copy"><span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copie</font></font></span></button></div></div></div>
            <h2 id="final-thoughts"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Réflexions finales</font></font></h2>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Le
 système de comité d'investissement démontre la puissance des agents IA 
coordonnés dans la prise de décisions complexes. En combinant des agents
 spécialisés aux perspectives différentes, nous avons créé un système 
capable de&nbsp;:</font></font></p>
            <ul>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Fournir une analyse équilibrée grâce à un débat structuré</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Considérer plusieurs points de vue simultanément</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Prendre des décisions éclairées basées sur des données complètes</font></font></li>
             <li><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Adaptez-vous aux nouvelles informations grâce aux mises à jour en temps réel</font></font></li>
            </ul>
            <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto" class="">Cette
 approche peut être étendue à d’autres domaines où des perspectives 
multiples et une prise de décision structurée sont cruciales, comme 
l’évaluation des risques, l’analyse des politiques ou la planification 
stratégique.</font></font></p><button class="simplefavorite-button" data-postid="15293" data-siteid="1" data-groupid="1" data-favoritecount="2" style=""><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Préféré</font></font><i class="sf-icon-star-empty"></i></button>
           </div>
          </div>
         </div>
        </div>
        <div class="elementor-element elementor-element-6c9c2df rp-wrap e-flex e-con-boxed e-con e-parent e-lazyloaded" data-id="6c9c2df" data-element_type="container">
         <div class="e-con-inner">
          <div class="elementor-element elementor-element-1d0db22 elementor-widget elementor-widget-heading" data-id="1d0db22" data-element_type="widget" data-widget_type="heading.default">
           <div class="elementor-widget-container">
            <h2 class="elementor-heading-title elementor-size-default"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Articles connexes</font></font></h2>
           </div>
          </div>
          <div class="elementor-element elementor-element-63f8595 elementor-widget-widescreen__width-initial elementor-grid-tablet-1 elementor-grid-widescreen-3 elementor-posts--align-center elementor-grid-3 elementor-grid-mobile-1 elementor-posts--thumbnail-top elementor-posts__hover-gradient elementor-widget elementor-widget-posts" data-id="63f8595" data-element_type="widget" data-settings="{&quot;cards_row_gap&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:30,&quot;sizes&quot;:[]},&quot;cards_columns_tablet&quot;:&quot;1&quot;,&quot;cards_row_gap_widescreen&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:59,&quot;sizes&quot;:[]},&quot;cards_row_gap_tablet&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:30,&quot;sizes&quot;:[]},&quot;cards_columns_widescreen&quot;:&quot;3&quot;,&quot;cards_masonry&quot;:&quot;yes&quot;,&quot;cards_columns&quot;:&quot;3&quot;,&quot;cards_columns_mobile&quot;:&quot;1&quot;,&quot;cards_row_gap_laptop&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:[]},&quot;cards_row_gap_mobile&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:[]}}" data-widget_type="posts.cards">
           <div class="elementor-widget-container">
            <div class="elementor-posts-container elementor-posts elementor-posts--skin-cards elementor-grid elementor-posts-masonry" role="list">
             <article class="elementor-post elementor-grid-item post-16860 post type-post status-publish format-standard has-post-thumbnail hentry category-blog category-data-engineer category-llm tag-cloudquery tag-embeddings tag-etl tag-pgvector tag-rag tag-tutorial tag-vector-database" role="listitem">
              <div class="elementor-post__card"><a class="elementor-post__thumbnail__link" href="https://codecut-ai.translate.goog/cloudquery-pgvector-rag-pipelines/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" tabindex="-1">
                <div class="elementor-post__thumbnail">
                 <img width="1200" height="628" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/cloudquery.webp" class="attachment-full size-full wp-image-16862" alt="" decoding="async" srcset="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/cloudquery.png 1200w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/cloudquery-986x516.png 986w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/cloudquery-768x402.png 768w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/cloudquery-600x314.png 600w" sizes="(max-width: 1200px) 100vw, 1200px">
                </div></a>
               <div class="elementor-post__text">
                <p class="elementor-post__title"><a href="https://codecut-ai.translate.goog/cloudquery-pgvector-rag-pipelines/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Recherche sémantique Hacker News&nbsp;: RAG de production avec CloudQuery et Postgres</font></font></a></p>
               </div>
               <div class="elementor-post__meta-data"><span class="elementor-post-date"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">22 septembre 2025</font></font></span>
               </div>
              </div>
             </article>
             <article class="elementor-post elementor-grid-item post-16823 post type-post status-publish format-standard has-post-thumbnail hentry category-blog category-python-utilities tag-documentation tag-jupyter tag-latex tag-mathematics tag-python" role="listitem">
              <div class="elementor-post__card"><a class="elementor-post__thumbnail__link" href="https://codecut-ai.translate.goog/python-code-to-latex-jupyter-notebooks/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" tabindex="-1">
                <div class="elementor-post__thumbnail">
                 <img loading="lazy" width="1200" height="628" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/latex-1.webp" class="attachment-full size-full wp-image-16848" alt="" decoding="async" srcset="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/latex-1.png 1200w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/latex-1-986x516.png 986w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/latex-1-768x402.png 768w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/latex-1-600x314.png 600w" sizes="(max-width: 1200px) 100vw, 1200px">
                </div></a>
               <div class="elementor-post__text">
                <p class="elementor-post__title"><a href="https://codecut-ai.translate.goog/python-code-to-latex-jupyter-notebooks/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">3 outils qui convertissent automatiquement le code Python en mathématiques LaTeX</font></font></a></p>
               </div>
               <div class="elementor-post__meta-data"><span class="elementor-post-date"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">14 septembre 2025</font></font></span>
               </div>
              </div>
             </article>
             <article class="elementor-post elementor-grid-item post-16796 post type-post status-publish format-standard has-post-thumbnail hentry category-blog category-llm category-machine-learning-article tag-entity-extraction tag-gliner tag-langextract tag-nlp tag-python tag-regex tag-spacy" role="listitem">
              <div class="elementor-post__card"><a class="elementor-post__thumbnail__link" href="https://codecut-ai.translate.goog/langextract-vs-spacy-entity-extraction-comparison/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" tabindex="-1">
                <div class="elementor-post__thumbnail">
                 <img loading="lazy" width="1200" height="628" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/entity-extraction.webp" class="attachment-full size-full wp-image-16801" alt="" decoding="async" srcset="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/entity-extraction.png 1200w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/entity-extraction-986x516.png 986w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/entity-extraction-768x402.png 768w, Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/entity-extraction-600x314.png 600w" sizes="(max-width: 1200px) 100vw, 1200px">
                </div></a>
               <div class="elementor-post__text">
                <p class="elementor-post__title"><a href="https://codecut-ai.translate.goog/langextract-vs-spacy-entity-extraction-comparison/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">langextract vs spaCy&nbsp;: Extraction d'entités basée sur l'IA ou sur des règles</font></font></a></p>
               </div>
               <div class="elementor-post__meta-data"><span class="elementor-post-date"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">10 septembre 2025</font></font></span>
               </div>
              </div>
             </article>
            </div>
           </div>
          </div>
         </div>
        </div>
        <section class="elementor-section elementor-top-section elementor-element elementor-element-dd6b76b elementor-section-boxed elementor-section-height-default elementor-section-height-default" data-id="dd6b76b" data-element_type="section" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
         <div class="elementor-container elementor-column-gap-default">
          <div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-1d18334" data-id="1d18334" data-element_type="column">
           <div class="elementor-widget-wrap elementor-element-populated">
            <div class="elementor-element elementor-element-959881c elementor-widget elementor-widget-post-comments" data-id="959881c" data-element_type="widget" data-widget_type="post-comments.theme_comments">
             <div class="elementor-widget-container">
              <div id="comments" class="comments-area comment-form-position-below ">
               <div id="respond" class="comment-respond">
                <h3 id="reply-title" class="comment-reply-title"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Laissez un commentaire</font></font><small><a rel="nofollow" id="cancel-comment-reply-link" href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#respond" style="display:none;"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Annuler la réponse</font></font></a></small></h3>
                <form action="https://codecut.ai/wp-comments-post.php" method="post" id="ast-commentform" class="comment-form">
                 <p class="comment-notes"><span id="email-notes"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Votre adresse e-mail ne sera pas publiée. </font></font></span> <span class="required-field-message"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Les champs obligatoires sont marqués d' un astérisque </font></font><span class="required"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">(*).</font></font></span></span></p>
                 <div class="ast-row comment-textarea">
                  <fieldset class="comment-form-comment">
                   <legend class="comment-form-legend"></legend>
                   <div class="comment-form-textarea ast-col-lg-12">
                    <label for="comment" class="screen-reader-text"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Tapez ici..</font></font></label><textarea id="comment" name="comment" placeholder="Tapez ici.." cols="45" rows="8" aria-required="true"></textarea>
                   </div>
                  </fieldset>
                 </div>
                 <div class="ast-comment-formwrap ast-row">
                  <p class="comment-form-author ast-col-xs-12 ast-col-sm-12 ast-col-md-4 ast-col-lg-4"><label for="author" class="screen-reader-text"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Nom</font></font></label> <input id="author" name="author" type="text" placeholder="Nom" size="30" autocomplete="name"></p>
                  <p class="comment-form-email ast-col-xs-12 ast-col-sm-12 ast-col-md-4 ast-col-lg-4"><label for="email" class="screen-reader-text"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">E-mail</font></font></label> <input id="email" name="email" type="text" placeholder="E-mail" size="30" autocomplete="email"></p>
                  <p class="comment-form-url ast-col-xs-12 ast-col-sm-12 ast-col-md-4 ast-col-lg-4"><label for="url" class="screen-reader-text"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Site web</font></font></label> <input id="url" name="url" type="text" placeholder="Site web" size="30" autocomplete="url"></p>
                 </div>
                 <p class="comment-form-cookies-consent"><input id="wp-comment-cookies-consent" name="wp-comment-cookies-consent" type="checkbox" value="yes"> <label for="wp-comment-cookies-consent"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Enregistrer mon nom, mon e-mail et mon site web dans le navigateur pour mon prochain commentaire.</font></font></label></p>
                 <p class="form-submit"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><input name="submit" type="submit" id="submit" class="submit" value="Poster un commentaire »"></font></font></font></font> <input type="hidden" name="comment_post_ID" value="15293" id="comment_post_ID"> <input type="hidden" name="comment_parent" id="comment_parent" value="0"></p>
                 <p style="display: none;"><input type="hidden" id="akismet_comment_nonce" name="akismet_comment_nonce" value="81d49e1bf3"></p>
                 <p style="display: none !important;" class="akismet-fields-container" data-prefix="ak_"><label><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Δ</font></font><textarea name="ak_hp_textarea" cols="45" rows="8" maxlength="100"></textarea></label><input type="hidden" id="ak_js_1" name="ak_js" value="1759234615315"><script>document.getElementById( "ak_js_1" ).setAttribute( "value", ( new Date() ).getTime() );</script></p>
                </form>
               </div><!-- #respond -->
              </div><!-- #comments -->
             </div>
            </div>
           </div>
          </div>
         </div>
        </section>
       </div>
      </main><!-- #main -->
     </div><!-- #primary -->
    </div><!-- ast-container -->
   </div><!-- #content -->
   <footer data-elementor-type="footer" data-elementor-id="7720" class="elementor elementor-7720 elementor-location-footer" data-elementor-post-type="elementor_library">
    <div class="elementor-element elementor-element-68e855b e-flex e-con-boxed e-con e-parent" data-id="68e855b" data-element_type="container">
     <div class="e-con-inner">
      <div class="elementor-element elementor-element-6846aef e-con-full e-flex e-con e-child" data-id="6846aef" data-element_type="container">
       <div class="elementor-element elementor-element-a2c6279 elementor-widget elementor-widget-heading" data-id="a2c6279" data-element_type="widget" data-widget_type="heading.default">
        <div class="elementor-widget-container">
         <h4 class="elementor-heading-title elementor-size-default"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Restez à jour avec </font></font><br><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">
           vos compétences en matière de données grâce à </font></font><br><span style="color:#e583b6"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">CodeCut</font></font></span></h4>
        </div>
       </div>
       <div class="elementor-element elementor-element-d24a60a elementor-widget__width-initial elementor-widget-tablet__width-inherit elementor-widget-mobile__width-inherit elementor-widget elementor-widget-text-editor" data-id="d24a60a" data-element_type="widget" data-widget_type="text-editor.default">
        <div class="elementor-widget-container">
         <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">CodeCut
 est une plateforme qui propose des extraits de code courts et 
visuellement attrayants liés à la science des données, à l'analyse des 
données, à l'ingénierie des données et à la programmation Python.</font></font></p>
        </div>
       </div>
      </div>
      <div class="elementor-element elementor-element-10561f5 e-con-full e-flex e-con e-child" data-id="10561f5" data-element_type="container">
       <div class="elementor-element elementor-element-9db092d e-con-full e-flex e-con e-child" data-id="9db092d" data-element_type="container">
        <div class="elementor-element elementor-element-8e86b63 e-con-full e-flex e-con e-child" data-id="8e86b63" data-element_type="container">
         <div class="elementor-element elementor-element-23b4596 elementor-view-default elementor-widget elementor-widget-icon" data-id="23b4596" data-element_type="widget" data-widget_type="icon.default">
          <div class="elementor-widget-container">
           <div class="elementor-icon-wrapper"><a class="elementor-icon" href="mailto:<EMAIL>?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"> <i aria-hidden="true" class="hm hm-envelop"></i> </a>
           </div>
          </div>
         </div>
         <div class="elementor-element elementor-element-f059a0a elementor-widget elementor-widget-heading" data-id="f059a0a" data-element_type="widget" data-widget_type="heading.default">
          <div class="elementor-widget-container">
           <h5 class="elementor-heading-title elementor-size-default"><a href="mailto:<EMAIL>?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Envoyez un message</font></font></a></h5>
          </div>
         </div>
         <div class="elementor-element elementor-element-5ca2933 elementor-widget-tablet__width-inherit elementor-widget elementor-widget-text-editor" data-id="5ca2933" data-element_type="widget" data-widget_type="text-editor.default">
          <div class="elementor-widget-container">
           <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto"><EMAIL></font></font></p>
          </div>
         </div>
        </div>
        <div class="elementor-element elementor-element-c022a4c e-con-full e-flex e-con e-child" data-id="c022a4c" data-element_type="container">
         <div class="elementor-element elementor-element-b5de218 elementor-view-default elementor-widget elementor-widget-icon" data-id="b5de218" data-element_type="widget" data-widget_type="icon.default">
          <div class="elementor-widget-container">
           <div class="elementor-icon-wrapper"><a class="elementor-icon" href="https://codecut-ai.translate.goog/contact-us-page-new/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"> <i aria-hidden="true" class="hm hm-chat-bubble-single"></i> </a>
           </div>
          </div>
         </div>
         <div class="elementor-element elementor-element-9f9b58a elementor-widget elementor-widget-heading" data-id="9f9b58a" data-element_type="widget" data-widget_type="heading.default">
          <div class="elementor-widget-container">
           <h5 class="elementor-heading-title elementor-size-default"><a href="https://codecut-ai.translate.goog/contact-us-page-new/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Entrer en contact</font></font></a></h5>
          </div>
         </div>
         <div class="elementor-element elementor-element-69ae15f elementor-widget elementor-widget-text-editor" data-id="69ae15f" data-element_type="widget" data-widget_type="text-editor.default">
          <div class="elementor-widget-container">
           <p><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">J'aimerais entrer en contact avec vous !</font></font></p>
          </div>
         </div>
        </div>
       </div>
       <div class="elementor-element elementor-element-06eb9d5 e-con-full e-flex e-con e-child" data-id="06eb9d5" data-element_type="container">
        <div class="elementor-element elementor-element-85c914c elementor-shape-circle elementor-grid-0 e-grid-align-center elementor-widget elementor-widget-social-icons" data-id="85c914c" data-element_type="widget" data-widget_type="social-icons.default">
         <div class="elementor-widget-container">
          <div class="elementor-social-icons-wrapper elementor-grid" role="list"><span class="elementor-grid-item" role="listitem"> <a class="elementor-icon elementor-social-icon elementor-social-icon-linkedin-in elementor-repeater-item-fddf51d" href="https://translate.google.com/website?sl=en&amp;tl=fr&amp;hl=fr&amp;client=webapp&amp;u=https://www.linkedin.com/in/khuyen-tran-1ab926151/" target="_blank"> <span class="elementor-screen-only"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">LinkedIn</font></font></span> <i aria-hidden="true" class="fab fa-linkedin-in"></i> </a> </span> <span class="elementor-grid-item" role="listitem"> <a class="elementor-icon elementor-social-icon elementor-social-icon-twitter elementor-repeater-item-3590114" href="https://translate.google.com/website?sl=en&amp;tl=fr&amp;hl=fr&amp;client=webapp&amp;u=https://twitter.com/KhuyenTran16" target="_blank"> <span class="elementor-screen-only"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Gazouillement</font></font></span> <i aria-hidden="true" class="fab fa-twitter"></i> </a> </span> <span class="elementor-grid-item" role="listitem"> <a class="elementor-icon elementor-social-icon elementor-social-icon- elementor-repeater-item-106cbff" href="https://translate.google.com/website?sl=en&amp;tl=fr&amp;hl=fr&amp;client=webapp&amp;u=https://github.com/khuyentran1401/" target="_blank"> <span class="elementor-screen-only"></span>
             <svg xmlns="http://www.w3.org/2000/svg" width="800px" height="800px" viewBox="0 0 24 24" fill="none">
              <path d="M4.0744 2.9938C4.13263 1.96371 4.37869 1.51577 5.08432 1.15606C5.84357 0.768899 7.04106 0.949072 8.45014 1.66261C9.05706 1.97009 9.11886 1.97635 10.1825 1.83998C11.5963 1.65865 13.4164 1.65929 14.7213 1.84164C15.7081 1.97954 15.7729 1.97265 16.3813 1.66453C18.3814 0.651679 19.9605 0.71795 20.5323 1.8387C20.8177 2.39812 20.8707 3.84971 20.6494 5.04695C20.5267 5.71069 20.5397 5.79356 20.8353 6.22912C22.915 9.29385 21.4165 14.2616 17.8528 16.1155C17.5801 16.2574 17.3503 16.3452 17.163 16.4167C16.5879 16.6363 16.4133 16.703 16.6247 17.7138C16.7265 18.2 16.8491 19.4088 16.8973 20.4002C16.9844 22.1922 16.9831 22.2047 16.6688 22.5703C16.241 23.0676 15.6244 23.076 15.2066 22.5902C14.9341 22.2734 14.9075 22.1238 14.9075 20.9015C14.9075 19.0952 14.7095 17.8946 14.2417 16.8658C13.6854 15.6415 14.0978 15.185 15.37 14.9114C17.1383 14.531 18.5194 13.4397 19.2892 11.8146C20.0211 10.2698 20.1314 8.13501 18.8082 6.83668C18.4319 6.3895 18.4057 5.98446 18.6744 4.76309C18.7748 4.3066 18.859 3.71768 18.8615 3.45425C18.8653 3.03823 18.8274 2.97541 18.5719 2.97541C18.4102 2.97541 17.7924 3.21062 17.1992 3.49805L16.2524 3.95695C16.1663 3.99866 16.07 4.0147 15.975 4.0038C13.5675 3.72746 11.2799 3.72319 8.86062 4.00488C8.76526 4.01598 8.66853 3.99994 8.58215 3.95802L7.63585 3.49882C7.04259 3.21087 6.42482 2.97541 6.26317 2.97541C5.88941 2.97541 5.88379 3.25135 6.22447 4.89078C6.43258 5.89203 6.57262 6.11513 5.97101 6.91572C5.06925 8.11576 4.844 9.60592 5.32757 11.1716C5.93704 13.1446 7.4295 14.4775 9.52773 14.9222C10.7926 15.1903 11.1232 15.5401 10.6402 16.9905C10.26 18.1319 10.0196 18.4261 9.46707 18.4261C8.72365 18.4261 8.25796 17.7821 8.51424 17.1082C8.62712 16.8112 8.59354 16.7795 7.89711 16.5255C5.77117 15.7504 4.14514 14.0131 3.40172 11.7223C2.82711 9.95184 3.07994 7.64739 4.00175 6.25453C4.31561 5.78028 4.32047 5.74006 4.174 4.83217C4.09113 4.31822 4.04631 3.49103 4.0744 2.9938Z" fill="#0F0F0F"></path><path d="M3.33203 15.9454C3.02568 15.4859 2.40481 15.3617 1.94528 15.6681C1.48576 15.9744 1.36158 16.5953 1.66793 17.0548C1.8941 17.3941 2.16467 17.6728 2.39444 17.9025C2.4368 17.9449 2.47796 17.9858 2.51815 18.0257C2.71062 18.2169 2.88056 18.3857 3.05124 18.5861C3.42875 19.0292 3.80536 19.626 4.0194 20.6962C4.11474 21.1729 4.45739 21.4297 4.64725 21.5419C4.85315 21.6635 5.07812 21.7352 5.26325 21.7819C5.64196 21.8774 6.10169 21.927 6.53799 21.9559C7.01695 21.9877 7.53592 21.998 7.99999 22.0008C8.00033 22.5527 8.44791 23.0001 8.99998 23.0001C9.55227 23.0001 9.99998 22.5524 9.99998 22.0001V21.0001C9.99998 20.4478 9.55227 20.0001 8.99998 20.0001C8.90571 20.0001 8.80372 20.0004 8.69569 20.0008C8.10883 20.0026 7.34388 20.0049 6.67018 19.9603C6.34531 19.9388 6.07825 19.9083 5.88241 19.871C5.58083 18.6871 5.09362 17.8994 4.57373 17.2891C4.34391 17.0194 4.10593 16.7834 3.91236 16.5914C3.87612 16.5555 3.84144 16.5211 3.80865 16.4883C3.5853 16.265 3.4392 16.1062 3.33203 15.9454Z" fill="#0F0F0F"></path>
             </svg></a> </span> <span class="elementor-grid-item" role="listitem"> <a class="elementor-icon elementor-social-icon elementor-social-icon-youtube elementor-repeater-item-ff446bb" href="https://translate.google.com/website?sl=en&amp;tl=fr&amp;hl=fr&amp;client=webapp&amp;u=https://www.youtube.com/@datasciencesimplified" target="_blank"> <span class="elementor-screen-only"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Youtube</font></font></span> <i aria-hidden="true" class="fab fa-youtube"></i> </a> </span>
          </div>
         </div>
        </div>
        <div class="elementor-element elementor-element-4977a03 elementor-widget elementor-widget-heading" data-id="4977a03" data-element_type="widget" data-widget_type="heading.default">
         <div class="elementor-widget-container">
          <h5 class="elementor-heading-title elementor-size-default"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Suivez-nous sur les réseaux sociaux</font></font></h5>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
    <div class="elementor-element elementor-element-f93946e e-flex e-con-boxed e-con e-parent" data-id="f93946e" data-element_type="container">
     <div class="e-con-inner">
      <div class="elementor-element elementor-element-4e1fdf0 elementor-widget-divider--view-line elementor-widget elementor-widget-divider" data-id="4e1fdf0" data-element_type="widget" data-widget_type="divider.default">
       <div class="elementor-widget-container">
        <div class="elementor-divider"><span class="elementor-divider-separator"> </span>
        </div>
       </div>
      </div>
      <div class="elementor-element elementor-element-2644821 elementor-widget elementor-widget-heading" data-id="2644821" data-element_type="widget" data-widget_type="heading.default">
       <div class="elementor-widget-container">
        <p class="elementor-heading-title elementor-size-default"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Copyright © 2025 Code Cut - Tous droits réservés.</font></font></p>
       </div>
      </div>
     </div>
    </div>
   </footer>
  </div><!-- #page -->
  <div id="um_upload_single" style="display:none;"></div>
  <div id="um_view_photo" style="display:none;"><a href="javascript:void(0);?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp" data-action="um_remove_modal" class="um-modal-close" aria-label="Vue rapprochée de la photo modale"> <i class="um-faicon-times"></i> </a>
   <div class="um-modal-body photo">
    <div class="um-modal-photo"></div>
   </div>
  </div>
  <script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/astra-child\/*","\/wp-content\/themes\/astra\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
  <script>
            // Do not change this comment line otherwise Speed Optimizer won't be able to detect this script

            (function () {
                const calculateParentDistance = (child, parent) => {
                    let count = 0;
                    let currentElement = child;

                    // Traverse up the DOM tree until we reach parent or the top of the DOM
                    while (currentElement && currentElement !== parent) {
                        currentElement = currentElement.parentNode;
                        count++;
                    }

                    // If parent was not found in the hierarchy, return -1
                    if (!currentElement) {
                        return -1; // Indicates parent is not an ancestor of element
                    }

                    return count; // Number of layers between element and parent
                }
                const isMatchingClass = (linkRule, href, classes, ids) => {
                    return classes.includes(linkRule.value)
                }
                const isMatchingId = (linkRule, href, classes, ids) => {
                    return ids.includes(linkRule.value)
                }
                const isMatchingDomain = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href)) {
                        return false
                    }

                    const url = new URL(href)

                    return linkRule.value === url.host
                }
                const isMatchingExtension = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href)) {
                        return false
                    }

                    const url = new URL(href)

                    return url.pathname.endsWith('.' + linkRule.value)
                }
                const isMatchingSubdirectory = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href)) {
                        return false
                    }

                    const url = new URL(href)

                    return url.pathname.startsWith('/' + linkRule.value + '/')
                }
                const isMatchingProtocol = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href)) {
                        return false
                    }

                    const url = new URL(href)

                    return url.protocol === linkRule.value + ':'
                }
                const isMatchingExternal = (linkRule, href, classes, ids) => {
                    if(!URL.canParse(href) || !URL.canParse(document.location.href)) {
                        return false
                    }

                    const matchingProtocols = ['http:', 'https:']
                    const siteUrl = new URL(document.location.href)
                    const linkUrl = new URL(href)

                    // Links to subdomains will appear to be external matches according to JavaScript,
                    // but the PHP rules will filter those events out.
                    return matchingProtocols.includes(linkUrl.protocol) && siteUrl.host !== linkUrl.host
                }
                const isMatch = (linkRule, href, classes, ids) => {
                    switch (linkRule.type) {
                        case 'class':
                            return isMatchingClass(linkRule, href, classes, ids)
                        case 'id':
                            return isMatchingId(linkRule, href, classes, ids)
                        case 'domain':
                            return isMatchingDomain(linkRule, href, classes, ids)
                        case 'extension':
                            return isMatchingExtension(linkRule, href, classes, ids)
                        case 'subdirectory':
                            return isMatchingSubdirectory(linkRule, href, classes, ids)
                        case 'protocol':
                            return isMatchingProtocol(linkRule, href, classes, ids)
                        case 'external':
                            return isMatchingExternal(linkRule, href, classes, ids)
                        default:
                            return false;
                    }
                }
                const track = (element) => {
                    const href = element.href ?? null
                    const classes = Array.from(element.classList)
                    const ids = [element.id]
                    const linkRules = [{"type":"domain","value":"github.com"},{"type":"extension","value":"pdf"},{"type":"extension","value":"zip"},{"type":"protocol","value":"mailto"},{"type":"protocol","value":"tel"}]
                    if(linkRules.length === 0) {
                        return
                    }

                    // For link rules that target an id, we need to allow that id to appear
                    // in any ancestor up to the 7th ancestor. This loop looks for those matches
                    // and counts them.
                    linkRules.forEach((linkRule) => {
                        if(linkRule.type !== 'id') {
                            return;
                        }

                        const matchingAncestor = element.closest('#' + linkRule.value)

                        if(!matchingAncestor || matchingAncestor.matches('html, body')) {
                            return;
                        }

                        const depth = calculateParentDistance(element, matchingAncestor)

                        if(depth < 7) {
                            ids.push(linkRule.value)
                        }
                    });

                    // For link rules that target a class, we need to allow that class to appear
                    // in any ancestor up to the 7th ancestor. This loop looks for those matches
                    // and counts them.
                    linkRules.forEach((linkRule) => {
                        if(linkRule.type !== 'class') {
                            return;
                        }

                        const matchingAncestor = element.closest('.' + linkRule.value)

                        if(!matchingAncestor || matchingAncestor.matches('html, body')) {
                            return;
                        }

                        const depth = calculateParentDistance(element, matchingAncestor)

                        if(depth < 7) {
                            classes.push(linkRule.value)
                        }
                    });

                    const hasMatch = linkRules.some((linkRule) => {
                        return isMatch(linkRule, href, classes, ids)
                    })

                    if(!hasMatch) {
                        return
                    }

                    const url = "https://codecut.ai/wp-content/plugins/independent-analytics-pro/iawp-click-endpoint.php";
                    const body = {
                        href: href,
                        classes: classes.join(' '),
                        ids: ids.join(' '),
                        ...{"payload":{"resource":"singular","singular_id":15293,"page":1},"signature":"f3f43fe45d3c26f81e9703355ab8990b"}                    };

                    if (navigator.sendBeacon) {
                        let blob = new Blob([JSON.stringify(body)], {
                            type: "application/json"
                        });
                        navigator.sendBeacon(url, blob);
                    } else {
                        const xhr = new XMLHttpRequest();
                        xhr.open("POST", url, true);
                        xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
                        xhr.send(JSON.stringify(body))
                    }
                }
                document.addEventListener('mousedown', function (event) {
                                        if (navigator.webdriver || /bot|crawler|spider|crawling|semrushbot|chrome-lighthouse/i.test(navigator.userAgent)) {
                        return;
                    }
                    
                    const element = event.target.closest('a')

                    if(!element) {
                        return
                    }

                    const isPro = true
                    if(!isPro) {
                        return
                    }

                    // Don't track left clicks with this event. The click event is used for that.
                    if(event.button === 0) {
                        return
                    }

                    track(element)
                })
                document.addEventListener('click', function (event) {
                                        if (navigator.webdriver || /bot|crawler|spider|crawling|semrushbot|chrome-lighthouse/i.test(navigator.userAgent)) {
                        return;
                    }
                    
                    const element = event.target.closest('a, button, input[type="submit"], input[type="button"]')

                    if(!element) {
                        return
                    }

                    const isPro = true
                    if(!isPro) {
                        return
                    }

                    track(element)
                })
                document.addEventListener('play', function (event) {
                                        if (navigator.webdriver || /bot|crawler|spider|crawling|semrushbot|chrome-lighthouse/i.test(navigator.userAgent)) {
                        return;
                    }
                    
                    const element = event.target.closest('audio, video')

                    if(!element) {
                        return
                    }

                    const isPro = true
                    if(!isPro) {
                        return
                    }

                    track(element)
                }, true)
                document.addEventListener("DOMContentLoaded", function (e) {
                    if (document.hasOwnProperty("visibilityState") && document.visibilityState === "prerender") {
                        return;
                    }

                                            if (navigator.webdriver || /bot|crawler|spider|crawling|semrushbot|chrome-lighthouse/i.test(navigator.userAgent)) {
                            return;
                        }
                    
                    let referrer_url = null;

                    if (typeof document.referrer === 'string' && document.referrer.length > 0) {
                        referrer_url = document.referrer;
                    }

                    const params = location.search.slice(1).split('&').reduce((acc, s) => {
                        const [k, v] = s.split('=');
                        return Object.assign(acc, {[k]: v});
                    }, {});

                    const url = "https://codecut.ai/wp-json/iawp/search";
                    const body = {
                        referrer_url,
                        utm_source: params.utm_source,
                        utm_medium: params.utm_medium,
                        utm_campaign: params.utm_campaign,
                        utm_term: params.utm_term,
                        utm_content: params.utm_content,
                        gclid: params.gclid,
                        ...{"payload":{"resource":"singular","singular_id":15293,"page":1},"signature":"f3f43fe45d3c26f81e9703355ab8990b"}                    };

                    if (navigator.sendBeacon) {
                        let blob = new Blob([JSON.stringify(body)], {
                            type: "application/json"
                        });
                        navigator.sendBeacon(url, blob);
                    } else {
                        const xhr = new XMLHttpRequest();
                        xhr.open("POST", url, true);
                        xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
                        xhr.send(JSON.stringify(body))
                    }
                });
            })();
        </script>
  <div class="xoo-wsc-markup-notices"></div>
  <div class="xoo-wsc-markup">
   <div class="xoo-wsc-modal">
    <div class="xoo-wsc-container">
     <div class="xoo-wsc-basket"><span class="xoo-wsc-items-count"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">0</font></font></span> <span class="xoo-wsc-bki xoo-wsc-icon-basket1"></span>
     </div>
     <div class="xoo-wsc-header">
      <div class="xoo-wsch-top">
       <div class="xoo-wsc-notice-container" data-section="cart">
        <ul class="xoo-wsc-notices"></ul>
       </div>
       <div class="xoo-wsch-basket"><span class="xoo-wscb-icon xoo-wsc-icon-bag2"></span> <span class="xoo-wscb-count"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">0</font></font></span>
       </div><span class="xoo-wsch-text"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Votre panier</font></font></span> <span class="xoo-wsch-close xoo-wsc-icon-cross"></span>
      </div>
     </div>
     <div class="xoo-wsc-body">
      <div class="xoo-wsc-empty-cart">
       <span><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Votre panier est vide</font></font></span>
      </div>
     </div>
     <div class="xoo-wsc-footer">
      <div class="xoo-wsc-ft-buttons-cont"><a href="https://codecut-ai.translate.goog/building-multi-agent-ai-langgraph-tutorial/?_x_tr_sl=en&amp;_x_tr_tl=fr&amp;_x_tr_hl=fr&amp;_x_tr_pto=wapp#" class="xoo-wsc-ft-btn xoo-wsc-btn xoo-wsc-cart-close xoo-wsc-ft-btn-continue"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Continuer les achats</font></font></a>
      </div>
     </div><span class="xoo-wsc-loader"></span> <span class="xoo-wsc-icon-spinner8 xoo-wsc-loader-icon"></span>
    </div><span class="xoo-wsc-opac"></span>
   </div>
  </div>
  <div id="ast-scroll-top" tabindex="0" class="ast-scroll-top-icon ast-scroll-to-top-right" data-on-devices="both" style="display: block;"><span class="screen-reader-text"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Faites défiler vers le haut</font></font></span>
  </div>
  
  
  <script type="application/ld+json">{"@context":"https:\/\/schema.org\/","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":{"name":"Home","@id":"https:\/\/codecut.ai"}},{"@type":"ListItem","position":2,"item":{"name":"Blog","@id":"https:\/\/codecut.ai\/blog\/"}},{"@type":"ListItem","position":3,"item":{"name":"Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial","@id":"https:\/\/codecut.ai\/building-multi-agent-ai-langgraph-tutorial\/"}}]}</script>
  <script>
				const lazyloadRunObserver = () => {
					const lazyloadBackgrounds = document.querySelectorAll( `.e-con.e-parent:not(.e-lazyloaded)` );
					const lazyloadBackgroundObserver = new IntersectionObserver( ( entries ) => {
						entries.forEach( ( entry ) => {
							if ( entry.isIntersecting ) {
								let lazyloadBackground = entry.target;
								if( lazyloadBackground ) {
									lazyloadBackground.classList.add( 'e-lazyloaded' );
								}
								lazyloadBackgroundObserver.unobserve( entry.target );
							}
						});
					}, { rootMargin: '200px 0px 200px 0px' } );
					lazyloadBackgrounds.forEach( ( lazyloadBackground ) => {
						lazyloadBackgroundObserver.observe( lazyloadBackground );
					} );
				};
				const events = [
					'DOMContentLoaded',
					'elementor/lazyload/observe',
				];
				events.forEach( ( event ) => {
					document.addEventListener( event, lazyloadRunObserver );
				} );
			</script>
  <style id="mutliple-author-box-inline-style">    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .box-header-title {
        font-size: 30px !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .box-header-title {
        text-align: center !important; 
    }

.pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-avatar img { 
        width: 100px !important; 
        height: 100px !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-avatar img {
        border-style: none !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-avatar img {
        border-radius: 50% !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-name a {
        font-size: 20px !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-name a {
        font-weight: 500 !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-name a {
        text-transform: capitalize !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-meta a {
        background-color: #655997 !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-meta a {
        color: #ffffff !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-meta a:hover {
        color: #ffffff !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-recent-posts-title {
        border-bottom-style: dotted !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-author-boxes-recent-posts-item {
        text-align: left !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-multiple-authors-boxes-li {
        padding-right: 100px !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-multiple-authors-boxes-li {
        border-style: none !important; 
    }

    .pp-multiple-authors-boxes-wrapper.pp-multiple-authors-layout-inline.multiple-authors-target-the-content .pp-multiple-authors-boxes-li {
        color: #3c434a !important; 
    }

.pp-multiple-authors-layout-inline ul.pp-multiple-authors-boxes-ul {
    display: flex;
}

.pp-multiple-authors-layout-inline ul.pp-multiple-authors-boxes-ul li {
    margin-right: 10px
}

.pp-multiple-authors-layout-inline ul.pp-multiple-authors-boxes-ul li.has-avatar .pp-author-boxes-avatar,
.pp-multiple-authors-layout-inline ul.pp-multiple-authors-boxes-ul li.has-avatar .pp-author-boxes-avatar-details {
    display: inline-block;
}</style>
  <script>
		(function () {
			var c = document.body.className;
			c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
			document.body.className = c;
		})();
	</script>
  <link rel="stylesheet" id="wc-stripe-blocks-checkout-style-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/upe-blocks.css" media="all">
  <link rel="stylesheet" id="wc-ppcp-blocks-styles-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/styles.css" media="all">
  <link rel="stylesheet" id="wc-ppcp-style-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/styles_002.css" media="all">
  <link rel="stylesheet" id="wc-blocks-style-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/wc-blocks.css" media="all">
  <link rel="stylesheet" id="multiple-authors-widget-css-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/multiple-authors-widget.css" media="all">
  <style id="multiple-authors-widget-css-inline-css">
:root { --ppa-color-scheme: #655997; --ppa-color-scheme-active: #514779; }
</style>
  <link rel="stylesheet" id="multiple-authors-fontawesome-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/all.min.css" media="all">
  <link rel="stylesheet" id="elementor-icons-ekiticons-css" href="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/ekiticons.css" media="all">
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/comment-reply.min.js" id="comment-reply-js" async="" data-wp-strategy="async"></script>
  <script id="astra-theme-js-js-extra">
var astra = {"break_point":"10","isRtl":"","is_scroll_to_id":"","is_scroll_to_top":"1","is_header_footer_builder_active":"","responsive_cart_click":"flyout","is_dark_palette":""};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/style.min.js" id="astra-theme-js-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/react.min.js" id="react-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/react-dom.min.js" id="react-dom-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/react-jsx-runtime.min.js" id="react-jsx-runtime-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/dom-ready.min.js" id="wp-dom-ready-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/hooks.min.js" id="wp-hooks-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/i18n.min.js" id="wp-i18n-js"></script>
  <script id="wp-i18n-js-after">
wp.i18n.setLocaleData( { 'text direction\u0004ltr': [ 'ltr' ] } );
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/a11y.min.js" id="wp-a11y-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/wp-polyfill.min.js" id="wp-polyfill-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/url.min.js" id="wp-url-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/api-fetch.min.js" id="wp-api-fetch-js"></script>
  <script id="wp-api-fetch-js-after">
wp.apiFetch.use( wp.apiFetch.createRootURLMiddleware( "https://codecut.ai/wp-json/" ) );
wp.apiFetch.nonceMiddleware = wp.apiFetch.createNonceMiddleware( "6ab77a347f" );
wp.apiFetch.use( wp.apiFetch.nonceMiddleware );
wp.apiFetch.use( wp.apiFetch.mediaUploadMiddleware );
wp.apiFetch.nonceEndpoint = "https://codecut.ai/wp-admin/admin-ajax.php?action=rest-nonce";
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/blob.min.js" id="wp-blob-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/block-serialization-default-parser.min.js" id="wp-block-serialization-default-parser-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/autop.min.js" id="wp-autop-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/deprecated.min.js" id="wp-deprecated-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/dom.min.js" id="wp-dom-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/escape-html.min.js" id="wp-escape-html-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/element.min.js" id="wp-element-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/is-shallow-equal.min.js" id="wp-is-shallow-equal-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/keycodes.min.js" id="wp-keycodes-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/priority-queue.min.js" id="wp-priority-queue-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/compose.min.js" id="wp-compose-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/private-apis.min.js" id="wp-private-apis-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/redux-routine.min.js" id="wp-redux-routine-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/data.min.js" id="wp-data-js"></script>
  <script id="wp-data-js-after">
( function() {
	var userId = 0;
	var storageKey = "WP_DATA_USER_" + userId;
	wp.data
		.use( wp.data.plugins.persistence, { storageKey: storageKey } );
} )();
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/html-entities.min.js" id="wp-html-entities-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/rich-text.min.js" id="wp-rich-text-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/shortcode.min.js" id="wp-shortcode-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/warning.min.js" id="wp-warning-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/blocks.min.js" id="wp-blocks-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/moment.min.js" id="moment-js"></script>
  <script id="moment-js-after">
moment.updateLocale( 'en_US', {"months":["January","February","March","April","May","June","July","August","September","October","November","December"],"monthsShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"weekdays":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"weekdaysShort":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"week":{"dow":1},"longDateFormat":{"LT":"g:i a","LTS":null,"L":null,"LL":"F j, Y","LLL":"F j, Y g:i a","LLLL":null}} );
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/date.min.js" id="wp-date-js"></script>
  <script id="wp-date-js-after">
wp.date.setSettings( {"l10n":{"locale":"en_US","months":["January","February","March","April","May","June","July","August","September","October","November","December"],"monthsShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"weekdays":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"weekdaysShort":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"meridiem":{"am":"am","pm":"pm","AM":"AM","PM":"PM"},"relative":{"future":"%s from now","past":"%s ago","s":"a second","ss":"%d seconds","m":"a minute","mm":"%d minutes","h":"an hour","hh":"%d hours","d":"a day","dd":"%d days","M":"a month","MM":"%d months","y":"a year","yy":"%d years"},"startOfWeek":1},"formats":{"time":"g:i a","date":"F j, Y","datetime":"F j, Y g:i a","datetimeAbbreviated":"M j, Y g:i a"},"timezone":{"offset":-5,"offsetFormatted":"-5","string":"America\/Chicago","abbr":"CDT"}} );
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/primitives.min.js" id="wp-primitives-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/components.min.js" id="wp-components-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/keyboard-shortcuts.min.js" id="wp-keyboard-shortcuts-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/commands.min.js" id="wp-commands-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/notices.min.js" id="wp-notices-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/preferences-persistence.min.js" id="wp-preferences-persistence-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/preferences.min.js" id="wp-preferences-js"></script>
  <script id="wp-preferences-js-after">
( function() {
				var serverData = false;
				var userId = "0";
				var persistenceLayer = wp.preferencesPersistence.__unstableCreatePersistenceLayer( serverData, userId );
				var preferencesStore = wp.preferences.store;
				wp.data.dispatch( preferencesStore ).setPersistenceLayer( persistenceLayer );
			} ) ();
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/style-engine.min.js" id="wp-style-engine-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/token-list.min.js" id="wp-token-list-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/block-editor.min.js" id="wp-block-editor-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/server-side-render.min.js" id="wp-server-side-render-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/faster-youtube.js" id="ht-youtub-embed-blocks-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/LiteYTEmbed.js" id="ht-youtube-embed-js-js"></script>
  <script id="cr-frontend-js-js-extra">
var cr_ajax_object = {"ajax_url":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php"};
var cr_ajax_object = {"ajax_url":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","disable_lightbox":"0"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/frontend_002.js" id="cr-frontend-js-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/colcade.js" id="cr-colcade-js"></script>
  <script id="bt_conversion_scripts-js-extra">
var btab_vars = {"is_admin":"","post_id":"15293","is_preview":"","is_agency":"","is_free":"","tagging":"1","do_fingerprint":"0","advanced_tracking":"0","abst_server_convert_woo":"0","plugins_uri":"https:\/\/codecut.ai\/wp-content\/plugins\/bt-bb-ab\/","domain":"https:\/\/codecut.ai"};
</script>
  <script data-cfasync="false" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/bt_conversion-min.js" id="bt_conversion_scripts-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/prism-core.js" id="prismatic-prism-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/plugin-toolbar.js" id="prismatic-prism-toolbar-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/plugin-show-language.js" id="prismatic-prism-show-language-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/plugin-copy-clipboard.js" id="prismatic-copy-clipboard-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/plugin-command-line.js" id="prismatic-command-line-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/lang-bash.js" id="prismatic-prism-bash-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/lang-python.js" id="prismatic-prism-python-js"></script>
  <script id="wp-postviews-cache-js-extra">
var viewsCacheL10n = {"admin_ajax_url":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","nonce":"f4a03d8d2c","post_id":"15293"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/postviews-cache.js" id="wp-postviews-cache-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/core.min.js" id="jquery-ui-core-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/datepicker.min.js" id="jquery-ui-datepicker-js"></script>
  <script id="jquery-ui-datepicker-js-after">
jQuery(function(jQuery){jQuery.datepicker.setDefaults({"closeText":"Close","currentText":"Today","monthNames":["January","February","March","April","May","June","July","August","September","October","November","December"],"monthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"nextText":"Next","prevText":"Previous","dayNames":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"dayNamesShort":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"dayNamesMin":["S","M","T","W","T","F","S"],"dateFormat":"MM d, yy","firstDay":1,"isRTL":false});});
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/date-picker.js" id="preorders-field-date-js-js"></script>
  <script id="preorders-main-js-js-extra">
var DBData = {"default_add_to_cart_text":"Add to cart","preorders_add_to_cart_text":"Pre Order Now!"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/main.js" id="preorders-main-js-js"></script>
  <script id="woosb-frontend-js-extra">
var woosb_vars = {"wc_price_decimals":"2","wc_price_format":"%1$s%2$s","wc_price_thousand_separator":",","wc_price_decimal_separator":".","wc_currency_symbol":"$","price_decimals":"2","price_format":"%1$s%2$s","price_thousand_separator":",","price_decimal_separator":".","currency_symbol":"$","trim_zeros":"","round_price":"1","change_image":"yes","bundled_price":"price","bundled_price_from":"sale_price","change_price":"yes","price_selector":"","saved_text":"(saved [d])","price_text":"Bundle price:","selected_text":"Selected:","alert_selection":"Please select a purchasable variation for [name] before adding this bundle to the cart.","alert_unpurchasable":"Product [name] is unpurchasable. Please remove it before adding the bundle to the cart.","alert_empty":"Please choose at least one product before adding this bundle to the cart.","alert_min":"Please choose at least a total quantity of [min] products before adding this bundle to the cart.","alert_max":"Sorry, you can only choose at max a total quantity of [max] products before adding this bundle to the cart.","alert_total_min":"The total must meet the minimum amount of [min].","alert_total_max":"The total must meet the maximum amount of [max]."};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/frontend.js" id="woosb-frontend-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/dflip.min.js" id="dflip-script-js"></script>
  <script id="wd-asl-ajaxsearchlite-js-before">
window.ASL = typeof window.ASL !== 'undefined' ? window.ASL : {}; window.ASL.wp_rocket_exception = "DOMContentLoaded"; window.ASL.ajaxurl = "https:\/\/codecut.ai\/wp-admin\/admin-ajax.php"; window.ASL.backend_ajaxurl = "https:\/\/codecut.ai\/wp-admin\/admin-ajax.php"; window.ASL.js_scope = "jQuery"; window.ASL.asl_url = "https:\/\/codecut.ai\/wp-content\/plugins\/ajax-search-lite\/"; window.ASL.detect_ajax = 1; window.ASL.media_query = 4774; window.ASL.version = 4774; window.ASL.pageHTML = ""; window.ASL.additional_scripts = []; window.ASL.script_async_load = false; window.ASL.init_only_in_viewport = true; window.ASL.font_url = "https:\/\/codecut.ai\/wp-content\/plugins\/ajax-search-lite\/css\/fonts\/icons2.woff2"; window.ASL.highlight = {"enabled":false,"data":[]}; window.ASL.analytics = {"method":0,"tracking_id":"","string":"?ajax_search={asl_term}","event":{"focus":{"active":true,"action":"focus","category":"ASL","label":"Input focus","value":"1"},"search_start":{"active":false,"action":"search_start","category":"ASL","label":"Phrase: {phrase}","value":"1"},"search_end":{"active":true,"action":"search_end","category":"ASL","label":"{phrase} | {results_count}","value":"1"},"magnifier":{"active":true,"action":"magnifier","category":"ASL","label":"Magnifier clicked","value":"1"},"return":{"active":true,"action":"return","category":"ASL","label":"Return button pressed","value":"1"},"facet_change":{"active":false,"action":"facet_change","category":"ASL","label":"{option_label} | {option_value}","value":"1"},"result_click":{"active":true,"action":"result_click","category":"ASL","label":"{result_title} | {result_url}","value":"1"}}};
window.ASL_INSTANCES = [];window.ASL_INSTANCES[1] = {"homeurl":"https:\/\/codecut.ai\/","resultstype":"vertical","resultsposition":"hover","itemscount":4,"charcount":0,"highlight":0,"highlightwholewords":1,"singleHighlight":0,"scrollToResults":{"enabled":0,"offset":0},"resultareaclickable":1,"autocomplete":{"enabled":1,"lang":"en","trigger_charcount":0},"mobile":{"menu_selector":"#menu-toggle"},"trigger":{"click":"results_page","click_location":"same","update_href":0,"return":"results_page","return_location":"same","facet":1,"type":1,"redirect_url":"?s={phrase}","delay":300},"animations":{"pc":{"settings":{"anim":"fadedrop","dur":300},"results":{"anim":"fadedrop","dur":300},"items":"voidanim"},"mob":{"settings":{"anim":"fadedrop","dur":300},"results":{"anim":"fadedrop","dur":300},"items":"voidanim"}},"autop":{"state":"disabled","phrase":"","count":1},"resPage":{"useAjax":0,"selector":"#main","trigger_type":1,"trigger_facet":1,"trigger_magnifier":0,"trigger_return":0},"resultsSnapTo":"left","results":{"width":"auto","width_tablet":"auto","width_phone":"auto"},"settingsimagepos":"right","closeOnDocClick":1,"overridewpdefault":1,"override_method":"get"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/asl.min.js" id="wd-asl-ajaxsearchlite-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/webpack.runtime.min.js" id="elementor-webpack-runtime-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/frontend-modules.min.js" id="elementor-frontend-modules-js"></script>
  <script id="elementor-frontend-js-before">
var elementorFrontendConfig = {"environmentMode":{"edit":false,"wpPreview":false,"isScriptDebug":false},"i18n":{"shareOnFacebook":"Share on Facebook","shareOnTwitter":"Share on Twitter","pinIt":"Pin it","download":"Download","downloadImage":"Download image","fullscreen":"Fullscreen","zoom":"Zoom","share":"Share","playVideo":"Play Video","previous":"Previous","next":"Next","close":"Close","a11yCarouselPrevSlideMessage":"Previous slide","a11yCarouselNextSlideMessage":"Next slide","a11yCarouselFirstSlideMessage":"This is the first slide","a11yCarouselLastSlideMessage":"This is the last slide","a11yCarouselPaginationBulletMessage":"Go to slide"},"is_rtl":false,"breakpoints":{"xs":0,"sm":480,"md":768,"lg":1025,"xl":1440,"xxl":1600},"responsive":{"breakpoints":{"mobile":{"label":"Mobile Portrait","value":767,"default_value":767,"direction":"max","is_enabled":true},"mobile_extra":{"label":"Mobile Landscape","value":880,"default_value":880,"direction":"max","is_enabled":false},"tablet":{"label":"Tablet Portrait","value":1024,"default_value":1024,"direction":"max","is_enabled":true},"tablet_extra":{"label":"Tablet Landscape","value":1200,"default_value":1200,"direction":"max","is_enabled":false},"laptop":{"label":"Laptop","value":1366,"default_value":1366,"direction":"max","is_enabled":true},"widescreen":{"label":"Widescreen","value":2400,"default_value":2400,"direction":"min","is_enabled":true}},"hasCustomBreakpoints":true},"version":"3.32.3","is_static":false,"experimentalFeatures":{"additional_custom_breakpoints":true,"container":true,"theme_builder_v2":true,"nested-elements":true,"home_screen":true,"global_classes_should_enforce_capabilities":true,"e_variables":true,"cloud-library":true,"e_opt_in_v4_page":true,"import-export-customization":true,"e_pro_variables":true},"urls":{"assets":"https:\/\/codecut.ai\/wp-content\/plugins\/elementor\/assets\/","ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","uploadUrl":"https:\/\/codecut.ai\/wp-content\/uploads"},"nonces":{"floatingButtonsClickTracking":"da354521c9"},"swiperClass":"swiper","settings":{"page":[],"editorPreferences":[]},"kit":{"active_breakpoints":["viewport_mobile","viewport_tablet","viewport_laptop","viewport_widescreen"],"global_image_lightbox":"yes","lightbox_enable_counter":"yes","lightbox_enable_fullscreen":"yes","lightbox_enable_zoom":"yes","lightbox_enable_share":"yes","lightbox_title_src":"title","lightbox_description_src":"description","woocommerce_notices_elements":[]},"post":{"id":15293,"title":"Building%20Coordinated%20AI%20Agents%20with%20LangGraph%3A%20A%20Hands-On%20Tutorial%20%7C%20CodeCut","excerpt":"","featuredImage":"https:\/\/codecut.ai\/wp-content\/uploads\/2025\/06\/langraph-featured-image.png"}};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/frontend.min_002.js" id="elementor-frontend-js"></script><span id="elementor-device-mode" class="elementor-screen-only"></span>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/jquery.smartmenus.min.js" id="smartmenus-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/imagesloaded.min.js" id="imagesloaded-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/frontend-script.js" id="elementskit-framework-js-frontend-js"></script>
  <script id="elementskit-framework-js-frontend-js-after">
		var elementskit = {
			resturl: 'https://codecut.ai/wp-json/elementskit/v1/',
		}

		
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/widget-scripts.js" id="ekit-widget-scripts-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/sourcebuster.min.js" id="sourcebuster-js-js"></script>
  <script id="wc-order-attribution-js-extra">
var wc_order_attribution = {"params":{"lifetime":1.0e-5,"session":30,"base64":false,"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","prefix":"wc_order_attribution_","allowTracking":true},"fields":{"source_type":"current.typ","referrer":"current_add.rf","utm_campaign":"current.cmp","utm_source":"current.src","utm_medium":"current.mdm","utm_content":"current.cnt","utm_id":"current.id","utm_term":"current.trm","utm_source_platform":"current.plt","utm_creative_format":"current.fmt","utm_marketing_tactic":"current.tct","session_entry":"current_add.ep","session_start_time":"current_add.fd","session_pages":"session.pgs","session_count":"udata.vst","user_agent":"udata.uag"}};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/order-attribution.min.js" id="wc-order-attribution-js"></script>
  <script id="xoo-wsc-main-js-js-extra">
var xoo_wsc_params = {"adminurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","qtyUpdateDelay":"500","notificationTime":"5000","html":{"successNotice":"<ul class=\"xoo-wsc-notices\"><li class=\"xoo-wsc-notice-success\"><span class=\"xoo-wsc-icon-check_circle\"><\/span>%s%<\/li><\/ul>","errorNotice":"<ul class=\"xoo-wsc-notices\"><li class=\"xoo-wsc-notice-error\"><span class=\"xoo-wsc-icon-cross\"><\/span>%s%<\/li><\/ul>"},"strings":{"maxQtyError":"Only %s% in stock","stepQtyError":"Quantity can only be purchased in multiple of %s%","calculateCheckout":"Please use checkout form to calculate shipping","couponEmpty":"Please enter promo code"},"isCheckout":"","isCart":"","sliderAutoClose":"1","shippingEnabled":"1","couponsEnabled":"1","autoOpenCart":"yes","addedToCart":"","ajaxAddToCart":"yes","skipAjaxForData":[],"showBasket":"hide_empty","flyToCart":"no","productFlyClass":"","refreshCart":"no","fetchDelay":"200","triggerClass":"","productLayout":"rows","cardAnimate":{"enable":"yes","type":"slideUp","event":"back_hover","duration":"0.5"}};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/xoo-wsc-main.js" id="xoo-wsc-main-js-js" defer="defer" data-wp-strategy="defer"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/underscore.min.js" id="underscore-js"></script>
  <script id="wp-util-js-extra">
var _wpUtilSettings = {"ajax":{"url":"\/wp-admin\/admin-ajax.php"}};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/wp-util.min.js" id="wp-util-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/tipsy.min.js" id="um_tipsy-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-confirm.min.js" id="um_confirm-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/picker.min.js" id="um_datetime-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/picker.date.min.js" id="um_datetime_date-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/picker.time.min.js" id="um_datetime_time-js"></script>
  <script id="um_common-js-extra">
var um_common_variables = {"locale":"en_US"};
var um_common_variables = {"locale":"en_US"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/common.min.js" id="um_common-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/cropper.min.js" id="um_crop-js"></script>
  <script id="um_frontend_common-js-extra">
var um_frontend_common_variables = [];
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/common-frontend.min.js" id="um_frontend_common-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-modal.min.js" id="um_modal-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/jquery-form.min.js" id="um_jquery_form-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/fileupload.js" id="um_fileupload-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-functions.min.js" id="um_functions-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-responsive.min.js" id="um_responsive-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-conditional.min.js" id="um_conditional-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/select2.full.min.js" id="select2-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/en.js" id="um_select2_locale-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-raty.min.js" id="um_raty-js"></script>
  <script id="um_scripts-js-extra">
var um_scripts = {"max_upload_size":"2147483648","nonce":"f920946e91"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-scripts.min.js" id="um_scripts-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-profile.min.js" id="um_profile-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/um-account.min.js" id="um_account-js"></script>
  <script id="ivory-search-scripts-js-extra">
var IvorySearchVars = {"is_analytics_enabled":"1"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/ivory-search.min.js" id="ivory-search-scripts-js"></script>
  <script defer="defer" src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/akismet-frontend.js" id="akismet-frontend-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/ecspro.js" id="ecspro-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/webpack-pro.runtime.min.js" id="elementor-pro-webpack-runtime-js"></script>
  <script id="elementor-pro-frontend-js-before">
var ElementorProFrontendConfig = {"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","nonce":"d6fc6c1011","urls":{"assets":"https:\/\/codecut.ai\/wp-content\/plugins\/elementor-pro\/assets\/","rest":"https:\/\/codecut.ai\/wp-json\/"},"settings":{"lazy_load_background_images":true},"popup":{"hasPopUps":true},"shareButtonsNetworks":{"facebook":{"title":"Facebook","has_counter":true},"twitter":{"title":"Twitter"},"linkedin":{"title":"LinkedIn","has_counter":true},"pinterest":{"title":"Pinterest","has_counter":true},"reddit":{"title":"Reddit","has_counter":true},"vk":{"title":"VK","has_counter":true},"odnoklassniki":{"title":"OK","has_counter":true},"tumblr":{"title":"Tumblr"},"digg":{"title":"Digg"},"skype":{"title":"Skype"},"stumbleupon":{"title":"StumbleUpon","has_counter":true},"mix":{"title":"Mix"},"telegram":{"title":"Telegram"},"pocket":{"title":"Pocket","has_counter":true},"xing":{"title":"XING","has_counter":true},"whatsapp":{"title":"WhatsApp"},"email":{"title":"Email"},"print":{"title":"Print"},"x-twitter":{"title":"X"},"threads":{"title":"Threads"}},"woocommerce":{"menu_cart":{"cart_page_url":"https:\/\/codecut.ai\/cart\/","checkout_page_url":"https:\/\/codecut.ai\/checkout\/","fragments_nonce":"5210d041a3"}},"facebook_sdk":{"lang":"en_US","app_id":""},"lottie":{"defaultAnimationUrl":"https:\/\/codecut.ai\/wp-content\/plugins\/elementor-pro\/modules\/lottie\/assets\/animations\/default.json"}};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/frontend.min.js" id="elementor-pro-frontend-js"></script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/elements-handlers.min.js" id="pro-elements-handlers-js"></script><svg style="display: none;" class="e-font-icon-svg-symbols"></svg>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/animate-circle.min.js" id="animate-circle-js"></script>
  <script id="elementskit-elementor-js-extra">
var ekit_config = {"ajaxurl":"https:\/\/codecut.ai\/wp-admin\/admin-ajax.php","nonce":"2a6deaa770"};
</script>
  <script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/elementor.js" id="elementskit-elementor-js"></script>
  <script>
			/(trident|msie)/i.test(navigator.userAgent)&&document.getElementById&&window.addEventListener&&window.addEventListener("hashchange",function(){var t,e=location.hash.substring(1);/^[A-z0-9_-]+$/.test(e)&&(t=document.getElementById(e))&&(/^(?:a|select|input|button|textarea)$/i.test(t.tagName)||(t.tabIndex=-1),t.focus())},!1);
			</script>
  <script data-cfasync="false">
          var dFlipLocation = 'https://codecut.ai/wp-content/plugins/3d-flipbook-dflip-lite/assets/';
          var dFlipWPGlobal = {"text":{"toggleSound":"Turn on\/off Sound","toggleThumbnails":"Toggle Thumbnails","toggleOutline":"Toggle Outline\/Bookmark","previousPage":"Previous Page","nextPage":"Next Page","toggleFullscreen":"Toggle Fullscreen","zoomIn":"Zoom In","zoomOut":"Zoom Out","toggleHelp":"Toggle Help","singlePageMode":"Single Page Mode","doublePageMode":"Double Page Mode","downloadPDFFile":"Download PDF File","gotoFirstPage":"Goto First Page","gotoLastPage":"Goto Last Page","share":"Share","mailSubject":"I wanted you to see this FlipBook","mailBody":"Check out this site {{url}}","loading":"DearFlip: Loading "},"viewerType":"flipbook","moreControls":"download,pageMode,startPage,endPage,sound","hideControls":"","scrollWheel":"false","backgroundColor":"#777","backgroundImage":"","height":"auto","paddingLeft":"20","paddingRight":"20","controlsPosition":"bottom","duration":800,"soundEnable":"true","enableDownload":"true","showSearchControl":"false","showPrintControl":"false","enableAnnotation":false,"enableAnalytics":"false","webgl":"true","hard":"none","maxTextureSize":"1600","rangeChunkSize":"524288","zoomRatio":1.5,"stiffness":3,"pageMode":"0","singlePageMode":"0","pageSize":"0","autoPlay":"false","autoPlayDuration":5000,"autoPlayStart":"false","linkTarget":"2","sharePrefix":"flipbook-"};
        </script>
  <script type="text/javascript">
        window.addEventListener("load", function() {
            const postId = 15293;
            const ajaxUrl = 'https://codecut.ai/wp-admin/admin-ajax.php';
            if (!localStorage.getItem('post_viewed_' + postId)) {
                fetch(ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
                    },
                    body: new URLSearchParams({
                        'action': 'track_post_views',
                        'post_id': postId
                    })
                }).then(response => {
                    if (response.ok) {
                        localStorage.setItem('post_viewed_' + postId, 'true');
                    }
                });
            }
        });
        </script>
  <script>function gtElInit() {var lib = new google.translate.TranslateService();lib.translatePage('en', 'fr', function () {});}</script>
  <script id="google-translate-element-script">
    // go/mss-setup#7-load-the-js-or-css-from-your-initial-page
    if(!window['_DumpException']) {
      const _DumpException = window['_DumpException'] || function(e) {
        throw e;
      };
      window['_DumpException'] = _DumpException;
    }
  "use strict";this.default_tr=this.default_tr||{};(function(_){var window=this;
try{
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles_default_tr=a||[]};(0,_._F_toggles_initialize)([0xc080, ]);
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var aa,ba,ea,ja,sa,ya,Ba,Ca,Fa,Ga,Ha,Ja,Za,cb,hb,jb,kb,qb,rb,sb,vb,wb,xb,x,zb,Ab,Cb,Gb,Ib,Jb;aa=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,aa);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)};ba=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");aa.call(this,c+a[d])};ea=function(a){if(_.ca)a(_.ca);else{var b;((b=da)!=null?b:da=[]).push(a)}};
ja=function(){!_.ca&&_.ha&&_.ia();return _.ca};_.ia=function(){_.ca=_.ha();var a;(a=da)==null||a.forEach(ea);da=void 0};_.la=function(a){_.ca&&ka(a)};_.na=function(){_.ca&&ma(_.ca)};_.pa=function(a,b){b.hasOwnProperty("displayName")||(b.displayName=a.toString());b[oa]=a};_.qa=function(a){a&&typeof a.dispose=="function"&&a.dispose()};sa=function(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];_.ra(d)?sa.apply(null,d):_.qa(d)}};_.ua=function(a,b){return ta(a,b)>=0};
_.va=function(a,b){_.ua(a,b)||a.push(b)};_.wa=function(a,b){b=ta(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.xa=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};ya=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.ra(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}};
Ba=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],g=_.za(f)?"o"+_.Aa(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,g)||(e[g]=!0,b[c++]=f)}b.length=c};Ca=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.Da=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};
Fa=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Ea.length;f++)c=Ea[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};Ga=function(a){var b=arguments.length;if(b==1&&Array.isArray(arguments[0]))return Ga.apply(null,arguments[0]);for(var c={},d=0;d<b;d++)c[arguments[d]]=!0;return c};Ha=function(a){return{valueOf:a}.valueOf()};
Ja=function(){var a=null;if(!Ia)return a;try{var b=function(c){return c};a=Ia.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};_.La=function(){Ka===void 0&&(Ka=Ja());return Ka};_.Na=function(a){var b=_.La();a=b?b.createScriptURL(a):a;return new _.Ma(a)};_.Oa=function(a){if(a instanceof _.Ma)return a.g;throw Error("v");};
_.Pa=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Ra=function(a){var b=_.La();a=b?b.createScript(a):a;return new _.Qa(a)};_.Sa=function(a){if(a instanceof _.Qa)return a.g;throw Error("v");};_.Ta=function(a,b){a.src=_.Oa(b);(b=_.Pa("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Ua=function(){var a=_.t.navigator;return a&&(a=a.userAgent)?a:""};
_.v=function(a){return _.Ua().indexOf(a)!=-1};_.Xa=function(){return _.Va?!!_.Wa&&_.Wa.brands.length>0:!1};_.Ya=function(){return _.Xa()?!1:_.v("Opera")};Za=function(){return _.Va?!!_.Wa&&!!_.Wa.platform:!1};_.$a=function(){return _.v("iPhone")&&!_.v("iPod")&&!_.v("iPad")};_.ab=function(){return _.$a()||_.v("iPad")||_.v("iPod")};_.bb=function(){return Za()?_.Wa.platform==="macOS":_.v("Macintosh")};cb=function(a){this.src=a;this.g={};this.h=0};
hb=function(a,b){_.w.call(this);this.o=a;if(b){if(this.l)throw Error("z");this.l=b;this.h=_.db(b);this.g=new eb(_.fb(b));this.g.le(this.o.h());this.j=new gb(this.h);this.j.start()}};jb=function(a){a=a.fg.charCodeAt(a.Yb++);return ib[a]};kb=function(a){var b=0,c=0;do{var d=jb(a);b|=(d&31)<<c;c+=5}while(d&32);return b<0?b+4294967296:b};_.lb=function(a){_.t.setTimeout(function(){throw a;},0)};qb=function(){for(var a;a=mb.remove();){try{a.g.call(a.scope)}catch(b){_.lb(b)}nb(ob,a)}pb=!1};rb=function(){};
sb=function(){};_.ub=function(a){a=_.tb(a);return _.Na(a)};_.tb=function(a){return a===null?"null":a===void 0?"undefined":a};vb=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};wb=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
xb=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.yb=xb(this);x=function(a,b){if(b)a:{var c=_.yb;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&wb(c,a,{configurable:!0,writable:!0,value:b})}};
x("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;wb(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("b");return new b(c+(f||"")+"_"+d++,f)};return e});x("Symbol.iterator",function(a){if(a)return a;a=Symbol("c");wb(Array.prototype,a,{configurable:!0,writable:!0,value:function(){return zb(vb(this))}});return a});
zb=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};Ab=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};
_.Bb=function(){function a(){function c(){}new c;Reflect.construct(c,[],function(){});return new c instanceof c}if(typeof Reflect!="undefined"&&Reflect.construct){if(a())return Reflect.construct;var b=Reflect.construct;return function(c,d,e){c=b(c,d);e&&Reflect.setPrototypeOf(c,e.prototype);return c}}return function(c,d,e){e===void 0&&(e=c);e=Ab(e.prototype||Object.prototype);return Function.prototype.apply.call(c,e,d)||e}}();
if(typeof Object.setPrototypeOf=="function")Cb=Object.setPrototypeOf;else{var Db;a:{var Eb={a:!0},Fb={};try{Fb.__proto__=Eb;Db=Fb.a;break a}catch(a){}Db=!1}Cb=Db?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError("d`"+a);return a}:null}Gb=Cb;
_.y=function(a,b){a.prototype=Ab(b.prototype);a.prototype.constructor=a;if(Gb)Gb(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.P=b.prototype};_.z=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:vb(a)};throw Error("e`"+String(a));};
_.Hb=function(a){if(!(a instanceof Array)){a=_.z(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};Ib=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Jb=typeof Object.assign=="function"?Object.assign:function(a,b){if(a==null)throw new TypeError("f");a=Object(a);for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Ib(d,e)&&(a[e]=d[e])}return a};x("Object.assign",function(a){return a||Jb});
_.Kb=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};x("globalThis",function(a){return a||_.yb});x("Reflect",function(a){return a?a:{}});x("Reflect.construct",function(){return _.Bb});x("Reflect.setPrototypeOf",function(a){return a?a:Gb?function(b,c){try{return Gb(b,c),!0}catch(d){return!1}}:null});
x("Promise",function(a){function b(){this.g=null}function c(g){return g instanceof e?g:new e(function(h){h(g)})}if(a)return a;b.prototype.h=function(g){if(this.g==null){this.g=[];var h=this;this.j(function(){h.o()})}this.g.push(g)};var d=_.yb.setTimeout;b.prototype.j=function(g){d(g,0)};b.prototype.o=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var k=g[h];g[h]=null;try{k()}catch(m){this.l(m)}}}this.g=null};b.prototype.l=function(g){this.j(function(){throw g;
})};var e=function(g){this.g=0;this.j=void 0;this.h=[];this.B=!1;var h=this.l();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.l=function(){function g(m){return function(n){k||(k=!0,m.call(h,n))}}var h=this,k=!1;return{resolve:g(this.L),reject:g(this.o)}};e.prototype.L=function(g){if(g===this)this.o(new TypeError("i"));else if(g instanceof e)this.O(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.I(g):this.A(g)}};e.prototype.I=
function(g){var h=void 0;try{h=g.then}catch(k){this.o(k);return}typeof h=="function"?this.qa(h,g):this.A(g)};e.prototype.o=function(g){this.D(2,g)};e.prototype.A=function(g){this.D(1,g)};e.prototype.D=function(g,h){if(this.g!=0)throw Error("j`"+g+"`"+h+"`"+this.g);this.g=g;this.j=h;this.g===2&&this.J();this.G()};e.prototype.J=function(){var g=this;d(function(){if(g.F()){var h=_.yb.console;typeof h!=="undefined"&&h.error(g.j)}},1)};e.prototype.F=function(){if(this.B)return!1;var g=_.yb.CustomEvent,
h=_.yb.Event,k=_.yb.dispatchEvent;if(typeof k==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=_.yb.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.j;return k(g)};e.prototype.G=function(){if(this.h!=null){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);this.h=null}};var f=new b;e.prototype.O=function(g){var h=this.l();g.xd(h.resolve,
h.reject)};e.prototype.qa=function(g,h){var k=this.l();try{g.call(h,k.resolve,k.reject)}catch(m){k.reject(m)}};e.prototype.then=function(g,h){function k(q,r){return typeof q=="function"?function(u){try{m(q(u))}catch(A){n(A)}}:r}var m,n,p=new e(function(q,r){m=q;n=r});this.xd(k(g,m),k(h,n));return p};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.xd=function(g,h){function k(){switch(m.g){case 1:g(m.j);break;case 2:h(m.j);break;default:throw Error("k`"+m.g);}}var m=this;this.h==
null?f.h(k):this.h.push(k);this.B=!0};e.resolve=c;e.reject=function(g){return new e(function(h,k){k(g)})};e.race=function(g){return new e(function(h,k){for(var m=_.z(g),n=m.next();!n.done;n=m.next())c(n.value).xd(h,k)})};e.all=function(g){var h=_.z(g),k=h.next();return k.done?c([]):new e(function(m,n){function p(u){return function(A){q[u]=A;r--;r==0&&m(q)}}var q=[],r=0;do q.push(void 0),r++,c(k.value).xd(p(q.length-1),n),k=h.next();while(!k.done)})};return e});
var Lb=function(a,b,c){if(a==null)throw new TypeError("l`"+c);if(b instanceof RegExp)throw new TypeError("m`"+c);return a+""};x("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Lb(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});x("Object.setPrototypeOf",function(a){return a||Gb});x("Symbol.dispose",function(a){return a?a:Symbol("n")});
x("WeakMap",function(a){function b(){}function c(k){var m=typeof k;return m==="object"&&k!==null||m==="function"}function d(k){if(!Ib(k,f)){var m=new b;wb(k,f,{value:m})}}function e(k){var m=Object[k];m&&(Object[k]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),m=Object.seal({}),n=new a([[k,2],[m,3]]);if(n.get(k)!=2||n.get(m)!=3)return!1;n.delete(k);n.set(m,4);return!n.has(k)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(k){this.g=(g+=Math.random()+1).toString();if(k){k=_.z(k);for(var m;!(m=k.next()).done;)m=m.value,this.set(m[0],m[1])}};h.prototype.set=function(k,m){if(!c(k))throw Error("o");d(k);if(!Ib(k,f))throw Error("p`"+k);k[f][this.g]=m;return this};h.prototype.get=function(k){return c(k)&&Ib(k,f)?k[f][this.g]:void 0};h.prototype.has=function(k){return c(k)&&Ib(k,f)&&Ib(k[f],this.g)};h.prototype.delete=function(k){return c(k)&&
Ib(k,f)&&Ib(k[f],this.g)?delete k[f][this.g]:!1};return h});
x("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(_.z([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var m=k.entries(),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]=
f();this.size=0;if(h){h=_.z(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.entry?m.entry.value=k:(m.entry={next:this[1],ub:this[1].ub,head:this[1],key:h,value:k},m.list.push(m.entry),this[1].ub.next=m.entry,this[1].ub=m.entry,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.entry.ub.next=
h.entry.next,h.entry.next.ub=h.entry.ub,h.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].ub=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).entry};c.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=
function(h,k){for(var m=this.entries(),n;!(n=m.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var m=k&&typeof k;m=="object"||m=="function"?b.has(k)?m=b.get(k):(m=""+ ++g,b.set(k,m)):m="p_"+k;var n=h[0][m];if(n&&Ib(h[0],m))for(h=0;h<n.length;h++){var p=n[h];if(k!==k&&p.key!==p.key||k===p.key)return{id:m,list:n,index:h,entry:p}}return{id:m,list:n,index:-1,entry:void 0}},e=function(h,k){var m=h[1];return zb(function(){if(m){for(;m.head!=
h[1];)m=m.ub;for(;m.next!=m.head;)return m=m.next,{done:!1,value:k(m)};m=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.ub=h.next=h.head=h},g=0;return c});
x("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.z([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.g=new Map;if(c){c=
_.z(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size};b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});var Mb=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};x("Array.prototype.entries",function(a){return a?a:function(){return Mb(this,function(b,c){return[b,c]})}});
x("Array.prototype.keys",function(a){return a?a:function(){return Mb(this,function(b){return b})}});x("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Lb(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});x("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
x("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});x("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Ib(b,d)&&c.push([d,b[d]]);return c}});
x("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});x("Array.prototype.values",function(a){return a?a:function(){return Mb(this,function(b,c){return c})}});
x("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Ib(b,d)&&c.push(b[d]);return c}});x("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});x("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
x("String.prototype.includes",function(a){return a?a:function(b,c){return Lb(this,b,"includes").indexOf(b,c||0)!==-1}});x("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});x("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});x("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});x("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});
x("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});x("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});x("Array.prototype.fill",function(a){return a?a:function(b,c,d){var e=this.length||0;c<0&&(c=Math.max(0,e+c));if(d==null||d>e)d=e;d=Number(d);d<0&&(d=Math.max(0,e+d));for(c=Number(c||0);c<d;c++)this[c]=b;return this}});
var Nb=function(a){return a?a:Array.prototype.fill};x("Int8Array.prototype.fill",Nb);x("Uint8Array.prototype.fill",Nb);x("Uint8ClampedArray.prototype.fill",Nb);x("Int16Array.prototype.fill",Nb);x("Uint16Array.prototype.fill",Nb);x("Int32Array.prototype.fill",Nb);x("Uint32Array.prototype.fill",Nb);x("Float32Array.prototype.fill",Nb);x("Float64Array.prototype.fill",Nb);
x("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("q");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});x("Object.getOwnPropertySymbols",function(a){return a?a:function(){return[]}});
x("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});x("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});
_._DumpException=window._DumpException||function(a){throw a;};window._DumpException=_._DumpException;var Ob,Qb,Rb,Sb,Ub,Vb,Wb,Xb;Ob=Ob||{};_.t=this||self;Qb=function(a,b){var c=_.Pb("WIZ_global_data.oxN3nb");a=c&&c[a];return a!=null?a:b};Rb=_.t._F_toggles_default_tr||[];Sb=function(){};Sb.get=function(){return null};_.Pb=function(a,b){a=a.split(".");b=b||_.t;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};_.Tb=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.ra=function(a){var b=_.Tb(a);return b=="array"||b=="object"&&typeof a.length=="number"};
_.za=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};_.Aa=function(a){return Object.prototype.hasOwnProperty.call(a,Ub)&&a[Ub]||(a[Ub]=++Vb)};Ub="closure_uid_"+(Math.random()*1E9>>>0);Vb=0;Wb=function(a,b,c){return a.call.apply(a.bind,arguments)};
Xb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.B=function(a,b,c){_.B=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?Wb:Xb;return _.B.apply(null,arguments)};
_.Yb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.Zb=function(){return Date.now()};_.$b=function(a,b){a=a.split(".");for(var c=_.t,d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};
_.D=function(a,b){function c(){}c.prototype=b.prototype;a.P=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.wm=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};_.D(aa,Error);aa.prototype.name="CustomError";var ac;_.D(ba,aa);ba.prototype.name="AssertionError";var da;var bc=function(a,b){this.h=a;this.g=b||null};bc.prototype.toString=function(){return this.h};var cc=new bc("n73qwf","n73qwf");var oa=Symbol("s");_.w=function(){this.Da=this.Da;this.qa=this.qa};_.w.prototype.Da=!1;_.w.prototype.Za=function(){return this.Da};_.w.prototype.dispose=function(){this.Da||(this.Da=!0,this.M())};_.w.prototype[Symbol.dispose]=function(){this.dispose()};_.w.prototype.M=function(){if(this.qa)for(;this.qa.length;)this.qa.shift()()};var ta,ec;ta=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};_.dc=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)};
ec=Array.prototype.reduce?function(a,b,c){Array.prototype.reduce.call(a,b,c)}:function(a,b,c){var d=c;(0,_.dc)(a,function(e,f){d=b.call(void 0,d,e,f,a)})};_.fc=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};_.hc=function(a,b){this.width=a;this.height=b};_.ic=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.hc.prototype.aspectRatio=function(){return this.width/this.height};_.hc.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};_.hc.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
_.hc.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};var Ea;Ea="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.jc=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};_.kc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};var lc=globalThis.trustedTypes,Ia=lc,Ka;_.Ma=function(a){this.g=a};_.Ma.prototype.toString=function(){return this.g+""};_.mc=Ha(function(){return typeof URL==="function"});_.nc=function(a){this.g=a};_.nc.prototype.toString=function(){return this.g+""};_.oc=Ha(function(){return new _.nc(lc?lc.emptyHTML:"")});_.Qa=function(a){this.g=a};_.Qa.prototype.toString=function(){return this.g+""};_.pc=function(a){return encodeURIComponent(String(a))};_.qc=function(a){return decodeURIComponent(a.replace(/\+/g," "))};_.rc=function(){return Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^_.Zb()).toString(36)};var sc=!!(Rb[0]>>15&1),tc=!!(Rb[0]&1024),uc=!!(Rb[0]>>16&1),vc=!!(Rb[0]&128);var wc;wc=Qb(1,!0);_.Va=sc?uc:Qb(610401301,!1);_.xc=sc?tc||!vc:Qb(748402147,wc);var yc;yc=_.t.navigator;_.Wa=yc?yc.userAgentData||null:null;_.zc=function(a){_.zc[" "](a);return a};_.zc[" "]=function(){};var Nc;_.Ac=_.Ya();_.Bc=_.Xa()?!1:_.v("Trident")||_.v("MSIE");_.Cc=_.v("Edge");_.Dc=_.v("Gecko")&&!(_.Ua().toLowerCase().indexOf("webkit")!=-1&&!_.v("Edge"))&&!(_.v("Trident")||_.v("MSIE"))&&!_.v("Edge");_.Ec=_.Ua().toLowerCase().indexOf("webkit")!=-1&&!_.v("Edge");_.Fc=_.Ec&&_.v("Mobile");_.Gc=_.bb();_.Hc=Za()?_.Wa.platform==="Windows":_.v("Windows");_.Ic=Za()?_.Wa.platform==="Android":_.v("Android");_.Jc=_.$a();_.Kc=_.v("iPad");_.Lc=_.v("iPod");_.Mc=_.ab();
a:{var Oc="",Pc=function(){var a=_.Ua();if(_.Dc)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.Cc)return/Edge\/([\d\.]+)/.exec(a);if(_.Bc)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Ec)return/WebKit\/(\S+)/.exec(a);if(_.Ac)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Pc&&(Oc=Pc?Pc[1]:"");if(_.Bc){var Qc,Rc=_.t.document;Qc=Rc?Rc.documentMode:void 0;if(Qc!=null&&Qc>parseFloat(Oc)){Nc=String(Qc);break a}}Nc=Oc}_.Sc=Nc;var Tc="ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" "),
Uc=[["A",new Map([["href",{Aa:7}]])],["AREA",new Map([["href",{Aa:7}]])],["LINK",new Map([["href",{Aa:5,conditions:new Map([["rel",new Set("alternate author bookmark canonical cite help icon license next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" "))]])}]])],["SOURCE",new Map([["src",{Aa:5}],["srcset",{Aa:6}]])],["IMG",new Map([["src",{Aa:5}],["srcset",{Aa:6}]])],["VIDEO",new Map([["src",{Aa:5}]])],["AUDIO",new Map([["src",{Aa:5}]])]],Vc="title aria-atomic aria-autocomplete aria-busy aria-checked aria-current aria-disabled aria-dropeffect aria-expanded aria-haspopup aria-hidden aria-invalid aria-label aria-level aria-live aria-multiline aria-multiselectable aria-orientation aria-posinset aria-pressed aria-readonly aria-relevant aria-required aria-selected aria-setsize aria-sort aria-valuemax aria-valuemin aria-valuenow aria-valuetext alt align autocapitalize autocomplete autocorrect autofocus autoplay bgcolor border cellpadding cellspacing checked cite color cols colspan controls controlslist coords crossorigin datetime disabled download draggable enctype face formenctype frameborder height hreflang hidden inert ismap label lang loop max maxlength media minlength min multiple muted nonce open playsinline placeholder poster preload rel required reversed role rows rowspan selected shape size sizes slot span spellcheck start step summary translate type usemap valign value width wrap itemscope itemtype itemid itemprop itemref".split(" "),
Wc=[["dir",{Aa:3,conditions:Ha(function(){return new Map([["dir",new Set(["auto","ltr","rtl"])]])})}],["async",{Aa:3,conditions:Ha(function(){return new Map([["async",new Set(["async"])]])})}],["loading",{Aa:3,conditions:Ha(function(){return new Map([["loading",new Set(["eager","lazy"])]])})}],["target",{Aa:3,conditions:Ha(function(){return new Map([["target",new Set(["_self","_blank"])]])})}]],Xc=new function(a,b,c){var d=new Set(["data-","aria-"]),e=new Map(Uc);this.j=a;this.g=e;this.l=b;this.o=
c;this.h=d}(new Set(Ha(function(){return Tc.concat("STYLE TITLE INPUT TEXTAREA BUTTON LABEL".split(" "))})),new Set(Ha(function(){return Vc.concat(["class","id","tabindex","contenteditable","name"])})),new Map(Ha(function(){return Wc.concat([["style",{Aa:1}]])})));var Yc;Yc=function(){this.g=Xc};_.Zc=Ha(function(){return new Yc});var dd,cd,gd;_.db=function(a){return a?new _.$c(_.ad(a)):ac||(ac=new _.$c)};_.bd=function(a,b){return typeof b==="string"?a.getElementById(b):b};dd=function(a,b){_.jc(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:cd.hasOwnProperty(d)?a.setAttribute(cd[d],c):d.lastIndexOf("aria-",0)==0||d.lastIndexOf("data-",0)==0?a.setAttribute(d,c):a[d]=c})};
cd={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.ed=function(a){a=a.document;a=a.compatMode=="CSS1Compat"?a.documentElement:a.body;return new _.hc(a.clientWidth,a.clientHeight)};_.fb=function(a){return a?a.defaultView:window};
_.hd=function(a,b){var c=b[1],d=_.fd(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):dd(d,c));b.length>2&&gd(a,d,b,2);return d};
gd=function(a,b,c,d){function e(h){h&&b.appendChild(typeof h==="string"?a.createTextNode(h):h)}for(;d<c.length;d++){var f=c[d];if(!_.ra(f)||_.za(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.za(f)){var g=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){g=typeof f.item=="function";break a}}g=!1}_.dc(g?_.xa(f):f,e)}}};_.fd=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};
_.id=function(a,b){gd(_.ad(a),a,arguments,1)};_.jd=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.kd=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.ld=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.ad=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.md=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.jd(a),a.appendChild(_.ad(a).createTextNode(String(b)))};_.$c=function(a){this.g=a||_.t.document||document};_.l=_.$c.prototype;_.l.C=function(a){return _.bd(this.g,a)};_.l.Hk=_.$c.prototype.C;_.l.getElementsByTagName=function(a,b){return(b||this.g).getElementsByTagName(String(a))};
_.l.R=function(a,b,c){return _.hd(this.g,arguments)};_.l.createElement=function(a){return _.fd(this.g,a)};_.l.appendChild=function(a,b){a.appendChild(b)};_.l.append=_.id;_.l.canHaveChildren=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.l.rf=_.jd;_.l.removeNode=_.kd;_.l.contains=_.ld;_.l.zc=_.md;var nd=function(){this.id="b"};nd.prototype.toString=function(){return this.id};_.od=function(a,b){this.type=a instanceof nd?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.h=!1};_.od.prototype.stopPropagation=function(){this.h=!0};_.od.prototype.preventDefault=function(){this.defaultPrevented=!0};var pd=function(){if(!_.t.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.t.addEventListener("test",c,b);_.t.removeEventListener("test",c,b)}catch(d){}return a}();_.qd=function(a,b){_.od.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.j=!1;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.g=null;a&&this.init(a,b)};_.D(_.qd,_.od);
_.qd.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.Ec||a.offsetX!==void 0?a.offsetX:a.layerX,
this.offsetY=_.Ec||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.j=_.Gc?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||0;this.pointerType=
a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.g=a;a.defaultPrevented&&_.qd.P.preventDefault.call(this)};_.qd.prototype.stopPropagation=function(){_.qd.P.stopPropagation.call(this);this.g.stopPropagation?this.g.stopPropagation():this.g.cancelBubble=!0};_.qd.prototype.preventDefault=function(){_.qd.P.preventDefault.call(this);var a=this.g;a.preventDefault?a.preventDefault():a.returnValue=!1};var rd;rd="closure_listenable_"+(Math.random()*1E6|0);_.sd=function(a){return!(!a||!a[rd])};var td=0;var ud=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Nd=e;this.key=++td;this.Wc=this.wd=!1},vd=function(a){a.Wc=!0;a.listener=null;a.proxy=null;a.src=null;a.Nd=null};var xd;cb.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=wd(a,b,d,e);g>-1?(b=a[g],c||(b.wd=!1)):(b=new ud(b,this.src,f,!!d,e),b.wd=c,a.push(b));return b};cb.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.g))return!1;var e=this.g[a];b=wd(e,b,c,d);return b>-1?(vd(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.g[a],this.h--),!0):!1};
xd=function(a,b){var c=b.type;if(!(c in a.g))return!1;var d=_.wa(a.g[c],b);d&&(vd(b),a.g[c].length==0&&(delete a.g[c],a.h--));return d};_.yd=function(a){var b=0,c;for(c in a.g){for(var d=a.g[c],e=0;e<d.length;e++)++b,vd(d[e]);delete a.g[c];a.h--}};cb.prototype.Qc=function(a,b,c,d){a=this.g[a.toString()];var e=-1;a&&(e=wd(a,b,c,d));return e>-1?a[e]:null};
cb.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return Ca(this.g,function(f){for(var g=0;g<f.length;++g)if(!(c&&f[g].type!=d||e&&f[g].capture!=b))return!0;return!1})};var wd=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.Wc&&f.listener==b&&f.capture==!!c&&f.Nd==d)return e}return-1};var zd,Ad,Bd,Ed,Gd,Hd,Id,Ld,Dd;zd="closure_lm_"+(Math.random()*1E6|0);Ad={};Bd=0;_.E=function(a,b,c,d,e){if(d&&d.once)return _.Cd(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.E(a,b[f],c,d,e);return null}c=Dd(c);return _.sd(a)?a.K(b,c,_.za(d)?!!d.capture:!!d,e):Ed(a,b,c,!1,d,e)};
Ed=function(a,b,c,d,e,f){if(!b)throw Error("x");var g=_.za(e)?!!e.capture:!!e,h=_.Fd(a);h||(a[zd]=h=new cb(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Gd();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)pd||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Hd(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("y");Bd++;return c};Gd=function(){var a=Id,b=function(c){return a.call(b.src,b.listener,c)};return b};
_.Cd=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Cd(a,b[f],c,d,e);return null}c=Dd(c);return _.sd(a)?a.Cb(b,c,_.za(d)?!!d.capture:!!d,e):Ed(a,b,c,!0,d,e)};_.Jd=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.Jd(a,b[f],c,d,e);else d=_.za(d)?!!d.capture:!!d,c=Dd(c),_.sd(a)?a.bb(b,c,d,e):a&&(a=_.Fd(a))&&(b=a.Qc(b,c,d,e))&&_.Kd(b)};
_.Kd=function(a){if(typeof a==="number"||!a||a.Wc)return!1;var b=a.src;if(_.sd(b))return xd(b.Va,a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Hd(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Bd--;(c=_.Fd(b))?(xd(c,a),c.h==0&&(c.src=null,b[zd]=null)):vd(a);return!0};Hd=function(a){return a in Ad?Ad[a]:Ad[a]="on"+a};
Id=function(a,b){if(a.Wc)a=!0;else{b=new _.qd(b,this);var c=a.listener,d=a.Nd||a.src;a.wd&&_.Kd(a);a=c.call(d,b)}return a};_.Fd=function(a){a=a[zd];return a instanceof cb?a:null};Ld="__closure_events_fn_"+(Math.random()*1E9>>>0);Dd=function(a){if(typeof a==="function")return a;a[Ld]||(a[Ld]=function(b){return a.handleEvent(b)});return a[Ld]};_.F=function(){_.w.call(this);this.Va=new cb(this);this.xi=this;this.ee=null};_.D(_.F,_.w);_.F.prototype[rd]=!0;_.l=_.F.prototype;_.l.le=function(a){this.ee=a};_.l.addEventListener=function(a,b,c,d){_.E(this,a,b,c,d)};_.l.removeEventListener=function(a,b,c,d){_.Jd(this,a,b,c,d)};
_.l.dispatchEvent=function(a){var b,c=this.ee;if(c)for(b=[];c;c=c.ee)b.push(c);c=this.xi;var d=a.type||a;if(typeof a==="string")a=new _.od(a,c);else if(a instanceof _.od)a.target=a.target||c;else{var e=a;a=new _.od(d,c);Fa(a,e)}e=!0;var f;if(b)for(f=b.length-1;!a.h&&f>=0;f--){var g=a.currentTarget=b[f];e=Md(g,d,!0,a)&&e}a.h||(g=a.currentTarget=c,e=Md(g,d,!0,a)&&e,a.h||(e=Md(g,d,!1,a)&&e));if(b)for(f=0;!a.h&&f<b.length;f++)g=a.currentTarget=b[f],e=Md(g,d,!1,a)&&e;return e};
_.l.M=function(){_.F.P.M.call(this);this.Va&&_.yd(this.Va);this.ee=null};_.l.K=function(a,b,c,d){return this.Va.add(String(a),b,!1,c,d)};_.l.Cb=function(a,b,c,d){return this.Va.add(String(a),b,!0,c,d)};_.l.bb=function(a,b,c,d){return this.Va.remove(String(a),b,c,d)};var Md=function(a,b,c,d){b=a.Va.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.Wc&&g.capture==c){var h=g.listener,k=g.Nd||g.src;g.wd&&xd(a.Va,g);e=h.call(k,d)!==!1&&e}}return e&&!d.defaultPrevented};
_.F.prototype.Qc=function(a,b,c,d){return this.Va.Qc(String(a),b,c,d)};_.F.prototype.hasListener=function(a,b){return this.Va.hasListener(a!==void 0?String(a):void 0,b)};var eb=function(a){_.F.call(this);this.g=a||window;this.h=_.E(this.g,"resize",this.l,!1,this);this.j=_.ed(this.g||window)};_.D(eb,_.F);eb.prototype.M=function(){eb.P.M.call(this);this.h&&(_.Kd(this.h),this.h=null);this.j=this.g=null};eb.prototype.l=function(){var a=_.ed(this.g||window);_.ic(a,this.j)||(this.j=a,this.dispatchEvent("resize"))};var gb=function(a){_.F.call(this);this.j=a?a.g.defaultView:window;this.o=this.j.devicePixelRatio>=1.5?2:1;this.h=(0,_.B)(this.A,this);this.l=null;(this.g=this.j.matchMedia?this.j.matchMedia("(min-resolution: 1.5dppx), (-webkit-min-device-pixel-ratio: 1.5)"):null)&&typeof this.g.addListener!=="function"&&typeof this.g.addEventListener!=="function"&&(this.g=null)};_.D(gb,_.F);
gb.prototype.start=function(){var a=this;this.g&&(typeof this.g.addEventListener==="function"?(this.g.addEventListener("change",this.h),this.l=function(){a.g.removeEventListener("change",a.h)}):(this.g.addListener(this.h),this.l=function(){a.g.removeListener(a.h)}))};gb.prototype.A=function(){var a=this.j.devicePixelRatio>=1.5?2:1;this.o!=a&&(this.o=a,this.dispatchEvent("a"))};gb.prototype.M=function(){this.l&&this.l();gb.P.M.call(this)};_.D(hb,_.w);hb.prototype.M=function(){this.h=this.l=null;this.g&&(this.g.dispose(),this.g=null);_.qa(this.j);this.j=null};_.pa(cc,hb);var ib=new Uint8Array(123);var Nd=[];Sb=Sb||{};var Od=function(){_.w.call(this)};_.D(Od,_.w);Od.prototype.initialize=function(){};var Pd=function(a,b){this.g=a;this.h=b};Pd.prototype.execute=function(a){this.g&&(this.g.call(this.h||null,a),this.g=this.h=null)};Pd.prototype.abort=function(){this.h=this.g=null};var Qd=function(a,b){_.w.call(this);this.h=a;this.A=b;this.l=[];this.j=[];this.o=[]};_.D(Qd,_.w);Qd.prototype.B=Od;Qd.prototype.g=null;Qd.prototype.Wa=function(){return this.A};var Rd=function(a,b){a.j.push(new Pd(b))};Qd.prototype.onLoad=function(a){var b=new this.B;b.initialize(a());this.g=b;b=(b=Sd(this.o,a()))||Sd(this.l,a());b||(this.j.length=0);return b};Qd.prototype.nf=function(a){(a=Sd(this.j,a))&&_.lb(Error("A`"+a));this.o.length=0;this.l.length=0};
var Sd=function(a,b){for(var c=[],d=0;d<a.length;d++)try{a[d].execute(b)}catch(e){_.lb(e),c.push(e)}a.length=0;return c.length?c:null};Qd.prototype.M=function(){Qd.P.M.call(this);_.qa(this.g)};var Td=function(){this.S=this.Da=null};_.l=Td.prototype;_.l.rh=function(){};_.l.Af=function(){};_.l.nh=function(){throw Error("C");};_.l.ug=function(){return this.Da};_.l.Bf=function(a){this.Da=a};_.l.isActive=function(){return!1};_.l.Qg=function(){return!1};var Ud=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var Vd=function(a,b){this.l=a;this.j=b;this.h=0;this.g=null};Vd.prototype.get=function(){if(this.h>0){this.h--;var a=this.g;this.g=a.next;a.next=null}else a=this.l();return a};var nb=function(a,b){a.j(b);a.h<100&&(a.h++,b.next=a.g,a.g=b)};var Wd=function(){this.h=this.g=null};Wd.prototype.add=function(a,b){var c=ob.get();c.set(a,b);this.h?this.h.next=c:this.g=c;this.h=c};Wd.prototype.remove=function(){var a=null;this.g&&(a=this.g,this.g=this.g.next,this.g||(this.h=null),a.next=null);return a};var ob=new Vd(function(){return new Xd},function(a){return a.reset()}),Xd=function(){this.next=this.scope=this.g=null};Xd.prototype.set=function(a,b){this.g=a;this.scope=b;this.next=null};
Xd.prototype.reset=function(){this.next=this.scope=this.g=null};var Yd,pb=!1,mb=new Wd,$d=function(a,b){Yd||Zd();pb||(Yd(),pb=!0);mb.add(a,b)},Zd=function(){var a=Promise.resolve(void 0);Yd=function(){a.then(qb)}};_.ae=function(){};var be=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};var ee,oe,me,ke;_.de=function(a){this.g=0;this.B=void 0;this.l=this.h=this.j=null;this.o=this.A=!1;if(a!=_.ae)try{var b=this;a.call(void 0,function(c){_.ce(b,2,c)},function(c){_.ce(b,3,c)})}catch(c){_.ce(this,3,c)}};ee=function(){this.next=this.j=this.h=this.l=this.g=null;this.o=!1};ee.prototype.reset=function(){this.j=this.h=this.l=this.g=null;this.o=!1};var fe=new Vd(function(){return new ee},function(a){a.reset()}),ge=function(a,b,c){var d=fe.get();d.l=a;d.h=b;d.j=c;return d};
_.de.prototype.then=function(a,b,c){return he(this,Ud(typeof a==="function"?a:null),Ud(typeof b==="function"?b:null),c)};_.de.prototype.$goog_Thenable=!0;_.de.prototype.D=function(a,b){return he(this,null,Ud(a),b)};_.de.prototype.catch=_.de.prototype.D;_.de.prototype.cancel=function(a){if(this.g==0){var b=new ie(a);$d(function(){je(this,b)},this)}};
var je=function(a,b){if(a.g==0)if(a.j){var c=a.j;if(c.h){for(var d=0,e=null,f=null,g=c.h;g&&(g.o||(d++,g.g==a&&(e=g),!(e&&d>1)));g=g.next)e||(f=g);e&&(c.g==0&&d==1?je(c,b):(f?(d=f,d.next==c.l&&(c.l=d),d.next=d.next.next):ke(c),le(c,e,3,b)))}a.j=null}else _.ce(a,3,b)},ne=function(a,b){a.h||a.g!=2&&a.g!=3||me(a);a.l?a.l.next=b:a.h=b;a.l=b},he=function(a,b,c,d){var e=ge(null,null,null);e.g=new _.de(function(f,g){e.l=b?function(h){try{var k=b.call(d,h);f(k)}catch(m){g(m)}}:f;e.h=c?function(h){try{var k=
c.call(d,h);k===void 0&&h instanceof ie?g(h):f(k)}catch(m){g(m)}}:g});e.g.j=a;ne(a,e);return e.g};_.de.prototype.F=function(a){this.g=0;_.ce(this,2,a)};_.de.prototype.I=function(a){this.g=0;_.ce(this,3,a)};
_.ce=function(a,b,c){if(a.g==0){a===c&&(b=3,c=new TypeError("D"));a.g=1;a:{var d=c,e=a.F,f=a.I;if(d instanceof _.de){ne(d,ge(e||_.ae,f||null,a));var g=!0}else if(be(d))d.then(e,f,a),g=!0;else{if(_.za(d))try{var h=d.then;if(typeof h==="function"){oe(d,h,e,f,a);g=!0;break a}}catch(k){f.call(a,k);g=!0;break a}g=!1}}g||(a.B=c,a.g=b,a.j=null,me(a),b!=3||c instanceof ie||pe(a,c))}};oe=function(a,b,c,d,e){var f=!1,g=function(k){f||(f=!0,c.call(e,k))},h=function(k){f||(f=!0,d.call(e,k))};try{b.call(a,g,h)}catch(k){h(k)}};
me=function(a){a.A||(a.A=!0,$d(a.G,a))};ke=function(a){var b=null;a.h&&(b=a.h,a.h=b.next,b.next=null);a.h||(a.l=null);return b};_.de.prototype.G=function(){for(var a;a=ke(this);)le(this,a,this.g,this.B);this.A=!1};
var le=function(a,b,c,d){if(c==3&&b.h&&!b.o)for(;a&&a.o;a=a.j)a.o=!1;if(b.g)b.g.j=null,qe(b,c,d);else try{b.o?b.l.call(b.j):qe(b,c,d)}catch(e){re.call(null,e)}nb(fe,b)},qe=function(a,b,c){b==2?a.l.call(a.j,c):a.h&&a.h.call(a.j,c)},pe=function(a,b){a.o=!0;$d(function(){a.o&&re.call(null,b)})},re=_.lb,ie=function(a){aa.call(this,a)};_.D(ie,aa);ie.prototype.name="cancel";/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
var se=function(){this.B=[];this.o=this.h=!1;this.l=void 0;this.I=this.J=this.G=!1;this.D=0;this.j=null;this.A=0};se.prototype.cancel=function(a){if(this.h)this.l instanceof se&&this.l.cancel();else{if(this.j){var b=this.j;delete this.j;a?b.cancel(a):(b.A--,b.A<=0&&b.cancel())}this.I=!0;this.h||this.F(new te(this))}};se.prototype.L=function(a,b){this.G=!1;ue(this,a,b)};var ue=function(a,b,c){a.h=!0;a.l=c;a.o=!b;ve(a)},xe=function(a){if(a.h){if(!a.I)throw new we(a);a.I=!1}};
se.prototype.g=function(a){xe(this);ue(this,!0,a)};se.prototype.F=function(a){xe(this);ue(this,!1,a)};var ze=function(a,b,c){ye(a,b,null,c)},Ae=function(a,b,c){ye(a,null,b,c)},ye=function(a,b,c,d){var e=a.h;e||(b===c?b=c=Ud(b):(b=Ud(b),c=Ud(c)));a.B.push([b,c,d]);e&&ve(a)};se.prototype.then=function(a,b,c){var d,e,f=new _.de(function(g,h){e=g;d=h});ye(this,e,function(g){g instanceof te?f.cancel():d(g);return Be},this);return f.then(a,b,c)};se.prototype.$goog_Thenable=!0;
var Ce=function(a,b){b instanceof se?ze(a,(0,_.B)(b.O,b)):ze(a,function(){return b})};se.prototype.O=function(a){var b=new se;ye(this,b.g,b.F,b);a&&(b.j=this,this.A++);return b};
var De=function(a){return _.fc(a.B,function(b){return typeof b[1]==="function"})},Be={},ve=function(a){if(a.D&&a.h&&De(a)){var b=a.D,c=Ee[b];c&&(_.t.clearTimeout(c.g),delete Ee[b]);a.D=0}a.j&&(a.j.A--,delete a.j);b=a.l;for(var d=c=!1;a.B.length&&!a.G;){var e=a.B.shift(),f=e[0],g=e[1];e=e[2];if(f=a.o?g:f)try{var h=f.call(e||null,b);h===Be&&(h=void 0);h!==void 0&&(a.o=a.o&&(h==b||h instanceof Error),a.l=b=h);if(be(b)||typeof _.t.Promise==="function"&&b instanceof _.t.Promise)d=!0,a.G=!0}catch(k){b=
k,a.o=!0,De(a)||(c=!0)}}a.l=b;d&&(h=(0,_.B)(a.L,a,!0),d=(0,_.B)(a.L,a,!1),b instanceof se?(ye(b,h,d),b.J=!0):b.then(h,d));c&&(b=new Fe(b),Ee[b.g]=b,a.D=b.g)},we=function(){aa.call(this)};_.D(we,aa);we.prototype.message="Deferred has already fired";we.prototype.name="AlreadyCalledError";var te=function(){aa.call(this)};_.D(te,aa);te.prototype.message="Deferred was canceled";te.prototype.name="CanceledError";var Fe=function(a){this.g=_.t.setTimeout((0,_.B)(this.throwError,this),0);this.h=a};
Fe.prototype.throwError=function(){delete Ee[this.g];throw this.h;};var Ee={};var Ge=function(a,b,c,d,e){c=Error.call(this);this.message=c.message;"stack"in c&&(this.stack=c.stack);this.name="ModuleLoadFailure";this.type=a;this.status=b;this.url=d;this.cause=e;this.message=this.toString()};_.y(Ge,Error);Ge.prototype.toString=function(){return He(this)+" ("+(this.status!=void 0?this.status:"?")+")"};
var He=function(a){switch(a.type){case Ge.g.Wf:return"Unauthorized";case Ge.g.Lf:return"Consecutive load failures";case Ge.g.TIMEOUT:return"Timed out";case Ge.g.Uf:return"Out of date module id";case Ge.g.re:return"Init error";default:return"Unknown failure type "+a.type}};Sb.Pa=Ge;Sb.Pa.g={Wf:0,Lf:1,TIMEOUT:2,Uf:3,re:4};var Ie=function(){Td.call(this);this.I=null;this.g={};this.j=[];this.l=[];this.L=[];this.h=[];this.B=[];this.A={};this.O={};this.o=this.D=new Qd([],"");this.qa=null;this.F=new se;this.J=!1;this.G=0;this.X=this.Y=this.T=!1};_.D(Ie,Td);var Je=function(a,b){aa.call(this,"Error loading "+a+": "+b)};_.D(Je,aa);Ie.prototype.rh=function(a){this.J=a};
Ie.prototype.Af=function(a,b){if(!(this instanceof Ie))this.Af(a,b);else if(typeof a==="string"){if(a.startsWith("d$")){a=a.substring(2);for(var c=[],d=0,e=a.indexOf("/"),f=0,g=!1,h=0;;){var k=g?a.substring(f):a.substring(f,e);if(k.length===0)d++,f="sy"+d.toString(36),k=[];else{var m=k.indexOf(":");if(m<0)f=k,k=[];else if(m===k.length-1)f=k.substring(0,m),k=Array(c[h-1]);else{f=k.substring(0,m);k=k.substring(m+1).split(",");m=h;for(var n=0;n<k.length;n++)m-=k[n].length===0?1:Number(k[n]),k[n]=c[m]}m=
0;if(f.length===0)m=1;else if(f.charAt(0)==="+"||f.charAt(0)==="-")m=Number(f);m!==0&&(d+=m,f="sy"+d.toString(36))}c.push(f);Ke(this,f,k);if(g)break;f=e+1;e=a.indexOf("/",f);e===-1&&(g=!0);h++}this.I=c}else if(a.startsWith("p$"))Le(this,a);else{a=a.split("/");c=[];for(d=0;d<a.length;d++){h=a[d].split(":");e=h[0];g=[];if(h[1])for(g=h[1].split(","),h=0;h<g.length;h++)g[h]=c[parseInt(g[h],36)];c.push(e);Ke(this,e,g)}this.I=c}b&&b.length?(ya(this.j,b),this.qa=b[b.length-1]):this.F.h||this.F.g();Object.freeze(this.I);
Me(this)}};
var Le=function(a,b){var c=b.substring(2);for(b=0;b<64;b++)ib["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".charCodeAt(b)]=b;var d={fg:c,Yb:0};kb(d);var e=kb(d),f=kb(d)+1;b=Array(e);var g=Array(e),h=Array(e);f=Array(f);var k=0,m=0,n=d.Yb;d.Yb=d.fg.indexOf("|",d.Yb)+1;for(var p=0;p<e;p++){var q=kb(d),r=q&2,u=q&1;q>>>=2;u?(k+=q>>>1^-(q&1),q="sy"+k.toString(36)):(u=n,n+=q,q=c.substring(u,n));b[p]=q;r&&(f[m++]=q)}f[m]="";d.Yb++;m=e&-2;c=e&1;for(k=0;k<m;k+=2)n=jb(d),h[k]=n&7,h[k+1]=
n>>>3&7;c&&(c=jb(d),h[m]=c&7);d.Yb++;for(m=0;m<e;m++)h[m]===7&&(h[m]=kb(d));d.Yb++;for(c=m=0;c<e;c++){k=h[c];n=k===0?Nd:Array(k);g[c]=n;p=m;for(r=0;r<k;r++)p-=kb(d),n[r]=f[p];f[m]===b[c]&&m++}for(d=0;d<b.length;d++)Ke(a,b[d],g[d]);a.I=b};Ie.prototype.nh=function(a,b){if(this.A[a]){delete this.A[a][b];for(var c in this.A[a])return;delete this.A[a]}};Ie.prototype.Bf=function(a){Ie.P.Bf.call(this,a);Me(this)};Ie.prototype.isActive=function(){return this.j.length>0};
Ie.prototype.Qg=function(){return this.B.length>0};
var Oe=function(a){var b=a.T,c=a.isActive();c!=b&&(Ne(a,c?"active":"idle"),a.T=c);b=a.Qg();b!=a.Y&&(Ne(a,b?"userActive":"userIdle"),a.Y=b)},Ke=function(a,b,c){a.g[b]?(a=a.g[b].h,a!=c&&a.splice.apply(a,[0,a.length].concat(_.Hb(c)))):a.g[b]=new Qd(c,b)},Re=function(a,b,c){var d=[];Ba(b,d);b=[];for(var e={},f=0;f<d.length;f++){var g=d[f],h=a.g[g];if(!h)throw Error("E`"+g);var k=new se;e[g]=k;h.g?k.g(a.Da):(Pe(a,g,h,!!c,k),Qe(a,g)||b.push(g))}b.length>0&&(a.j.length===0?a.N(b):(a.h.push(b),Oe(a)));return e},
Pe=function(a,b,c,d,e){c.l.push(new Pd(e.g,e));Rd(c,function(f){e.F(new Je(b,f))});Qe(a,b)?d&&(_.ua(a.B,b)||a.B.push(b),Oe(a)):d&&(_.ua(a.B,b)||a.B.push(b))};
Ie.prototype.N=function(a,b,c){var d=this;b||(this.G=0);var e=Se(this,a);this.j=e;this.l=this.J?a:_.xa(e);Oe(this);if(e.length!==0){this.L.push.apply(this.L,e);a=this.S;if(!a)throw Error("F");if(Object.keys(this.A).length>0&&!a.J)throw Error("G");c=(0,_.B)(a.I,a,_.xa(e),this.g,{Ji:this.A,Mi:!!c,nf:function(f,g){var h=d.l;f=f!=null?f:void 0;d.G++;var k=_.xa(e);d.l=h;e.forEach(_.Yb(_.wa,d.L),d);f==401?(Te(d,new Sb.Pa(Sb.Pa.g.Wf,f)),d.h.length=0):f==410?(Ue(d,new Sb.Pa(Sb.Pa.g.Uf,f)),Ve(d)):d.G>=3?(Ue(d,
new Sb.Pa(Sb.Pa.g.Lf,f,k,g)),Ve(d)):d.N(d.l,!0,f==8001||!1)},gk:(0,_.B)(this.la,this)});(a=Math.pow(this.G,2)*5E3)?_.t.setTimeout(c,a):c()}};
var Se=function(a,b){b=b.filter(function(e){return a.g[e].g?(_.t.setTimeout(function(){return Error("H`"+e)},0),!1):!0});for(var c=[],d=0;d<b.length;d++)c=c.concat(We(a,b[d]));Ba(c);return!a.J&&c.length>1?(b=c.shift(),a.h=c.map(function(e){return[e]}).concat(a.h),[b]):c},We=function(a,b){var c=Ga(a.L),d=[];c[b]||d.push(b);b=[b];for(var e=0;e<b.length;e++)for(var f=a.g[b[e]].h,g=f.length-1;g>=0;g--){var h=f[g];a.g[h].g||c[h]||(d.push(h),b.push(h))}d.reverse();Ba(d);return d},Me=function(a){if(a.o==
a.D){a.o=null;var b=a.D.onLoad((0,_.B)(a.ug,a));b&&b.length&&Te(a,new Sb.Pa(Sb.Pa.g.re,void 0,void 0,void 0,b[0]));Oe(a)}},ma=function(a){if(a.o){var b=a.o.Wa(),c=[];if(a.A[b]){for(var d=_.z(Object.keys(a.A[b])),e=d.next();!e.done;e=d.next()){e=e.value;var f=a.g[e];f&&!f.g&&(a.nh(b,e),c.push(e))}Re(a,c)}a.Za()||((c=a.g[b].onLoad((0,_.B)(a.ug,a)))&&c.length&&Te(a,new Sb.Pa(Sb.Pa.g.re,void 0,void 0,void 0,c[0])),_.wa(a.B,b),_.wa(a.j,b),a.j.length===0&&Ve(a),a.qa&&b==a.qa&&(a.F.h||a.F.g()),Oe(a),a.o=
null)}},Qe=function(a,b){if(_.ua(a.j,b))return!0;for(var c=0;c<a.h.length;c++)if(_.ua(a.h[c],b))return!0;return!1};Ie.prototype.load=function(a,b){return Re(this,[a],b)[a]};var ka=function(a){var b=_.ca;b.o&&b.o.Wa()==="synthetic_module_overhead"&&(ma(b),delete b.g.synthetic_module_overhead);b.g[a]&&Xe(b,b.g[a].h||[],function(c){c.g=new Od;_.wa(b.j,c.Wa())},function(c){return!c.g});b.o=b.g[a]};Ie.prototype.la=function(){Ue(this,new Sb.Pa(Sb.Pa.g.TIMEOUT));Ve(this)};
var Ue=function(a,b){a.l.length>1?a.h=a.l.map(function(c){return[c]}).concat(a.h):Te(a,b)},Te=function(a,b){var c=a.l;a.j.length=0;for(var d=[],e=0;e<a.h.length;e++){var f=a.h[e].filter(function(k){var m=We(this,k);return _.fc(c,function(n){return _.ua(m,n)})},a);ya(d,f)}for(e=0;e<c.length;e++)_.va(d,c[e]);for(e=0;e<d.length;e++){for(f=0;f<a.h.length;f++)_.wa(a.h[f],d[e]);_.wa(a.B,d[e])}if(e=a.O.error)for(f=0;f<e.length;f++)for(var g=e[f],h=0;h<d.length;h++)g("error",d[h],b);for(d=0;d<c.length;d++)a.g[c[d]]&&
a.g[c[d]].nf(b);a.l.length=0;Oe(a)},Ve=function(a){for(;a.h.length;){var b=a.h.shift().filter(function(c){return!this.g[c].g},a);if(b.length>0){a.N(b);return}}Oe(a)},Ne=function(a,b){a=a.O[b];for(var c=0;a&&c<a.length;c++)a[c](b)},Xe=function(a,b,c,d,e){d=d===void 0?function(){return!0}:d;e=e===void 0?{}:e;b=_.z(b);for(var f=b.next();!f.done;f=b.next()){f=f.value;var g=a.g[f];!e[f]&&d(g)&&(e[f]=!0,Xe(a,g.h||[],c,d,e),c(g))}};
Ie.prototype.dispose=function(){sa(_.Da(this.g),this.D);this.g={};this.j=[];this.l=[];this.B=[];this.h=[];this.O={};this.X=!0};Ie.prototype.Za=function(){return this.X};_.ha=function(){return new Ie};var Ye=[],Ze=function(a){function b(d){d&&ec(d,function(e,f){e[f.id]=!0;return e},c.mk)}var c={mk:{},index:Ye.length,An:a};b(a.ke);b(a.Pn);Ye.push(c);a.ke&&_.dc(a.ke,function(d){var e=d.id;e instanceof bc&&d.module&&(e.g=d.module)})};Ze({ke:[{id:cc,jc:hb,multiple:!0}]});var df;_.$e=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");_.af=function(a){return a?decodeURI(a):a};_.bf=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?_.qc(e):"")}}};
df=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)df(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+_.pc(b)))};var ef={};var ff=new bc("MpJwZc","MpJwZc");_.gf=new bc("UUJqVe","UUJqVe");var hf=new nd,jf=function(a,b,c){_.od.call(this,a,b);this.node=b;this.kind=c};_.y(jf,_.od);_.kf=RegExp("^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)","i");_.lf=function(a,b){b||_.db();this.j=a||null};_.lf.prototype.ha=function(a,b){a=a(b||{},this.j?this.j.g():{});this.h(null,"function"==typeof _.mf&&a instanceof _.mf?a.Nb:null);return String(a)};_.lf.prototype.h=function(){};var nf=function(a){this.h=a;this.j=this.h.g(_.gf)};nf.prototype.g=function(){this.h.Za()||(this.j=this.h.g(_.gf));return this.j?this.j.j():{}};var of=function(a){var b=new nf(a);_.lf.call(this,b,a.get(cc).h);this.l=new _.F;this.o=b};_.y(of,_.lf);of.prototype.g=function(){return this.o.g()};of.prototype.h=function(a,b){_.lf.prototype.h.call(this,a,b);this.l.dispatchEvent(new jf(hf,a,b))};_.pa(ff,of);Ze({ke:[{id:ff,jc:of,multiple:!0}]});var pf=function(a,b){this.defaultValue=a;this.type=b;this.value=a};pf.prototype.get=function(){return this.value};pf.prototype.set=function(a){this.value=a};var qf=function(a){pf.call(this,a,"b")};_.y(qf,pf);qf.prototype.get=function(){return this.value};var rf=function(){this.g={};this.j="";this.h={}};rf.prototype.toString=function(){var a=this.j+sf(this);var b=this.h;var c=[],d;for(d in b)df(d,b[d],c);b=c.join("&");c="";b!=""&&(c="?"+b);return a+c};
var uf=function(a){a=tf(a,"md");return!!a&&a!=="0"},sf=function(a){var b=[],c=function(d){a.g[d]!==void 0&&b.push(d+"="+a.g[d])};uf(a)?(c("md"),c("k"),c("ck"),c("am"),c("rs"),c("gssmodulesetproto"),c("slk"),c("dti")):(c("sdch"),c("k"),c("ck"),c("am"),c("rt"),"d"in a.g||vf(a,"d","0"),c("d"),c("exm"),c("excm"),(a.g.excm||a.g.exm)&&b.push("ed=1"),c("im"),c("dg"),c("sm"),tf(a,"br")!="1"&&tf(a,"br")!="0"||c("br"),c("br-d"),tf(a,"rb")=="1"&&c("rb"),tf(a,"zs")!=="0"&&c("zs"),wf(a)!==""&&c("wt"),c("gssmodulesetproto"),
c("ujg"),c("sp"),c("rs"),c("cb"),c("ee"),c("slk"),c("dti"),c("m"));return b.join("/")},tf=function(a,b){return a.g[b]?a.g[b]:null},vf=function(a,b,c){c?a.g[b]=c:delete a.g[b]},wf=function(a){switch(tf(a,"wt")){case "0":return"0";case "1":return"1";case "2":return"2";default:return""}},zf=function(a){var b=b===void 0?!0:b;var c=xf(a),d=new rf,e=c.match(_.$e)[5];_.jc(yf,function(g){var h=e.match("/"+g+"=([^/]+)");h&&vf(d,g,h[1])});var f="";f=a.indexOf("_/ss/")!=-1?"_/ss/":"_/js/";d.j=a.substr(0,a.indexOf(f)+
f.length);if(!b)return d;(a=c.match(_.$e)[6]||null)&&_.bf(a,function(g,h){d.h[g]=h});return d},xf=function(a){return a.startsWith("https://uberproxy-pen-redirect.corp.google.com/uberproxy/pen?url=")?a.substr(65):a},yf={Fl:"k",Rk:"ck",rl:"m",cl:"exm",al:"excm",Ik:"am",pl:"mm",El:"rt",ll:"d",bl:"ed",Pl:"sv",Sk:"deob",Pk:"cb",Ml:"rs",Gl:"sdch",nl:"im",Tk:"dg",Yk:"br",Xk:"br-d",Zk:"rb",jm:"zs",im:"wt",dl:"ee",Ol:"sm",ql:"md",jl:"gssmodulesetproto",gm:"ujg",fm:"sp",Ll:"slk",Uk:"dti"};_.Af=function(a){_.w.call(this);this.h=a;this.g={}};_.D(_.Af,_.w);var Bf=[];_.Af.prototype.K=function(a,b,c,d){return Cf(this,a,b,c,d)};var Cf=function(a,b,c,d,e,f){Array.isArray(c)||(c&&(Bf[0]=c.toString()),c=Bf);for(var g=0;g<c.length;g++){var h=_.E(b,c[g],d||a.handleEvent,e||!1,f||a.h||a);if(!h)break;a.g[h.key]=h}return a};_.Af.prototype.Cb=function(a,b,c,d){return Df(this,a,b,c,d)};
var Df=function(a,b,c,d,e,f){if(Array.isArray(c))for(var g=0;g<c.length;g++)Df(a,b,c[g],d,e,f);else{b=_.Cd(b,c,d||a.handleEvent,e,f||a.h||a);if(!b)return a;a.g[b.key]=b}return a};_.Af.prototype.bb=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.bb(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.za(d)?!!d.capture:!!d,e=e||this.h||this,c=Dd(c),d=!!d,b=_.sd(a)?a.Qc(b,c,d,e):a?(a=_.Fd(a))?a.Qc(b,c,d,e):null:null,b&&(_.Kd(b),delete this.g[b.key]);return this};
_.Ef=function(a){_.jc(a.g,function(b,c){this.g.hasOwnProperty(c)&&_.Kd(b)},a);a.g={}};_.Af.prototype.M=function(){_.Af.P.M.call(this);_.Ef(this)};_.Af.prototype.handleEvent=function(){throw Error("N");};var Ff,Gf=function(){};_.D(Gf,rb);Gf.prototype.g=function(){return new XMLHttpRequest};Ff=new Gf;_.D(sb,rb);sb.prototype.g=function(){var a=new XMLHttpRequest;if("withCredentials"in a)return a;if(typeof XDomainRequest!="undefined")return new Hf;throw Error("O");};var Hf=function(){this.g=new XDomainRequest;this.readyState=0;this.onreadystatechange=null;this.responseType=this.responseText="";this.status=-1;this.statusText="";this.g.onload=(0,_.B)(this.Jh,this);this.g.onerror=(0,_.B)(this.Nf,this);this.g.onprogress=(0,_.B)(this.xj,this);this.g.ontimeout=(0,_.B)(this.Bj,this)};_.l=Hf.prototype;
_.l.open=function(a,b,c){if(c!=null&&!c)throw Error("P");this.g.open(a,b)};_.l.send=function(a){if(a)if(typeof a=="string")this.g.send(a);else throw Error("Q");else this.g.send()};_.l.abort=function(){this.g.abort()};_.l.setRequestHeader=function(){};_.l.getResponseHeader=function(a){return a.toLowerCase()=="content-type"?this.g.contentType:""};_.l.Jh=function(){this.status=200;this.responseText=this.g.responseText;If(this,4)};_.l.Nf=function(){this.status=500;this.responseText="";If(this,4)};
_.l.Bj=function(){this.Nf()};_.l.xj=function(){this.status=200;If(this,1)};var If=function(a,b){a.readyState=b;if(a.onreadystatechange)a.onreadystatechange()};Hf.prototype.getAllResponseHeaders=function(){return"content-type: "+this.g.contentType};var Kf,Lf;_.Jf=function(a){_.F.call(this);this.headers=new Map;this.J=a||null;this.h=!1;this.g=null;this.o="";this.j=this.F=this.A=this.G=!1;this.B=0;this.l=null;this.L="";this.D=!1};_.D(_.Jf,_.F);Kf=/^https?$/i;Lf=["POST","PUT"];_.Mf=[];_.Jf.prototype.O=function(){this.dispose();_.wa(_.Mf,this)};
_.Jf.prototype.send=function(a,b,c,d){if(this.g)throw Error("S`"+this.o+"`"+a);b=b?b.toUpperCase():"GET";this.o=a;this.G=!1;this.h=!0;this.g=this.J?this.J.g():Ff.g();this.g.onreadystatechange=Ud((0,_.B)(this.I,this));try{this.F=!0,this.g.open(b,String(a),!0),this.F=!1}catch(g){Nf(this);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get==="function"){e=_.z(d.keys());for(var f=e.next();!f.done;f=
e.next())f=f.value,c.set(f,d.get(f))}else throw Error("T`"+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});e=_.t.FormData&&a instanceof _.t.FormData;!_.ua(Lf,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=_.z(c);for(d=b.next();!d.done;d=b.next())c=_.z(d.value),d=c.next().value,c=c.next().value,this.g.setRequestHeader(d,c);this.L&&(this.g.responseType=this.L);"withCredentials"in this.g&&this.g.withCredentials!==this.D&&
(this.g.withCredentials=this.D);try{this.l&&(clearTimeout(this.l),this.l=null),this.B>0&&(this.l=setTimeout(this.S.bind(this),this.B)),this.A=!0,this.g.send(a),this.A=!1}catch(g){Nf(this)}};_.Jf.prototype.S=function(){typeof Ob!="undefined"&&this.g&&(this.dispatchEvent("timeout"),this.abort(8))};var Nf=function(a){a.h=!1;a.g&&(a.j=!0,a.g.abort(),a.j=!1);Of(a);Pf(a)},Of=function(a){a.G||(a.G=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};
_.Jf.prototype.abort=function(){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Pf(this))};_.Jf.prototype.M=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Pf(this,!0));_.Jf.P.M.call(this)};_.Jf.prototype.I=function(){this.Za()||(this.F||this.A||this.j?Qf(this):this.N())};_.Jf.prototype.N=function(){Qf(this)};
var Qf=function(a){if(a.h&&typeof Ob!="undefined")if(a.A&&(a.g?a.g.readyState:0)==4)setTimeout(a.I.bind(a),0);else if(a.dispatchEvent("readystatechange"),(a.g?a.g.readyState:0)==4){a.h=!1;try{_.Rf(a)?(a.dispatchEvent("complete"),a.dispatchEvent("success")):Of(a)}finally{Pf(a)}}},Pf=function(a,b){if(a.g){a.l&&(clearTimeout(a.l),a.l=null);var c=a.g;a.g=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}};_.Jf.prototype.isActive=function(){return!!this.g};
_.Rf=function(a){var b=_.Sf(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}if(!c){if(b=b===0)a=String(a.o).match(_.$e)[1]||null,!a&&_.t.self&&_.t.self.location&&(a=_.t.self.location.protocol.slice(0,-1)),b=!Kf.test(a?a.toLowerCase():"");c=b}return c};_.Sf=function(a){try{return(a.g?a.g.readyState:0)>2?a.g.status:-1}catch(b){return-1}};_.Tf=function(a){try{return a.g?a.g.responseText:""}catch(b){return""}};var Vf=function(a){_.w.call(this);this.F=a;this.B=zf(a);this.l=this.o=null;this.J=!0;this.h=new _.Af(this);this.L=[];this.A=new Set;this.g=[];this.O=new Uf;this.j=[];this.D=!1;a=(0,_.B)(this.G,this);ef.version=a};_.y(Vf,_.w);var Wf=function(a,b){a.g.length&&Ce(b,a.g[a.g.length-1]);a.g.push(b);ze(b,function(){_.wa(this.g,b)},a)};Vf.prototype.I=function(a,b,c){var d=c===void 0?{}:c;var e=d.Ji;c=d.Mi;var f=d.nf;d=d.gk;a=Xf(this,a,b,e,c);Yf(this,a,f,d,c)};
var Xf=function(a,b,c,d,e){d=d===void 0?{}:d;var f=[];Zf(a,b,c,d,e===void 0?!1:e,function(g){f.push(g.Wa())});return f},Zf=function(a,b,c,d,e,f,g){g=g===void 0?{}:g;b=_.z(b);for(var h=b.next();!h.done;h=b.next()){var k=h.value;h=c[k];!e&&(a.A.has(k)||h.g)||g[k]||(g[k]=!0,k=d[k]?Object.keys(d[k]):[],Zf(a,h.h.concat(k),c,d,e,f,g),f(h))}},Yf=function(a,b,c,d,e){e=e===void 0?!1:e;var f=[],g=new se;b=[b];for(var h=function(p,q){for(var r=[],u=0,A=Math.floor(p.length/q)+1,C=0;C<q;C++){var K=(C+1)*A;r.push(p.slice(u,
K));u=K}return r},k=b.shift();k;){var m=$f(a,k,!!e,!0);if(m.length<=2E3){if(k=ag(a,k,e))f.push(k),Ce(g,k.g)}else b=h(k,Math.ceil(m.length/2E3)).concat(b);k=b.shift()}var n=new se;Wf(a,n);ze(n,function(){return bg(a,f,c,d)});Ae(n,function(){var p=new cg;p.j=!0;p.h=-1;bg(this,[p],c,d)},a);ze(g,function(){return n.g()});g.g()},ag=function(a,b,c){var d=$f(a,b,!(c===void 0||!c));a.L.push(d);b=_.z(b);for(c=b.next();!c.done;c=b.next())a.A.add(c.value);if(a.D)a=_.fd(document,"SCRIPT"),_.Ta(a,_.ub(d)),a.type=
"text/javascript",a.async=!1,document.body.appendChild(a);else{var e=new cg,f=new _.Jf(a.j.length>0?new sb:void 0);a.h.K(f,"success",(0,_.B)(e.B,e,f));a.h.K(f,"error",(0,_.B)(e.A,e,f));a.h.K(f,"timeout",(0,_.B)(e.D,e));Cf(a.h,f,"ready",f.dispose,!1,f);f.B=3E4;dg(a.O,function(){f.send(d);return e.g});return e}return null},bg=function(a,b,c,d){for(var e=!1,f,g=!1,h=0;h<b.length;h++){var k=b[h];if(!f&&k.j){e=!0;f=k.h;break}else k.l&&(g=!0)}h=_.xa(a.g);(e||g)&&f!=-1&&(a.g.length=0);if(e)c&&c(f);else if(g)d&&
d();else for(a=0;a<b.length;a++)d=b[a],eg(d.o,d.Ma)||c&&c(8001);(e||g)&&f!=-1&&_.dc(h,function(m){m.cancel()})};Vf.prototype.M=function(){this.h.dispose();delete ef.version;_.w.prototype.M.call(this)};Vf.prototype.G=function(){return tf(this.B,"k")};
var $f=function(a,b,c,d){d=d===void 0?!1:d;var e=_.af(a.F.match(_.$e)[3]||null);if(a.j.length>0&&!_.ua(a.j,e)&&e!=null&&window.location.hostname!=e)throw Error("W`"+e);var f=a.B;e=new rf;e.g=Object.assign({},f.g);e.j=f.j;e.h=Object.assign({},f.h);delete e.g.m;delete e.g.exm;delete e.g.ed;vf(e,"m",b.join(","));a.o&&(vf(e,"ck",a.o),a.l&&vf(e,"rs",a.l));vf(e,"d","0");c&&(a=_.rc(),e.h.zx=a);a=e.toString();d&&a.lastIndexOf("/",0)==0&&(e=document.location.href.match(_.$e),d=e[1],b=e[2],c=e[3],e=e[4],f=
"",d&&(f+=d+":"),c&&(f+="//",b&&(f+=b+"@"),f+=c,e&&(f+=":"+e)),a=f+a);return a},eg=function(a,b){var c="";if(a.length>1&&a.charAt(a.length-1)==="\n"){var d=a.lastIndexOf("\n",a.length-2);d>=0&&(c=a.substring(d+1,a.length-1))}d=c.length-11;if(d>=0&&c.indexOf("Google Inc.",d)==d||c.lastIndexOf("//# sourceMappingURL=",0)==0)try{c=window;a=a+"\r\n//# sourceURL="+b;a=_.tb(a);var e=_.Ra(a);var f=_.Sa(e);c.eval(f)===f&&c.eval(f.toString())}catch(g){return!1}else return!1;return!0},fg=function(a){var b=_.af(a.match(_.$e)[5]||
null)||"";b=_.af(xf(b).match(_.$e)[5]||null);return(b===null?0:RegExp("(/_/js/)|(/_/ss/)","g").test(b)&&/\/k=/.test(b))?a:null},cg=function(){this.g=new se;this.Ma=this.o="";this.j=!1;this.h=0;this.l=!1};cg.prototype.B=function(a){this.o=_.Tf(a);this.Ma=String(a.o);this.g.g()};cg.prototype.A=function(a){this.j=!0;this.h=_.Sf(a);this.g.g()};cg.prototype.D=function(){this.l=!0;this.g.g()};
var Uf=function(){this.g=0;this.h=[]},dg=function(a,b){a.h.push(b);gg(a)},gg=function(a){for(;a.g<5&&a.h.length;)hg(a,a.h.shift())},hg=function(a,b){a.g++;ze(b(),function(){this.g--;gg(this)},a)};var ig=new qf(!1),jg=document.location.href;
Ze({flags:{dml:ig},initialize:function(a){var b=ig.get(),c="",d="";window&&window._F_cssRowKey&&(c=window._F_cssRowKey,window._F_combinedSignature&&(d=window._F_combinedSignature));if(c&&typeof window._F_installCss!=="function")throw Error("U");var e,f=_.t._F_jsUrl;f&&(e=fg(f));!e&&(f=document.getElementById("base-js"))&&(e=f.src?f.src:f.getAttribute("href"),e=fg(e));e||(e=fg(jg));e||(e=document.getElementsByTagName("script"),e=fg(e[e.length-1].src));if(!e)throw Error("V");e=new Vf(e);c&&(e.o=c);
d&&(e.l=d);e.D=b;b=ja();b.S=e;b.rh(!0);b=ja();b.Bf(a);a.j(b)}});
_._ModuleManager_initialize=function(a,b){if(!_.ca){if(!_.ha)return;_.ia()}_.ca.Af(a,b)};
_._ModuleManager_initialize('b/n73qwf/UUJqVe/MpJwZc/sy0/el_conf:4/el_main_css/sy2/sy3:7/sy4:8/sy5:7/el_main:4,6,9,a/corsproxy/website_error/navigationui:8/phishing_protection:9/_stam:a',['sy0','el_conf']);
}catch(e){_._DumpException(e)}
try{
_.G={};MSG_TRANSLATE="Traduire";_.G[0]=MSG_TRANSLATE;MSG_CANCEL="Annuler";_.G[1]=MSG_CANCEL;MSG_CLOSE="Fermer";_.G[2]=MSG_CLOSE;MSGFUNC_PAGE_TRANSLATED_TO=function(a){return"Google a traduit cette page automatiquement en\u00a0: "+a};_.G[3]=MSGFUNC_PAGE_TRANSLATED_TO;MSGFUNC_TRANSLATED_TO=function(a){return"Traduit en\u00a0: "+a};_.G[4]=MSGFUNC_TRANSLATED_TO;MSG_GENERAL_ERROR="Erreur\u00a0: Le serveur n'a pas pu ex\u00e9cuter votre requ\u00eate. Veuillez r\u00e9essayer ult\u00e9rieurement.";
_.G[5]=MSG_GENERAL_ERROR;MSG_LEARN_MORE="En savoir plus";_.G[6]=MSG_LEARN_MORE;MSGFUNC_POWERED_BY=function(a){return"Fourni par\u00a0"+a};_.G[7]=MSGFUNC_POWERED_BY;MSG_TRANSLATE_PRODUCT_NAME="Traduction";_.G[8]=MSG_TRANSLATE_PRODUCT_NAME;MSG_TRANSLATION_IN_PROGRESS="Traduction en cours";_.G[9]=MSG_TRANSLATION_IN_PROGRESS;MSGFUNC_TRANSLATE_PAGE_TO=function(a){return"Traduire cette page en "+a+" avec Google\u00a0Traduction\u00a0?"};_.G[10]=MSGFUNC_TRANSLATE_PAGE_TO;
MSGFUNC_VIEW_PAGE_IN=function(a){return"Afficher cette page en\u00a0: "+a};_.G[11]=MSGFUNC_VIEW_PAGE_IN;MSG_RESTORE="Afficher l'original";_.G[12]=MSG_RESTORE;MSG_SSL_INFO_LOCAL_FILE="Le contenu de ce fichier local sera envoy\u00e9 \u00e0 Google pour traduction via une connexion s\u00e9curis\u00e9e.";_.G[13]=MSG_SSL_INFO_LOCAL_FILE;MSG_SSL_INFO_SECURE_PAGE="Le contenu de cette page s\u00e9curis\u00e9e sera envoy\u00e9 \u00e0 Google pour traduction via une connexion s\u00e9curis\u00e9e.";_.G[14]=MSG_SSL_INFO_SECURE_PAGE;
MSG_SSL_INFO_INTRANET_PAGE="Le contenu de cette page intranet sera envoy\u00e9 \u00e0 Google pour traduction via une connexion s\u00e9curis\u00e9e.";_.G[15]=MSG_SSL_INFO_INTRANET_PAGE;MSG_SELECT_LANGUAGE="S\u00e9lectionner une langue";_.G[16]=MSG_SELECT_LANGUAGE;MSGFUNC_TURN_OFF_TRANSLATION=function(a){return"D\u00e9sactiver la traduction ("+a+")"};_.G[17]=MSGFUNC_TURN_OFF_TRANSLATION;MSGFUNC_TURN_OFF_FOR=function(a){return"D\u00e9sactiver pour\u00a0: "+a};_.G[18]=MSGFUNC_TURN_OFF_FOR;
MSG_ALWAYS_HIDE_AUTO_POPUP_BANNER="Toujours masquer";_.G[19]=MSG_ALWAYS_HIDE_AUTO_POPUP_BANNER;MSG_ORIGINAL_TEXT="Texte original\u00a0:";_.G[20]=MSG_ORIGINAL_TEXT;MSG_FILL_SUGGESTION="Proposer une meilleure traduction";_.G[21]=MSG_FILL_SUGGESTION;MSG_SUBMIT_SUGGESTION="Envoyer";_.G[22]=MSG_SUBMIT_SUGGESTION;MSG_SHOW_TRANSLATE_ALL="Tout traduire";_.G[23]=MSG_SHOW_TRANSLATE_ALL;MSG_SHOW_RESTORE_ALL="Tout restaurer";_.G[24]=MSG_SHOW_RESTORE_ALL;MSG_SHOW_CANCEL_ALL="Tout annuler";_.G[25]=MSG_SHOW_CANCEL_ALL;
MSG_TRANSLATE_TO_MY_LANGUAGE="Traduire les sections dans ma langue";_.G[26]=MSG_TRANSLATE_TO_MY_LANGUAGE;MSGFUNC_TRANSLATE_EVERYTHING_TO=function(a){return"Tout traduire en "+a};_.G[27]=MSGFUNC_TRANSLATE_EVERYTHING_TO;MSG_SHOW_ORIGINAL_LANGUAGES="Afficher les versions originales";_.G[28]=MSG_SHOW_ORIGINAL_LANGUAGES;MSG_OPTIONS="Options";_.G[29]=MSG_OPTIONS;MSG_TURN_OFF_TRANSLATION_FOR_THIS_SITE="D\u00e9sactiver la traduction pour ce site";_.G[30]=MSG_TURN_OFF_TRANSLATION_FOR_THIS_SITE;_.G[31]=null;
MSG_ALT_SUGGESTION="Afficher d'autres traductions";_.G[32]=MSG_ALT_SUGGESTION;MSG_ALT_ACTIVITY_HELPER_TEXT="Cliquez sur les termes ci-dessus pour obtenir des traductions alternatives.";_.G[33]=MSG_ALT_ACTIVITY_HELPER_TEXT;MSG_USE_ALTERNATIVES="Utiliser";_.G[34]=MSG_USE_ALTERNATIVES;MSG_DRAG_TIP="Appuyez sur la touche Maj pour faire glisser et r\u00e9organiser";_.G[35]=MSG_DRAG_TIP;MSG_CLICK_FOR_ALT="Cliquez ici pour voir d'autres traductions";_.G[36]=MSG_CLICK_FOR_ALT;MSG_DRAG_INSTUCTIONS="Maintenez la touche Maj enfonc\u00e9e, cliquez sur les termes ci-dessus et faites-les glisser pour les r\u00e9organiser.";
_.G[37]=MSG_DRAG_INSTUCTIONS;MSG_SUGGESTION_SUBMITTED="Merci de votre contribution \u00e0 Google Traduction.";_.G[38]=MSG_SUGGESTION_SUBMITTED;MSG_MANAGE_TRANSLATION_FOR_THIS_SITE="G\u00e9rer la traduction pour ce site";_.G[39]=MSG_MANAGE_TRANSLATION_FOR_THIS_SITE;MSG_ALT_AND_CONTRIBUTE_ACTIVITY_HELPER_TEXT="Cliquez sur un mot pour obtenir d'autres traductions ou double-cliquez sur celui-ci pour le modifier directement.";_.G[40]=MSG_ALT_AND_CONTRIBUTE_ACTIVITY_HELPER_TEXT;
MSG_ORIGINAL_TEXT_NO_COLON="Texte d'origine";_.G[41]=MSG_ORIGINAL_TEXT_NO_COLON;_.G[42]="Traduction";_.G[43]="Traduire";_.G[44]="Votre correction a bien \u00e9t\u00e9 soumise.";MSG_LANGUAGE_UNSUPPORTED="Erreur\u00a0: la langue de la page Web n'est pas disponible.";_.G[45]=MSG_LANGUAGE_UNSUPPORTED;MSG_LANGUAGE_TRANSLATE_WIDGET="Widget de traduction";_.G[46]=MSG_LANGUAGE_TRANSLATE_WIDGET;MSG_RATE_THIS_TRANSLATION="\u00c9valuez cette traduction";_.G[47]=MSG_RATE_THIS_TRANSLATION;
MSG_FEEDBACK_USAGE_FOR_IMPROVEMENT="Votre avis nous aidera \u00e0 am\u00e9liorer Google\u00a0Traduction";_.G[48]=MSG_FEEDBACK_USAGE_FOR_IMPROVEMENT;MSG_FEEDBACK_SATISFIED_LABEL="Bonne traduction";_.G[49]=MSG_FEEDBACK_SATISFIED_LABEL;MSG_FEEDBACK_DISSATISFIED_LABEL="Mauvaise traduction";_.G[50]=MSG_FEEDBACK_DISSATISFIED_LABEL;MSG_TRANSLATION_NO_COLON="Traduction";_.G[51]=MSG_TRANSLATION_NO_COLON;
}catch(e){_._DumpException(e)}
try{
_.la("el_conf");
var lg;_._exportVersion=function(a){_.$b("google.translate.v",a)};_._getCallbackFunction=function(a){return _.Pb(a)};_._exportMessages=function(){_.$b("google.translate.m",_.G)};lg=function(a){var b=document.getElementsByTagName("head")[0];b||(b=document.body.parentNode.appendChild(document.createElement("head")));b.appendChild(a)};_._loadJs=function(a){var b=_.fd(document,"SCRIPT");b.type="text/javascript";b.charset="UTF-8";_.Ta(b,_.ub(a));lg(b)};
_._loadCss=function(a){var b=document.createElement("link");b.type="text/css";b.rel="stylesheet";b.charset="UTF-8";b.href=a;lg(b)};_._isNS=function(a){a=a.split(".");for(var b=window,c=0;c<a.length;++c)if(!(b=b[a[c]]))return!1;return!0};_._setupNS=function(a){a=a.split(".");for(var b=window,c=0;c<a.length;++c)b.hasOwnProperty?b.hasOwnProperty(a[c])?b=b[a[c]]:b=b[a[c]]={}:b=b[a[c]]||(b[a[c]]={});return b};_.$b("_exportVersion",_._exportVersion);_.$b("_getCallbackFunction",_._getCallbackFunction);
_.$b("_exportMessages",_._exportMessages);_.$b("_loadJs",_._loadJs);_.$b("_loadCss",_._loadCss);_.$b("_isNS",_._isNS);_.$b("_setupNS",_._setupNS);window.addEventListener&&typeof document.readyState=="undefined"&&window.addEventListener("DOMContentLoaded",function(){document.readyState="complete"},!1);
_.na();
}catch(e){_._DumpException(e)}
}).call(this,this.default_tr);
// Google Inc.

//# sourceURL=/_/translate_http/_/js/k=translate_http.tr.fr.hxDB59J29DM.O/am=gMA/d=1/rs=AN8SPfqPVQmsxzYhYXQg3oF3jQbzzX8JMg/m=el_conf
    // Configure Constants
    (function(){
      let gtConstEvalStartTime = new Date();
      if (_isNS('google.translate.Element')){return}

      (function(){
        const c=_setupNS('google.translate._const');

        c._cest = gtConstEvalStartTime;
        gtConstEvalStartTime = undefined; // hide this eval start time constant
  c._cl='fr'; c._cuc='gtElInit'; c._cef='\x5b0,1,0\x5d'; c._cac='wt'; c._cam='lib'; c._cenv='prod'; c._cll='INFO'; c._ctkk='488676.3876608372'; const h='translate.googleapis.com'; const oph='translate-pa.googleapis.com'; const s='https'+'://'; c._pah=h; c._pas=s; const b=s+'translate.googleapis.com'; const staticPath = '/translate_static/'; c._pci=b+staticPath+'img/te_ctrl3.gif'; c._pmi=b+staticPath+'img/mini_google.png'; c._pbi=b+staticPath+'img/te_bk.gif'; c._pli=b+staticPath+'img/loading.gif'; c._ps='https:\/\/www.gstatic.com\/_\/translate_http\/_\/ss\/k\x3dtranslate_http.tr.MOhKAPSS1-s.L.F4.O\/am\x3dgMA\/d\x3d0\/rs\x3dAN8SPfoT-dXTdPTRYQlo91QfUWPUz7dIvA\/m\x3del_main_css'; c._plla=oph+'\/v1\/supportedLanguages'; c._puh='translate.google.com';c._cnal={};_loadCss(c._ps); _loadJs('https:\/\/translate.googleapis.com\/_\/translate_http\/_\/js\/k\x3dtranslate_http.tr.fr.hxDB59J29DM.O\/am\x3dAAAE\/d\x3d1\/exm\x3del_conf\/ed\x3d1\/rs\x3dAN8SPfoHxQfKjhXXT4cZViyB13zy6ckmrg\/m\x3del_main');_exportMessages(); _exportVersion('TE_20250923');
        })();
      })();
  </script>
 
<p id="a11y-speak-intro-text" class="a11y-speak-intro-text" style="position: absolute;margin: -1px;padding: 0;height: 1px;width: 1px;overflow: hidden;clip: rect(1px, 1px, 1px, 1px);-webkit-clip-path: inset(50%);clip-path: inset(50%);border: 0;word-wrap: normal !important;" hidden="hidden"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Notifications</font></font></p><div id="a11y-speak-assertive" class="a11y-speak-region" style="position: absolute;margin: -1px;padding: 0;height: 1px;width: 1px;overflow: hidden;clip: rect(1px, 1px, 1px, 1px);-webkit-clip-path: inset(50%);clip-path: inset(50%);border: 0;word-wrap: normal !important;" aria-live="assertive" aria-relevant="additions text" aria-atomic="true"></div><div id="a11y-speak-polite" class="a11y-speak-region" style="position: absolute;margin: -1px;padding: 0;height: 1px;width: 1px;overflow: hidden;clip: rect(1px, 1px, 1px, 1px);-webkit-clip-path: inset(50%);clip-path: inset(50%);border: 0;word-wrap: normal !important;" aria-live="polite" aria-relevant="additions text" aria-atomic="true"></div><style>
    :root {
      /* System tokens */
      --md-sys-typescale-body-medium: system-ui 16px/24px;
      --md-sys-typescale-headline-small: system-ui 24px/32px;

      /* Component tokens */
      --md-dialog-container-shape: 8px;
      --md-dialog-container-color: #ffffff;
      --md-filled-button-container-shape: 4px;
      --md-filled-button-container-color: #1a73e8;
      --md-text-button-container-shape: 4px;
      --md-text-button-container-color: #1a73e8;
      --md-text-button-label-text-color: #1a73e8;
      --md-text-button-focus-label-text-color: #1a73e8;
      --md-text-button-hover-label-text-color: #1a73e8;
      --md-text-button-pressed-label-text-color: #1a73e8;
    }</style><div id="goog-gt-tt" class="VIpgJd-suEOdc VIpgJd-yAWNEb-L7lbkb skiptranslate" style="border-radius: 12px; margin: 0px 0px 0px -23px; padding: 0px; font-family: &quot;Google Sans&quot;, Arial, sans-serif; visibility: hidden; left: 426px; top: 16537px; width: 536px; display: none;" data-id="84" lang="fr"><div id="goog-gt-vt" class="VIpgJd-yAWNEb-hvhgNd"><div class="VIpgJd-yAWNEb-hvhgNd-Ud7fr"><img src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/24px.svg" width="24" height="24" alt=""><div class=" VIpgJd-yAWNEb-hvhgNd-IuizWc-i3jM8c " dir="ltr">Texte d'origine</div></div><div class="VIpgJd-yAWNEb-hvhgNd-k77Iif"><div id="goog-gt-original-text" class="VIpgJd-yAWNEb-nVMfcd-fmcmS VIpgJd-yAWNEb-hvhgNd-axAV1" lang="en">This
 approach can be extended to other domains where multiple perspectives 
and structured decision-making are crucial, such as risk assessment, 
policy analysis, or strategic planning.</div></div><div class="VIpgJd-yAWNEb-hvhgNd-N7Eqid ltr"><div class="VIpgJd-yAWNEb-hvhgNd-N7Eqid-B7I4Od ltr" dir="ltr"><div class="VIpgJd-yAWNEb-hvhgNd-UTujCb">Évaluez cette traduction</div><div class="VIpgJd-yAWNEb-hvhgNd-eO9mKe">Votre avis nous aidera à améliorer Google&nbsp;Traduction</div></div><div class="VIpgJd-yAWNEb-hvhgNd-xgov5 ltr"><button id="goog-gt-thumbUpButton" type="button" class="VIpgJd-yAWNEb-hvhgNd-bgm6sf" title="Bonne traduction" aria-label="Bonne traduction" aria-pressed="false"><span id="goog-gt-thumbUpIcon"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M21 7h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 0S7.08 6.85 7 7H2v13h16c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73V9c0-1.1-.9-2-2-2zM7 18H4V9h3v9zm14-7l-3 7H9V8l4.34-4.34L12 9h9v2z"></path></svg></span><span id="goog-gt-thumbUpIconFilled" style="display: none;"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M21 7h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 0S7.08 6.85 7 7v13h11c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73V9c0-1.1-.9-2-2-2zM5 7H1v13h4V7z"></path></svg></span></button><button id="goog-gt-thumbDownButton" type="button" class="VIpgJd-yAWNEb-hvhgNd-bgm6sf" title="Mauvaise traduction" aria-label="Mauvaise traduction" aria-pressed="false"><span id="goog-gt-thumbDownIcon"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M3 17h6.31l-.95 4.57-.03.32c0 .*********** 1.06L9.83 24s7.09-6.85 7.17-7h5V4H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2zM17 6h3v9h-3V6zM3 13l3-7h9v10l-4.34 4.34L12 15H3v-2z"></path></svg></span><span id="goog-gt-thumbDownIconFilled" style="display: none;"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M3 17h6.31l-.95 4.57-.03.32c0 .*********** 1.06L9.83 24s7.09-6.85 7.17-7V4H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2zm16 0h4V4h-4v13z"></path></svg></span></button></div></div><div id="goog-gt-votingHiddenPane" class="VIpgJd-yAWNEb-hvhgNd-aXYTce"><form id="goog-gt-votingForm" action="//translate.googleapis.com/translate_voting?client=wt_lib" method="post" target="votingFrame" class="VIpgJd-yAWNEb-hvhgNd-aXYTce"><input type="text" name="sl" id="goog-gt-votingInputSrcLang" value="en"><input type="text" name="tl" id="goog-gt-votingInputTrgLang" value="fr"><input type="text" name="query" id="goog-gt-votingInputSrcText" value="This approach can be extended to other domains where multiple perspectives and structured decision-making are crucial, such as risk assessment, policy analysis, or strategic planning."><input type="text" name="gtrans" id="goog-gt-votingInputTrgText" value="Cette approche peut être étendue à d’autres domaines où des perspectives multiples et une prise de décision structurée sont cruciales, comme l’évaluation des risques, l’analyse des politiques ou la planification stratégique."><input type="text" name="vote" id="goog-gt-votingInputVote" value="0"></form><iframe name="votingFrame" frameborder="0"></iframe></div></div></div><md-dialog type="alert"><div slot="headline"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Formulaire non accepté</font></font></div><form slot="content" id="KucnDc" method="dialog"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Pour des raisons de sécurité lorsque vous utilisez Google Traduction, n'envoyez pas d'informations dans ce type de formulaire.</font></font></form><div slot="actions"><md-text-button form="KucnDc" value="cancel"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">D'ACCORD</font></font></md-text-button><md-filled-button form="KucnDc" trailing-icon="" has-icon="" value="submit"><font style="vertical-align: inherit;" dir="auto"><font style="vertical-align: inherit;" dir="auto">Accéder à l'URL d'origine</font></font><md-icon slot="icon" translate="no" aria-hidden="true">open_in_new</md-icon></md-filled-button></div></md-dialog><script src="Cr%C3%A9ation%20d'agents%20d'IA%20coordonn%C3%A9s%20avec%20LangGraph%20_%20tutoriel%20pratique%20_%20CodeCut_fichiers/dialog.min.js"></script></body></html>
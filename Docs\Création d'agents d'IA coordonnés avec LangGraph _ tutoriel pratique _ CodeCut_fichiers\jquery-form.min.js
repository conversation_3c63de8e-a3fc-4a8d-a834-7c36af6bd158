!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):e("undefined"!=typeof jQuery?jQuery:window.Zepto)}(function(L){"use strict";var h={},t=(h.fileapi=void 0!==L("<input type='file'/>").get(0).files,h.formdata=void 0!==window.FormData,!!L.fn.prop);function a(e){var t=e.data;e.isDefaultPrevented()||(e.preventDefault(),L(e.target).ajaxSubmit(t))}function r(e){var t=e.target,a=L(t);if(!a.is("[type=submit],[type=image]")){var r=a.closest("[type=submit]");if(0===r.length)return;t=r[0]}var n=this;"image"==(n.clk=t).type&&(void 0!==e.offsetX?(n.clk_x=e.offsetX,n.clk_y=e.offsetY):"function"==typeof L.fn.offset?(r=a.offset(),n.clk_x=e.pageX-r.left,n.clk_y=e.pageY-r.top):(n.clk_x=e.pageX-t.offsetLeft,n.clk_y=e.pageY-t.offsetTop)),setTimeout(function(){n.clk=n.clk_x=n.clk_y=null},100)}function M(){var e;L.fn.ajaxSubmit.debug&&(e="[jquery.form] "+Array.prototype.join.call(arguments,""),window.console&&window.console.log?window.console.log(e):window.opera&&window.opera.postError&&window.opera.postError(e))}L.fn.attr2=function(){var e;return t&&((e=this.prop.apply(this,arguments))&&e.jquery||"string"==typeof e)?e:this.attr.apply(this,arguments)},L.fn.ajaxSubmit=function(c){if(this.length){var A,E=this,e=("function"==typeof c?c={success:c}:void 0===c&&(c={}),A=c.type||this.attr2("method"),e=(e=(e="string"==typeof(e=c.url||this.attr2("action"))?L.trim(e):"")||window.location.href||"")&&(e.match(/^([^#]+)/)||[])[1],c=L.extend(!0,{url:e,success:L.ajaxSettings.success,type:A||L.ajaxSettings.type,iframeSrc:/^https/i.test(window.location.href||"")?"javascript:false":"about:blank"},c),{});if(this.trigger("form-pre-serialize",[this,c,e]),e.veto)M("ajaxSubmit: submit vetoed via form-pre-serialize trigger");else if(c.beforeSerialize&&!1===c.beforeSerialize(this,c))M("ajaxSubmit: submit aborted via beforeSerialize callback");else{var t=c.traditional,n=(void 0===t&&(t=L.ajaxSettings.traditional),[]),a=this.formToArray(c.semantic,n);if(c.data&&(c.extraData=c.data,u=L.param(c.data,t)),c.beforeSubmit&&!1===c.beforeSubmit(a,this,c))M("ajaxSubmit: submit aborted via beforeSubmit callback");else if(this.trigger("form-submit-validate",[a,this,c,e]),e.veto)M("ajaxSubmit: submit vetoed via form-submit-validate trigger");else{var r,o,i,e=L.param(a,t),s=(u&&(e=e?e+"&"+u:u),"GET"==c.type.toUpperCase()?(c.url+=(0<=c.url.indexOf("?")?"&":"?")+e,c.data=null):c.data=e,[]);c.resetForm&&s.push(function(){E.resetForm()}),c.clearForm&&s.push(function(){E.clearForm(c.includeHidden)}),!c.dataType&&c.target?(r=c.success||function(){},s.push(function(e){var t=c.replaceTarget?"replaceWith":"html";L(c.target)[t](e).each(r,arguments)})):c.success&&s.push(c.success),c.success=function(e,t,a){for(var r=c.context||this,n=0,o=s.length;n<o;n++)s[n].apply(r,[e,t,a||E,E])},c.error&&(o=c.error,c.error=function(e,t,a){var r=c.context||this;o.apply(r,[e,t,a,E])}),c.complete&&(i=c.complete,c.complete=function(e,t){var a=c.context||this;i.apply(a,[e,t,E])});var l,t=0<L("input[type=file]:enabled",this).filter(function(){return""!==L(this).val()}).length,u="multipart/form-data",e=E.attr("enctype")==u||E.attr("encoding")==u,u=h.fileapi&&h.formdata;M("fileAPI :"+u),!1!==c.iframe&&(c.iframe||(t||e)&&!u)?c.closeKeepAlive?L.get(c.closeKeepAlive,function(){l=m(a)}):l=m(a):l=(t||e)&&u?function(e){for(var a=new FormData,t=0;t<e.length;t++)a.append(e[t].name,e[t].value);if(c.extraData){var r=function(e){var t,a,r=L.param(e,c.traditional).split("&"),n=r.length,o=[];for(t=0;t<n;t++)r[t]=r[t].replace(/\+/g," "),a=r[t].split("="),o.push([decodeURIComponent(a[0]),decodeURIComponent(a[1])]);return o}(c.extraData);for(t=0;t<r.length;t++)r[t]&&a.append(r[t][0],r[t][1])}c.data=null;var n=L.extend(!0,{},L.ajaxSettings,c,{contentType:!1,processData:!1,cache:!1,type:A||"POST"});c.uploadProgress&&(n.xhr=function(){var e=L.ajaxSettings.xhr();return e.upload&&e.upload.addEventListener("progress",function(e){var t=0,a=e.loaded||e.position,r=e.total;e.lengthComputable&&(t=Math.ceil(a/r*100)),c.uploadProgress(e,a,r,t)},!1),e});n.data=null;var o=n.beforeSend;return n.beforeSend=function(e,t){c.formData?t.data=c.formData:t.data=a,o&&o.call(this,e,t)},L.ajax(n)}(a):L.ajax(c),E.removeData("jqxhr").data("jqxhr",l);for(var f=0;f<n.length;f++)n[f]=null;this.trigger("form-submit-notify",[this,c])}}}else M("ajaxSubmit: skipping submit process - no element selected");return this;function m(e){var t,l,u,o,f,m,p,d,h,g,v,a,x,b,y,T,j,i,w,s=E[0],S=L.Deferred();if(S.abort=function(e){p.abort(e)},e)for(t=0;t<n.length;t++)L(n[t]).prop("disabled",!1);return(l=L.extend(!0,{},L.ajaxSettings,c)).context=l.context||l,o="jqFormIO"+(new Date).getTime(),l.iframeTarget?(a=(f=L(l.iframeTarget)).attr2("name"))?o=a:f.attr2("name",o):(f=L('<iframe name="'+o+'" src="'+l.iframeSrc+'" />')).css({position:"absolute",top:"-1000px",left:"-1000px"}),m=f[0],p={aborted:0,responseText:null,responseXML:null,status:0,statusText:"n/a",getAllResponseHeaders:function(){},getResponseHeader:function(){},setRequestHeader:function(){},abort:function(e){var t="timeout"===e?"timeout":"aborted";M("aborting upload... "+t),this.aborted=1;try{m.contentWindow.document.execCommand&&m.contentWindow.document.execCommand("Stop")}catch(e){}f.attr("src",l.iframeSrc),p.error=t,l.error&&l.error.call(l.context,p,t,e),u&&L.event.trigger("ajaxError",[p,l,t]),l.complete&&l.complete.call(l.context,p,t)}},(u=l.global)&&0==L.active++&&L.event.trigger("ajaxStart"),u&&L.event.trigger("ajaxSend",[p,l]),l.beforeSend&&!1===l.beforeSend.call(l.context,p,l)?(l.global&&L.active--,S.reject()):p.aborted?S.reject():((e=s.clk)&&(a=e.name)&&!e.disabled&&(l.extraData=l.extraData||{},l.extraData[a]=e.value,"image"==e.type)&&(l.extraData[a+".x"]=s.clk_x,l.extraData[a+".y"]=s.clk_y),g=1,v=2,e=L("meta[name=csrf-token]").attr("content"),(a=L("meta[name=csrf-param]").attr("content"))&&e&&(l.extraData=l.extraData||{},l.extraData[a]=e),l.forceSync?r():setTimeout(r,10),y=50,j=L.parseXML||function(e,t){return window.ActiveXObject?((t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e)):t=(new DOMParser).parseFromString(e,"text/xml"),t&&t.documentElement&&"parsererror"!=t.documentElement.nodeName?t:null},i=L.parseJSON||function(e){return window.eval("("+e+")")},w=function(e,t,a){var r=e.getResponseHeader("content-type")||"",n="xml"===t||!t&&0<=r.indexOf("xml"),e=n?e.responseXML:e.responseText;return n&&"parsererror"===e.documentElement.nodeName&&L.error&&L.error("parsererror"),"string"==typeof(e=a&&a.dataFilter?a.dataFilter(e,t):e)&&("json"===t||!t&&0<=r.indexOf("json")?e=i(e):("script"===t||!t&&0<=r.indexOf("javascript"))&&L.globalEval(e)),e}),S;function k(t){var a=null;try{t.contentWindow&&(a=t.contentWindow.document)}catch(e){M("cannot get iframe.contentWindow document: "+e)}if(!a)try{a=t.contentDocument||t.document}catch(e){M("cannot get iframe.contentDocument: "+e),a=t.document}return a}function r(){var e=E.attr2("target"),t=E.attr2("action"),a=E.attr("enctype")||E.attr("encoding")||"multipart/form-data";s.setAttribute("target",o),A&&!/post/i.test(A)||s.setAttribute("method","POST"),t!=l.url&&s.setAttribute("action",l.url),l.skipEncodingOverride||A&&!/post/i.test(A)||E.attr({encoding:"multipart/form-data",enctype:"multipart/form-data"}),l.timeout&&(h=setTimeout(function(){d=!0,D(g)},l.timeout));var r=[];try{if(l.extraData)for(var n in l.extraData)l.extraData.hasOwnProperty(n)&&(L.isPlainObject(l.extraData[n])&&l.extraData[n].hasOwnProperty("name")&&l.extraData[n].hasOwnProperty("value")?r.push(L('<input type="hidden" name="'+l.extraData[n].name+'">').val(l.extraData[n].value).appendTo(s)[0]):r.push(L('<input type="hidden" name="'+n+'">').val(l.extraData[n]).appendTo(s)[0]));l.iframeTarget||f.appendTo("body"),m.attachEvent?m.attachEvent("onload",D):m.addEventListener("load",D,!1),setTimeout(function e(){try{var t=k(m).readyState;M("state = "+t),t&&"uninitialized"==t.toLowerCase()&&setTimeout(e,50)}catch(e){M("Server abort: ",e," (",e.name,")"),D(v),h&&clearTimeout(h),h=void 0}},15);try{s.submit()}catch(e){document.createElement("form").submit.apply(s)}}finally{s.setAttribute("action",t),s.setAttribute("enctype",a),e?s.setAttribute("target",e):E.removeAttr("target"),L(r).remove()}}function D(t){if(!p.aborted&&!T)if((b=k(m))||(M("cannot access response document"),t=v),t===g&&p)p.abort("timeout"),S.reject(p,"timeout");else if(t==v&&p)p.abort("server abort"),S.reject(p,"error","server abort");else if(b&&b.location.href!=l.iframeSrc||d){m.detachEvent?m.detachEvent("onload",D):m.removeEventListener("load",D,!1);var a,t="success";try{if(d)throw"timeout";var e="xml"==l.dataType||b.XMLDocument||L.isXMLDoc(b);if(M("isXml="+e),!e&&window.opera&&(null===b.body||!b.body.innerHTML)&&--y)return M("requeing onLoad callback, DOM not available"),void setTimeout(D,250);var r,n,o,i=b.body||b.documentElement,s=(p.responseText=i?i.innerHTML:null,p.responseXML=b.XMLDocument||b,e&&(l.dataType="xml"),p.getResponseHeader=function(e){return{"content-type":l.dataType}[e.toLowerCase()]},i&&(p.status=Number(i.getAttribute("status"))||p.status,p.statusText=i.getAttribute("statusText")||p.statusText),(l.dataType||"").toLowerCase()),c=/(json|script|text)/.test(s);c||l.textarea?(r=b.getElementsByTagName("textarea")[0])?(p.responseText=r.value,p.status=Number(r.getAttribute("status"))||p.status,p.statusText=r.getAttribute("statusText")||p.statusText):c&&(n=b.getElementsByTagName("pre")[0],o=b.getElementsByTagName("body")[0],n?p.responseText=n.textContent||n.innerText:o&&(p.responseText=o.textContent||o.innerText)):"xml"==s&&!p.responseXML&&p.responseText&&(p.responseXML=j(p.responseText));try{x=w(p,s,l)}catch(e){t="parsererror",p.error=a=e||t}}catch(e){M("error caught: ",e),t="error",p.error=a=e||t}p.aborted&&(M("upload aborted"),t=null),"success"===(t=p.status?200<=p.status&&p.status<300||304===p.status?"success":"error":t)?(l.success&&l.success.call(l.context,x,"success",p),S.resolve(p.responseText,"success",p),u&&L.event.trigger("ajaxSuccess",[p,l])):t&&(void 0===a&&(a=p.statusText),l.error&&l.error.call(l.context,p,t,a),S.reject(p,"error",a),u)&&L.event.trigger("ajaxError",[p,l,a]),u&&L.event.trigger("ajaxComplete",[p,l]),u&&!--L.active&&L.event.trigger("ajaxStop"),l.complete&&l.complete.call(l.context,p,t),T=!0,l.timeout&&clearTimeout(h),setTimeout(function(){l.iframeTarget?f.attr("src",l.iframeSrc):f.remove(),p.responseXML=null},100)}}}},L.fn.ajaxForm=function(e){var t;return(e=e||{}).delegation=e.delegation&&L.isFunction(L.fn.on),e.delegation||0!==this.length?e.delegation?(L(document).off("submit.form-plugin",this.selector,a).off("click.form-plugin",this.selector,r).on("submit.form-plugin",this.selector,e,a).on("click.form-plugin",this.selector,e,r),this):this.ajaxFormUnbind().bind("submit.form-plugin",e,a).bind("click.form-plugin",e,r):(t={s:this.selector,c:this.context},!L.isReady&&t.s?(M("DOM not ready, queuing ajaxForm"),L(function(){L(t.s,t.c).ajaxForm(e)})):M("terminating; zero elements found by selector"+(L.isReady?"":" (DOM not ready)")),this)},L.fn.ajaxFormUnbind=function(){return this.unbind("submit.form-plugin click.form-plugin")},L.fn.formToArray=function(e,t){var a=[];if(0!==this.length){var r=this[0],n=this.attr("id"),o=e?r.getElementsByTagName("*"):r.elements;if(o&&!/MSIE [678]/.test(navigator.userAgent)&&(o=L(o).get()),(o=n&&(n=L(':input[form="'+n+'"]').get()).length?(o||[]).concat(n):o)&&o.length){for(var i,s,c,l,u,f,m=0,p=o.length;m<p;m++)if((f=(c=o[m]).name)&&!c.disabled)if(e&&r.clk&&"image"==c.type)r.clk==c&&(a.push({name:f,value:L(c).val(),type:c.type}),a.push({name:f+".x",value:r.clk_x},{name:f+".y",value:r.clk_y}));else if((s=L.fieldValue(c,!0))&&s.constructor==Array)for(t&&t.push(c),i=0,l=s.length;i<l;i++)a.push({name:f,value:s[i]});else if(h.fileapi&&"file"==c.type){t&&t.push(c);var d=c.files;if(d.length)for(i=0;i<d.length;i++)a.push({name:f,value:d[i],type:c.type});else a.push({name:f,value:"",type:c.type})}else null!=s&&(t&&t.push(c),a.push({name:f,value:s,type:c.type,required:c.required}));!e&&r.clk&&(f=(u=(n=L(r.clk))[0]).name)&&!u.disabled&&"image"==u.type&&(a.push({name:f,value:n.val()}),a.push({name:f+".x",value:r.clk_x},{name:f+".y",value:r.clk_y}))}}return a},L.fn.formSerialize=function(e){return L.param(this.formToArray(e))},L.fn.fieldSerialize=function(n){var o=[];return this.each(function(){var e=this.name;if(e){var t=L.fieldValue(this,n);if(t&&t.constructor==Array)for(var a=0,r=t.length;a<r;a++)o.push({name:e,value:t[a]});else null!=t&&o.push({name:this.name,value:t})}}),L.param(o)},L.fn.fieldValue=function(e){for(var t=[],a=0,r=this.length;a<r;a++){var n=this[a],n=L.fieldValue(n,e);null==n||n.constructor==Array&&!n.length||(n.constructor==Array?L.merge(t,n):t.push(n))}return t},L.fieldValue=function(e,t){var a=e.name,r=e.type,n=e.tagName.toLowerCase();if((t=void 0===t?!0:t)&&(!a||e.disabled||"reset"==r||"button"==r||("checkbox"==r||"radio"==r)&&!e.checked||("submit"==r||"image"==r)&&e.form&&e.form.clk!=e||"select"==n&&-1==e.selectedIndex))return null;if("select"!=n)return L(e).val();t=e.selectedIndex;if(t<0)return null;for(var o=[],i=e.options,s="select-one"==r,c=s?t+1:i.length,l=s?t:0;l<c;l++){var u=i[l];if(u.selected){var f=(f=u.value)||(u.attributes&&u.attributes.value&&!u.attributes.value.specified?u.text:u.value);if(s)return f;o.push(f)}}return o},L.fn.clearForm=function(e){return this.each(function(){L("input,select,textarea",this).clearFields(e)})},L.fn.clearFields=L.fn.clearInputs=function(a){var r=/^(?:color|date|datetime|email|month|number|password|range|search|tel|text|time|url|week)$/i;return this.each(function(){var e=this.type,t=this.tagName.toLowerCase();r.test(e)||"textarea"==t?this.value="":"checkbox"==e||"radio"==e?this.checked=!1:"select"==t?this.selectedIndex=-1:"file"==e?/MSIE/.test(navigator.userAgent)?L(this).replaceWith(L(this).clone(!0)):L(this).val(""):a&&(!0===a&&/hidden/.test(e)||"string"==typeof a&&L(this).is(a))&&(this.value="")})},L.fn.resetForm=function(){return this.each(function(){"function"!=typeof this.reset&&("object"!=typeof this.reset||this.reset.nodeType)||this.reset()})},L.fn.enable=function(e){return void 0===e&&(e=!0),this.each(function(){this.disabled=!e})},L.fn.selected=function(t){return void 0===t&&(t=!0),this.each(function(){var e=this.type;"checkbox"==e||"radio"==e?this.checked=t:"option"==this.tagName.toLowerCase()&&(e=L(this).parent("select"),t&&e[0]&&"select-one"==e[0].type&&e.find("option").selected(!1),this.selected=t)})},L.fn.ajaxSubmit.debug=!1});
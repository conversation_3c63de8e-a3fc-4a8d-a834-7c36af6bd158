var arr_all_conditions=[],um_field_conditions={},um_field_default_values={};function um_get_field_default_value(e){var i,n="",t=um_get_field_type(e);switch(t){case"text":case"number":case"date":case"textarea":case"select":n=e.find('input:text,input[type="number"],textarea,select').val();break;case"multiselect":n=e.find("select").val();break;case"radio":1<=e.find('input[type="radio"]:checked').length&&(n=e.find('input[type="radio"]:checked').val());break;case"checkbox":1<=e.find('input[type="checkbox"]:checked').length&&(n=1<e.find('input[type="checkbox"]:checked').length?((i=[]).push(n),e.find('input[type="checkbox"]:checked').each(function(){i.push(jQuery(this).val())}),i):e.find('input[type="checkbox"]:checked').val());break;default:n=wp.hooks.applyFilters("um_conditional_logic_default_value",n,t,e)}return{type:t,value:n}}function um_get_field_element(e){var i=e.find("input,textarea,select"),n=um_get_field_type(e);return wp.hooks.applyFilters("um_conditional_logic_field_element",i,n,e)}function um_get_field_type(e){var n="",e=e.attr("class").split(" ");return jQuery.each(e,function(e,i){/um-field-type_/.test(i)&&(n=i.replace("um-field-type_","").trim())}),n}function um_get_field_children(n){var t=[];return jQuery.each(arr_all_conditions,function(e,i){i.field.parent===n&&t.push(i.field.condition)}),t}function um_splitup_array(e,i){for(var n=e.length%i,t=n,a=Math.floor(e.length/i),u=[],o=0;o<e.length;o+=a){var r=a+o,d=!1;0!=n&&t&&(r++,t--,d=!0),u.push(e.slice(o,r)),d&&o++}var c=[];return jQuery.each(u,function(e,i){c.push({action:i[0],if_field:i[1],operator:i[2],value:i[3]})}),c}function um_get_field_data(e){return um_live_field=e.parents(".um-field").data("key"),um_live_value=e.val(),e.is(":checkbox")&&(um_live_value="",1<e.parents(".um-field").find("input:checked").length?e.parents(".um-field").find("input:checked").each(function(){um_live_value=um_live_value+jQuery(this).val()+" "}):1<=e.parents(".um-field").find("input:checked").length&&(um_live_value=e.parents(".um-field").find("input:checked").val())),um_live_value=e.is(":radio")?e.parents(".um-field").find("input[type=radio]:checked").val():um_live_value}function um_in_array(e,i,n){var t,a=!1;for(t in n=!!n,i)if(n&&i[t]===e||!n&&i[t]==e){a=!0;break}return a}function um_apply_conditions(n,e){var i,t,a,u,o,r;n.parents(".um-field[data-key]").length&&(i=n.parents(".um-field[data-key]").data("key"),void 0!==(i=um_field_conditions[i]))&&(t=um_get_field_type(n.parents(".um-field[data-key]")),"empty_file"===(a=um_get_field_data(n))&&(a=""),u={},o={},r={},jQuery.each(i,function(e,i){void 0===o[i.owner]&&(o[i.owner]=[],r[i.owner]={}),o[i.owner].push(i.value),r[i.owner]=i}),jQuery.each(i,function(e,i){var n;if(void 0===u[i.owner]&&(u[i.owner]={}),"empty"===i.operator&&(n=Array.isArray(a)?a.join(""):a,u[i.owner][e]=!n||""===n),"not empty"===i.operator&&(n=Array.isArray(a)?a.join(""):a,u[i.owner][e]=!(!n||""===n)),"equals to"===i.operator&&(n=Array.isArray(a)&&1===a.length?a[0]:a,i.value===n&&um_in_array(n,o[i.owner])?u[i.owner][e]=!0:u[i.owner][e]=!1),"not equals"===i.operator&&(n=Array.isArray(a)&&1===a.length?a[0]:a,(!jQuery.isNumeric(i.value)||parseInt(n)===parseInt(i.value)||!n||um_in_array(n,o[i.owner]))&&(i.value==n||um_in_array(n,o[i.owner]))?u[i.owner][e]=!1:u[i.owner][e]=!0),"greater than"===i.operator&&(n=Array.isArray(a)&&1===a.length?a[0]:a,jQuery.isNumeric(i.value)&&parseInt(n)>parseInt(i.value)?u[i.owner][e]=!0:u[i.owner][e]=!1),"less than"===i.operator&&(n=Array.isArray(a)&&1===a.length?a[0]:a,jQuery.isNumeric(i.value)&&parseInt(n)<parseInt(i.value)?u[i.owner][e]=!0:u[i.owner][e]=!1),"contains"===i.operator)switch(t){case"multiselect":a&&0<=a.indexOf(i.value)&&um_in_array(i.value,a)?u[i.owner][e]=!0:u[i.owner][e]=!1;break;case"checkbox":a&&0<=a.indexOf(i.value)?u[i.owner][e]=!0:u[i.owner][e]=!1;break;default:void 0===(u=wp.hooks.applyFilters("um_conditional_logic_contains_operator_owners",u,t,a,i,e))[i.owner][e]&&(a&&0<=a.indexOf(i.value)&&um_in_array(a,o[i.owner])?u[i.owner][e]=!0:u[i.owner][e]=!1)}}),jQuery.each(u,function(e,i){um_in_array(!0,i)?um_field_apply_action(n,r[e],!0):um_field_apply_action(n,r[e],!1)}),n.trigger("um_fields_change"))}function um_field_apply_action(e,i,n){var t=jQuery('div.um-field[data-key="'+i.owner+'"]');return"show"===i.action&&n&&(t.is(":hidden")&&um_field_restore_default_value(t),t.show(),_show_in_ie(t)),"show"!==i.action||n||(t.hide(),_hide_in_ie(t)),"hide"===i.action&&n&&(t.hide(),_hide_in_ie(t)),"hide"!==i.action||n||(t.is(":hidden")&&um_field_restore_default_value(t),t.show(),_show_in_ie(t)),e.removeClass("um-field-has-changed")}function um_field_restore_default_value(n){var e,i=um_get_field_type(n),t=n.data("key"),a=um_field_default_values[t];switch(i){case"text":case"number":case"date":case"textarea":n.find('input:text,input[type="number"],textareas').val(a.value);break;case"select":n.find("select").find("option").prop("selected",!1),n.find("select").val(a.value),n.find("select").trigger("change");break;case"multiselect":n.find("select").find("option").prop("selected",!1),jQuery.each(a.value,function(e,i){n.find("select").find('option[value="'+i+'"]').attr("selected",!0)}),n.find("select").trigger("change");break;case"checkbox":1<=n.find('input[type="checkbox"]:checked').length&&(n.find('input[type="checkbox"]:checked').prop("checked",!1),n.find("span.um-field-checkbox-state i").removeClass("um-icon-android-checkbox-outline"),n.find("span.um-field-checkbox-state i").addClass("um-icon-android-checkbox-outline-blank"),n.find(".um-field-checkbox.active").removeClass("active"),Array.isArray(a.value)?jQuery.each(a.value,function(e,i){i=n.find('input[type="checkbox"][value="'+i+'"]');i.attr("checked",!0),i.closest(".um-field-checkbox").find("i").removeClass("um-icon-android-checkbox-outline-blank"),i.closest(".um-field-checkbox").find("i").addClass("um-icon-android-checkbox-outline"),i.closest(".um-field-checkbox").addClass("active")}):((e=n.find('input[type="checkbox"][value="'+a.value+'"]')).attr("checked",!0),e.closest(".um-field-checkbox").find("i").removeClass("um-icon-android-checkbox-outline-blank"),e.closest(".um-field-checkbox").find("i").addClass("um-icon-android-checkbox-outline"),e.closest(".um-field-checkbox").addClass("active")));break;case"radio":1<=n.find('input[type="radio"]:checked').length&&setTimeout(function(){n.find('input[type="radio"]:checked').prop("checked",!1),n.find("span.um-field-radio-state i").removeClass("um-icon-android-radio-button-on"),n.find("span.um-field-radio-state i").addClass("um-icon-android-radio-button-off"),n.find(".um-field-radio.active").removeClass("active");var e=n.find('input[type="radio"][value="'+a.value+'"]');e.attr("checked",!0),e.closest(".um-field-radio").find("i").removeClass("um-icon-android-radio-button-off"),e.closest(".um-field-radio").find("i").addClass("um-icon-android-radio-button-on"),e.closest(".um-field-radio").addClass("active")},100);break;default:wp.hooks.doAction("um_conditional_logic_restore_default_value",i,n,a)}!n.hasClass("um-field-has-changed")&&(t=um_get_field_element(n),t="radio"!==i&&"checkbox"!==i?t:t.find(":checked"))&&(t.trigger("change"),n.addClass("um-field-has-changed"))}function um_field_hide_siblings(){jQuery.each(um_field_conditions,function(e,i){(1<=jQuery('.um-field[data-key="'+e+'"]:hidden').length||"none"===jQuery('.um-field[data-key="'+e+'"]').css("display"))&&jQuery.each(i,function(e,i){jQuery('.um-field[data-key="'+i.owner+'"]').hide()})})}function _hide_in_ie(e){void 0!==jQuery.browser&&jQuery.browser.msie&&e.css({visibility:"hidden"})}function _show_in_ie(e){void 0!==jQuery.browser&&jQuery.browser.msie&&e.css({visibility:"visible"})}function um_init_field_conditions(){var e=[];jQuery(".um-field[data-key]").each(function(){var n=jQuery(this).data("key"),a=(e.push(n),{});jQuery.each(jQuery(this)[0].attributes,function(e,i){var n,t;-1!==i.name.indexOf("data-cond")&&(n=(t=i.name.slice(10)).substring(1,0),t=t.slice(2),void 0===a[n]&&(a[n]={}),a[n][t]=i.value)}),jQuery.each(a,function(e,i){i={field:{owner:n,action:i.action,parent:i.field,operator:i.operator,value:i.value,condition:{owner:n,action:i.action,operator:i.operator,value:i.value}}};arr_all_conditions.push(i)}),um_field_default_values[jQuery(this).data("key")]=um_get_field_default_value(jQuery(this))}),jQuery.each(e,function(e,i){um_field_conditions[i]=um_get_field_children(i)}),jQuery(".um-field[data-key]:visible").each(function(){var e=um_get_field_element(jQuery(this));void 0!==e.trigger&&e.trigger("change")})}jQuery(document).ready(function(){jQuery(document).on("change",'.um-field select, .um-field input[type="radio"], .um-field input[type="checkbox"]',function(){um_apply_conditions(jQuery(this),!1)}),jQuery(document).on("input change",'.um-field input[type="text"]',function(){um_apply_conditions(jQuery(this),!1)}),jQuery(document).on("input change",'.um-field input[type="number"]',function(){um_apply_conditions(jQuery(this),!1)}),jQuery(document).on("input change",'.um-field input[type="password"]',function(){um_apply_conditions(jQuery(this),!1)}),jQuery(document).on("change",'.um-field-image input[type="hidden"],.um-field-file input[type="hidden"]',function(){um_apply_conditions(jQuery(this),!1)}),jQuery(document).on("click",".um-finish-upload",function(){var e=jQuery(this).attr("data-key"),i=jQuery(".um-field-"+e+" input");setTimeout(function(){um_apply_conditions(i,!1)},100)}),jQuery(document).on("click",".um-field .cancel",function(){var e=jQuery(this).parent().attr("data-key"),i=jQuery(".um-field-"+e+" input");setTimeout(function(){um_apply_conditions(i,!1)},1e3)}),jQuery(document).on("um_fields_change",function(){um_field_hide_siblings(),um_field_hide_siblings()}),um_init_field_conditions()});
.elementor-kit-887{--e-global-color-primary:#20C997;--e-global-color-secondary:#868E96;--e-global-color-text:#868E96;--e-global-color-accent:#868E96;--e-global-color-24f28c35:#6EC1E4;--e-global-color-69417c20:#54595F;--e-global-color-58c6f35d:#7A7A7A;--e-global-color-79ec148:#61CE70;--e-global-color-6f3e2d77:#4054B2;--e-global-color-110aaf9a:#23A455;--e-global-color-c557296:#000;--e-global-color-47dc99c9:#FFF;--e-global-color-3a8d445:#72BEFA;--e-global-color-4592af9:#E583B6;--e-global-color-af812ec:#404A5E;--e-global-color-5ab7050:#FFF8F3;--e-global-color-da77079:#2F2D2E;--e-global-typography-primary-font-family:"Comforta";--e-global-typography-primary-font-weight:Bold;--e-global-typography-secondary-font-family:"Comforta";--e-global-typography-secondary-font-weight:600;--e-global-typography-text-font-family:"Comforta";--e-global-typography-text-font-weight:400;--e-global-typography-text-line-height:1.5em;--e-global-typography-accent-font-family:"Comforta";--e-global-typography-accent-font-weight:400;line-height:1.5em;}.elementor-kit-887 e-page-transition{background-color:#FFBC7D;}.elementor-kit-887 a{font-size:15px;line-height:1.3em;}.elementor-kit-887 h2{color:#FFFFFF;font-size:25px;text-transform:capitalize;}.elementor-kit-887 h3{color:#FFFFFF;}.elementor-kit-887 h4{color:#FFFFFF;}.elementor-kit-887 h5{color:#FFFFFF;}.elementor-kit-887 h6{color:#FFFFFF;}.elementor-kit-887 label{font-size:15px;}.elementor-kit-887 input:not([type="button"]):not([type="submit"]),.elementor-kit-887 textarea,.elementor-kit-887 .elementor-field-textual{font-size:15px;}.elementor-section.elementor-section-boxed > .elementor-container{max-width:1230px;}.e-con{--container-max-width:1230px;}.elementor-widget:not(:last-child){margin-block-end:20px;}.elementor-element{--widgets-spacing:20px 20px;--widgets-spacing-row:20px;--widgets-spacing-column:20px;}{}h1.entry-title{display:var(--page-title-display);}@media(min-width:2400px){.elementor-section.elementor-section-boxed > .elementor-container{max-width:2400px;}.e-con{--container-max-width:2400px;}}@media(max-width:1024px){.elementor-section.elementor-section-boxed > .elementor-container{max-width:1024px;}.e-con{--container-max-width:1024px;}}@media(max-width:767px){.elementor-section.elementor-section-boxed > .elementor-container{max-width:767px;}.e-con{--container-max-width:767px;}}/* Start Custom Fonts CSS */@font-face {
	font-family: 'Comforta';
	font-style: normal;
	font-weight: normal;
	font-display: auto;
	src: url('https://codecut.ai/wp-content/uploads/2023/12/Comfortaa-SemiBold.ttf') format('truetype');
}
/* End Custom Fonts CSS */
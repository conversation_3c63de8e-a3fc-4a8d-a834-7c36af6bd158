.xoo-wsc-markup *::-webkit-scrollbar {
    width: 8px;
}

.xoo-wsc-markup *::-webkit-scrollbar-track {
    border-radius: 10px;
    background: #f0f0f0;
}

.xoo-wsc-markup *::-webkit-scrollbar-thumb {
    border-radius: 50px;
     background: #d1d1d1
}


.xoo-wsc-container, .xoo-wsc-slider {
    position: fixed;
    background-color: #fff;
    z-index: 999999;
    display: flex;
    flex-direction: column;
    width: 95%;
    transition: 0.5s ease-out;
}

.xoo-wsc-modal * {
    box-sizing: border-box;
}

.xoo-wsc-markup a{
    text-decoration: none;
}


.xoo-wsc-opac {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #000;
    z-index: 999996;
    opacity: 0;
    height: 0;
    transition: height 0s 0.4s, opacity 0.4s 0s;
}

.xoo-wsc-cart-active .xoo-wsc-opac {
    opacity: 0.8;
    height: 100%;
    transition: height 0s 0s, opacity 0.4s;
}

.xoo-wsc-body {
    flex-grow: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

.xoo-wsc-loading .xoo-wsc-loader{
    display: block;
}

span.xoo-wsc-loader {
    display: none;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    opacity: 0.5;
    background-color: #eee;
    opacity: 0.7;
    z-index: 10;
}


.xoo-wsc-loader-icon{
    display: none;
}

.xoo-wsc-loading .xoo-wsc-loader-icon {
    position: absolute;
    transform: translate(-50%,-50%);
    top: 47%;
    left: 49%;
    z-index: 11;
    font-size: 24px;
    animation: xoo-wsc-spin 2s linear infinite;
    display: block;
}

@keyframes xoo-wsc-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



body.xoo-wsc-cart-active, html.xoo-wsc-cart-active{
    overflow: hidden!important;
}

.xoo-wsc-basket {
    margin: 10px;
    z-index: 9999997;
    cursor: pointer;
    position: fixed;
    transition: 0.5s ease-out;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
}

.xoo-wsc-basket[style*='block'] {
    display: flex!important;
}
.xoo-wsc-items-count{
    border-radius: 50%;
    position: absolute;
    font-size: 13px;
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    overflow: hidden;
}

.xoo-wsc-fly-animating{
    opacity: 1;
    position: absolute!important;
    height: 150px;
    width: 150px;
    z-index: 100;
}

.xoo-wsc-sc-bkcont {
    position: relative;
}


.xoo-wsc-sc-cont .xoo-wsc-cart-trigger {
    display: flex;
    position: relative;
    cursor: pointer;
    align-items: center;
    justify-content: center;
}


span.xoo-wsc-sc-count {
    border-radius: 50%;
    height: 18px;
    line-height: 18px;
    width: 18px;
    display: inline-block;
    text-align: center;
    font-size: 13px;
    right: -7px;
    position: absolute;
    top: -4px;
}

.xoo-wsc-sc-cont .xoo-wsc-cart-trigger > * {
    margin-right: 3px;
}


/* Notices */
.xoo-wsc-notice-container {
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: none;
}

ul.xoo-wsc-notices {
    margin: 0;
    list-style-type: none;
    padding: 0;
}

.xoo-wsc-notice-success {
    background-color: #DFF0D8;
    color: #3C763D;
}
.xoo-wsc-notice-error {
    color: #a94442;
    background-color: #f2dede;
}

ul.xoo-wsc-notices li {
    padding: 15px 20px;
    list-style-type: none;
}

ul.xoo-wsc-notices li span {
    margin-right: 6px;
}

span.xoo-wsc-undo-item {
    float: right;
    text-decoration: underline;
    cursor: pointer;
}


.xoo-wsc-notices a.button.wc-forward {
    display: none;
}

/* Basket */
.xoo-wscb-icon {
    font-size: 37px;
}

.xoo-wscb-count {
    position: absolute;
    z-index: 1;
    background-color: transparent;
    font-size: 15px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    left: 0;
    right: 0;
}

/* Header */
.xoo-wsc-header {
    padding: 15px;
    color: #000;
}

span.xoo-wsch-close {
    position: absolute;
    cursor: pointer;
}

span.xoo-wsch-text {
    margin-left: 8px;
}

.xoo-wsch-top {
    align-items: center;
}

.xoo-wsch-top .xoo-wsch-basket {
    display: table;
    position: relative;
}

.xoo-wsch-top {
    display: flex;
    margin: 0 auto;
}


/****** BODY ***********/

/*** Product ***/
.xoo-wsc-product {
    display: flex;
    border-bottom: 1px solid #eee;
}

.xoo-wsc-img-col {
    align-self: center;
}

.xoo-wsc-sum-col {
    flex-grow: 1;
    padding-left: 15px;
    display: flex;
    flex-direction: column;
}

.xoo-wsc-img-col img {
    width: 100%;
    height: auto;
}

.xoo-wsc-sm-left {
    flex-grow: 1;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    min-width: 0;
}

.xoo-wsc-sm-right {
    align-items: flex-end;
    padding-left: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 30px;
}


span.xoo-wsc-smr-del+.xoo-wsc-smr-ptotal {
    margin-top: auto;
}


span.xoo-wsc-pname, span.xoo-wsc-pname a {
    font-weight: 600;
}

span.xoo-wsc-smr-del {
    cursor: pointer;
    margin-top: 5px;
}

.xoo-wsc-img-col span.xoo-wsc-smr-del {
    margin: 5px auto;
    display: table;
}

.xoo-wsc-sm-info {
    display: flex;
    min-width: 0;
}

.xoo-wsc-sm-sales {
    text-transform: uppercase;
    border: 1px solid #333;
    padding: 2px 10px;
    display: block;
    margin-bottom: 5px;
    border-radius: 10px;
    font-size: 10px;
}

.xoo-wsc-sm-left > *:not(:last-child) {
    padding-bottom: 4px;
}

/* Qty Box */
.xoo-wsc-qty-price span {
    display: inline-block;
}

/* Variation */
.xoo-wsc-product dl.variation dd, .xoo-wsc-product dl.variation dt {
    margin: 0 4px 0 0;
    display: inline-block;
    float: left;
    font-style: italic;
}

.xoo-wsc-product dl.variation dt {
    clear: left;
}

.xoo-wsc-product dl.variation, .xoo-wsc-product dl.variation p {
    margin: 0;
    font-size: 13px;
}


/** Footer */
.xoo-wsc-ft-buttons-cont {
    display: grid;
    text-align: center;
    grid-column-gap: 5px;
    grid-row-gap: 5px;
}

a.xoo-wsc-ft-btn, .xoo-wsc-container .xoo-wsc-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

a.xoo-wsc-ft-btn, a.xoo-wsc-ft-btn:hover, .xoo-wsc-container .xoo-wsc-btn, .xoo-wsc-container .xoo-wsc-btn:hover{
    text-decoration: none;
}


.xoo-wsc-ft-buttons-cont a.xoo-wsc-ft-btn:only-child {
    grid-column: 1/-1;
}


/** FOOTER Totals **/
.xoo-wsc-ft-totals {
    width: 100%;
    padding: 5px 0 10px 0;
}

.xoo-wsc-ft-amt {
    padding: 3px;
    display: flex;
    justify-content: space-between;
}

.xoo-wsc-ft-amt-value .xoo-wsc-toggle-slider {
    cursor: pointer;
    margin-left: 5px;
}

span.xoo-wsc-ft-amt-label {
    font-weight: 600;
}

.xoo-wsc-toggle-slider {
    cursor: pointer;
}

.xoo-wsc-ft-amt-shipping .xoo-wsc-toggle-slider span {
    margin-left: 5px;
}

.xoo-wsc-ft-amt-label {
    flex-grow: 1;
    padding-right: 10px;
}

.xoo-wsc-ft-amt-value {
    text-align: right;
}

.xoo-wsc-ft-amt.less {
    color: #4CAF50;
}

.xoo-wsc-ft-amt-total {
    border-top: 1px dashed #9E9E9E;
    margin-top: 5px;
    padding-top: 5px;
}


/* Bundled product */

.xoo-wsc-product.xoo-wsc-is-parent {
    border-bottom: 0;
    padding-bottom: 0;
}

.xoo-wsc-product.xoo-wsc-is-child img {
    max-width: 50px;
    margin-left: auto;
    float: right;
}

.xoo-wsc-product.xoo-wsc-is-child {
    padding-top: 5px;
}

.xoo-wsc-empty-cart {
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.xoo-wsc-empty-cart > * {
    margin-bottom: 20px;
}

/** Free **/
span.xoo-wsc-ft-amt-label:after {
    content: ":";
}

.xoo-wsc-ft-amt {
    display: table;
    margin: 0 auto;
    font-weight: 600;
}

span.xoo-wsc-ft-amt-label {
    padding-right: 5px;
}

span.xoo-wsc-footer-txt {
    text-align: center;
    margin: -8px auto 10px;
    display: block;
    font-size: 90%;
}

span.xoo-wsc-del-txt {
    text-align: center;
    display: block;
    font-size: 11px;
    text-transform: uppercase;
}

a.xoo-wsc-ft-btn-checkout .amount {
    font-style: italic;
}

a.xoo-wsc-ft-btn-checkout .amount:before {
    content: "-";
    margin: 0 8px;
    font-weight: bold;
}


/*Child selector except last */
.xoo-wsc-is-child:has( +.xoo-wsc-is-child ), .xoo-wsc-is-parent + .xoo-wsc-is-child{
    padding-bottom: 5px;
    padding-top: 10px;
    margin-top: 0;
    border-radius: 0;
    margin-bottom: 0;
    box-shadow: 0 0;
    border-bottom: 0;
}
.xoo-wsc-product.xoo-wsc-is-parent{
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Only last child */
.xoo-wsc-product.xoo-wsc-is-child:not(:has( + *)), .xoo-wsc-is-child:has( + .xoo-wsc-product:not(.xoo-wsc-is-child) ){
    margin-top: 0;
    padding-top: 5px;
}

.xoo-wsc-product dl.variation {
    flex-wrap: wrap;
}


.xoo-wsc-product del {
    opacity: 0.5;
    text-decoration: line-through;
    display: inline-flex;
}

.xoo-wsc-product del+ins {
    font-weight: 600;
}

.xoo-wsc-product del .amount {
    font-size: 95%;
}


body.rtl .xoo-wsc-sum-col {
    padding-right: 15px;
    padding-left: 0;
}

body.rtl span.xoo-wsch-text {
    margin-right: 8px;
    margin-left: 0;
}

body.rtl span.xoo-wsc-ft-amt-label {
    padding-left: 5px;
    padding-right: 0;
}

body.rtl .xoo-wsc-product dl.variation dd, body.rtl .xoo-wsc-product dl.variation dt {
    float: right;
}

body.rtl xoo-wsc-qty-price > span{
    margin-left: 4px;
    margin-right: 0;
}

.xoo-wsc-markup-notices {
    top: 20%;
    right: -100%;
    left: auto;
    position: fixed;
    box-shadow: 2px 2px 9px 3px #eee;
    transition: all 0.5s;
    z-index: 999997;
    width: 0;
    max-width: 400px;
}

.xoo-wsc-markup-notices ul.xoo-wsc-notices li {
    padding: 25px;
}

.xoo-wsc-markup-notices.xoo-wsc-active {
    right: 0;
    width: 100%;
}

.xoo-wsc-markup-notices .xoo-wsc-notice-container {
    position: relative;
    display: block;
}
.xoo-wsc-qty-price > span {
    display: inline-block;
    margin-right: 4px;
}
.xoo-wsc-sml-qty span {
    display: inline-block;
}

.xoo-wsc-markup[data-wpr-lazyrender] {
    content-visibility: visible!important;
}

.xoo-wsc-qty-price {
    display: flex;
    width: 100%;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-start;
}



/* Pattern Card */
.xoo-wsc-pattern-card {
    
margin: 10px 20px;
    
padding: 0;
}

.xoo-wsc-pattern-card .xoo-wsc-product dl.variation {
    display: flex;
}


.xoo-wsc-pattern-card .xoo-wsc-product {
    flex-direction: column;
    position: relative;
    border-bottom: 0;
    padding: 0;
    height: 100%;
    margin: 0;
}

.xoo-wsc-pattern-card span.xoo-wsc-smr-del {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 5;
    padding: 10px;
    margin-top: 0;
}

.xoo-wsc-pattern-card .variation {
    justify-content: center;
}


.xoo-wsc-sm-back, .xoo-wsc-sm-front {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.xoo-wsc-sm-back-cont {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    overflow: auto;
}

.xoo-wsc-pattern-card .xoo-wsc-img-col.xoo-wsc-caniming {
    z-index: 10;
}

.xoo-wsc-sm-front {
    z-index: 1;
    height: 100%;
}

.xoo-wsc-sm-front:has(.xoo-wsc-sm-emp+*) {
    padding: 7px 10px;
}

.xoo-wsc-card-cont {
    position: relative;
}

.xoo-wsc-sm-back {
    padding: 10px;
    min-height: 100%;
}

a.xoo-wsc-smr-link i {
    margin-right: 3px;
}

a.xoo-wsc-smr-link {
    opacity: 0.7;
    font-size: 14px;
}

.xoo-wsc-sm-front:has(.xoo-wsc-sm-emp+*) .xoo-wsc-sm-emp {
    display: none;
}

.xoo-wsc-pattern-card .xoo-wsc-qty-price {
    justify-content: center;
    padding: 0;
}

.xoo-wsc-qty-price:has(.xoo-wsc-card-ptotal) {
    justify-content: space-between;
}


.xoo-wsc-pattern-card .xoo-wsc-img-col {
    align-self: center;
    z-index: 2;
    position: relative;
    margin: -1px -1px 0 -1px;
}
.xoo-wsc-pattern-card .xoo-wsc-img-col img{
    display: table;
    margin: 0 auto;
}

.xoo-wsc-sm-back > *, .xoo-wsc-sm-front > * {
    margin: 2px 0;
}

.xoo-wsc-sm-back > :first-child, .xoo-wsc-sm-front > :last-child {
    margin-top: 0;
}

.xoo-wsc-sm-back > :last-child, .xoo-wsc-sm-front > :last-child {
    margin-bottom: 0;
}

.xoo-wsc-qty-box-cont {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    margin: 0!important;
}

.xoo-wsc-qty-box-cont > * {
    margin: 3px 3px 0;
}

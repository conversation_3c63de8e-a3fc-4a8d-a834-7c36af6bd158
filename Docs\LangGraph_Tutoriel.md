# Créer des agents d'IA coordonnés avec LangGraph


[Building Coordinated AI Agents with LangGraph: A Hands-On Tutorial](https://codecut.ai/building-multi-agent-ai-langgraph-tutorial/)

![LangGraph Feature](langraph-featured-image.webp)

---

## Table des matières

- [Créer des agents d'IA coordonnés avec LangGraph](#créer-des-agents-dia-coordonnés-avec-langgraph)
  - [Table des matières](#table-des-matières)
  - [Introduction](#introduction)
  - [Ce que nous allons étudier](#ce-que-nous-allons-étudier)
  - [Prise en main de LangGraph](#prise-en-main-de-langgraph)
    - [Mise en place de l’environnement](#mise-en-place-de-lenvironnement)
    - [Création d'agents avec LangGraph](#création-dagents-avec-langgraph)
    - [Création d'un système multi-agent de supervision](#création-dun-système-multi-agent-de-supervision)
  - [Création du Conseil d'investissement des agents d'investissement](#création-du-conseil-dinvestissement-des-agents-dinvestissement)
    - [Vue d’ensemble de l’application](#vue-densemble-de-lapplication)
    - [Configuration](#configuration)
    - [Définition des outils](#définition-des-outils)
    - [Création des agents](#création-des-agents)
    - [Affichage des résulatats](#affichage-des-résulatats)
  - [Conclusion](#conclusion)

---

## Introduction

Avez-vous déjà remarqué comment un seul chatbot ne peut analyser les problèmes que sous **un seul angle à la fois** au lieu de mettre en perpectives plusieurs points de vue comme le font les humains  ?

Pour démontrer cela, voyons comment un seul agent analyserait la santé financière d’Apple :

```python
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate

# Approche à agent unique
single_agent = ChatOpenAI(model="gpt-4")
prompt = ChatPromptTemplate.from_messages([
    ("system", "Vous êtes un analyste financier. Analysez la santé de l’entreprise sous tous les angles."),
    ("user", "Analysez la santé financière d’Apple en tenant compte de la croissance, des risques et de la position sur le marché.")
])

response = single_agent.invoke(prompt.format_messages())
print(response.content)
```

Le résultat pourrait ressembler à ceci :

```text
Apple affiche une situation financière saine, une croissance soutenue et une position forte sur le marché. Son portefeuille de produits innovants, son segment de services en pleine croissance, ses marges bénéficiaires élevées et ses bilans solides témoignent de sa stabilité.
```

**Cette réponse/approche pose plusieurs problèmes :**

1. **Biais de confirmation** : la réponse peut être trop optimiste ou pessimiste. Elle tend à être unilatérale (dans ce cas, excessivement positive) car il n'y a pas de perspective contrebalançante. C'est comme n'avoir qu'une seule voix dans un débat.
2. **Aucune prise de décision** : l’agent ne fournit que de l’analyse, sans conclusion. Il ne prend auncunes décisions ou de recommandations concrètes.

➡️ Cette perspective limitée rend particulièrement difficile la réalisation de tâches complexes comme l’analyse d’investissement, qui nécessite d’équilibrer différents facteurs et perspectives.

**Solution LangGraph** : LangGraph résout ce problème en fournissant un cadre pour la construction de systèmes multi-agents coordonnés avec des agents spécialisés travaillant ensemble via une communication structurée.

Exemple de sortie multi-agents (Bull vs Bear + Président) :

```txt
Arguments bull : profits records Q1 2024, trésorerie élevée, croissance des services, écosystème solide.
Arguments bear : dépendance à l’iPhone, ralentissement de la croissance, risques réglementaires, exposition à la Chine.
Décision du président : HOLD / Rechercher plus de postions, équilibrant la force financière et la stabilité d'Apple par rapport aux risques macroéconomiques, réglementaires et de croissance importants.
```

Cette approche multi-agents produit une analyse **plus approfondie et équilibrée** par rapport à un agent unique essayant de prendre en compte tous les angles à la fois.

## Ce que nous allons étudier

Mise en oeuvre d'un systéme nécessitant des points de vue multiples et une prise de décision structurée.

- Construction d'un système de  multi-agents coordonnés qui débattent à partir de perspectives différentes au lieu de réponses biaisées uniques.
- Création d'agents spécialisés avec des outils et des invites distincts pour l'analyse des investissements haussiers/baissiers.
- Mise en œuvre des flux de travail de supervision qui acheminent les conversations entre les agents dans des séquences structurées.
- Déploiement de comités d'investissement avec intégration des données de marché en temps réel.

---

## Prise en main de LangGraph

### Mise en place de l’environnement

Tout d’abord, vous devez configurer votre environnement avec les packages suivants :

```bash
pip install langgraph langgraph-supervisor langchain langchain-core langchain-tavily langchain-openai python-dotenv
```

- langgraph : Framework principal pour la création de systèmes multi-agents avec gestion d'état
- langgraph-supervisor : Fournit le modèle de supervision pour la coordination des agents
- langchain et langchain-core : Composants fondamentaux pour les applications LLM
- langchain-tavily : Intégration avec l'API de recherche Tavily pour les études de marché
- langchain-openai : Intégrations de modèles OpenAI
- python-dotenv : Gestion des variables d'environnement pour les clés API

Fichier `.env` :

Ensuite, créez un fichier .env et remplissez-le avec vos clés API d’OpenAI et de Tavily (une API de moteur de recherche).

```bash
# https://app.tavily.com/home
TAVILY_API_KEY='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'
# https://platform.openai.com/api-keys
OPENAI_API_KEY='votre_clef_openai'
```

### Création d'agents avec LangGraph

LangGraph simplifie grandement la création de votre premier agent. Voyons comment créer un assistant polyvalent avec fonction de recherche web en quelques lignes de code.

```python
from dotenv import load_dotenv
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI
from langchain_tavily import TavilySearch

load_dotenv()

web_search = TavilySearch(max_results=3)

agent = create_react_agent(
    model=ChatOpenAI(model="gpt-4o"),
    tools=[web_search],
    prompt="Vous êtes un assistant qui peut chercher sur le web et résumer les résultats."
)

response = agent.invoke({
    "messages": [{"role": "user", "content": "Donne le cours d’ouverture et de clôture de l’action Apple du 1er juin 2025"}]
})

print(response['messages'][-1].content)
```

Nous configurons l'agent avec trois composants principaux :

- Un modèle de langage (GPT-4o dans ce cas)
- Une liste d'outils permettant à l'agent de se connecter à des outils et API externes, comme un moteur de recherche
- Une invite système définissant le rôle et le comportement de l'agent

Enfin, nous invoquons l'agent avec une question sur l'activité boursière du jour :

```txt
Le 1er juin 2025 était un dimanche, les marchés financiers étaient donc fermés. Le jour de négociation suivant était le 2 juin 2025. Ce jour-là, l'action Apple (AAPL) :

- Ouverture : 200,28 $
- Clôture : 201,70 $

Vous pouvez vérifier ces informations sur des sources fiables telles que Yahoo Finance et Macrotrends :
- Source : Yahoo Finance (page de données historiques)
- Source : Macrotrends, historique de l'action Apple

Si vous avez besoin de données pour la séance de bourse précédant immédiatement le 2 juin 2025, n'hésitez pas à me contacter !
```

Le résultat illustre le modèle ReAct en action :

- L'agent détermine les informations dont il a besoin (cours des actions à une date précise).
- Exécute la tâche à l'aide de l'outil de recherche web pour trouver l'information.
- Examine les résultats et analyse et raisonne à nouveau sur leur validité.
- Planifie les prochaines étapes en conséquence (dans ce cas, en fournissant les données pour le prochain jour de bourse)

---

### Création d'un système multi-agent de supervision

Un superviseur est un agent dédié qui gère le flux de travail entre plusieurs agents. Il est chargé de :

- Acheminer le flux de travail entre les agents
- Gérer l'historique des conversations 
- S'assurer que les agents collaborent pour atteindre l'objectif.

Créons un système multi-agents avec superviseur et trois agents :

```python
from langgraph_supervisor import create_supervisor
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver

agent1 = create_react_agent(...)
agent2 = create_react_agent(...)
agent3 = create_react_agent(...)

memory = MemorySaver()

supervisor = create_supervisor(
    model=ChatOpenAI(model="o3"),
    agents=[agent1, agent2, agent3],
    prompt="Instructions sur la façon de router le workflow."
).compile(checkpointer=memory)

response = supervisor.invoke({
    "messages": [{"role": "user", "content": "Votre question ici."}]
})
```

Le système multi-agents du superviseur suit un flux de travail structuré pour le traitement des requêtes des utilisateurs.

Voici son fonctionnement :

- L'utilisateur soumet une requête au superviseur.
- Le superviseur oriente la requète aux agents appropriés en fonction des instructions du système.
- Les agents analysent les informations de manière indépendante et renvoient leurs analyses au superviseur.
- Le superviseur agrège les résultats et génère une réponse finale.
- La réponse finale est renvoyée à l'utilisateur.

![Diagramme workflow](https://codecut.ai/wp-content/uploads/2025/06/langraph_2.png)

---

## Création du Conseil d'investissement des agents d'investissement

### Vue d’ensemble de l’application

```txt
investment-committee/
├── src/
│   ├── config.py     # Configuration (prompts + modèles)
│   ├── tools.py      # Outils des agents
│   ├── utils.py      # Fonctions utilitaires
│   └── agents.py     # Création des agents et superviseur
├── main.py           # Interface en ligne de commande
├── requirements.txt  # Dépendances Python
├── .env.example      # Modèle de variables d'environnement
└── README.md         # Documentation
```

Dans cette structure :

- src/config.py contient la configuration système
- src/tools.py contient les outils pour les agents
- src/utils.py contient les fonctions utilitaires pour l'application
- src/agents.py contient la création des agents et des superviseurs
- main.py est le point d'entrée de l'interface de ligne de commande

### Configuration

Exemple du **prompt superviseur** :

```python
SUPERVISOR_PROMPT = (
"Vous êtes un SIMPLE ROUTEUR avec une tâche récapitulative finale de résumé.\n\n"
"FLUX DE TRAVAIL OBLIGATOIRE (à suivre à la lettre) :\n"
"1. agent_bull : Établir un argument initial haussier\n"
"2. agent_bear : Établir un argument initial baissier\n"
"3. agent_bull : Contrer les arguments spécifiques de la baisse\n"
"4. agent_bear : Contrer les arguments spécifiques de la hausse\n"
"5. agent_president : Prendre la décision d'investissement finale\n"
"6. VOUS : Résumer le résultat du débat\n\n"
"RÈGLES :\n"
"- NE PAS résumer avant que le président ait pris sa décision\n"
"- TOUJOURS terminer avec la décision de l'agent_president en premier\n"
"- Acheminer les agents dans l'ordre exact ci-dessus\n"
"- Après la décision du président, fournir un bref résumé de :\n"
" • Arguments clés de la hausse\n"
" • Arguments clés de la baisse\n"
" • Arguments du président Décision finale et raisonnement\n"
"- Rédiger un résumé concis (3 à 4 phrases maximum)"
)
```

### Définition des outils

Les outils sont des fonctions permettant aux agents d'interagir avec des sources de données externes et d'effectuer des tâches spécifiques. Ce système utilise six outils :

- `find_positive_news`: Recherche des nouvelles et des développements positifs sur une action.
- `calculate_growth_potential` : Calcule les mesures de croissance de base et les indicateurs haussiers.
- `find_negative_news` : Recherche des nouvelles négatives et des risques concernant une action.
- `assess_market_risks` : Évalue les risques globaux du marché et les indicateurs baissiers.
- `get_current_market_sentiment` : Obtient le sentiment général du marché et les performances récentes.
- `make_investment_decision` : Fait une recommandation d'investissement finale basée sur des arguments haussiers et baissiers.

Chaque outil utilise l'API de recherche Tavily pour collecter des informations sur le marché en temps réel, garantissant que les agents basent leurs arguments sur des données actuelles plutôt que sur des informations obsolètes.

Voici le code de l' find_positive_newsoutil :

```python
def find_positive_news(stock_symbol: str):
    """Search for positive news and developments about a stock"""
    query = f"{stock_symbol} stock positive news earnings growth revenue profit upgrade"
    keywords = ["profit", "growth", "upgrade", "beat", "strong", "positive", "bullish"]
    prefix = "🐂 POSITIVE SIGNALS"
    default = "Limited positive news found, but that could mean it's undervalued!"
    return search_and_extract_signals(stock_symbol, query, keywords, prefix, default)
```

Afficher le code complet des outils dans le fichier [tools.py](https://translate.google.com/website?sl=en&tl=fr&hl=fr&client=webapp&u=https://github.com/CodeCutTech/langraph-demo/blob/main/src/tools.py).

### Création des agents

Les agents sont les composants centraux du système multi-agents. Ils sont chargés d'analyser les informations et de prendre des décisions.

Le agents.pyfichier crée les agents et le superviseur. Il existe trois agents spécialisés :

- Agent haussier (`Buul Agent`) : Un analyste optimiste qui recherche des indicateurs positifs et un potentiel de croissance.
- Agent baissier (`Bear Agent`) : Un analyste pessimiste qui identifie les risques et les signaux négatifs.
- Agent président (`President Agent`) : Un décideur neutre qui pèse les deux côtés et fait des recommandations d'investissement finales.

![Diagramme du comité d'investissement montrant les agents haussiers, baissiers et présidents avec leurs rôles dans le processus d'analyse](langraph.webp)

Chaque agent est créé avec la create_react_agentfonction avec les paramètres suivants :

- `model` : Le modèle de langage (LLM) à utiliser
- `tools` : Les outils à utiliser
- `prompt` : L'invite système à utiliser
- `name`: Le nom de l'agent

Voici le code Python pour les trois agents :

```python
def create_bull_agent():
    """Create the bull (optimistic) investment agent"""
    return create_react_agent(
        model=MODEL_NAME,
        tools=[find_positive_news, calculate_growth_potential],
        prompt=BULL_AGENT_PROMPT,
        name="bull_agent",
    )


def create_bear_agent():
    """Create the bear (pessimistic) investment agent"""
    return create_react_agent(
        model=MODEL_NAME,
        tools=[find_negative_news, assess_market_risks],
        prompt=BEAR_AGENT_PROMPT,
        name="bear_agent",
    )


def create_chairman_agent():
    """Create the chairman (decision maker) agent"""
    return create_react_agent(
        model=MODEL_NAME,
        tools=[get_current_market_sentiment, make_investment_decision],
        prompt=CHAIRMAN_AGENT_PROMPT,
        name="chairman_agent",
    )
```

Le superviseur regroupe tous les agents sous une gestion coordonnée des flux de travail. Voici son code :

```python
def create_investment_supervisor():
    """Create the supervisor that manages the investment committee"""
    bull_agent = create_bull_agent()
    bear_agent = create_bear_agent()
    chairman_agent = create_chairman_agent()

    supervisor = create_supervisor(
        model=init_chat_model(MODEL_NAME),
        agents=[bull_agent, bear_agent, chairman_agent],
        prompt=SUPERVISOR_PROMPT,
        add_handoff_back_messages=True,
        output_mode="full_history",
    ).compile()

    return supervisor
```

Dans ce code :

- `add_handoff_back_messages=True` Cela garantit que les agents peuvent faire référence aux arguments précédents des autres, créant ainsi un véritable débat conversationnel plutôt qu'une analyse isolée.
- `output_mode="full_history"`Fournit un contexte de conversation complet, vous permettant de voir l'ensemble du processus de raisonnement plutôt que simplement les décisions finales.

### Affichage des résulatats

Pour formater la sortie, nous utilisons la fonction `pretty_print_messages` se trouvant dans  `utils.py`. Elle prend les mises à jour du flux de conversation et les formate en sortie lisible afin :

- D'identifier quel agent parle.
- Le traitement des mises à jour du flux de travail du superviseur et des interactions des agents.
- La conversion des objets de message dans un format lisible.

Cela permet ainsi de voir clairement le débat de va-et-vient entre les agents lorsqu’ils analysent les opportunités d’investissement.

## Conclusion

Ce système de comité d'investissement démontre la puissance des agents IA coordonnés dans la prise de décisions complexes. En combinant des agents spécialisés aux perspectives différentes, nous avons créé un système capable de :

- Fournir une analyse équilibrée grâce à un débat structuré.
- Considérer plusieurs points de vue simultanément.
- Prendre des décisions éclairées basées sur des données complètes.
- S'adapter aux nouvelles informations grâce à des mises à jour en temps réel.

Avec LangGraph, il est possible de construire des systèmes multi-agents **cohérents, robustes et évolutifs**, allant bien au-delà de la finance.

Cette approche peut être étendue à d’autres domaines où des perspectives multiples et une prise de décision structurée sont cruciales. Toute démarche  nécessitant plusieurs points de vue ou des décisions structurées peut bénéficier de ce modèle.

.um .um-cover-add:hover,.um .um-field-checkbox.active:not(.um-field-radio-state-disabled) i,.um .um-field-radio.active:not(.um-field-radio-state-disabled) i,.um .um-item-meta a,.um .um-member-less a:hover,.um .um-member-more a:hover,.um .um-member-name a:hover,.um .um-members-pagi a:hover,.um .um-profile-subnav a.active,.um .um-tip:hover,.um-account-name a:hover,.um-account-nav a.current,.um-account-side li a.current span.um-account-icon,.um-account-side li a.current:hover span.um-account-icon,.um-dropdown li a:hover,i.um-active-color,span.um-active-color{color:#3ba1da}.picker__box,.picker__nav--next:hover,.picker__nav--prev:hover,.um .um-field-group-head,.um .um-members-pagi span.current,.um .um-members-pagi span.current:hover,.um .um-profile-nav-item.active a,.um .um-profile-nav-item.active a:hover,.um-modal-btn,.um-modal-btn.disabled,.um-modal-btn.disabled:hover,.um-modal-header,.upload,div.uimob800 .um-account-side li a.current,div.uimob800 .um-account-side li a.current:hover{background:#3ba1da}.picker--focused .picker__day--highlighted,.picker--focused .picker__list-item--highlighted,.picker--focused .picker__list-item--selected,.picker__day--highlighted:hover,.picker__day--infocus:hover,.picker__day--outfocus:hover,.picker__footer,.picker__header,.picker__list-item--highlighted:hover,.picker__list-item--selected,.picker__list-item--selected:hover,.picker__list-item:hover,.um .um-field-group-head:hover{background:#44b0ec}.um{margin-left:auto!important;margin-right:auto!important}.um input[type=submit]:disabled:hover{background:#3ba1da}.um a.um-button,.um a.um-button.um-disabled:active,.um a.um-button.um-disabled:focus,.um a.um-button.um-disabled:hover,.um input[type=submit].um-button,.um input[type=submit].um-button:focus{background:#3ba1da}.um a.um-link{color:#3ba1da}.um a.um-button:hover,.um input[type=submit].um-button:hover{background-color:#44b0ec}.um a.um-link-hvr:hover,.um a.um-link:hover{color:#44b0ec}.um .um-button{color:#fff}.um .um-button:before{color:#fff}.um .um-button.um-alt,.um input[type=submit].um-button.um-alt{background:#eee}.um .um-button.um-alt:hover,.um input[type=submit].um-button.um-alt:hover{background:#e5e5e5}.um .um-button.um-alt,.um input[type=submit].um-button.um-alt{color:#666}.um .um-button.um-alt:before,.um input[type=submit].um-button.um-alt:before{color:#666}.um .um-tip{color:#ccc}.um .um-field-label{color:#555}.um .um-row.um-customized-row .um-field-label{color:inherit}.select2-container .select2-choice,.select2-container-multi .select2-choices,.select2-drop,.select2-drop-active,.select2-drop.select2-drop-above,.um .um-form input[type=number],.um .um-form input[type=password],.um .um-form input[type=search],.um .um-form input[type=tel],.um .um-form input[type=text],.um .um-form textarea,.um .upload-progress{border:1px solid #ddd!important}.um .um-form .select2-container-multi .select2-choices .select2-search-field input[type=text]{border:none!important}.um .um-form .um-datepicker.picker__input.picker__input--active,.um .um-form .um-datepicker.picker__input.picker__input--target,.um .um-form input[type=number]:focus,.um .um-form input[type=password]:focus,.um .um-form input[type=search]:focus,.um .um-form input[type=tel]:focus,.um .um-form input[type=text]:focus,.um .um-form textarea:focus{border:1px solid #bbb!important}.select2-container .select2-choice,.select2-container-multi .select2-choices,.um .um-form input[type=number],.um .um-form input[type=password],.um .um-form input[type=search],.um .um-form input[type=tel],.um .um-form input[type=text],.um .um-form textarea{background-color:#fff}.um .um-form input[type=number]:focus,.um .um-form input[type=password]:focus,.um .um-form input[type=search]:focus,.um .um-form input[type=tel]:focus,.um .um-form input[type=text]:focus,.um .um-form textarea:focus{background-color:#fff}.um .um-form input[type=password],.um .um-form input[type=search],.um .um-form input[type=tel],.um .um-form input[type=text],.um .um-form textarea{color:#666}.um .um-form input:-webkit-autofill{-webkit-box-shadow:0 0 0 50px #fff inset;-webkit-text-fill-color:#666}.um .um-form input:-webkit-autofill:focus{-webkit-box-shadow:none,0 0 0 50px #fff inset;-webkit-text-fill-color:#666}.um .um-form ::-webkit-input-placeholder{color:#aaa;opacity:1!important}.um .um-form ::-moz-placeholder{color:#aaa;opacity:1!important}.um .um-form ::-moz-placeholder{color:#aaa;opacity:1!important}.um .um-form ::-ms-input-placeholder{color:#aaa;opacity:1!important}.select2-container-multi .select2-choices .select2-search-field input,.select2-default,.select2-default *{color:#aaa}.select2-container .select2-choice .select2-arrow:before,.select2-search-choice-close:before,.select2-search:before,.um .um-field-icon i{color:#aaa}.um span.um-req{color:#aaa}.um .um-profile-photo a.um-profile-photo-img,.um .um-profile-photo img,.um .um-profile-photo span.um-profile-photo-overlay{-moz-border-radius:999px!important;-webkit-border-radius:999px!important;border-radius:999px!important}.um-profile.um .um-profile-meta{color:#999}.um-profile.um .um-name a{color:#555}.um-profile.um .um-name a:hover{color:#444}.um-profile.um .um-profile-headericon a{color:#aaa}.um-profile.um .um-profile-edit-a.active,.um-profile.um .um-profile-headericon a:hover{color:#3ba1da}
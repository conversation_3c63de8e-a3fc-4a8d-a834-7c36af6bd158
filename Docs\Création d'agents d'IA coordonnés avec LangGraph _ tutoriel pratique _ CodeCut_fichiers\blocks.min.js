/*! This file is auto-generated */
(()=>{var e={1030:function(e,t,r){var n;/*! showdown v 1.9.1 - 02-11-2019 */
(function(){function o(e){"use strict";var t={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,description:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,description:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,description:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},completeHTMLDocument:{defaultValue:!1,description:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,description:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,description:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(t));var r={};for(var n in t)t.hasOwnProperty(n)&&(r[n]=t[n].defaultValue);return r}var a={},i={},s={},c=o(!0),l="vanilla",u={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:o(!0),allOn:function(){"use strict";var e=o(!0),t={};for(var r in e)e.hasOwnProperty(r)&&(t[r]=!0);return t}()};function d(e,t){"use strict";var r=t?"Error in "+t+" extension->":"Error in unnamed extension",n={valid:!0,error:""};a.helper.isArray(e)||(e=[e]);for(var o=0;o<e.length;++o){var i=r+" sub-extension "+o+": ",s=e[o];if("object"!=typeof s)return n.valid=!1,n.error=i+"must be an object, but "+typeof s+" given",n;if(!a.helper.isString(s.type))return n.valid=!1,n.error=i+'property "type" must be a string, but '+typeof s.type+" given",n;var c=s.type=s.type.toLowerCase();if("language"===c&&(c=s.type="lang"),"html"===c&&(c=s.type="output"),"lang"!==c&&"output"!==c&&"listener"!==c)return n.valid=!1,n.error=i+"type "+c+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',n;if("listener"===c){if(a.helper.isUndefined(s.listeners))return n.valid=!1,n.error=i+'. Extensions of type "listener" must have a property called "listeners"',n}else if(a.helper.isUndefined(s.filter)&&a.helper.isUndefined(s.regex))return n.valid=!1,n.error=i+c+' extensions must define either a "regex" property or a "filter" method',n;if(s.listeners){if("object"!=typeof s.listeners)return n.valid=!1,n.error=i+'"listeners" property must be an object but '+typeof s.listeners+" given",n;for(var l in s.listeners)if(s.listeners.hasOwnProperty(l)&&"function"!=typeof s.listeners[l])return n.valid=!1,n.error=i+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+l+" must be a function but "+typeof s.listeners[l]+" given",n}if(s.filter){if("function"!=typeof s.filter)return n.valid=!1,n.error=i+'"filter" must be a function, but '+typeof s.filter+" given",n}else if(s.regex){if(a.helper.isString(s.regex)&&(s.regex=new RegExp(s.regex,"g")),!(s.regex instanceof RegExp))return n.valid=!1,n.error=i+'"regex" property must either be a string or a RegExp object, but '+typeof s.regex+" given",n;if(a.helper.isUndefined(s.replace))return n.valid=!1,n.error=i+'"regex" extensions must implement a replace string or function',n}}return n}function p(e,t){"use strict";return"¨E"+t.charCodeAt(0)+"E"}a.helper={},a.extensions={},a.setOption=function(e,t){"use strict";return c[e]=t,this},a.getOption=function(e){"use strict";return c[e]},a.getOptions=function(){"use strict";return c},a.resetOptions=function(){"use strict";c=o(!0)},a.setFlavor=function(e){"use strict";if(!u.hasOwnProperty(e))throw Error(e+" flavor was not found");a.resetOptions();var t=u[e];for(var r in l=e,t)t.hasOwnProperty(r)&&(c[r]=t[r])},a.getFlavor=function(){"use strict";return l},a.getFlavorOptions=function(e){"use strict";if(u.hasOwnProperty(e))return u[e]},a.getDefaultOptions=function(e){"use strict";return o(e)},a.subParser=function(e,t){"use strict";if(a.helper.isString(e)){if(void 0===t){if(i.hasOwnProperty(e))return i[e];throw Error("SubParser named "+e+" not registered!")}i[e]=t}},a.extension=function(e,t){"use strict";if(!a.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=a.helper.stdExtName(e),a.helper.isUndefined(t)){if(!s.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return s[e]}"function"==typeof t&&(t=t()),a.helper.isArray(t)||(t=[t]);var r=d(t,e);if(!r.valid)throw Error(r.error);s[e]=t},a.getAllExtensions=function(){"use strict";return s},a.removeExtension=function(e){"use strict";delete s[e]},a.resetExtensions=function(){"use strict";s={}},a.validateExtension=function(e){"use strict";var t=d(e,null);return!!t.valid||(console.warn(t.error),!1)},a.hasOwnProperty("helper")||(a.helper={}),a.helper.isString=function(e){"use strict";return"string"==typeof e||e instanceof String},a.helper.isFunction=function(e){"use strict";return e&&"[object Function]"==={}.toString.call(e)},a.helper.isArray=function(e){"use strict";return Array.isArray(e)},a.helper.isUndefined=function(e){"use strict";return void 0===e},a.helper.forEach=function(e,t){"use strict";if(a.helper.isUndefined(e))throw new Error("obj param is required");if(a.helper.isUndefined(t))throw new Error("callback param is required");if(!a.helper.isFunction(t))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(t);else if(a.helper.isArray(e))for(var r=0;r<e.length;r++)t(e[r],r,e);else{if("object"!=typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var n in e)e.hasOwnProperty(n)&&t(e[n],n,e)}},a.helper.stdExtName=function(e){"use strict";return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},a.helper.escapeCharactersCallback=p,a.helper.escapeCharacters=function(e,t,r){"use strict";var n="(["+t.replace(/([\[\]\\])/g,"\\$1")+"])";r&&(n="\\\\"+n);var o=new RegExp(n,"g");return e=e.replace(o,p)},a.helper.unescapeHTMLEntities=function(e){"use strict";return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};var f=function(e,t,r,n){"use strict";var o,a,i,s,c,l=n||"",u=l.indexOf("g")>-1,d=new RegExp(t+"|"+r,"g"+l.replace(/g/g,"")),p=new RegExp(t,l.replace(/g/g,"")),f=[];do{for(o=0;i=d.exec(e);)if(p.test(i[0]))o++||(s=(a=d.lastIndex)-i[0].length);else if(o&&! --o){c=i.index+i[0].length;var h={left:{start:s,end:a},match:{start:a,end:i.index},right:{start:i.index,end:c},wholeMatch:{start:s,end:c}};if(f.push(h),!u)return f}}while(o&&(d.lastIndex=a));return f};a.helper.matchRecursiveRegExp=function(e,t,r,n){"use strict";for(var o=f(e,t,r,n),a=[],i=0;i<o.length;++i)a.push([e.slice(o[i].wholeMatch.start,o[i].wholeMatch.end),e.slice(o[i].match.start,o[i].match.end),e.slice(o[i].left.start,o[i].left.end),e.slice(o[i].right.start,o[i].right.end)]);return a},a.helper.replaceRecursiveRegExp=function(e,t,r,n,o){"use strict";if(!a.helper.isFunction(t)){var i=t;t=function(){return i}}var s=f(e,r,n,o),c=e,l=s.length;if(l>0){var u=[];0!==s[0].wholeMatch.start&&u.push(e.slice(0,s[0].wholeMatch.start));for(var d=0;d<l;++d)u.push(t(e.slice(s[d].wholeMatch.start,s[d].wholeMatch.end),e.slice(s[d].match.start,s[d].match.end),e.slice(s[d].left.start,s[d].left.end),e.slice(s[d].right.start,s[d].right.end))),d<l-1&&u.push(e.slice(s[d].wholeMatch.end,s[d+1].wholeMatch.start));s[l-1].wholeMatch.end<e.length&&u.push(e.slice(s[l-1].wholeMatch.end)),c=u.join("")}return c},a.helper.regexIndexOf=function(e,t,r){"use strict";if(!a.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(t instanceof RegExp==!1)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var n=e.substring(r||0).search(t);return n>=0?n+(r||0):n},a.helper.splitAtIndex=function(e,t){"use strict";if(!a.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,t),e.substring(t)]},a.helper.encodeEmailAddress=function(e){"use strict";var t=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e=e.replace(/./g,(function(e){if("@"===e)e=t[Math.floor(2*Math.random())](e);else{var r=Math.random();e=r>.9?t[2](e):r>.45?t[1](e):t[0](e)}return e}))},a.helper.padEnd=function(e,t,r){"use strict";return t|=0,r=String(r||" "),e.length>t?String(e):((t-=e.length)>r.length&&(r+=r.repeat(t/r.length)),String(e)+r.slice(0,t))},"undefined"==typeof console&&(console={warn:function(e){"use strict";alert(e)},log:function(e){"use strict";alert(e)},error:function(e){"use strict";throw e}}),a.helper.regexes={asteriskDashAndColon:/([*_:~])/g},a.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},a.Converter=function(e){"use strict";var t={},r=[],n=[],o={},i=l,p={parsed:{},raw:"",format:""};function f(e,t){if(t=t||null,a.helper.isString(e)){if(t=e=a.helper.stdExtName(e),a.extensions[e])return console.warn("DEPRECATION WARNING: "+e+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void function(e,t){"function"==typeof e&&(e=e(new a.Converter));a.helper.isArray(e)||(e=[e]);var o=d(e,t);if(!o.valid)throw Error(o.error);for(var i=0;i<e.length;++i)switch(e[i].type){case"lang":r.push(e[i]);break;case"output":n.push(e[i]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(a.extensions[e],e);if(a.helper.isUndefined(s[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=s[e]}"function"==typeof e&&(e=e()),a.helper.isArray(e)||(e=[e]);var o=d(e,t);if(!o.valid)throw Error(o.error);for(var i=0;i<e.length;++i){switch(e[i].type){case"lang":r.push(e[i]);break;case"output":n.push(e[i])}if(e[i].hasOwnProperty("listeners"))for(var c in e[i].listeners)e[i].listeners.hasOwnProperty(c)&&h(c,e[i].listeners[c])}}function h(e,t){if(!a.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!=typeof t)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof t+" given");o.hasOwnProperty(e)||(o[e]=[]),o[e].push(t)}!function(){for(var r in e=e||{},c)c.hasOwnProperty(r)&&(t[r]=c[r]);if("object"!=typeof e)throw Error("Converter expects the passed parameter to be an object, but "+typeof e+" was passed instead.");for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);t.extensions&&a.helper.forEach(t.extensions,f)}(),this._dispatch=function(e,t,r,n){if(o.hasOwnProperty(e))for(var a=0;a<o[e].length;++a){var i=o[e][a](e,t,this,r,n);i&&void 0!==i&&(t=i)}return t},this.listen=function(e,t){return h(e,t),this},this.makeHtml=function(e){if(!e)return e;var o={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:r,outputModifiers:n,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return e=(e=(e=(e=(e=e.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),t.smartIndentationFix&&(e=function(e){var t=e.match(/^\s*/)[0].length,r=new RegExp("^\\s{0,"+t+"}","gm");return e.replace(r,"")}(e)),e="\n\n"+e+"\n\n",e=(e=a.subParser("detab")(e,t,o)).replace(/^[ \t]+$/gm,""),a.helper.forEach(r,(function(r){e=a.subParser("runExtension")(r,e,t,o)})),e=a.subParser("metadata")(e,t,o),e=a.subParser("hashPreCodeTags")(e,t,o),e=a.subParser("githubCodeBlocks")(e,t,o),e=a.subParser("hashHTMLBlocks")(e,t,o),e=a.subParser("hashCodeTags")(e,t,o),e=a.subParser("stripLinkDefinitions")(e,t,o),e=a.subParser("blockGamut")(e,t,o),e=a.subParser("unhashHTMLSpans")(e,t,o),e=(e=(e=a.subParser("unescapeSpecialChars")(e,t,o)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),e=a.subParser("completeHTMLDocument")(e,t,o),a.helper.forEach(n,(function(r){e=a.subParser("runExtension")(r,e,t,o)})),p=o.metadata,e},this.makeMarkdown=this.makeMd=function(e,t){if(e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<"),!t){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");t=window.document}var r=t.createElement("div");r.innerHTML=e;var n={preList:function(e){for(var t=e.querySelectorAll("pre"),r=[],n=0;n<t.length;++n)if(1===t[n].childElementCount&&"code"===t[n].firstChild.tagName.toLowerCase()){var o=t[n].firstChild.innerHTML.trim(),i=t[n].firstChild.getAttribute("data-language")||"";if(""===i)for(var s=t[n].firstChild.className.split(" "),c=0;c<s.length;++c){var l=s[c].match(/^language-(.+)$/);if(null!==l){i=l[1];break}}o=a.helper.unescapeHTMLEntities(o),r.push(o),t[n].outerHTML='<precode language="'+i+'" precodenum="'+n.toString()+'"></precode>'}else r.push(t[n].innerHTML),t[n].innerHTML="",t[n].setAttribute("prenum",n.toString());return r}(r)};!function e(t){for(var r=0;r<t.childNodes.length;++r){var n=t.childNodes[r];3===n.nodeType?/\S/.test(n.nodeValue)?(n.nodeValue=n.nodeValue.split("\n").join(" "),n.nodeValue=n.nodeValue.replace(/(\s)+/g,"$1")):(t.removeChild(n),--r):1===n.nodeType&&e(n)}}(r);for(var o=r.childNodes,i="",s=0;s<o.length;s++)i+=a.subParser("makeMarkdown.node")(o[s],n);return i},this.setOption=function(e,r){t[e]=r},this.getOption=function(e){return t[e]},this.getOptions=function(){return t},this.addExtension=function(e,t){f(e,t=t||null)},this.useExtension=function(e){f(e)},this.setFlavor=function(e){if(!u.hasOwnProperty(e))throw Error(e+" flavor was not found");var r=u[e];for(var n in i=e,r)r.hasOwnProperty(n)&&(t[n]=r[n])},this.getFlavor=function(){return i},this.removeExtension=function(e){a.helper.isArray(e)||(e=[e]);for(var t=0;t<e.length;++t){for(var o=e[t],i=0;i<r.length;++i)r[i]===o&&r[i].splice(i,1);for(;0<n.length;++i)n[0]===o&&n[0].splice(i,1)}},this.getAllExtensions=function(){return{language:r,output:n}},this.getMetadata=function(e){return e?p.raw:p.parsed},this.getMetadataFormat=function(){return p.format},this._setMetadataPair=function(e,t){p.parsed[e]=t},this._setMetadataFormat=function(e){p.format=e},this._setMetadataRaw=function(e){p.raw=e}},a.subParser("anchors",(function(e,t,r){"use strict";var n=function(e,n,o,i,s,c,l){if(a.helper.isUndefined(l)&&(l=""),o=o.toLowerCase(),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)i="";else if(!i){if(o||(o=n.toLowerCase().replace(/ ?\n/g," ")),i="#"+o,a.helper.isUndefined(r.gUrls[o]))return e;i=r.gUrls[o],a.helper.isUndefined(r.gTitles[o])||(l=r.gTitles[o])}var u='<a href="'+(i=i.replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback))+'"';return""!==l&&null!==l&&(u+=' title="'+(l=(l=l.replace(/"/g,"&quot;")).replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback))+'"'),t.openLinksInNewWindow&&!/^#/.test(i)&&(u+=' rel="noopener noreferrer" target="¨E95Eblank"'),u+=">"+n+"</a>"};return e=(e=(e=(e=(e=r.converter._dispatch("anchors.before",e,t,r)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,n)).replace(/\[([^\[\]]+)]()()()()()/g,n),t.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,(function(e,r,n,o,i){if("\\"===n)return r+o;if(!a.helper.isString(t.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var s=t.ghMentionsLink.replace(/\{u}/g,i),c="";return t.openLinksInNewWindow&&(c=' rel="noopener noreferrer" target="¨E95Eblank"'),r+'<a href="'+s+'"'+c+">"+o+"</a>"}))),e=r.converter._dispatch("anchors.after",e,t,r)}));var h=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,g=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,m=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,b=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,_=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,y=function(e){"use strict";return function(t,r,n,o,i,s,c){var l=n=n.replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback),u="",d="",p=r||"",f=c||"";return/^www\./i.test(n)&&(n=n.replace(/^www\./i,"http://www.")),e.excludeTrailingPunctuationFromURLs&&s&&(u=s),e.openLinksInNewWindow&&(d=' rel="noopener noreferrer" target="¨E95Eblank"'),p+'<a href="'+n+'"'+d+">"+l+"</a>"+u+f}},k=function(e,t){"use strict";return function(r,n,o){var i="mailto:";return n=n||"",o=a.subParser("unescapeSpecialChars")(o,e,t),e.encodeEmails?(i=a.helper.encodeEmailAddress(i+o),o=a.helper.encodeEmailAddress(o)):i+=o,n+'<a href="'+i+'">'+o+"</a>"}};a.subParser("autoLinks",(function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("autoLinks.before",e,t,r)).replace(m,y(t))).replace(_,k(t,r)),e=r.converter._dispatch("autoLinks.after",e,t,r)})),a.subParser("simplifiedAutoLinks",(function(e,t,r){"use strict";return t.simplifiedAutoLink?(e=r.converter._dispatch("simplifiedAutoLinks.before",e,t,r),e=(e=t.excludeTrailingPunctuationFromURLs?e.replace(g,y(t)):e.replace(h,y(t))).replace(b,k(t,r)),e=r.converter._dispatch("simplifiedAutoLinks.after",e,t,r)):e})),a.subParser("blockGamut",(function(e,t,r){"use strict";return e=r.converter._dispatch("blockGamut.before",e,t,r),e=a.subParser("blockQuotes")(e,t,r),e=a.subParser("headers")(e,t,r),e=a.subParser("horizontalRule")(e,t,r),e=a.subParser("lists")(e,t,r),e=a.subParser("codeBlocks")(e,t,r),e=a.subParser("tables")(e,t,r),e=a.subParser("hashHTMLBlocks")(e,t,r),e=a.subParser("paragraphs")(e,t,r),e=r.converter._dispatch("blockGamut.after",e,t,r)})),a.subParser("blockQuotes",(function(e,t,r){"use strict";e=r.converter._dispatch("blockQuotes.before",e,t,r),e+="\n\n";var n=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return t.splitAdjacentBlockquotes&&(n=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=e.replace(n,(function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),e=a.subParser("githubCodeBlocks")(e,t,r),e=(e=(e=a.subParser("blockGamut")(e,t,r)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,(function(e,t){var r=t;return r=(r=r.replace(/^  /gm,"¨0")).replace(/¨0/g,"")})),a.subParser("hashBlock")("<blockquote>\n"+e+"\n</blockquote>",t,r)})),e=r.converter._dispatch("blockQuotes.after",e,t,r)})),a.subParser("codeBlocks",(function(e,t,r){"use strict";e=r.converter._dispatch("codeBlocks.before",e,t,r);return e=(e=(e+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,(function(e,n,o){var i=n,s=o,c="\n";return i=a.subParser("outdent")(i,t,r),i=a.subParser("encodeCode")(i,t,r),i=(i=(i=a.subParser("detab")(i,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""),t.omitExtraWLInCodeBlocks&&(c=""),i="<pre><code>"+i+c+"</code></pre>",a.subParser("hashBlock")(i,t,r)+s}))).replace(/¨0/,""),e=r.converter._dispatch("codeBlocks.after",e,t,r)})),a.subParser("codeSpans",(function(e,t,r){"use strict";return void 0===(e=r.converter._dispatch("codeSpans.before",e,t,r))&&(e=""),e=e.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,(function(e,n,o,i){var s=i;return s=(s=s.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),s=n+"<code>"+(s=a.subParser("encodeCode")(s,t,r))+"</code>",s=a.subParser("hashHTMLSpans")(s,t,r)})),e=r.converter._dispatch("codeSpans.after",e,t,r)})),a.subParser("completeHTMLDocument",(function(e,t,r){"use strict";if(!t.completeHTMLDocument)return e;e=r.converter._dispatch("completeHTMLDocument.before",e,t,r);var n="html",o="<!DOCTYPE HTML>\n",a="",i='<meta charset="utf-8">\n',s="",c="";for(var l in void 0!==r.metadata.parsed.doctype&&(o="<!DOCTYPE "+r.metadata.parsed.doctype+">\n","html"!==(n=r.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==n||(i='<meta charset="utf-8">')),r.metadata.parsed)if(r.metadata.parsed.hasOwnProperty(l))switch(l.toLowerCase()){case"doctype":break;case"title":a="<title>"+r.metadata.parsed.title+"</title>\n";break;case"charset":i="html"===n||"html5"===n?'<meta charset="'+r.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+r.metadata.parsed.charset+'">\n';break;case"language":case"lang":s=' lang="'+r.metadata.parsed[l]+'"',c+='<meta name="'+l+'" content="'+r.metadata.parsed[l]+'">\n';break;default:c+='<meta name="'+l+'" content="'+r.metadata.parsed[l]+'">\n'}return e=o+"<html"+s+">\n<head>\n"+a+i+c+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",e=r.converter._dispatch("completeHTMLDocument.after",e,t,r)})),a.subParser("detab",(function(e,t,r){"use strict";return e=(e=(e=(e=(e=(e=r.converter._dispatch("detab.before",e,t,r)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,(function(e,t){for(var r=t,n=4-r.length%4,o=0;o<n;o++)r+=" ";return r}))).replace(/¨A/g,"    ")).replace(/¨B/g,""),e=r.converter._dispatch("detab.after",e,t,r)})),a.subParser("ellipsis",(function(e,t,r){"use strict";return e=(e=r.converter._dispatch("ellipsis.before",e,t,r)).replace(/\.\.\./g,"…"),e=r.converter._dispatch("ellipsis.after",e,t,r)})),a.subParser("emoji",(function(e,t,r){"use strict";if(!t.emoji)return e;return e=(e=r.converter._dispatch("emoji.before",e,t,r)).replace(/:([\S]+?):/g,(function(e,t){return a.helper.emojis.hasOwnProperty(t)?a.helper.emojis[t]:e})),e=r.converter._dispatch("emoji.after",e,t,r)})),a.subParser("encodeAmpsAndAngles",(function(e,t,r){"use strict";return e=(e=(e=(e=(e=r.converter._dispatch("encodeAmpsAndAngles.before",e,t,r)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),e=r.converter._dispatch("encodeAmpsAndAngles.after",e,t,r)})),a.subParser("encodeBackslashEscapes",(function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("encodeBackslashEscapes.before",e,t,r)).replace(/\\(\\)/g,a.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|-])/g,a.helper.escapeCharactersCallback),e=r.converter._dispatch("encodeBackslashEscapes.after",e,t,r)})),a.subParser("encodeCode",(function(e,t,r){"use strict";return e=(e=r.converter._dispatch("encodeCode.before",e,t,r)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,a.helper.escapeCharactersCallback),e=r.converter._dispatch("encodeCode.after",e,t,r)})),a.subParser("escapeSpecialCharsWithinTagAttributes",(function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,t,r)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,(function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,a.helper.escapeCharactersCallback)}))).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,(function(e){return e.replace(/([\\`*_~=|])/g,a.helper.escapeCharactersCallback)})),e=r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,t,r)})),a.subParser("githubCodeBlocks",(function(e,t,r){"use strict";return t.ghCodeBlocks?(e=r.converter._dispatch("githubCodeBlocks.before",e,t,r),e=(e=(e+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,(function(e,n,o,i){var s=t.omitExtraWLInCodeBlocks?"":"\n";return i=a.subParser("encodeCode")(i,t,r),i="<pre><code"+(o?' class="'+o+" language-"+o+'"':"")+">"+(i=(i=(i=a.subParser("detab")(i,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+s+"</code></pre>",i=a.subParser("hashBlock")(i,t,r),"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:i})-1)+"G\n\n"}))).replace(/¨0/,""),r.converter._dispatch("githubCodeBlocks.after",e,t,r)):e})),a.subParser("hashBlock",(function(e,t,r){"use strict";return e=(e=r.converter._dispatch("hashBlock.before",e,t,r)).replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n",e=r.converter._dispatch("hashBlock.after",e,t,r)})),a.subParser("hashCodeTags",(function(e,t,r){"use strict";e=r.converter._dispatch("hashCodeTags.before",e,t,r);return e=a.helper.replaceRecursiveRegExp(e,(function(e,n,o,i){var s=o+a.subParser("encodeCode")(n,t,r)+i;return"¨C"+(r.gHtmlSpans.push(s)-1)+"C"}),"<code\\b[^>]*>","</code>","gim"),e=r.converter._dispatch("hashCodeTags.after",e,t,r)})),a.subParser("hashElement",(function(e,t,r){"use strict";return function(e,t){var n=t;return n=(n=(n=n.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),n="\n\n¨K"+(r.gHtmlBlocks.push(n)-1)+"K\n\n"}})),a.subParser("hashHTMLBlocks",(function(e,t,r){"use strict";e=r.converter._dispatch("hashHTMLBlocks.before",e,t,r);var n=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],o=function(e,t,n,o){var a=e;return-1!==n.search(/\bmarkdown\b/)&&(a=n+r.converter.makeHtml(t)+o),"\n\n¨K"+(r.gHtmlBlocks.push(a)-1)+"K\n\n"};t.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,(function(e,t){return"&lt;"+t+"&gt;"})));for(var i=0;i<n.length;++i)for(var s,c=new RegExp("^ {0,3}(<"+n[i]+"\\b[^>]*>)","im"),l="<"+n[i]+"\\b[^>]*>",u="</"+n[i]+">";-1!==(s=a.helper.regexIndexOf(e,c));){var d=a.helper.splitAtIndex(e,s),p=a.helper.replaceRecursiveRegExp(d[1],o,l,u,"im");if(p===d[1])break;e=d[0].concat(p)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,a.subParser("hashElement")(e,t,r)),e=(e=a.helper.replaceRecursiveRegExp(e,(function(e){return"\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n"}),"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,a.subParser("hashElement")(e,t,r)),e=r.converter._dispatch("hashHTMLBlocks.after",e,t,r)})),a.subParser("hashHTMLSpans",(function(e,t,r){"use strict";function n(e){return"¨C"+(r.gHtmlSpans.push(e)-1)+"C"}return e=(e=(e=(e=(e=r.converter._dispatch("hashHTMLSpans.before",e,t,r)).replace(/<[^>]+?\/>/gi,(function(e){return n(e)}))).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,(function(e){return n(e)}))).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,(function(e){return n(e)}))).replace(/<[^>]+?>/gi,(function(e){return n(e)})),e=r.converter._dispatch("hashHTMLSpans.after",e,t,r)})),a.subParser("unhashHTMLSpans",(function(e,t,r){"use strict";e=r.converter._dispatch("unhashHTMLSpans.before",e,t,r);for(var n=0;n<r.gHtmlSpans.length;++n){for(var o=r.gHtmlSpans[n],a=0;/¨C(\d+)C/.test(o);){var i=RegExp.$1;if(o=o.replace("¨C"+i+"C",r.gHtmlSpans[i]),10===a){console.error("maximum nesting of 10 spans reached!!!");break}++a}e=e.replace("¨C"+n+"C",o)}return e=r.converter._dispatch("unhashHTMLSpans.after",e,t,r)})),a.subParser("hashPreCodeTags",(function(e,t,r){"use strict";e=r.converter._dispatch("hashPreCodeTags.before",e,t,r);return e=a.helper.replaceRecursiveRegExp(e,(function(e,n,o,i){var s=o+a.subParser("encodeCode")(n,t,r)+i;return"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:s})-1)+"G\n\n"}),"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),e=r.converter._dispatch("hashPreCodeTags.after",e,t,r)})),a.subParser("headers",(function(e,t,r){"use strict";e=r.converter._dispatch("headers.before",e,t,r);var n=isNaN(parseInt(t.headerLevelStart))?1:parseInt(t.headerLevelStart),o=t.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,i=t.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;e=(e=e.replace(o,(function(e,o){var i=a.subParser("spanGamut")(o,t,r),s=t.noHeaderId?"":' id="'+c(o)+'"',l="<h"+n+s+">"+i+"</h"+n+">";return a.subParser("hashBlock")(l,t,r)}))).replace(i,(function(e,o){var i=a.subParser("spanGamut")(o,t,r),s=t.noHeaderId?"":' id="'+c(o)+'"',l=n+1,u="<h"+l+s+">"+i+"</h"+l+">";return a.subParser("hashBlock")(u,t,r)}));var s=t.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function c(e){var n,o;if(t.customizedHeaderId){var i=e.match(/\{([^{]+?)}\s*$/);i&&i[1]&&(e=i[1])}return n=e,o=a.helper.isString(t.prefixHeaderId)?t.prefixHeaderId:!0===t.prefixHeaderId?"section-":"",t.rawPrefixHeaderId||(n=o+n),n=t.ghCompatibleHeaderId?n.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():t.rawHeaderId?n.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():n.replace(/[^\w]/g,"").toLowerCase(),t.rawPrefixHeaderId&&(n=o+n),r.hashLinkCounts[n]?n=n+"-"+r.hashLinkCounts[n]++:r.hashLinkCounts[n]=1,n}return e=e.replace(s,(function(e,o,i){var s=i;t.customizedHeaderId&&(s=i.replace(/\s?\{([^{]+?)}\s*$/,""));var l=a.subParser("spanGamut")(s,t,r),u=t.noHeaderId?"":' id="'+c(i)+'"',d=n-1+o.length,p="<h"+d+u+">"+l+"</h"+d+">";return a.subParser("hashBlock")(p,t,r)})),e=r.converter._dispatch("headers.after",e,t,r)})),a.subParser("horizontalRule",(function(e,t,r){"use strict";e=r.converter._dispatch("horizontalRule.before",e,t,r);var n=a.subParser("hashBlock")("<hr />",t,r);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,n)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,n)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,n),e=r.converter._dispatch("horizontalRule.after",e,t,r)})),a.subParser("images",(function(e,t,r){"use strict";function n(e,t,n,o,i,s,c,l){var u=r.gUrls,d=r.gTitles,p=r.gDimensions;if(n=n.toLowerCase(),l||(l=""),e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)o="";else if(""===o||null===o){if(""!==n&&null!==n||(n=t.toLowerCase().replace(/ ?\n/g," ")),o="#"+n,a.helper.isUndefined(u[n]))return e;o=u[n],a.helper.isUndefined(d[n])||(l=d[n]),a.helper.isUndefined(p[n])||(i=p[n].width,s=p[n].height)}t=t.replace(/"/g,"&quot;").replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback);var f='<img src="'+(o=o.replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback))+'" alt="'+t+'"';return l&&a.helper.isString(l)&&(f+=' title="'+(l=l.replace(/"/g,"&quot;").replace(a.helper.regexes.asteriskDashAndColon,a.helper.escapeCharactersCallback))+'"'),i&&s&&(f+=' width="'+(i="*"===i?"auto":i)+'"',f+=' height="'+(s="*"===s?"auto":s)+'"'),f+=" />"}return e=(e=(e=(e=(e=(e=r.converter._dispatch("images.before",e,t,r)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,n)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,(function(e,t,r,o,a,i,s,c){return n(e,t,r,o=o.replace(/\s/g,""),a,i,s,c)}))).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,n)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,n)).replace(/!\[([^\[\]]+)]()()()()()/g,n),e=r.converter._dispatch("images.after",e,t,r)})),a.subParser("italicsAndBold",(function(e,t,r){"use strict";function n(e,t,r){return t+e+r}return e=r.converter._dispatch("italicsAndBold.before",e,t,r),e=t.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,t){return n(t,"<strong><em>","</em></strong>")}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,t){return n(t,"<strong>","</strong>")}))).replace(/\b_(\S[\s\S]*?)_\b/g,(function(e,t){return n(t,"<em>","</em>")})):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,t){return/\S$/.test(t)?n(t,"<strong><em>","</em></strong>"):e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,t){return/\S$/.test(t)?n(t,"<strong>","</strong>"):e}))).replace(/_([^\s_][\s\S]*?)_/g,(function(e,t){return/\S$/.test(t)?n(t,"<em>","</em>"):e})),e=t.literalMidWordAsterisks?(e=(e=e.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,(function(e,t,r){return n(r,t+"<strong><em>","</em></strong>")}))).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,(function(e,t,r){return n(r,t+"<strong>","</strong>")}))).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,(function(e,t,r){return n(r,t+"<em>","</em>")})):(e=(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,(function(e,t){return/\S$/.test(t)?n(t,"<strong><em>","</em></strong>"):e}))).replace(/\*\*(\S[\s\S]*?)\*\*/g,(function(e,t){return/\S$/.test(t)?n(t,"<strong>","</strong>"):e}))).replace(/\*([^\s*][\s\S]*?)\*/g,(function(e,t){return/\S$/.test(t)?n(t,"<em>","</em>"):e})),e=r.converter._dispatch("italicsAndBold.after",e,t,r)})),a.subParser("lists",(function(e,t,r){"use strict";function n(e,n){r.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var o=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,i=/\n[ \t]*\n(?!¨0)/.test(e+="¨0");return t.disableForced4SpacesIndentedSublists&&(o=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(o,(function(e,n,o,s,c,l,u){u=u&&""!==u.trim();var d=a.subParser("outdent")(c,t,r),p="";return l&&t.tasklists&&(p=' class="task-list-item" style="list-style-type: none;"',d=d.replace(/^[ \t]*\[(x|X| )?]/m,(function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return u&&(e+=" checked"),e+=">"}))),d=d.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,(function(e){return"¨A"+e})),n||d.search(/\n{2,}/)>-1?(d=a.subParser("githubCodeBlocks")(d,t,r),d=a.subParser("blockGamut")(d,t,r)):(d=(d=a.subParser("lists")(d,t,r)).replace(/\n$/,""),d=(d=a.subParser("hashHTMLBlocks")(d,t,r)).replace(/\n\n+/g,"\n\n"),d=i?a.subParser("paragraphs")(d,t,r):a.subParser("spanGamut")(d,t,r)),d="<li"+p+">"+(d=d.replace("¨A",""))+"</li>\n"}))).replace(/¨0/g,""),r.gListLevel--,n&&(e=e.replace(/\s+$/,"")),e}function o(e,t){if("ol"===t){var r=e.match(/^ *(\d+)\./);if(r&&"1"!==r[1])return' start="'+r[1]+'"'}return""}function i(e,r,a){var i=t.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,s=t.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,c="ul"===r?i:s,l="";if(-1!==e.search(c))!function t(u){var d=u.search(c),p=o(e,r);-1!==d?(l+="\n\n<"+r+p+">\n"+n(u.slice(0,d),!!a)+"</"+r+">\n",c="ul"===(r="ul"===r?"ol":"ul")?i:s,t(u.slice(d))):l+="\n\n<"+r+p+">\n"+n(u,!!a)+"</"+r+">\n"}(e);else{var u=o(e,r);l="\n\n<"+r+u+">\n"+n(e,!!a)+"</"+r+">\n"}return l}return e=r.converter._dispatch("lists.before",e,t,r),e+="¨0",e=(e=r.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,t,r){return i(t,r.search(/[*+-]/g)>-1?"ul":"ol",!0)})):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,t,r,n){return i(r,n.search(/[*+-]/g)>-1?"ul":"ol",!1)}))).replace(/¨0/,""),e=r.converter._dispatch("lists.after",e,t,r)})),a.subParser("metadata",(function(e,t,r){"use strict";if(!t.metadata)return e;function n(e){r.metadata.raw=e,(e=(e=e.replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,(function(e,t,n){return r.metadata.parsed[t]=n,""}))}return e=(e=(e=(e=r.converter._dispatch("metadata.before",e,t,r)).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,(function(e,t,r){return n(r),"¨M"}))).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,(function(e,t,o){return t&&(r.metadata.format=t),n(o),"¨M"}))).replace(/¨M/g,""),e=r.converter._dispatch("metadata.after",e,t,r)})),a.subParser("outdent",(function(e,t,r){"use strict";return e=(e=(e=r.converter._dispatch("outdent.before",e,t,r)).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),e=r.converter._dispatch("outdent.after",e,t,r)})),a.subParser("paragraphs",(function(e,t,r){"use strict";for(var n=(e=(e=(e=r.converter._dispatch("paragraphs.before",e,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),o=[],i=n.length,s=0;s<i;s++){var c=n[s];c.search(/¨(K|G)(\d+)\1/g)>=0?o.push(c):c.search(/\S/)>=0&&(c=(c=a.subParser("spanGamut")(c,t,r)).replace(/^([ \t]*)/g,"<p>"),c+="</p>",o.push(c))}for(i=o.length,s=0;s<i;s++){for(var l="",u=o[s],d=!1;/¨(K|G)(\d+)\1/.test(u);){var p=RegExp.$1,f=RegExp.$2;l=(l="K"===p?r.gHtmlBlocks[f]:d?a.subParser("encodeCode")(r.ghCodeBlocks[f].text,t,r):r.ghCodeBlocks[f].codeblock).replace(/\$/g,"$$$$"),u=u.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,l),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(u)&&(d=!0)}o[s]=u}return e=(e=(e=o.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),r.converter._dispatch("paragraphs.after",e,t,r)})),a.subParser("runExtension",(function(e,t,r,n){"use strict";if(e.filter)t=e.filter(t,n.converter,r);else if(e.regex){var o=e.regex;o instanceof RegExp||(o=new RegExp(o,"g")),t=t.replace(o,e.replace)}return t})),a.subParser("spanGamut",(function(e,t,r){"use strict";return e=r.converter._dispatch("spanGamut.before",e,t,r),e=a.subParser("codeSpans")(e,t,r),e=a.subParser("escapeSpecialCharsWithinTagAttributes")(e,t,r),e=a.subParser("encodeBackslashEscapes")(e,t,r),e=a.subParser("images")(e,t,r),e=a.subParser("anchors")(e,t,r),e=a.subParser("autoLinks")(e,t,r),e=a.subParser("simplifiedAutoLinks")(e,t,r),e=a.subParser("emoji")(e,t,r),e=a.subParser("underline")(e,t,r),e=a.subParser("italicsAndBold")(e,t,r),e=a.subParser("strikethrough")(e,t,r),e=a.subParser("ellipsis")(e,t,r),e=a.subParser("hashHTMLSpans")(e,t,r),e=a.subParser("encodeAmpsAndAngles")(e,t,r),t.simpleLineBreaks?/\n\n¨K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),e=r.converter._dispatch("spanGamut.after",e,t,r)})),a.subParser("strikethrough",(function(e,t,r){"use strict";return t.strikethrough&&(e=(e=r.converter._dispatch("strikethrough.before",e,t,r)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,(function(e,n){return function(e){return t.simplifiedAutoLink&&(e=a.subParser("simplifiedAutoLinks")(e,t,r)),"<del>"+e+"</del>"}(n)})),e=r.converter._dispatch("strikethrough.after",e,t,r)),e})),a.subParser("stripLinkDefinitions",(function(e,t,r){"use strict";var n=function(e,n,o,i,s,c,l){return n=n.toLowerCase(),o.match(/^data:.+?\/.+?;base64,/)?r.gUrls[n]=o.replace(/\s/g,""):r.gUrls[n]=a.subParser("encodeAmpsAndAngles")(o,t,r),c?c+l:(l&&(r.gTitles[n]=l.replace(/"|'/g,"&quot;")),t.parseImgDimensions&&i&&s&&(r.gDimensions[n]={width:i,height:s}),"")};return e=(e=(e=(e+="¨0").replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,n)).replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,n)).replace(/¨0/,"")})),a.subParser("tables",(function(e,t,r){"use strict";if(!t.tables)return e;function n(e,n){return"<td"+n+">"+a.subParser("spanGamut")(e,t,r)+"</td>\n"}function o(e){var o,i=e.split("\n");for(o=0;o<i.length;++o)/^ {0,3}\|/.test(i[o])&&(i[o]=i[o].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(i[o])&&(i[o]=i[o].replace(/\|[ \t]*$/,"")),i[o]=a.subParser("codeSpans")(i[o],t,r);var s,c,l,u,d=i[0].split("|").map((function(e){return e.trim()})),p=i[1].split("|").map((function(e){return e.trim()})),f=[],h=[],g=[],m=[];for(i.shift(),i.shift(),o=0;o<i.length;++o)""!==i[o].trim()&&f.push(i[o].split("|").map((function(e){return e.trim()})));if(d.length<p.length)return e;for(o=0;o<p.length;++o)g.push((s=p[o],/^:[ \t]*--*$/.test(s)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(s)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(s)?' style="text-align:center;"':""));for(o=0;o<d.length;++o)a.helper.isUndefined(g[o])&&(g[o]=""),h.push((c=d[o],l=g[o],u=void 0,u="",c=c.trim(),(t.tablesHeaderId||t.tableHeaderId)&&(u=' id="'+c.replace(/ /g,"_").toLowerCase()+'"'),"<th"+u+l+">"+(c=a.subParser("spanGamut")(c,t,r))+"</th>\n"));for(o=0;o<f.length;++o){for(var b=[],_=0;_<h.length;++_)a.helper.isUndefined(f[o][_]),b.push(n(f[o][_],g[_]));m.push(b)}return function(e,t){for(var r="<table>\n<thead>\n<tr>\n",n=e.length,o=0;o<n;++o)r+=e[o];for(r+="</tr>\n</thead>\n<tbody>\n",o=0;o<t.length;++o){r+="<tr>\n";for(var a=0;a<n;++a)r+=t[o][a];r+="</tr>\n"}return r+"</tbody>\n</table>\n"}(h,m)}return e=(e=(e=(e=r.converter._dispatch("tables.before",e,t,r)).replace(/\\(\|)/g,a.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,o)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,o),e=r.converter._dispatch("tables.after",e,t,r)})),a.subParser("underline",(function(e,t,r){"use strict";return t.underline?(e=r.converter._dispatch("underline.before",e,t,r),e=(e=t.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,t){return"<u>"+t+"</u>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,t){return"<u>"+t+"</u>"})):(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e}))).replace(/(_)/g,a.helper.escapeCharactersCallback),e=r.converter._dispatch("underline.after",e,t,r)):e})),a.subParser("unescapeSpecialChars",(function(e,t,r){"use strict";return e=(e=r.converter._dispatch("unescapeSpecialChars.before",e,t,r)).replace(/¨E(\d+)E/g,(function(e,t){var r=parseInt(t);return String.fromCharCode(r)})),e=r.converter._dispatch("unescapeSpecialChars.after",e,t,r)})),a.subParser("makeMarkdown.blockquote",(function(e,t){"use strict";var r="";if(e.hasChildNodes())for(var n=e.childNodes,o=n.length,i=0;i<o;++i){var s=a.subParser("makeMarkdown.node")(n[i],t);""!==s&&(r+=s)}return r="> "+(r=r.trim()).split("\n").join("\n> ")})),a.subParser("makeMarkdown.codeBlock",(function(e,t){"use strict";var r=e.getAttribute("language"),n=e.getAttribute("precodenum");return"```"+r+"\n"+t.preList[n]+"\n```"})),a.subParser("makeMarkdown.codeSpan",(function(e){"use strict";return"`"+e.innerHTML+"`"})),a.subParser("makeMarkdown.emphasis",(function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="*";for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);r+="*"}return r})),a.subParser("makeMarkdown.header",(function(e,t,r){"use strict";var n=new Array(r+1).join("#"),o="";if(e.hasChildNodes()){o=n+" ";for(var i=e.childNodes,s=i.length,c=0;c<s;++c)o+=a.subParser("makeMarkdown.node")(i[c],t)}return o})),a.subParser("makeMarkdown.hr",(function(){"use strict";return"---"})),a.subParser("makeMarkdown.image",(function(e){"use strict";var t="";return e.hasAttribute("src")&&(t+="!["+e.getAttribute("alt")+"](",t+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(t+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(t+=' "'+e.getAttribute("title")+'"'),t+=")"),t})),a.subParser("makeMarkdown.links",(function(e,t){"use strict";var r="";if(e.hasChildNodes()&&e.hasAttribute("href")){var n=e.childNodes,o=n.length;r="[";for(var i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);r+="](",r+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(r+=' "'+e.getAttribute("title")+'"'),r+=")"}return r})),a.subParser("makeMarkdown.list",(function(e,t,r){"use strict";var n="";if(!e.hasChildNodes())return"";for(var o=e.childNodes,i=o.length,s=e.getAttribute("start")||1,c=0;c<i;++c)if(void 0!==o[c].tagName&&"li"===o[c].tagName.toLowerCase()){n+=("ol"===r?s.toString()+". ":"- ")+a.subParser("makeMarkdown.listItem")(o[c],t),++s}return(n+="\n\x3c!-- --\x3e\n").trim()})),a.subParser("makeMarkdown.listItem",(function(e,t){"use strict";for(var r="",n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);return/\n$/.test(r)?r=r.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):r+="\n",r})),a.subParser("makeMarkdown.node",(function(e,t,r){"use strict";r=r||!1;var n="";if(3===e.nodeType)return a.subParser("makeMarkdown.txt")(e,t);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":r||(n=a.subParser("makeMarkdown.header")(e,t,1)+"\n\n");break;case"h2":r||(n=a.subParser("makeMarkdown.header")(e,t,2)+"\n\n");break;case"h3":r||(n=a.subParser("makeMarkdown.header")(e,t,3)+"\n\n");break;case"h4":r||(n=a.subParser("makeMarkdown.header")(e,t,4)+"\n\n");break;case"h5":r||(n=a.subParser("makeMarkdown.header")(e,t,5)+"\n\n");break;case"h6":r||(n=a.subParser("makeMarkdown.header")(e,t,6)+"\n\n");break;case"p":r||(n=a.subParser("makeMarkdown.paragraph")(e,t)+"\n\n");break;case"blockquote":r||(n=a.subParser("makeMarkdown.blockquote")(e,t)+"\n\n");break;case"hr":r||(n=a.subParser("makeMarkdown.hr")(e,t)+"\n\n");break;case"ol":r||(n=a.subParser("makeMarkdown.list")(e,t,"ol")+"\n\n");break;case"ul":r||(n=a.subParser("makeMarkdown.list")(e,t,"ul")+"\n\n");break;case"precode":r||(n=a.subParser("makeMarkdown.codeBlock")(e,t)+"\n\n");break;case"pre":r||(n=a.subParser("makeMarkdown.pre")(e,t)+"\n\n");break;case"table":r||(n=a.subParser("makeMarkdown.table")(e,t)+"\n\n");break;case"code":n=a.subParser("makeMarkdown.codeSpan")(e,t);break;case"em":case"i":n=a.subParser("makeMarkdown.emphasis")(e,t);break;case"strong":case"b":n=a.subParser("makeMarkdown.strong")(e,t);break;case"del":n=a.subParser("makeMarkdown.strikethrough")(e,t);break;case"a":n=a.subParser("makeMarkdown.links")(e,t);break;case"img":n=a.subParser("makeMarkdown.image")(e,t);break;default:n=e.outerHTML+"\n\n"}return n})),a.subParser("makeMarkdown.paragraph",(function(e,t){"use strict";var r="";if(e.hasChildNodes())for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);return r=r.trim()})),a.subParser("makeMarkdown.pre",(function(e,t){"use strict";var r=e.getAttribute("prenum");return"<pre>"+t.preList[r]+"</pre>"})),a.subParser("makeMarkdown.strikethrough",(function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="~~";for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);r+="~~"}return r})),a.subParser("makeMarkdown.strong",(function(e,t){"use strict";var r="";if(e.hasChildNodes()){r+="**";for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t);r+="**"}return r})),a.subParser("makeMarkdown.table",(function(e,t){"use strict";var r,n,o="",i=[[],[]],s=e.querySelectorAll("thead>tr>th"),c=e.querySelectorAll("tbody>tr");for(r=0;r<s.length;++r){var l=a.subParser("makeMarkdown.tableCell")(s[r],t),u="---";if(s[r].hasAttribute("style"))switch(s[r].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":u=":---";break;case"text-align:right;":u="---:";break;case"text-align:center;":u=":---:"}i[0][r]=l.trim(),i[1][r]=u}for(r=0;r<c.length;++r){var d=i.push([])-1,p=c[r].getElementsByTagName("td");for(n=0;n<s.length;++n){var f=" ";void 0!==p[n]&&(f=a.subParser("makeMarkdown.tableCell")(p[n],t)),i[d].push(f)}}var h=3;for(r=0;r<i.length;++r)for(n=0;n<i[r].length;++n){var g=i[r][n].length;g>h&&(h=g)}for(r=0;r<i.length;++r){for(n=0;n<i[r].length;++n)1===r?":"===i[r][n].slice(-1)?i[r][n]=a.helper.padEnd(i[r][n].slice(-1),h-1,"-")+":":i[r][n]=a.helper.padEnd(i[r][n],h,"-"):i[r][n]=a.helper.padEnd(i[r][n],h);o+="| "+i[r].join(" | ")+" |\n"}return o.trim()})),a.subParser("makeMarkdown.tableCell",(function(e,t){"use strict";var r="";if(!e.hasChildNodes())return"";for(var n=e.childNodes,o=n.length,i=0;i<o;++i)r+=a.subParser("makeMarkdown.node")(n[i],t,!0);return r.trim()})),a.subParser("makeMarkdown.txt",(function(e){"use strict";var t=e.nodeValue;return t=(t=t.replace(/ +/g," ")).replace(/¨NBSP;/g," "),t=(t=(t=(t=(t=(t=(t=(t=(t=a.helper.unescapeHTMLEntities(t)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")}));void 0===(n=function(){"use strict";return a}.call(t,r,t,e))||(e.exports=n)}).call(this)},5373:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen");
/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function b(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case a:case s:case i:case p:case f:return e;default:switch(e=e&&e.$$typeof){case u:case l:case d:case g:case h:case c:return e;default:return t}}case o:return t}}}r=Symbol.for("react.module.reference"),t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===a||e===s||e===i||e===p||e===f||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===h||e.$$typeof===c||e.$$typeof===l||e.$$typeof===d||e.$$typeof===r||void 0!==e.getModuleId)}},7734:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,o,a;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(!e(t[o],r[o]))return!1;return!0}if(t instanceof Map&&r instanceof Map){if(t.size!==r.size)return!1;for(o of t.entries())if(!r.has(o[0]))return!1;for(o of t.entries())if(!e(o[1],r.get(o[0])))return!1;return!0}if(t instanceof Set&&r instanceof Set){if(t.size!==r.size)return!1;for(o of t.entries())if(!r.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(r)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(t[o]!==r[o])return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(a=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(r,a[o]))return!1;for(o=n;0!=o--;){var i=a[o];if(!e(t[i],r[i]))return!1}return!0}return t!=t&&r!=r}},8529:(e,t,r)=>{"use strict";e.exports=r(5373)},9681:e=>{var t={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Ấ:"A",Ắ:"A",Ẳ:"A",Ẵ:"A",Ặ:"A",Æ:"AE",Ầ:"A",Ằ:"A",Ȃ:"A",Ả:"A",Ạ:"A",Ẩ:"A",Ẫ:"A",Ậ:"A",Ç:"C",Ḉ:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ế:"E",Ḗ:"E",Ề:"E",Ḕ:"E",Ḝ:"E",Ȇ:"E",Ẻ:"E",Ẽ:"E",Ẹ:"E",Ể:"E",Ễ:"E",Ệ:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ḯ:"I",Ȋ:"I",Ỉ:"I",Ị:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ố:"O",Ṍ:"O",Ṓ:"O",Ȏ:"O",Ỏ:"O",Ọ:"O",Ổ:"O",Ỗ:"O",Ộ:"O",Ờ:"O",Ở:"O",Ỡ:"O",Ớ:"O",Ợ:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ủ:"U",Ụ:"U",Ử:"U",Ữ:"U",Ự:"U",Ý:"Y",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",ấ:"a",ắ:"a",ẳ:"a",ẵ:"a",ặ:"a",æ:"ae",ầ:"a",ằ:"a",ȃ:"a",ả:"a",ạ:"a",ẩ:"a",ẫ:"a",ậ:"a",ç:"c",ḉ:"c",è:"e",é:"e",ê:"e",ë:"e",ế:"e",ḗ:"e",ề:"e",ḕ:"e",ḝ:"e",ȇ:"e",ẻ:"e",ẽ:"e",ẹ:"e",ể:"e",ễ:"e",ệ:"e",ì:"i",í:"i",î:"i",ï:"i",ḯ:"i",ȋ:"i",ỉ:"i",ị:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ố:"o",ṍ:"o",ṓ:"o",ȏ:"o",ỏ:"o",ọ:"o",ổ:"o",ỗ:"o",ộ:"o",ờ:"o",ở:"o",ỡ:"o",ớ:"o",ợ:"o",ù:"u",ú:"u",û:"u",ü:"u",ủ:"u",ụ:"u",ử:"u",ữ:"u",ự:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",C̆:"C",c̆:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",Ǵ:"G",ĝ:"g",ǵ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ḫ:"H",ḫ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ḱ:"K",ḱ:"k",K̆:"K",k̆:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ḿ:"M",ḿ:"m",M̆:"M",m̆:"m",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",N̆:"N",n̆:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",P̆:"P",p̆:"p",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",R̆:"R",r̆:"r",Ȓ:"R",ȓ:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",Ș:"S",ș:"s",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",ț:"t",Ț:"T",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",T̆:"T",t̆:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ȗ:"U",ȗ:"u",V̆:"V",v̆:"v",Ŵ:"W",ŵ:"w",Ẃ:"W",ẃ:"w",X̆:"X",x̆:"x",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Y̆:"Y",y̆:"y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ứ:"U",ứ:"u",Ṹ:"U",ṹ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o",Þ:"TH",þ:"th",Ṕ:"P",ṕ:"p",Ṥ:"S",ṥ:"s",X́:"X",x́:"x",Ѓ:"Г",ѓ:"г",Ќ:"К",ќ:"к",A̋:"A",a̋:"a",E̋:"E",e̋:"e",I̋:"I",i̋:"i",Ǹ:"N",ǹ:"n",Ồ:"O",ồ:"o",Ṑ:"O",ṑ:"o",Ừ:"U",ừ:"u",Ẁ:"W",ẁ:"w",Ỳ:"Y",ỳ:"y",Ȁ:"A",ȁ:"a",Ȅ:"E",ȅ:"e",Ȉ:"I",ȉ:"i",Ȍ:"O",ȍ:"o",Ȑ:"R",ȑ:"r",Ȕ:"U",ȕ:"u",B̌:"B",b̌:"b",Č̣:"C",č̣:"c",Ê̌:"E",ê̌:"e",F̌:"F",f̌:"f",Ǧ:"G",ǧ:"g",Ȟ:"H",ȟ:"h",J̌:"J",ǰ:"j",Ǩ:"K",ǩ:"k",M̌:"M",m̌:"m",P̌:"P",p̌:"p",Q̌:"Q",q̌:"q",Ř̩:"R",ř̩:"r",Ṧ:"S",ṧ:"s",V̌:"V",v̌:"v",W̌:"W",w̌:"w",X̌:"X",x̌:"x",Y̌:"Y",y̌:"y",A̧:"A",a̧:"a",B̧:"B",b̧:"b",Ḑ:"D",ḑ:"d",Ȩ:"E",ȩ:"e",Ɛ̧:"E",ɛ̧:"e",Ḩ:"H",ḩ:"h",I̧:"I",i̧:"i",Ɨ̧:"I",ɨ̧:"i",M̧:"M",m̧:"m",O̧:"O",o̧:"o",Q̧:"Q",q̧:"q",U̧:"U",u̧:"u",X̧:"X",x̧:"x",Z̧:"Z",z̧:"z",й:"и",Й:"И",ё:"е",Ё:"Е"},r=Object.keys(t).join("|"),n=new RegExp(r,"g"),o=new RegExp(r,"");function a(e){return t[e]}var i=function(e){return e.replace(n,a)};e.exports=i,e.exports.has=function(e){return!!e.match(o)},e.exports.remove=i}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n].call(a.exports,a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";r.r(n),r.d(n,{__EXPERIMENTAL_ELEMENTS:()=>ee,__EXPERIMENTAL_PATHS_WITH_OVERRIDE:()=>te,__EXPERIMENTAL_STYLE_PROPERTY:()=>J,__experimentalCloneSanitizedBlock:()=>xr,__experimentalGetAccessibleBlockLabel:()=>Ge,__experimentalGetBlockAttributesNamesByRole:()=>Ze,__experimentalGetBlockLabel:()=>qe,__experimentalSanitizeBlockAttributes:()=>Ye,__unstableGetBlockProps:()=>Wr,__unstableGetInnerBlocksProps:()=>Yr,__unstableSerializeAndClean:()=>tn,children:()=>Jn,cloneBlock:()=>Er,createBlock:()=>Tr,createBlocksFromInnerBlocksTemplate:()=>Cr,doBlocksMatchTemplate:()=>ia,findTransform:()=>Lr,getBlockAttributes:()=>ao,getBlockAttributesNamesByRole:()=>Qe,getBlockBindingsSource:()=>ze,getBlockBindingsSources:()=>Ie,getBlockContent:()=>Xr,getBlockDefaultClassName:()=>Fr,getBlockFromExample:()=>zr,getBlockMenuDefaultClassName:()=>qr,getBlockSupport:()=>Te,getBlockTransforms:()=>Mr,getBlockType:()=>we,getBlockTypes:()=>ve,getBlockVariations:()=>Oe,getCategories:()=>na,getChildBlockNames:()=>Se,getDefaultBlockName:()=>ke,getFreeformContentHandlerName:()=>he,getGroupingBlockName:()=>ge,getPhrasingContentSchema:()=>Lo,getPossibleBlockTransformations:()=>Or,getSaveContent:()=>Zr,getSaveElement:()=>Qr,getUnregisteredTypeHandlerName:()=>be,hasBlockSupport:()=>Ce,hasChildBlocks:()=>Be,hasChildBlocksWithInserterSupport:()=>Ae,isReusableBlock:()=>xe,isTemplatePart:()=>Ee,isUnmodifiedBlock:()=>He,isUnmodifiedDefaultBlock:()=>Ve,isValidBlockContent:()=>Hn,isValidIcon:()=>$e,node:()=>Yn,normalizeIconObject:()=>Ue,parse:()=>ho,parseWithAttributeSchema:()=>oo,pasteHandler:()=>ra,privateApis:()=>pa,rawHandler:()=>Mo,registerBlockBindingsSource:()=>je,registerBlockCollection:()=>de,registerBlockStyle:()=>Ne,registerBlockType:()=>le,registerBlockVariation:()=>Le,serialize:()=>rn,serializeRawBlock:()=>$r,setCategories:()=>oa,setDefaultBlockName:()=>_e,setFreeformContentHandlerName:()=>fe,setGroupingBlockName:()=>ye,setUnregisteredTypeHandlerName:()=>me,store:()=>gr,switchToBlockType:()=>Dr,synchronizeBlocksWithTemplate:()=>da,unregisterBlockBindingsSource:()=>De,unregisterBlockStyle:()=>Pe,unregisterBlockType:()=>pe,unregisterBlockVariation:()=>Me,unstable__bootstrapServerSideBlockDefinitions:()=>se,updateCategory:()=>aa,validateBlock:()=>Rn,withBlockContentContext:()=>fa});var e={};r.r(e),r.d(e,{getAllBlockBindingsSources:()=>yt,getBlockBindingsSource:()=>kt,getBootstrappedBlockType:()=>bt,getSupportedStyles:()=>mt,getUnprocessedBlockTypes:()=>_t,hasContentRoleAttribute:()=>wt});var t={};r.r(t),r.d(t,{__experimentalHasContentRoleAttribute:()=>$t,getActiveBlockVariation:()=>St,getBlockStyles:()=>xt,getBlockSupport:()=>Dt,getBlockType:()=>Ct,getBlockTypes:()=>Tt,getBlockVariations:()=>Et,getCategories:()=>At,getChildBlockNames:()=>jt,getCollections:()=>Nt,getDefaultBlockName:()=>Pt,getDefaultBlockVariation:()=>Bt,getFreeformFallbackBlockName:()=>Ot,getGroupingBlockName:()=>Mt,getUnregisteredFallbackBlockName:()=>Lt,hasBlockSupport:()=>zt,hasChildBlocks:()=>Ht,hasChildBlocksWithInserterSupport:()=>Vt,isMatchingSearchTerm:()=>Rt});var o={};r.r(o),r.d(o,{__experimentalReapplyBlockFilters:()=>Zt,addBlockCollection:()=>lr,addBlockStyles:()=>Jt,addBlockTypes:()=>Yt,addBlockVariations:()=>tr,reapplyBlockTypeFilters:()=>Qt,removeBlockCollection:()=>ur,removeBlockStyles:()=>er,removeBlockTypes:()=>Xt,removeBlockVariations:()=>rr,setCategories:()=>sr,setDefaultBlockName:()=>nr,setFreeformFallbackBlockName:()=>or,setGroupingBlockName:()=>ir,setUnregisteredFallbackBlockName:()=>ar,updateCategory:()=>cr});var a={};r.r(a),r.d(a,{addBlockBindingsSource:()=>fr,addBootstrappedBlockType:()=>dr,addUnprocessedBlockType:()=>pr,removeBlockBindingsSource:()=>hr});const i=window.wp.data;var s=function(){return s=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function c(e){return e.toLowerCase()}var l=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],u=/[^A-Z0-9]+/gi;function d(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce((function(e,t){return e.replace(t,r)}),e)}function p(e,t){var r=e.charAt(0),n=e.substr(1).toLowerCase();return t>0&&r>="0"&&r<="9"?"_"+r+n:""+r.toUpperCase()+n}function f(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=void 0===r?l:r,o=t.stripRegexp,a=void 0===o?u:o,i=t.transform,s=void 0===i?c:i,p=t.delimiter,f=void 0===p?" ":p,h=d(d(e,n,"$1\0$2"),a,"\0"),g=0,m=h.length;"\0"===h.charAt(g);)g++;for(;"\0"===h.charAt(m-1);)m--;return h.slice(g,m).split("\0").map(s).join(f)}(e,s({delimiter:"",transform:p},t))}function h(e,t){return 0===t?e.toLowerCase():p(e,t)}const g=window.wp.i18n;var m={grad:.9,turn:360,rad:360/(2*Math.PI)},b=function(e){return"string"==typeof e?e.length>0:"number"==typeof e},_=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=Math.pow(10,t)),Math.round(r*e)/r+0},y=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=1),e>r?r:e>t?e:t},k=function(e){return(e=isFinite(e)?e%360:0)>0?e:e+360},w=function(e){return{r:y(e.r,0,255),g:y(e.g,0,255),b:y(e.b,0,255),a:y(e.a)}},v=function(e){return{r:_(e.r),g:_(e.g),b:_(e.b),a:_(e.a,3)}},T=/^#([0-9a-f]{3,8})$/i,C=function(e){var t=e.toString(16);return t.length<2?"0"+t:t},x=function(e){var t=e.r,r=e.g,n=e.b,o=e.a,a=Math.max(t,r,n),i=a-Math.min(t,r,n),s=i?a===t?(r-n)/i:a===r?2+(n-t)/i:4+(t-r)/i:0;return{h:60*(s<0?s+6:s),s:a?i/a*100:0,v:a/255*100,a:o}},E=function(e){var t=e.h,r=e.s,n=e.v,o=e.a;t=t/360*6,r/=100,n/=100;var a=Math.floor(t),i=n*(1-r),s=n*(1-(t-a)*r),c=n*(1-(1-t+a)*r),l=a%6;return{r:255*[n,s,i,i,c,n][l],g:255*[c,n,n,s,i,i][l],b:255*[i,i,c,n,n,s][l],a:o}},S=function(e){return{h:k(e.h),s:y(e.s,0,100),l:y(e.l,0,100),a:y(e.a)}},B=function(e){return{h:_(e.h),s:_(e.s),l:_(e.l),a:_(e.a,3)}},A=function(e){return E((r=(t=e).s,{h:t.h,s:(r*=((n=t.l)<50?n:100-n)/100)>0?2*r/(n+r)*100:0,v:n+r,a:t.a}));var t,r,n},N=function(e){return{h:(t=x(e)).h,s:(o=(200-(r=t.s))*(n=t.v)/100)>0&&o<200?r*n/100/(o<=100?o:200-o)*100:0,l:o/2,a:t.a};var t,r,n,o},P=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,O=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,L=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,M=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,j={string:[[function(e){var t=T.exec(e);return t?(e=t[1]).length<=4?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:4===e.length?_(parseInt(e[3]+e[3],16)/255,2):1}:6===e.length||8===e.length?{r:parseInt(e.substr(0,2),16),g:parseInt(e.substr(2,2),16),b:parseInt(e.substr(4,2),16),a:8===e.length?_(parseInt(e.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(e){var t=L.exec(e)||M.exec(e);return t?t[2]!==t[4]||t[4]!==t[6]?null:w({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):null},"rgb"],[function(e){var t=P.exec(e)||O.exec(e);if(!t)return null;var r,n,o=S({h:(r=t[1],n=t[2],void 0===n&&(n="deg"),Number(r)*(m[n]||1)),s:Number(t[3]),l:Number(t[4]),a:void 0===t[5]?1:Number(t[5])/(t[6]?100:1)});return A(o)},"hsl"]],object:[[function(e){var t=e.r,r=e.g,n=e.b,o=e.a,a=void 0===o?1:o;return b(t)&&b(r)&&b(n)?w({r:Number(t),g:Number(r),b:Number(n),a:Number(a)}):null},"rgb"],[function(e){var t=e.h,r=e.s,n=e.l,o=e.a,a=void 0===o?1:o;if(!b(t)||!b(r)||!b(n))return null;var i=S({h:Number(t),s:Number(r),l:Number(n),a:Number(a)});return A(i)},"hsl"],[function(e){var t=e.h,r=e.s,n=e.v,o=e.a,a=void 0===o?1:o;if(!b(t)||!b(r)||!b(n))return null;var i=function(e){return{h:k(e.h),s:y(e.s,0,100),v:y(e.v,0,100),a:y(e.a)}}({h:Number(t),s:Number(r),v:Number(n),a:Number(a)});return E(i)},"hsv"]]},D=function(e,t){for(var r=0;r<t.length;r++){var n=t[r][0](e);if(n)return[n,t[r][1]]}return[null,void 0]},z=function(e){return"string"==typeof e?D(e.trim(),j.string):"object"==typeof e&&null!==e?D(e,j.object):[null,void 0]},I=function(e,t){var r=N(e);return{h:r.h,s:y(r.s+100*t,0,100),l:r.l,a:r.a}},R=function(e){return(299*e.r+587*e.g+114*e.b)/1e3/255},H=function(e,t){var r=N(e);return{h:r.h,s:r.s,l:y(r.l+100*t,0,100),a:r.a}},V=function(){function e(e){this.parsed=z(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return e.prototype.isValid=function(){return null!==this.parsed},e.prototype.brightness=function(){return _(R(this.rgba),2)},e.prototype.isDark=function(){return R(this.rgba)<.5},e.prototype.isLight=function(){return R(this.rgba)>=.5},e.prototype.toHex=function(){return t=(e=v(this.rgba)).r,r=e.g,n=e.b,a=(o=e.a)<1?C(_(255*o)):"","#"+C(t)+C(r)+C(n)+a;var e,t,r,n,o,a},e.prototype.toRgb=function(){return v(this.rgba)},e.prototype.toRgbString=function(){return t=(e=v(this.rgba)).r,r=e.g,n=e.b,(o=e.a)<1?"rgba("+t+", "+r+", "+n+", "+o+")":"rgb("+t+", "+r+", "+n+")";var e,t,r,n,o},e.prototype.toHsl=function(){return B(N(this.rgba))},e.prototype.toHslString=function(){return t=(e=B(N(this.rgba))).h,r=e.s,n=e.l,(o=e.a)<1?"hsla("+t+", "+r+"%, "+n+"%, "+o+")":"hsl("+t+", "+r+"%, "+n+"%)";var e,t,r,n,o},e.prototype.toHsv=function(){return e=x(this.rgba),{h:_(e.h),s:_(e.s),v:_(e.v),a:_(e.a,3)};var e},e.prototype.invert=function(){return $({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},e.prototype.saturate=function(e){return void 0===e&&(e=.1),$(I(this.rgba,e))},e.prototype.desaturate=function(e){return void 0===e&&(e=.1),$(I(this.rgba,-e))},e.prototype.grayscale=function(){return $(I(this.rgba,-1))},e.prototype.lighten=function(e){return void 0===e&&(e=.1),$(H(this.rgba,e))},e.prototype.darken=function(e){return void 0===e&&(e=.1),$(H(this.rgba,-e))},e.prototype.rotate=function(e){return void 0===e&&(e=15),this.hue(this.hue()+e)},e.prototype.alpha=function(e){return"number"==typeof e?$({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):_(this.rgba.a,3);var t},e.prototype.hue=function(e){var t=N(this.rgba);return"number"==typeof e?$({h:e,s:t.s,l:t.l,a:t.a}):_(t.h)},e.prototype.isEqual=function(e){return this.toHex()===$(e).toHex()},e}(),$=function(e){return e instanceof V?e:new V(e)},U=[];var F=function(e){var t=e/255;return t<.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)},q=function(e){return.2126*F(e.r)+.7152*F(e.g)+.0722*F(e.b)};const G=window.wp.element,K=window.wp.dom,W=window.wp.richText,Y=window.wp.deprecated;var Q=r.n(Y);const Z="block-default",X=["attributes","supports","save","migrate","isEligible","apiVersion"],J={"--wp--style--color--link":{value:["color","link"],support:["color","link"]},aspectRatio:{value:["dimensions","aspectRatio"],support:["dimensions","aspectRatio"],useEngine:!0},background:{value:["color","gradient"],support:["color","gradients"],useEngine:!0},backgroundColor:{value:["color","background"],support:["color","background"],requiresOptOut:!0,useEngine:!0},backgroundImage:{value:["background","backgroundImage"],support:["background","backgroundImage"],useEngine:!0},backgroundRepeat:{value:["background","backgroundRepeat"],support:["background","backgroundRepeat"],useEngine:!0},backgroundSize:{value:["background","backgroundSize"],support:["background","backgroundSize"],useEngine:!0},backgroundPosition:{value:["background","backgroundPosition"],support:["background","backgroundPosition"],useEngine:!0},borderColor:{value:["border","color"],support:["__experimentalBorder","color"],useEngine:!0},borderRadius:{value:["border","radius"],support:["__experimentalBorder","radius"],properties:{borderTopLeftRadius:"topLeft",borderTopRightRadius:"topRight",borderBottomLeftRadius:"bottomLeft",borderBottomRightRadius:"bottomRight"},useEngine:!0},borderStyle:{value:["border","style"],support:["__experimentalBorder","style"],useEngine:!0},borderWidth:{value:["border","width"],support:["__experimentalBorder","width"],useEngine:!0},borderTopColor:{value:["border","top","color"],support:["__experimentalBorder","color"],useEngine:!0},borderTopStyle:{value:["border","top","style"],support:["__experimentalBorder","style"],useEngine:!0},borderTopWidth:{value:["border","top","width"],support:["__experimentalBorder","width"],useEngine:!0},borderRightColor:{value:["border","right","color"],support:["__experimentalBorder","color"],useEngine:!0},borderRightStyle:{value:["border","right","style"],support:["__experimentalBorder","style"],useEngine:!0},borderRightWidth:{value:["border","right","width"],support:["__experimentalBorder","width"],useEngine:!0},borderBottomColor:{value:["border","bottom","color"],support:["__experimentalBorder","color"],useEngine:!0},borderBottomStyle:{value:["border","bottom","style"],support:["__experimentalBorder","style"],useEngine:!0},borderBottomWidth:{value:["border","bottom","width"],support:["__experimentalBorder","width"],useEngine:!0},borderLeftColor:{value:["border","left","color"],support:["__experimentalBorder","color"],useEngine:!0},borderLeftStyle:{value:["border","left","style"],support:["__experimentalBorder","style"],useEngine:!0},borderLeftWidth:{value:["border","left","width"],support:["__experimentalBorder","width"],useEngine:!0},color:{value:["color","text"],support:["color","text"],requiresOptOut:!0,useEngine:!0},columnCount:{value:["typography","textColumns"],support:["typography","textColumns"],useEngine:!0},filter:{value:["filter","duotone"],support:["filter","duotone"]},linkColor:{value:["elements","link","color","text"],support:["color","link"]},captionColor:{value:["elements","caption","color","text"],support:["color","caption"]},buttonColor:{value:["elements","button","color","text"],support:["color","button"]},buttonBackgroundColor:{value:["elements","button","color","background"],support:["color","button"]},headingColor:{value:["elements","heading","color","text"],support:["color","heading"]},headingBackgroundColor:{value:["elements","heading","color","background"],support:["color","heading"]},fontFamily:{value:["typography","fontFamily"],support:["typography","__experimentalFontFamily"],useEngine:!0},fontSize:{value:["typography","fontSize"],support:["typography","fontSize"],useEngine:!0},fontStyle:{value:["typography","fontStyle"],support:["typography","__experimentalFontStyle"],useEngine:!0},fontWeight:{value:["typography","fontWeight"],support:["typography","__experimentalFontWeight"],useEngine:!0},lineHeight:{value:["typography","lineHeight"],support:["typography","lineHeight"],useEngine:!0},margin:{value:["spacing","margin"],support:["spacing","margin"],properties:{marginTop:"top",marginRight:"right",marginBottom:"bottom",marginLeft:"left"},useEngine:!0},minHeight:{value:["dimensions","minHeight"],support:["dimensions","minHeight"],useEngine:!0},padding:{value:["spacing","padding"],support:["spacing","padding"],properties:{paddingTop:"top",paddingRight:"right",paddingBottom:"bottom",paddingLeft:"left"},useEngine:!0},textAlign:{value:["typography","textAlign"],support:["typography","textAlign"],useEngine:!1},textDecoration:{value:["typography","textDecoration"],support:["typography","__experimentalTextDecoration"],useEngine:!0},textTransform:{value:["typography","textTransform"],support:["typography","__experimentalTextTransform"],useEngine:!0},letterSpacing:{value:["typography","letterSpacing"],support:["typography","__experimentalLetterSpacing"],useEngine:!0},writingMode:{value:["typography","writingMode"],support:["typography","__experimentalWritingMode"],useEngine:!0},"--wp--style--root--padding":{value:["spacing","padding"],support:["spacing","padding"],properties:{"--wp--style--root--padding-top":"top","--wp--style--root--padding-right":"right","--wp--style--root--padding-bottom":"bottom","--wp--style--root--padding-left":"left"},rootOnly:!0}},ee={link:"a:where(:not(.wp-element-button))",heading:"h1, h2, h3, h4, h5, h6",h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",button:".wp-element-button, .wp-block-button__link",caption:".wp-element-caption, .wp-block-audio figcaption, .wp-block-embed figcaption, .wp-block-gallery figcaption, .wp-block-image figcaption, .wp-block-table figcaption, .wp-block-video figcaption",cite:"cite"},te={"color.duotone":!0,"color.gradients":!0,"color.palette":!0,"dimensions.aspectRatios":!0,"typography.fontSizes":!0,"spacing.spacingSizes":!0},re=(window.wp.warning,window.wp.privateApis),{lock:ne,unlock:oe}=(0,re.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/blocks"),ae={title:"block title",description:"block description",keywords:["block keyword"],styles:[{label:"block style label"}],variations:[{title:"block variation title",description:"block variation description",keywords:["block variation keyword"]}]};function ie(e){return null!==e&&"object"==typeof e}function se(e){const{addBootstrappedBlockType:t}=oe((0,i.dispatch)(gr));for(const[r,n]of Object.entries(e))t(r,n)}function ce({textdomain:e,...t}){const r=["apiVersion","title","category","parent","ancestor","icon","description","keywords","attributes","providesContext","usesContext","selectors","supports","styles","example","variations","blockHooks","allowedBlocks"],n=Object.fromEntries(Object.entries(t).filter((([e])=>r.includes(e))));return e&&Object.keys(ae).forEach((t=>{n[t]&&(n[t]=ue(ae[t],n[t],e))})),n}function le(e,t){const r=ie(e)?e.name:e;if("string"!=typeof r)return;if(!/^[a-z][a-z0-9-]*\/[a-z][a-z0-9-]*$/.test(r))return;if((0,i.select)(gr).getBlockType(r))return;const{addBootstrappedBlockType:n,addUnprocessedBlockType:o}=oe((0,i.dispatch)(gr));if(ie(e)){n(r,ce(e))}return o(r,t),(0,i.select)(gr).getBlockType(r)}function ue(e,t,r){return"string"==typeof e&&"string"==typeof t?(0,g._x)(t,e,r):Array.isArray(e)&&e.length&&Array.isArray(t)?t.map((t=>ue(e[0],t,r))):ie(e)&&Object.entries(e).length&&ie(t)?Object.keys(t).reduce(((n,o)=>e[o]?(n[o]=ue(e[o],t[o],r),n):(n[o]=t[o],n)),{}):t}function de(e,{title:t,icon:r}){(0,i.dispatch)(gr).addBlockCollection(e,t,r)}function pe(e){const t=(0,i.select)(gr).getBlockType(e);if(t)return(0,i.dispatch)(gr).removeBlockTypes(e),t}function fe(e){(0,i.dispatch)(gr).setFreeformFallbackBlockName(e)}function he(){return(0,i.select)(gr).getFreeformFallbackBlockName()}function ge(){return(0,i.select)(gr).getGroupingBlockName()}function me(e){(0,i.dispatch)(gr).setUnregisteredFallbackBlockName(e)}function be(){return(0,i.select)(gr).getUnregisteredFallbackBlockName()}function _e(e){(0,i.dispatch)(gr).setDefaultBlockName(e)}function ye(e){(0,i.dispatch)(gr).setGroupingBlockName(e)}function ke(){return(0,i.select)(gr).getDefaultBlockName()}function we(e){return(0,i.select)(gr)?.getBlockType(e)}function ve(){return(0,i.select)(gr).getBlockTypes()}function Te(e,t,r){return(0,i.select)(gr).getBlockSupport(e,t,r)}function Ce(e,t,r){return(0,i.select)(gr).hasBlockSupport(e,t,r)}function xe(e){return"core/block"===e?.name}function Ee(e){return"core/template-part"===e?.name}const Se=e=>(0,i.select)(gr).getChildBlockNames(e),Be=e=>(0,i.select)(gr).hasChildBlocks(e),Ae=e=>(0,i.select)(gr).hasChildBlocksWithInserterSupport(e),Ne=(e,t)=>{(0,i.dispatch)(gr).addBlockStyles(e,t)},Pe=(e,t)=>{(0,i.dispatch)(gr).removeBlockStyles(e,t)},Oe=(e,t)=>(0,i.select)(gr).getBlockVariations(e,t),Le=(e,t)=>{t.name,(0,i.dispatch)(gr).addBlockVariations(e,t)},Me=(e,t)=>{(0,i.dispatch)(gr).removeBlockVariations(e,t)},je=e=>{const{name:t,label:r,usesContext:n,getValues:o,setValues:a,canUserEditValue:s,getFieldsList:c}=e,l=oe((0,i.select)(gr)).getBlockBindingsSource(t),u=["label","usesContext"];for(const e in l)if(!u.includes(e)&&l[e])return;if(t&&"string"==typeof t&&!/[A-Z]+/.test(t)&&/^[a-z0-9/-]+$/.test(t)&&/^[a-z0-9-]+\/[a-z0-9-]+$/.test(t)&&(r||l?.label)&&(!r||"string"==typeof r)&&(!n||Array.isArray(n))&&!(o&&"function"!=typeof o||a&&"function"!=typeof a||s&&"function"!=typeof s||c&&"function"!=typeof c))return oe((0,i.dispatch)(gr)).addBlockBindingsSource(e)};function De(e){ze(e)&&oe((0,i.dispatch)(gr)).removeBlockBindingsSource(e)}function ze(e){return oe((0,i.select)(gr)).getBlockBindingsSource(e)}function Ie(){return oe((0,i.select)(gr)).getAllBlockBindingsSources()}!function(e){e.forEach((function(e){U.indexOf(e)<0&&(e(V,j),U.push(e))}))}([function(e,t){var r={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},n={};for(var o in r)n[r[o]]=o;var a={};e.prototype.toName=function(t){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var o,i,s=n[this.toHex()];if(s)return s;if(null==t?void 0:t.closest){var c=this.toRgb(),l=1/0,u="black";if(!a.length)for(var d in r)a[d]=new e(r[d]).toRgb();for(var p in r){var f=(o=c,i=a[p],Math.pow(o.r-i.r,2)+Math.pow(o.g-i.g,2)+Math.pow(o.b-i.b,2));f<l&&(l=f,u=p)}return u}},t.string.push([function(t){var n=t.toLowerCase(),o="transparent"===n?"#0000":r[n];return o?new e(o).toRgb():null},"name"])},function(e){e.prototype.luminance=function(){return e=q(this.rgba),void 0===(t=2)&&(t=0),void 0===r&&(r=Math.pow(10,t)),Math.round(r*e)/r+0;var e,t,r},e.prototype.contrast=function(t){void 0===t&&(t="#FFF");var r,n,o,a,i,s,c,l=t instanceof e?t:new e(t);return a=this.rgba,i=l.toRgb(),r=(s=q(a))>(c=q(i))?(s+.05)/(c+.05):(c+.05)/(s+.05),void 0===(n=2)&&(n=0),void 0===o&&(o=Math.pow(10,n)),Math.floor(o*r)/o+0},e.prototype.isReadable=function(e,t){return void 0===e&&(e="#FFF"),void 0===t&&(t={}),this.contrast(e)>=(i=void 0===(a=(r=t).size)?"normal":a,"AAA"===(o=void 0===(n=r.level)?"AA":n)&&"normal"===i?7:"AA"===o&&"large"===i?3:4.5);var r,n,o,a,i}}]);const Re=["#191e23","#f8f9f9"];function He(e){var t;return Object.entries(null!==(t=we(e.name)?.attributes)&&void 0!==t?t:{}).every((([t,r])=>{const n=e.attributes[t];return r.hasOwnProperty("default")?n===r.default:"rich-text"===r.type?!n?.length:void 0===n}))}function Ve(e){return e.name===ke()&&He(e)}function $e(e){return!!e&&("string"==typeof e||(0,G.isValidElement)(e)||"function"==typeof e||e instanceof G.Component)}function Ue(e){if($e(e=e||Z))return{src:e};if("background"in e){const t=$(e.background),r=e=>t.contrast(e),n=Math.max(...Re.map(r));return{...e,foreground:e.foreground?e.foreground:Re.find((e=>r(e)===n)),shadowColor:t.alpha(.3).toRgbString()}}return e}function Fe(e){return"string"==typeof e?we(e):e}function qe(e,t,r="visual"){const{__experimentalLabel:n,title:o}=e,a=n&&n(t,{context:r});return a?a.toPlainText?a.toPlainText():(0,K.__unstableStripHTML)(a):o}function Ge(e,t,r,n="vertical"){const o=e?.title,a=e?qe(e,t,"accessibility"):"",i=void 0!==r,s=a&&a!==o;return i&&"vertical"===n?s?(0,g.sprintf)((0,g.__)("%1$s Block. Row %2$d. %3$s"),o,r,a):(0,g.sprintf)((0,g.__)("%1$s Block. Row %2$d"),o,r):i&&"horizontal"===n?s?(0,g.sprintf)((0,g.__)("%1$s Block. Column %2$d. %3$s"),o,r,a):(0,g.sprintf)((0,g.__)("%1$s Block. Column %2$d"),o,r):s?(0,g.sprintf)((0,g.__)("%1$s Block. %2$s"),o,a):(0,g.sprintf)((0,g.__)("%s Block"),o)}function Ke(e){return void 0!==e.default?e.default:"rich-text"===e.type?new W.RichTextData:void 0}function We(e){return void 0!==we(e)}function Ye(e,t){const r=we(e);if(void 0===r)throw new Error(`Block type '${e}' is not registered.`);return Object.entries(r.attributes).reduce(((e,[r,n])=>{const o=t[r];if(void 0!==o)"rich-text"===n.type?o instanceof W.RichTextData?e[r]=o:"string"==typeof o&&(e[r]=W.RichTextData.fromHTMLString(o)):"string"===n.type&&o instanceof W.RichTextData?e[r]=o.toHTMLString():e[r]=o;else{const t=Ke(n);void 0!==t&&(e[r]=t)}return-1!==["node","children"].indexOf(n.source)&&("string"==typeof e[r]?e[r]=[e[r]]:Array.isArray(e[r])||(e[r]=[])),e}),{})}function Qe(e,t){const r=we(e)?.attributes;if(!r)return[];const n=Object.keys(r);return t?n.filter((n=>{const o=r[n];return o?.role===t||o?.__experimentalRole===t&&(Q()("__experimentalRole attribute",{since:"6.7",version:"6.8",alternative:"role attribute",hint:`Check the block.json of the ${e} block.`}),!0)})):n}const Ze=(...e)=>(Q()("__experimentalGetBlockAttributesNamesByRole",{since:"6.7",version:"6.8",alternative:"getBlockAttributesNamesByRole"}),Qe(...e));function Xe(e,t){return Object.fromEntries(Object.entries(e).filter((([e])=>!t.includes(e))))}const Je=[{slug:"text",title:(0,g.__)("Text")},{slug:"media",title:(0,g.__)("Media")},{slug:"design",title:(0,g.__)("Design")},{slug:"widgets",title:(0,g.__)("Widgets")},{slug:"theme",title:(0,g.__)("Theme")},{slug:"embed",title:(0,g.__)("Embeds")},{slug:"reusable",title:(0,g.__)("Reusable blocks")}];function et(e){return e.reduce(((e,t)=>({...e,[t.name]:t})),{})}function tt(e){return e.reduce(((e,t)=>(e.some((e=>e.name===t.name))||e.push(t),e)),[])}function rt(e){return(t=null,r)=>{switch(r.type){case"REMOVE_BLOCK_TYPES":return-1!==r.names.indexOf(t)?null:t;case e:return r.name||null}return t}}const nt=rt("SET_DEFAULT_BLOCK_NAME"),ot=rt("SET_FREEFORM_FALLBACK_BLOCK_NAME"),at=rt("SET_UNREGISTERED_FALLBACK_BLOCK_NAME"),it=rt("SET_GROUPING_BLOCK_NAME");function st(e=[],t=[]){const r=Array.from(new Set(e.concat(t)));return r.length>0?r:void 0}const ct=(0,i.combineReducers)({bootstrappedBlockTypes:function(e={},t){switch(t.type){case"ADD_BOOTSTRAPPED_BLOCK_TYPE":const{name:r,blockType:n}=t,o=e[r];let a;return o?(void 0===o.blockHooks&&n.blockHooks&&(a={...o,...a,blockHooks:n.blockHooks}),void 0===o.allowedBlocks&&n.allowedBlocks&&(a={...o,...a,allowedBlocks:n.allowedBlocks})):(a=Object.fromEntries(Object.entries(n).filter((([,e])=>null!=e)).map((([e,t])=>{return[(r=e,void 0===n&&(n={}),f(r,s({transform:h},n))),t];var r,n}))),a.name=r),a?{...e,[r]:a}:e;case"REMOVE_BLOCK_TYPES":return Xe(e,t.names)}return e},unprocessedBlockTypes:function(e={},t){switch(t.type){case"ADD_UNPROCESSED_BLOCK_TYPE":return{...e,[t.name]:t.blockType};case"REMOVE_BLOCK_TYPES":return Xe(e,t.names)}return e},blockTypes:function(e={},t){switch(t.type){case"ADD_BLOCK_TYPES":return{...e,...et(t.blockTypes)};case"REMOVE_BLOCK_TYPES":return Xe(e,t.names)}return e},blockStyles:function(e={},t){var r;switch(t.type){case"ADD_BLOCK_TYPES":return{...e,...Object.fromEntries(Object.entries(et(t.blockTypes)).map((([t,r])=>{var n,o;return[t,tt([...(null!==(n=r.styles)&&void 0!==n?n:[]).map((e=>({...e,source:"block"}))),...(null!==(o=e[r.name])&&void 0!==o?o:[]).filter((({source:e})=>"block"!==e))])]})))};case"ADD_BLOCK_STYLES":const n={};return t.blockNames.forEach((r=>{var o;n[r]=tt([...null!==(o=e[r])&&void 0!==o?o:[],...t.styles])})),{...e,...n};case"REMOVE_BLOCK_STYLES":return{...e,[t.blockName]:(null!==(r=e[t.blockName])&&void 0!==r?r:[]).filter((e=>-1===t.styleNames.indexOf(e.name)))}}return e},blockVariations:function(e={},t){var r,n;switch(t.type){case"ADD_BLOCK_TYPES":return{...e,...Object.fromEntries(Object.entries(et(t.blockTypes)).map((([t,r])=>{var n,o;return[t,tt([...(null!==(n=r.variations)&&void 0!==n?n:[]).map((e=>({...e,source:"block"}))),...(null!==(o=e[r.name])&&void 0!==o?o:[]).filter((({source:e})=>"block"!==e))])]})))};case"ADD_BLOCK_VARIATIONS":return{...e,[t.blockName]:tt([...null!==(r=e[t.blockName])&&void 0!==r?r:[],...t.variations])};case"REMOVE_BLOCK_VARIATIONS":return{...e,[t.blockName]:(null!==(n=e[t.blockName])&&void 0!==n?n:[]).filter((e=>-1===t.variationNames.indexOf(e.name)))}}return e},defaultBlockName:nt,freeformFallbackBlockName:ot,unregisteredFallbackBlockName:at,groupingBlockName:it,categories:function(e=Je,t){switch(t.type){case"SET_CATEGORIES":const r=new Map;return(t.categories||[]).forEach((e=>{r.set(e.slug,e)})),[...r.values()];case"UPDATE_CATEGORY":if(!t.category||!Object.keys(t.category).length)return e;if(e.find((({slug:e})=>e===t.slug)))return e.map((e=>e.slug===t.slug?{...e,...t.category}:e))}return e},collections:function(e={},t){switch(t.type){case"ADD_BLOCK_COLLECTION":return{...e,[t.namespace]:{title:t.title,icon:t.icon}};case"REMOVE_BLOCK_COLLECTION":return Xe(e,t.namespace)}return e},blockBindingsSources:function(e={},t){switch(t.type){case"ADD_BLOCK_BINDINGS_SOURCE":let r;return"core/post-meta"===t.name&&(r=t.getFieldsList),{...e,[t.name]:{label:t.label||e[t.name]?.label,usesContext:st(e[t.name]?.usesContext,t.usesContext),getValues:t.getValues,setValues:t.setValues,canUserEditValue:t.setValues&&t.canUserEditValue,getFieldsList:r}};case"REMOVE_BLOCK_BINDINGS_SOURCE":return Xe(e,t.name)}return e}});var lt=r(9681),ut=r.n(lt);const dt=(e,t,r)=>{var n;const o=Array.isArray(t)?t:t.split(".");let a=e;return o.forEach((e=>{a=a?.[e]})),null!==(n=a)&&void 0!==n?n:r};function pt(e){return"object"==typeof e&&e.constructor===Object&&null!==e}function ft(e,t){return pt(e)&&pt(t)?Object.entries(t).every((([t,r])=>ft(e?.[t],r))):e===t}const ht=["background","backgroundColor","color","linkColor","captionColor","buttonColor","headingColor","fontFamily","fontSize","fontStyle","fontWeight","lineHeight","padding","contentSize","wideSize","blockGap","textDecoration","textTransform","letterSpacing"];function gt(e,t,r){return e.filter((e=>("fontSize"!==e||"heading"!==r)&&(!("textDecoration"===e&&!t&&"link"!==r)&&(!("textTransform"===e&&!t&&!["heading","h1","h2","h3","h4","h5","h6"].includes(r)&&"button"!==r&&"caption"!==r&&"text"!==r)&&(!("letterSpacing"===e&&!t&&!["heading","h1","h2","h3","h4","h5","h6"].includes(r)&&"button"!==r&&"caption"!==r&&"text"!==r)&&!("textColumns"===e&&!t))))))}const mt=(0,i.createSelector)(((e,t,r)=>{if(!t)return gt(ht,t,r);const n=Ct(e,t);if(!n)return[];const o=[];return n?.supports?.spacing?.blockGap&&o.push("blockGap"),n?.supports?.shadow&&o.push("shadow"),Object.keys(J).forEach((e=>{J[e].support&&(J[e].requiresOptOut&&J[e].support[0]in n.supports&&!1!==dt(n.supports,J[e].support)||dt(n.supports,J[e].support,!1))&&o.push(e)})),gt(o,t,r)}),((e,t)=>[e.blockTypes[t]]));function bt(e,t){return e.bootstrappedBlockTypes[t]}function _t(e){return e.unprocessedBlockTypes}function yt(e){return e.blockBindingsSources}function kt(e,t){return e.blockBindingsSources[t]}const wt=(e,t)=>{const r=Ct(e,t);return!!r&&Object.values(r.attributes).some((({role:e,__experimentalRole:r})=>"content"===e||"content"===r&&(Q()("__experimentalRole attribute",{since:"6.7",version:"6.8",alternative:"role attribute",hint:`Check the block.json of the ${t} block.`}),!0)))},vt=(e,t)=>"string"==typeof t?Ct(e,t):t,Tt=(0,i.createSelector)((e=>Object.values(e.blockTypes)),(e=>[e.blockTypes]));function Ct(e,t){return e.blockTypes[t]}function xt(e,t){return e.blockStyles[t]}const Et=(0,i.createSelector)(((e,t,r)=>{const n=e.blockVariations[t];return n&&r?n.filter((e=>(e.scope||["block","inserter"]).includes(r))):n}),((e,t)=>[e.blockVariations[t]]));function St(e,t,r,n){const o=Et(e,t,n);if(!o)return o;const a=Ct(e,t),i=Object.keys(a?.attributes||{});let s,c=0;for(const e of o)if(Array.isArray(e.isActive)){const t=e.isActive.filter((e=>{const t=e.split(".")[0];return i.includes(t)})),n=t.length;if(0===n)continue;t.every((t=>{const n=dt(e.attributes,t);if(void 0===n)return!1;let o=dt(r,t);return o instanceof W.RichTextData&&(o=o.toHTMLString()),ft(o,n)}))&&n>c&&(s=e,c=n)}else if(e.isActive?.(r,e.attributes))return s||e;return s}function Bt(e,t,r){const n=Et(e,t,r);return[...n].reverse().find((({isDefault:e})=>!!e))||n[0]}function At(e){return e.categories}function Nt(e){return e.collections}function Pt(e){return e.defaultBlockName}function Ot(e){return e.freeformFallbackBlockName}function Lt(e){return e.unregisteredFallbackBlockName}function Mt(e){return e.groupingBlockName}const jt=(0,i.createSelector)(((e,t)=>Tt(e).filter((e=>e.parent?.includes(t))).map((({name:e})=>e))),(e=>[e.blockTypes])),Dt=(e,t,r,n)=>{const o=vt(e,t);return o?.supports?dt(o.supports,r,n):n};function zt(e,t,r,n){return!!Dt(e,t,r,n)}function It(e){return ut()(null!=e?e:"").toLowerCase().trim()}function Rt(e,t,r=""){const n=vt(e,t),o=It(r),a=e=>It(e).includes(o);return a(n.title)||n.keywords?.some(a)||a(n.category)||"string"==typeof n.description&&a(n.description)}const Ht=(e,t)=>jt(e,t).length>0,Vt=(e,t)=>jt(e,t).some((t=>zt(e,t,"inserter",!0))),$t=(...e)=>(Q()("__experimentalHasContentRoleAttribute",{since:"6.7",version:"6.8",hint:"This is a private selector."}),wt(...e));
/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */
function Ut(e){return"[object Object]"===Object.prototype.toString.call(e)}var Ft=r(8529);const qt=window.wp.hooks,Gt={common:"text",formatting:"text",layout:"design"};function Kt(e=[],t=[]){const r=[...e];return t.forEach((e=>{const t=r.findIndex((t=>t.name===e.name));-1!==t?r[t]={...r[t],...e}:r.push(e)})),r}const Wt=(e,t)=>({select:r})=>{const n=r.getBootstrappedBlockType(e),o={name:e,icon:Z,keywords:[],attributes:{},providesContext:{},usesContext:[],selectors:{},supports:{},styles:[],blockHooks:{},save:()=>null,...n,...t,variations:Kt(Array.isArray(n?.variations)?n.variations:[],Array.isArray(t?.variations)?t.variations:[])},a=(0,qt.applyFilters)("blocks.registerBlockType",o,e,null);if(a.description&&"string"!=typeof a.description&&Q()("Declaring non-string block descriptions",{since:"6.2"}),a.deprecated&&(a.deprecated=a.deprecated.map((e=>Object.fromEntries(Object.entries((0,qt.applyFilters)("blocks.registerBlockType",{...Xe(o,X),...e},o.name,e)).filter((([e])=>X.includes(e))))))),function(e){var t,r;return!1!==Ut(e)&&(void 0===(t=e.constructor)||!1!==Ut(r=t.prototype)&&!1!==r.hasOwnProperty("isPrototypeOf"))}(a)&&"function"==typeof a.save&&(!("edit"in a)||(0,Ft.isValidElementType)(a.edit))&&(Gt.hasOwnProperty(a.category)&&(a.category=Gt[a.category]),"category"in a&&!r.getCategories().some((({slug:e})=>e===a.category))&&delete a.category,"title"in a&&""!==a.title&&"string"==typeof a.title&&(a.icon=Ue(a.icon),$e(a.icon.src)&&(("string"==typeof a?.parent||a?.parent instanceof String)&&(a.parent=[a.parent]),(Array.isArray(a?.parent)||void 0===a?.parent)&&(1!==a?.parent?.length||e!==a.parent[0])))))return a};function Yt(e){return{type:"ADD_BLOCK_TYPES",blockTypes:Array.isArray(e)?e:[e]}}function Qt(){return({dispatch:e,select:t})=>{const r=[];for(const[n,o]of Object.entries(t.getUnprocessedBlockTypes())){const t=e(Wt(n,o));t&&r.push(t)}r.length&&e.addBlockTypes(r)}}function Zt(){return Q()('wp.data.dispatch( "core/blocks" ).__experimentalReapplyBlockFilters',{since:"6.4",alternative:"reapplyBlockFilters"}),Qt()}function Xt(e){return{type:"REMOVE_BLOCK_TYPES",names:Array.isArray(e)?e:[e]}}function Jt(e,t){return{type:"ADD_BLOCK_STYLES",styles:Array.isArray(t)?t:[t],blockNames:Array.isArray(e)?e:[e]}}function er(e,t){return{type:"REMOVE_BLOCK_STYLES",styleNames:Array.isArray(t)?t:[t],blockName:e}}function tr(e,t){return{type:"ADD_BLOCK_VARIATIONS",variations:Array.isArray(t)?t:[t],blockName:e}}function rr(e,t){return{type:"REMOVE_BLOCK_VARIATIONS",variationNames:Array.isArray(t)?t:[t],blockName:e}}function nr(e){return{type:"SET_DEFAULT_BLOCK_NAME",name:e}}function or(e){return{type:"SET_FREEFORM_FALLBACK_BLOCK_NAME",name:e}}function ar(e){return{type:"SET_UNREGISTERED_FALLBACK_BLOCK_NAME",name:e}}function ir(e){return{type:"SET_GROUPING_BLOCK_NAME",name:e}}function sr(e){return{type:"SET_CATEGORIES",categories:e}}function cr(e,t){return{type:"UPDATE_CATEGORY",slug:e,category:t}}function lr(e,t,r){return{type:"ADD_BLOCK_COLLECTION",namespace:e,title:t,icon:r}}function ur(e){return{type:"REMOVE_BLOCK_COLLECTION",namespace:e}}function dr(e,t){return{type:"ADD_BOOTSTRAPPED_BLOCK_TYPE",name:e,blockType:t}}function pr(e,t){return({dispatch:r})=>{r({type:"ADD_UNPROCESSED_BLOCK_TYPE",name:e,blockType:t});const n=r(Wt(e,t));n&&r.addBlockTypes(n)}}function fr(e){return{type:"ADD_BLOCK_BINDINGS_SOURCE",name:e.name,label:e.label,usesContext:e.usesContext,getValues:e.getValues,setValues:e.setValues,canUserEditValue:e.canUserEditValue,getFieldsList:e.getFieldsList}}function hr(e){return{type:"REMOVE_BLOCK_BINDINGS_SOURCE",name:e}}const gr=(0,i.createReduxStore)("core/blocks",{reducer:ct,selectors:t,actions:o});(0,i.register)(gr),oe(gr).registerPrivateSelectors(e),oe(gr).registerPrivateActions(a);const mr={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let br;const _r=new Uint8Array(16);function yr(){if(!br&&(br="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!br))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return br(_r)}const kr=[];for(let e=0;e<256;++e)kr.push((e+256).toString(16).slice(1));function wr(e,t=0){return kr[e[t+0]]+kr[e[t+1]]+kr[e[t+2]]+kr[e[t+3]]+"-"+kr[e[t+4]]+kr[e[t+5]]+"-"+kr[e[t+6]]+kr[e[t+7]]+"-"+kr[e[t+8]]+kr[e[t+9]]+"-"+kr[e[t+10]]+kr[e[t+11]]+kr[e[t+12]]+kr[e[t+13]]+kr[e[t+14]]+kr[e[t+15]]}const vr=function(e,t,r){if(mr.randomUUID&&!t&&!e)return mr.randomUUID();const n=(e=e||{}).random||(e.rng||yr)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=n[e];return t}return wr(n)};function Tr(e,t={},r=[]){if(!We(e))return Tr("core/missing",{originalName:e,originalContent:"",originalUndelimitedContent:""});const n=Ye(e,t);return{clientId:vr(),name:e,isValid:!0,attributes:n,innerBlocks:r}}function Cr(e=[]){return e.map((e=>{const t=Array.isArray(e)?e:[e.name,e.attributes,e.innerBlocks],[r,n,o=[]]=t;return Tr(r,n,Cr(o))}))}function xr(e,t={},r){const{name:n}=e;if(!We(n))return Tr("core/missing",{originalName:n,originalContent:"",originalUndelimitedContent:""});const o=vr(),a=Ye(n,{...e.attributes,...t});return{...e,clientId:o,attributes:a,innerBlocks:r||e.innerBlocks.map((e=>xr(e)))}}function Er(e,t={},r){const n=vr();return{...e,clientId:n,attributes:{...e.attributes,...t},innerBlocks:r||e.innerBlocks.map((e=>Er(e)))}}const Sr=(e,t,r)=>{if(!r.length)return!1;const n=r.length>1,o=r[0].name;if(!(Nr(e)||!n||e.isMultiBlock))return!1;if(!Nr(e)&&!r.every((e=>e.name===o)))return!1;if(!("block"===e.type))return!1;const a=r[0];return!("from"===t&&-1===e.blocks.indexOf(a.name)&&!Nr(e))&&(!(!n&&"from"===t&&Pr(a.name)&&Pr(e.blockName))&&!!jr(e,r))},Br=e=>{if(!e.length)return[];return ve().filter((t=>!!Lr(Mr("from",t.name),(t=>Sr(t,"from",e)))))},Ar=e=>{if(!e.length)return[];const t=we(e[0].name);return(t?Mr("to",t.name):[]).filter((t=>t&&Sr(t,"to",e))).map((e=>e.blocks)).flat().map(we)},Nr=e=>e&&"block"===e.type&&Array.isArray(e.blocks)&&e.blocks.includes("*"),Pr=e=>e===ge();function Or(e){if(!e.length)return[];const t=Br(e),r=Ar(e);return[...new Set([...t,...r])]}function Lr(e,t){const r=(0,qt.createHooks)();for(let n=0;n<e.length;n++){const o=e[n];t(o)&&r.addFilter("transform","transform/"+n.toString(),(e=>e||o),o.priority)}return r.applyFilters("transform",null)}function Mr(e,t){if(void 0===t)return ve().map((({name:t})=>Mr(e,t))).flat();const r=Fe(t),{name:n,transforms:o}=r||{};if(!o||!Array.isArray(o[e]))return[];const a=o.supportedMobileTransforms&&Array.isArray(o.supportedMobileTransforms),i=a?o[e].filter((e=>"raw"===e.type||("prefix"===e.type||!(!e.blocks||!e.blocks.length)&&(!!Nr(e)||e.blocks.every((e=>o.supportedMobileTransforms.includes(e))))))):o[e];return i.map((e=>({...e,blockName:n,usingMobileTransformations:a})))}function jr(e,t){if("function"!=typeof e.isMatch)return!0;const r=t[0],n=e.isMultiBlock?t.map((e=>e.attributes)):r.attributes,o=e.isMultiBlock?t:r;return e.isMatch(n,o)}function Dr(e,t){const r=Array.isArray(e)?e:[e],n=r.length>1,o=r[0],a=o.name,i=Mr("from",t),s=Lr(Mr("to",a),(e=>"block"===e.type&&(Nr(e)||-1!==e.blocks.indexOf(t))&&(!n||e.isMultiBlock)&&jr(e,r)))||Lr(i,(e=>"block"===e.type&&(Nr(e)||-1!==e.blocks.indexOf(a))&&(!n||e.isMultiBlock)&&jr(e,r)));if(!s)return null;let c;if(c=s.isMultiBlock?"__experimentalConvert"in s?s.__experimentalConvert(r):s.transform(r.map((e=>e.attributes)),r.map((e=>e.innerBlocks))):"__experimentalConvert"in s?s.__experimentalConvert(o):s.transform(o.attributes,o.innerBlocks),null===c||"object"!=typeof c)return null;if(c=Array.isArray(c)?c:[c],c.some((e=>!we(e.name))))return null;if(!c.some((e=>e.name===t)))return null;return c.map(((t,r,n)=>(0,qt.applyFilters)("blocks.switchToBlockType.transformedBlock",t,e,r,n)))}const zr=(e,t)=>{var r;return Tr(e,t.attributes,(null!==(r=t.innerBlocks)&&void 0!==r?r:[]).map((e=>zr(e.name,e))))},Ir=window.wp.blockSerializationDefaultParser,Rr=window.wp.autop,Hr=window.wp.isShallowEqual;var Vr=r.n(Hr);function $r(e,t={}){const{isCommentDelimited:r=!0}=t,{blockName:n,attrs:o={},innerBlocks:a=[],innerContent:i=[]}=e;let s=0;const c=i.map((e=>null!==e?e:$r(a[s++],t))).join("\n").replace(/\n+/g,"\n").trim();return r?Jr(n,o,c):c}const Ur=window.ReactJSXRuntime;function Fr(e){const t="wp-block-"+e.replace(/\//,"-").replace(/^core-/,"");return(0,qt.applyFilters)("blocks.getBlockDefaultClassName",t,e)}function qr(e){const t="editor-block-list-item-"+e.replace(/\//,"-").replace(/^core-/,"");return(0,qt.applyFilters)("blocks.getBlockMenuDefaultClassName",t,e)}const Gr={},Kr={};function Wr(e={}){const{blockType:t,attributes:r}=Gr;return Wr.skipFilters?e:(0,qt.applyFilters)("blocks.getSaveContent.extraProps",{...e},t,r)}function Yr(e={}){const{innerBlocks:t}=Kr;if(!Array.isArray(t))return{...e,children:t};const r=rn(t,{isInnerBlocks:!0}),n=(0,Ur.jsx)(G.RawHTML,{children:r});return{...e,children:n}}function Qr(e,t,r=[]){const n=Fe(e);if(!n?.save)return null;let{save:o}=n;if(o.prototype instanceof G.Component){const e=new o({attributes:t});o=e.render.bind(e)}Gr.blockType=n,Gr.attributes=t,Kr.innerBlocks=r;let a=o({attributes:t,innerBlocks:r});if(null!==a&&"object"==typeof a&&(0,qt.hasFilter)("blocks.getSaveContent.extraProps")&&!(n.apiVersion>1)){const e=(0,qt.applyFilters)("blocks.getSaveContent.extraProps",{...a.props},n,t);Vr()(e,a.props)||(a=(0,G.cloneElement)(a,e))}return(0,qt.applyFilters)("blocks.getSaveElement",a,n,t)}function Zr(e,t,r){const n=Fe(e);return(0,G.renderToString)(Qr(n,t,r))}function Xr(e){let t=e.originalContent;if(e.isValid||e.innerBlocks.length)try{t=Zr(e.name,e.attributes,e.innerBlocks)}catch(e){}return t}function Jr(e,t,r){const n=t&&Object.entries(t).length?function(e){return JSON.stringify(e).replace(/--/g,"\\u002d\\u002d").replace(/</g,"\\u003c").replace(/>/g,"\\u003e").replace(/&/g,"\\u0026").replace(/\\"/g,"\\u0022")}(t)+" ":"",o=e?.startsWith("core/")?e.slice(5):e;return r?`\x3c!-- wp:${o} ${n}--\x3e\n`+r+`\n\x3c!-- /wp:${o} --\x3e`:`\x3c!-- wp:${o} ${n}/--\x3e`}function en(e,{isInnerBlocks:t=!1}={}){if(!e.isValid&&e.__unstableBlockSource)return $r(e.__unstableBlockSource);const r=e.name,n=Xr(e);if(r===be()||!t&&r===he())return n;const o=we(r);if(!o)return n;const a=function(e,t){var r;return Object.entries(null!==(r=e.attributes)&&void 0!==r?r:{}).reduce(((r,[n,o])=>{const a=t[n];return void 0===a||void 0!==o.source||"local"===o.role?r:"local"===o.__experimentalRole?(Q()("__experimentalRole attribute",{since:"6.7",version:"6.8",alternative:"role attribute",hint:`Check the block.json of the ${e?.name} block.`}),r):("default"in o&&JSON.stringify(o.default)===JSON.stringify(a)||(r[n]=a),r)}),{})}(o,e.attributes);return Jr(r,a,n)}function tn(e){1===e.length&&Ve(e[0])&&(e=[]);let t=rn(e);return 1===e.length&&e[0].name===he()&&"core/freeform"===e[0].name&&(t=(0,Rr.removep)(t)),t}function rn(e,t){return(Array.isArray(e)?e:[e]).map((e=>en(e,t))).join("\n\n")}var nn=/^#[xX]([A-Fa-f0-9]+)$/,on=/^#([0-9]+)$/,an=/^([A-Za-z0-9]+)$/,sn=(function(){function e(e){this.named=e}e.prototype.parse=function(e){if(e){var t=e.match(nn);return t?String.fromCharCode(parseInt(t[1],16)):(t=e.match(on))?String.fromCharCode(parseInt(t[1],10)):(t=e.match(an))?this.named[t[1]]:void 0}}}(),/[\t\n\f ]/),cn=/[A-Za-z]/,ln=/\r\n?/g;function un(e){return sn.test(e)}function dn(e){return cn.test(e)}var pn=function(){function e(e,t,r){void 0===r&&(r="precompile"),this.delegate=e,this.entityParser=t,this.mode=r,this.state="beforeData",this.line=-1,this.column=-1,this.input="",this.index=-1,this.tagNameBuffer="",this.states={beforeData:function(){var e=this.peek();if("<"!==e||this.isIgnoredEndTag()){if("precompile"===this.mode&&"\n"===e){var t=this.tagNameBuffer.toLowerCase();"pre"!==t&&"textarea"!==t||this.consume()}this.transitionTo("data"),this.delegate.beginData()}else this.transitionTo("tagOpen"),this.markTagStart(),this.consume()},data:function(){var e=this.peek(),t=this.tagNameBuffer;"<"!==e||this.isIgnoredEndTag()?"&"===e&&"script"!==t&&"style"!==t?(this.consume(),this.delegate.appendToData(this.consumeCharRef()||"&")):(this.consume(),this.delegate.appendToData(e)):(this.delegate.finishData(),this.transitionTo("tagOpen"),this.markTagStart(),this.consume())},tagOpen:function(){var e=this.consume();"!"===e?this.transitionTo("markupDeclarationOpen"):"/"===e?this.transitionTo("endTagOpen"):("@"===e||":"===e||dn(e))&&(this.transitionTo("tagName"),this.tagNameBuffer="",this.delegate.beginStartTag(),this.appendToTagName(e))},markupDeclarationOpen:function(){var e=this.consume();"-"===e&&"-"===this.peek()?(this.consume(),this.transitionTo("commentStart"),this.delegate.beginComment()):"DOCTYPE"===e.toUpperCase()+this.input.substring(this.index,this.index+6).toUpperCase()&&(this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.transitionTo("doctype"),this.delegate.beginDoctype&&this.delegate.beginDoctype())},doctype:function(){un(this.consume())&&this.transitionTo("beforeDoctypeName")},beforeDoctypeName:function(){var e=this.consume();un(e)||(this.transitionTo("doctypeName"),this.delegate.appendToDoctypeName&&this.delegate.appendToDoctypeName(e.toLowerCase()))},doctypeName:function(){var e=this.consume();un(e)?this.transitionTo("afterDoctypeName"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeName&&this.delegate.appendToDoctypeName(e.toLowerCase())},afterDoctypeName:function(){var e=this.consume();if(!un(e))if(">"===e)this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData");else{var t=e.toUpperCase()+this.input.substring(this.index,this.index+5).toUpperCase(),r="PUBLIC"===t.toUpperCase(),n="SYSTEM"===t.toUpperCase();(r||n)&&(this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.consume()),r?this.transitionTo("afterDoctypePublicKeyword"):n&&this.transitionTo("afterDoctypeSystemKeyword")}},afterDoctypePublicKeyword:function(){var e=this.peek();un(e)?(this.transitionTo("beforeDoctypePublicIdentifier"),this.consume()):'"'===e?(this.transitionTo("doctypePublicIdentifierDoubleQuoted"),this.consume()):"'"===e?(this.transitionTo("doctypePublicIdentifierSingleQuoted"),this.consume()):">"===e&&(this.consume(),this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData"))},doctypePublicIdentifierDoubleQuoted:function(){var e=this.consume();'"'===e?this.transitionTo("afterDoctypePublicIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypePublicIdentifier&&this.delegate.appendToDoctypePublicIdentifier(e)},doctypePublicIdentifierSingleQuoted:function(){var e=this.consume();"'"===e?this.transitionTo("afterDoctypePublicIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypePublicIdentifier&&this.delegate.appendToDoctypePublicIdentifier(e)},afterDoctypePublicIdentifier:function(){var e=this.consume();un(e)?this.transitionTo("betweenDoctypePublicAndSystemIdentifiers"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):'"'===e?this.transitionTo("doctypeSystemIdentifierDoubleQuoted"):"'"===e&&this.transitionTo("doctypeSystemIdentifierSingleQuoted")},betweenDoctypePublicAndSystemIdentifiers:function(){var e=this.consume();un(e)||(">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):'"'===e?this.transitionTo("doctypeSystemIdentifierDoubleQuoted"):"'"===e&&this.transitionTo("doctypeSystemIdentifierSingleQuoted"))},doctypeSystemIdentifierDoubleQuoted:function(){var e=this.consume();'"'===e?this.transitionTo("afterDoctypeSystemIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeSystemIdentifier&&this.delegate.appendToDoctypeSystemIdentifier(e)},doctypeSystemIdentifierSingleQuoted:function(){var e=this.consume();"'"===e?this.transitionTo("afterDoctypeSystemIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeSystemIdentifier&&this.delegate.appendToDoctypeSystemIdentifier(e)},afterDoctypeSystemIdentifier:function(){var e=this.consume();un(e)||">"===e&&(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData"))},commentStart:function(){var e=this.consume();"-"===e?this.transitionTo("commentStartDash"):">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData(e),this.transitionTo("comment"))},commentStartDash:function(){var e=this.consume();"-"===e?this.transitionTo("commentEnd"):">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData("-"),this.transitionTo("comment"))},comment:function(){var e=this.consume();"-"===e?this.transitionTo("commentEndDash"):this.delegate.appendToCommentData(e)},commentEndDash:function(){var e=this.consume();"-"===e?this.transitionTo("commentEnd"):(this.delegate.appendToCommentData("-"+e),this.transitionTo("comment"))},commentEnd:function(){var e=this.consume();">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData("--"+e),this.transitionTo("comment"))},tagName:function(){var e=this.consume();un(e)?this.transitionTo("beforeAttributeName"):"/"===e?this.transitionTo("selfClosingStartTag"):">"===e?(this.delegate.finishTag(),this.transitionTo("beforeData")):this.appendToTagName(e)},endTagName:function(){var e=this.consume();un(e)?(this.transitionTo("beforeAttributeName"),this.tagNameBuffer=""):"/"===e?(this.transitionTo("selfClosingStartTag"),this.tagNameBuffer=""):">"===e?(this.delegate.finishTag(),this.transitionTo("beforeData"),this.tagNameBuffer=""):this.appendToTagName(e)},beforeAttributeName:function(){var e=this.peek();un(e)?this.consume():"/"===e?(this.transitionTo("selfClosingStartTag"),this.consume()):">"===e?(this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):"="===e?(this.delegate.reportSyntaxError("attribute name cannot start with equals sign"),this.transitionTo("attributeName"),this.delegate.beginAttribute(),this.consume(),this.delegate.appendToAttributeName(e)):(this.transitionTo("attributeName"),this.delegate.beginAttribute())},attributeName:function(){var e=this.peek();un(e)?(this.transitionTo("afterAttributeName"),this.consume()):"/"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"="===e?(this.transitionTo("beforeAttributeValue"),this.consume()):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):'"'===e||"'"===e||"<"===e?(this.delegate.reportSyntaxError(e+" is not a valid character within attribute names"),this.consume(),this.delegate.appendToAttributeName(e)):(this.consume(),this.delegate.appendToAttributeName(e))},afterAttributeName:function(){var e=this.peek();un(e)?this.consume():"/"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"="===e?(this.consume(),this.transitionTo("beforeAttributeValue")):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.transitionTo("attributeName"),this.delegate.beginAttribute(),this.consume(),this.delegate.appendToAttributeName(e))},beforeAttributeValue:function(){var e=this.peek();un(e)?this.consume():'"'===e?(this.transitionTo("attributeValueDoubleQuoted"),this.delegate.beginAttributeValue(!0),this.consume()):"'"===e?(this.transitionTo("attributeValueSingleQuoted"),this.delegate.beginAttributeValue(!0),this.consume()):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.transitionTo("attributeValueUnquoted"),this.delegate.beginAttributeValue(!1),this.consume(),this.delegate.appendToAttributeValue(e))},attributeValueDoubleQuoted:function(){var e=this.consume();'"'===e?(this.delegate.finishAttributeValue(),this.transitionTo("afterAttributeValueQuoted")):"&"===e?this.delegate.appendToAttributeValue(this.consumeCharRef()||"&"):this.delegate.appendToAttributeValue(e)},attributeValueSingleQuoted:function(){var e=this.consume();"'"===e?(this.delegate.finishAttributeValue(),this.transitionTo("afterAttributeValueQuoted")):"&"===e?this.delegate.appendToAttributeValue(this.consumeCharRef()||"&"):this.delegate.appendToAttributeValue(e)},attributeValueUnquoted:function(){var e=this.peek();un(e)?(this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("beforeAttributeName")):"/"===e?(this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"&"===e?(this.consume(),this.delegate.appendToAttributeValue(this.consumeCharRef()||"&")):">"===e?(this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.consume(),this.delegate.appendToAttributeValue(e))},afterAttributeValueQuoted:function(){var e=this.peek();un(e)?(this.consume(),this.transitionTo("beforeAttributeName")):"/"===e?(this.consume(),this.transitionTo("selfClosingStartTag")):">"===e?(this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):this.transitionTo("beforeAttributeName")},selfClosingStartTag:function(){">"===this.peek()?(this.consume(),this.delegate.markTagAsSelfClosing(),this.delegate.finishTag(),this.transitionTo("beforeData")):this.transitionTo("beforeAttributeName")},endTagOpen:function(){var e=this.consume();("@"===e||":"===e||dn(e))&&(this.transitionTo("endTagName"),this.tagNameBuffer="",this.delegate.beginEndTag(),this.appendToTagName(e))}},this.reset()}return e.prototype.reset=function(){this.transitionTo("beforeData"),this.input="",this.tagNameBuffer="",this.index=0,this.line=1,this.column=0,this.delegate.reset()},e.prototype.transitionTo=function(e){this.state=e},e.prototype.tokenize=function(e){this.reset(),this.tokenizePart(e),this.tokenizeEOF()},e.prototype.tokenizePart=function(e){for(this.input+=function(e){return e.replace(ln,"\n")}(e);this.index<this.input.length;){var t=this.states[this.state];if(void 0===t)throw new Error("unhandled state "+this.state);t.call(this)}},e.prototype.tokenizeEOF=function(){this.flushData()},e.prototype.flushData=function(){"data"===this.state&&(this.delegate.finishData(),this.transitionTo("beforeData"))},e.prototype.peek=function(){return this.input.charAt(this.index)},e.prototype.consume=function(){var e=this.peek();return this.index++,"\n"===e?(this.line++,this.column=0):this.column++,e},e.prototype.consumeCharRef=function(){var e=this.input.indexOf(";",this.index);if(-1!==e){var t=this.input.slice(this.index,e),r=this.entityParser.parse(t);if(r){for(var n=t.length;n;)this.consume(),n--;return this.consume(),r}}},e.prototype.markTagStart=function(){this.delegate.tagOpen()},e.prototype.appendToTagName=function(e){this.tagNameBuffer+=e,this.delegate.appendToTagName(e)},e.prototype.isIgnoredEndTag=function(){var e=this.tagNameBuffer;return"title"===e&&"</title>"!==this.input.substring(this.index,this.index+8)||"style"===e&&"</style>"!==this.input.substring(this.index,this.index+8)||"script"===e&&"<\/script>"!==this.input.substring(this.index,this.index+9)},e}(),fn=function(){function e(e,t){void 0===t&&(t={}),this.options=t,this.token=null,this.startLine=1,this.startColumn=0,this.tokens=[],this.tokenizer=new pn(this,e,t.mode),this._currentAttribute=void 0}return e.prototype.tokenize=function(e){return this.tokens=[],this.tokenizer.tokenize(e),this.tokens},e.prototype.tokenizePart=function(e){return this.tokens=[],this.tokenizer.tokenizePart(e),this.tokens},e.prototype.tokenizeEOF=function(){return this.tokens=[],this.tokenizer.tokenizeEOF(),this.tokens[0]},e.prototype.reset=function(){this.token=null,this.startLine=1,this.startColumn=0},e.prototype.current=function(){var e=this.token;if(null===e)throw new Error("token was unexpectedly null");if(0===arguments.length)return e;for(var t=0;t<arguments.length;t++)if(e.type===arguments[t])return e;throw new Error("token type was unexpectedly "+e.type)},e.prototype.push=function(e){this.token=e,this.tokens.push(e)},e.prototype.currentAttribute=function(){return this._currentAttribute},e.prototype.addLocInfo=function(){this.options.loc&&(this.current().loc={start:{line:this.startLine,column:this.startColumn},end:{line:this.tokenizer.line,column:this.tokenizer.column}}),this.startLine=this.tokenizer.line,this.startColumn=this.tokenizer.column},e.prototype.beginDoctype=function(){this.push({type:"Doctype",name:""})},e.prototype.appendToDoctypeName=function(e){this.current("Doctype").name+=e},e.prototype.appendToDoctypePublicIdentifier=function(e){var t=this.current("Doctype");void 0===t.publicIdentifier?t.publicIdentifier=e:t.publicIdentifier+=e},e.prototype.appendToDoctypeSystemIdentifier=function(e){var t=this.current("Doctype");void 0===t.systemIdentifier?t.systemIdentifier=e:t.systemIdentifier+=e},e.prototype.endDoctype=function(){this.addLocInfo()},e.prototype.beginData=function(){this.push({type:"Chars",chars:""})},e.prototype.appendToData=function(e){this.current("Chars").chars+=e},e.prototype.finishData=function(){this.addLocInfo()},e.prototype.beginComment=function(){this.push({type:"Comment",chars:""})},e.prototype.appendToCommentData=function(e){this.current("Comment").chars+=e},e.prototype.finishComment=function(){this.addLocInfo()},e.prototype.tagOpen=function(){},e.prototype.beginStartTag=function(){this.push({type:"StartTag",tagName:"",attributes:[],selfClosing:!1})},e.prototype.beginEndTag=function(){this.push({type:"EndTag",tagName:""})},e.prototype.finishTag=function(){this.addLocInfo()},e.prototype.markTagAsSelfClosing=function(){this.current("StartTag").selfClosing=!0},e.prototype.appendToTagName=function(e){this.current("StartTag","EndTag").tagName+=e},e.prototype.beginAttribute=function(){this._currentAttribute=["","",!1]},e.prototype.appendToAttributeName=function(e){this.currentAttribute()[0]+=e},e.prototype.beginAttributeValue=function(e){this.currentAttribute()[2]=e},e.prototype.appendToAttributeValue=function(e){this.currentAttribute()[1]+=e},e.prototype.finishAttributeValue=function(){this.current("StartTag").attributes.push(this._currentAttribute)},e.prototype.reportSyntaxError=function(e){this.current().syntaxError=e},e}();var hn=r(7734),gn=r.n(hn);const mn=window.wp.htmlEntities;function bn(){function e(e){return(t,...r)=>e("Block validation: "+t,...r)}return{error:e(console.error),warning:e(console.warn),getItems:()=>[]}}const _n=/[\t\n\r\v\f ]+/g,yn=/^[\t\n\r\v\f ]*$/,kn=/^url\s*\(['"\s]*(.*?)['"\s]*\)$/,wn=["allowfullscreen","allowpaymentrequest","allowusermedia","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","typemustmatch"],vn=[...wn,"autocapitalize","autocomplete","charset","contenteditable","crossorigin","decoding","dir","draggable","enctype","formenctype","formmethod","http-equiv","inputmode","kind","method","preload","scope","shape","spellcheck","translate","type","wrap"],Tn=[e=>e,function(e){return Bn(e).join(" ")}],Cn=/^[\da-z]+$/i,xn=/^#\d+$/,En=/^#x[\da-f]+$/i;class Sn{parse(e){if(t=e,Cn.test(t)||xn.test(t)||En.test(t))return(0,mn.decodeEntities)("&"+e+";");var t}}function Bn(e){return e.trim().split(_n)}function An(e){return e.attributes.filter((e=>{const[t,r]=e;return r||0===t.indexOf("data-")||vn.includes(t)}))}function Nn(e,t,r=bn()){let n=e.chars,o=t.chars;for(let e=0;e<Tn.length;e++){const t=Tn[e];if(n=t(n),o=t(o),n===o)return!0}return r.warning("Expected text `%s`, saw `%s`.",t.chars,e.chars),!1}function Pn(e){return 0===parseFloat(e)?"0":0===e.indexOf(".")?"0"+e:e}function On(e){return Bn(e).map(Pn).join(" ").replace(kn,"url($1)")}function Ln(e){const t=e.replace(/;?\s*$/,"").split(";").map((e=>{const[t,...r]=e.split(":"),n=r.join(":");return[t.trim(),On(n.trim())]}));return Object.fromEntries(t)}const Mn={class:(e,t)=>{const[r,n]=[e,t].map(Bn),o=r.filter((e=>!n.includes(e))),a=n.filter((e=>!r.includes(e)));return 0===o.length&&0===a.length},style:(e,t)=>gn()(...[e,t].map(Ln)),...Object.fromEntries(wn.map((e=>[e,()=>!0])))};const jn={StartTag:(e,t,r=bn())=>e.tagName!==t.tagName&&e.tagName.toLowerCase()!==t.tagName.toLowerCase()?(r.warning("Expected tag name `%s`, instead saw `%s`.",t.tagName,e.tagName),!1):function(e,t,r=bn()){if(e.length!==t.length)return r.warning("Expected attributes %o, instead saw %o.",t,e),!1;const n={};for(let e=0;e<t.length;e++)n[t[e][0].toLowerCase()]=t[e][1];for(let t=0;t<e.length;t++){const[o,a]=e[t],i=o.toLowerCase();if(!n.hasOwnProperty(i))return r.warning("Encountered unexpected attribute `%s`.",o),!1;const s=n[i],c=Mn[i];if(c){if(!c(a,s))return r.warning("Expected attribute `%s` of value `%s`, saw `%s`.",o,s,a),!1}else if(a!==s)return r.warning("Expected attribute `%s` of value `%s`, saw `%s`.",o,s,a),!1}return!0}(...[e,t].map(An),r),Chars:Nn,Comment:Nn};function Dn(e){let t;for(;t=e.shift();){if("Chars"!==t.type)return t;if(!yn.test(t.chars))return t}}function zn(e,t){return!!e.selfClosing&&!(!t||t.tagName!==e.tagName||"EndTag"!==t.type)}function In(e,t,r=bn()){if(e===t)return!0;const[n,o]=[e,t].map((e=>function(e,t=bn()){try{return new fn(new Sn).tokenize(e)}catch(r){t.warning("Malformed HTML detected: %s",e)}return null}(e,r)));if(!n||!o)return!1;let a,i;for(;a=Dn(n);){if(i=Dn(o),!i)return r.warning("Expected end of content, instead saw %o.",a),!1;if(a.type!==i.type)return r.warning("Expected token of type `%s` (%o), instead saw `%s` (%o).",i.type,i,a.type,a),!1;const e=jn[a.type];if(e&&!e(a,i,r))return!1;zn(a,o[0])?Dn(o):zn(i,n[0])&&Dn(n)}return!(i=Dn(o))||(r.warning("Expected %o, instead saw end of content.",i),!1)}function Rn(e,t=e.name){if(e.name===he()||e.name===be())return[!0,[]];const r=function(){const e=[],t=bn();return{error(...r){e.push({log:t.error,args:r})},warning(...r){e.push({log:t.warning,args:r})},getItems:()=>e}}(),n=Fe(t);let o;try{o=Zr(n,e.attributes)}catch(e){return r.error("Block validation failed because an error occurred while generating block content:\n\n%s",e.toString()),[!1,r.getItems()]}const a=In(e.originalContent,o,r);return a||r.error("Block validation failed for `%s` (%o).\n\nContent generated by `save` function:\n\n%s\n\nContent retrieved from post body:\n\n%s",n.name,n,o,e.originalContent),[a,r.getItems()]}function Hn(e,t,r){Q()("isValidBlockContent introduces opportunity for data loss",{since:"12.6",plugin:"Gutenberg",alternative:"validateBlock"});const n=Fe(e),o={name:n.name,attributes:t,innerBlocks:[],originalContent:r},[a]=Rn(o,n);return a}function Vn(e,t){const r={...t};if("core/cover-image"===e&&(e="core/cover"),"core/text"!==e&&"core/cover-text"!==e||(e="core/paragraph"),e&&0===e.indexOf("core/social-link-")&&(r.service=e.substring(17),e="core/social-link"),e&&0===e.indexOf("core-embed/")){const t=e.substring(11),n={speaker:"speaker-deck",polldaddy:"crowdsignal"};r.providerNameSlug=t in n?n[t]:t,["amazon-kindle","wordpress"].includes(t)||(r.responsive=!0),e="core/embed"}if("core/post-comment-author"===e&&(e="core/comment-author-name"),"core/post-comment-content"===e&&(e="core/comment-content"),"core/post-comment-date"===e&&(e="core/comment-date"),"core/comments-query-loop"===e){e="core/comments";const{className:t=""}=r;t.includes("wp-block-comments-query-loop")||(r.className=["wp-block-comments-query-loop",t].join(" "))}if("core/post-comments"===e&&(e="core/comments",r.legacy=!0),"grid"===t.layout?.type&&"string"==typeof t.layout?.columnCount&&(r.layout={...r.layout,columnCount:parseInt(t.layout.columnCount,10)}),"string"==typeof t.style?.layout?.columnSpan){const e=parseInt(t.style.layout.columnSpan,10);r.style={...r.style,layout:{...r.style.layout,columnSpan:isNaN(e)?void 0:e}}}if("string"==typeof t.style?.layout?.rowSpan){const e=parseInt(t.style.layout.rowSpan,10);r.style={...r.style,layout:{...r.style.layout,rowSpan:isNaN(e)?void 0:e}}}return[e,r]}var $n,Un=function(){return $n||($n=document.implementation.createHTMLDocument("")),$n};function Fn(e,t){if(t){if("string"==typeof e){var r=Un();r.body.innerHTML=e,e=r.body}if("function"==typeof t)return t(e);if(Object===t.constructor)return Object.keys(t).reduce((function(r,n){return r[n]=Fn(e,t[n]),r}),{})}}function qn(e,t){return 1===arguments.length&&(t=e,e=void 0),function(r){var n=r;if(e&&(n=r.querySelector(e)),n)return function(e,t){for(var r,n=t.split(".");r=n.shift();){if(!(r in e))return;e=e[r]}return e}(n,t)}}function Gn(e){const t={};for(let r=0;r<e.length;r++){const{name:n,value:o}=e[r];t[n]=o}return t}function Kn(e){if(Q()("wp.blocks.node.fromDOM",{since:"6.1",version:"6.3",alternative:"wp.richText.create",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),e.nodeType===e.TEXT_NODE)return e.nodeValue;if(e.nodeType!==e.ELEMENT_NODE)throw new TypeError("A block node can only be created from a node of type text or element.");return{type:e.nodeName.toLowerCase(),props:{...Gn(e.attributes),children:Qn(e.childNodes)}}}function Wn(e){return Q()("wp.blocks.node.matcher",{since:"6.1",version:"6.3",alternative:"html source",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),t=>{let r=t;e&&(r=t.querySelector(e));try{return Kn(r)}catch(e){return null}}}const Yn={isNodeOfType:function(e,t){return Q()("wp.blocks.node.isNodeOfType",{since:"6.1",version:"6.3",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),e&&e.type===t},fromDOM:Kn,toHTML:function(e){return Q()("wp.blocks.node.toHTML",{since:"6.1",version:"6.3",alternative:"wp.richText.toHTMLString",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),Zn([e])},matcher:Wn};function Qn(e){Q()("wp.blocks.children.fromDOM",{since:"6.1",version:"6.3",alternative:"wp.richText.create",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"});const t=[];for(let r=0;r<e.length;r++)try{t.push(Kn(e[r]))}catch(e){}return t}function Zn(e){Q()("wp.blocks.children.toHTML",{since:"6.1",version:"6.3",alternative:"wp.richText.toHTMLString",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"});const t=e;return(0,G.renderToString)(t)}function Xn(e){return Q()("wp.blocks.children.matcher",{since:"6.1",version:"6.3",alternative:"html source",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),t=>{let r=t;return e&&(r=t.querySelector(e)),r?Qn(r.childNodes):[]}}const Jn={concat:function(...e){Q()("wp.blocks.children.concat",{since:"6.1",version:"6.3",alternative:"wp.richText.concat",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"});const t=[];for(let r=0;r<e.length;r++){const n=Array.isArray(e[r])?e[r]:[e[r]];for(let e=0;e<n.length;e++){const r=n[e];"string"==typeof r&&"string"==typeof t[t.length-1]?t[t.length-1]+=r:t.push(r)}}return t},getChildrenArray:function(e){return Q()("wp.blocks.children.getChildrenArray",{since:"6.1",version:"6.3",link:"https://developer.wordpress.org/block-editor/how-to-guides/block-tutorial/introducing-attributes-and-editable-fields/"}),e},fromDOM:Qn,toHTML:Zn,matcher:Xn};function eo(e,t){return t.some((t=>function(e,t){switch(t){case"rich-text":return e instanceof W.RichTextData;case"string":return"string"==typeof e;case"boolean":return"boolean"==typeof e;case"object":return!!e&&e.constructor===Object;case"null":return null===e;case"array":return Array.isArray(e);case"integer":case"number":return"number"==typeof e}return!0}(e,t)))}function to(e,t,r,n,o){let a;switch(t.source){case void 0:a=n?n[e]:void 0;break;case"raw":a=o;break;case"attribute":case"property":case"html":case"text":case"rich-text":case"children":case"node":case"query":case"tag":a=oo(r,t)}return function(e,t){return void 0===t||eo(e,Array.isArray(t)?t:[t])}(a,t.type)&&function(e,t){return!Array.isArray(t)||t.includes(e)}(a,t.enum)||(a=void 0),void 0===a&&(a=Ke(t)),a}const ro=function(e,t){var r,n,o=0;function a(){var a,i,s=r,c=arguments.length;e:for(;s;){if(s.args.length===arguments.length){for(i=0;i<c;i++)if(s.args[i]!==arguments[i]){s=s.next;continue e}return s!==r&&(s===n&&(n=s.prev),s.prev.next=s.next,s.next&&(s.next.prev=s.prev),s.next=r,s.prev=null,r.prev=s,r=s),s.val}s=s.next}for(a=new Array(c),i=0;i<c;i++)a[i]=arguments[i];return s={args:a,val:e.apply(null,a)},r?(r.prev=s,s.next=r):n=s,o===t.maxSize?(n=n.prev).next=null:o++,r=s,s.val}return t=t||{},a.clear=function(){r=null,n=null,o=0},a}((e=>{switch(e.source){case"attribute":{let t=function(e,t){return 1===arguments.length&&(t=e,e=void 0),function(r){var n=qn(e,"attributes")(r);if(n&&n.hasOwnProperty(t))return n[t].value}}(e.selector,e.attribute);return"boolean"===e.type&&(t=(e=>t=>void 0!==e(t))(t)),t}case"html":return t=e.selector,r=e.multiline,e=>{let n=e;if(t&&(n=e.querySelector(t)),!n)return"";if(r){let e="";const t=n.children.length;for(let o=0;o<t;o++){const t=n.children[o];t.nodeName.toLowerCase()===r&&(e+=t.outerHTML)}return e}return n.innerHTML};case"text":return function(e){return qn(e,"textContent")}(e.selector);case"rich-text":return((e,t)=>r=>{const n=e?r.querySelector(e):r;return n?W.RichTextData.fromHTMLElement(n,{preserveWhiteSpace:t}):W.RichTextData.empty()})(e.selector,e.__unstablePreserveWhiteSpace);case"children":return Xn(e.selector);case"node":return Wn(e.selector);case"query":const n=Object.fromEntries(Object.entries(e.query).map((([e,t])=>[e,ro(t)])));return function(e,t){return function(r){var n=r.querySelectorAll(e);return[].map.call(n,(function(e){return Fn(e,t)}))}}(e.selector,n);case"tag":{const t=qn(e.selector,"nodeName");return e=>t(e)?.toLowerCase()}default:console.error(`Unknown source type "${e.source}"`)}var t,r}));function no(e){return Fn(e,(e=>e))}function oo(e,t){return ro(t)(no(e))}function ao(e,t,r={}){var n;const o=no(t),a=Fe(e),i=Object.fromEntries(Object.entries(null!==(n=a.attributes)&&void 0!==n?n:{}).map((([e,n])=>[e,to(e,n,o,r,t)])));return(0,qt.applyFilters)("blocks.getBlockAttributes",i,a,t,r)}const io={type:"string",source:"attribute",selector:"[data-custom-class-name] > *",attribute:"class"};function so(e){const t=oo(`<div data-custom-class-name>${e}</div>`,io);return t?t.trim().split(/\s+/):[]}const co={type:"string",source:"attribute",selector:"[data-aria-label] > *",attribute:"aria-label"};function lo(e,t,r){if(!Ce(t,"ariaLabel",!1))return e;const n={...e},o=function(e){return oo(`<div data-aria-label>${e}</div>`,co)}(r);return o&&(n.ariaLabel=o),n}function uo(e,t){const{attributes:r,originalContent:n}=e;let o=r;return o=function(e,t,r){if(!Ce(t,"customClassName",!0))return e;const n={...e},{className:o,...a}=n,i=Zr(t,a),s=so(i),c=so(r).filter((e=>!s.includes(e)));return c.length?n.className=c.join(" "):i&&delete n.className,n}(r,t,n),o=lo(o,t,n),{...e,attributes:o}}function po(){return!1}function fo(e,t){let r=function(e,t){const r=he(),n=e.blockName||he(),o=e.attrs||{},a=e.innerBlocks||[];let i=e.innerHTML.trim();return n!==r||"core/freeform"!==n||t?.__unstableSkipAutop||(i=(0,Rr.autop)(i).trim()),{...e,blockName:n,attrs:o,innerHTML:i,innerBlocks:a}}(e,t);r=function(e){const[t,r]=Vn(e.blockName,e.attrs);return{...e,blockName:t,attrs:r}}(r);let n=we(r.blockName);n||(r=function(e){const t=be()||he(),r=$r(e,{isCommentDelimited:!1}),n=$r(e,{isCommentDelimited:!0});return{blockName:t,attrs:{originalName:e.blockName,originalContent:n,originalUndelimitedContent:r},innerHTML:e.blockName?n:e.innerHTML,innerBlocks:e.innerBlocks,innerContent:e.innerContent}}(r),n=we(r.blockName));const o=r.blockName===he()||r.blockName===be();if(!n||!r.innerHTML&&o)return;const a=r.innerBlocks.map((e=>fo(e,t))).filter((e=>!!e)),i=Tr(r.blockName,ao(n,r.innerHTML,r.attrs),a);i.originalContent=r.innerHTML;const s=function(e,t){const[r]=Rn(e,t);if(r)return{...e,isValid:r,validationIssues:[]};const n=uo(e,t),[o,a]=Rn(n,t);return{...n,isValid:o,validationIssues:a}}(i,n),{validationIssues:c}=s,l=function(e,t,r){const n=t.attrs,{deprecated:o}=r;if(!o||!o.length)return e;for(let a=0;a<o.length;a++){const{isEligible:i=po}=o[a];if(e.isValid&&!i(n,e.innerBlocks,{blockNode:t,block:e}))continue;const s=Object.assign(Xe(r,X),o[a]);let c={...e,attributes:ao(s,e.originalContent,n)},[l]=Rn(c,s);if(l||(c=uo(c,s),[l]=Rn(c,s)),!l)continue;let u=c.innerBlocks,d=c.attributes;const{migrate:p}=s;if(p){let t=p(d,e.innerBlocks);Array.isArray(t)||(t=[t]),[d=n,u=e.innerBlocks]=t}e={...e,attributes:d,innerBlocks:u,isValid:!0,validationIssues:[]}}return e}(s,r,n);return l.isValid||(l.__unstableBlockSource=e),s.isValid||!l.isValid||t?.__unstableSkipMigrationLogs?s.isValid||l.isValid||c.forEach((({log:e,args:t})=>e(...t))):(console.groupCollapsed("Updated Block: %s",n.name),console.info("Block successfully updated for `%s` (%o).\n\nNew content generated by `save` function:\n\n%s\n\nContent retrieved from post body:\n\n%s",n.name,n,Zr(n,l.attributes),l.originalContent),console.groupEnd()),l}function ho(e,t){return(0,Ir.parse)(e).reduce(((e,r)=>{const n=fo(r,t);return n&&e.push(n),e}),[])}function go(){return Mr("from").filter((({type:e})=>"raw"===e)).map((e=>e.isMatch?e:{...e,isMatch:t=>e.selector&&t.matches(e.selector)}))}function mo(e,t){const r=document.implementation.createHTMLDocument("");return r.body.innerHTML=e,Array.from(r.body.children).flatMap((e=>{const r=Lr(go(),(({isMatch:t})=>t(e)));if(!r)return G.Platform.isNative?ho(`\x3c!-- wp:html --\x3e${e.outerHTML}\x3c!-- /wp:html --\x3e`):Tr("core/html",ao("core/html",e.outerHTML));const{transform:n,blockName:o}=r;if(n){const r=n(e,t);return e.hasAttribute("class")&&(r.attributes.className=e.getAttribute("class")),r}return Tr(o,ao(o,e.outerHTML))}))}function bo(e,t={}){const r=document.implementation.createHTMLDocument(""),n=document.implementation.createHTMLDocument(""),o=r.body,a=n.body;for(o.innerHTML=e;o.firstChild;){const e=o.firstChild;e.nodeType===e.TEXT_NODE?(0,K.isEmpty)(e)?o.removeChild(e):(a.lastChild&&"P"===a.lastChild.nodeName||a.appendChild(n.createElement("P")),a.lastChild.appendChild(e)):e.nodeType===e.ELEMENT_NODE?"BR"===e.nodeName?(e.nextSibling&&"BR"===e.nextSibling.nodeName&&(a.appendChild(n.createElement("P")),o.removeChild(e.nextSibling)),a.lastChild&&"P"===a.lastChild.nodeName&&a.lastChild.hasChildNodes()?a.lastChild.appendChild(e):o.removeChild(e)):"P"===e.nodeName?(0,K.isEmpty)(e)&&!t.raw?o.removeChild(e):a.appendChild(e):(0,K.isPhrasingContent)(e)?(a.lastChild&&"P"===a.lastChild.nodeName||a.appendChild(n.createElement("P")),a.lastChild.appendChild(e)):a.appendChild(e):o.removeChild(e)}return a.innerHTML}function _o(e,t){if(e.nodeType!==e.COMMENT_NODE)return;if("nextpage"!==e.nodeValue&&0!==e.nodeValue.indexOf("more"))return;const r=function(e,t){if("nextpage"===e.nodeValue)return function(e){const t=e.createElement("wp-block");return t.dataset.block="core/nextpage",t}(t);const r=e.nodeValue.slice(4).trim();let n=e,o=!1;for(;n=n.nextSibling;)if(n.nodeType===n.COMMENT_NODE&&"noteaser"===n.nodeValue){o=!0,(0,K.remove)(n);break}return function(e,t,r){const n=r.createElement("wp-block");n.dataset.block="core/more",e&&(n.dataset.customText=e);t&&(n.dataset.noTeaser="");return n}(r,o,t)}(e,t);if(e.parentNode&&"P"===e.parentNode.nodeName){const n=Array.from(e.parentNode.childNodes),o=n.indexOf(e),a=e.parentNode.parentNode||t.body,i=(e,r)=>(e||(e=t.createElement("p")),e.appendChild(r),e);[n.slice(0,o).reduce(i,null),r,n.slice(o+1).reduce(i,null)].forEach((t=>t&&a.insertBefore(t,e.parentNode))),(0,K.remove)(e.parentNode)}else(0,K.replace)(e,r)}function yo(e){return"OL"===e.nodeName||"UL"===e.nodeName}function ko(e){if(!yo(e))return;const t=e,r=e.previousElementSibling;if(r&&r.nodeName===e.nodeName&&1===t.children.length){for(;t.firstChild;)r.appendChild(t.firstChild);t.parentNode.removeChild(t)}const n=e.parentNode;if(n&&"LI"===n.nodeName&&1===n.children.length&&!/\S/.test((o=n,Array.from(o.childNodes).map((({nodeValue:e=""})=>e)).join("")))){const e=n,r=e.previousElementSibling,o=e.parentNode;r&&(r.appendChild(t),o.removeChild(e))}var o;if(n&&yo(n)){const t=e.previousElementSibling;t?t.appendChild(e):(0,K.unwrap)(e)}}function wo(e){return t=>{"BLOCKQUOTE"===t.nodeName&&(t.innerHTML=bo(t.innerHTML,e))}}function vo(e,t=e){const r=e.ownerDocument.createElement("figure");t.parentNode.insertBefore(r,t),r.appendChild(e)}function To(e,t,r){if(!function(e,t){var r;const n=e.nodeName.toLowerCase();return"figcaption"!==n&&!(0,K.isTextContent)(e)&&n in(null!==(r=t?.figure?.children)&&void 0!==r?r:{})}(e,r))return;let n=e;const o=e.parentNode;(function(e,t){var r;return e.nodeName.toLowerCase()in(null!==(r=t?.figure?.children?.a?.children)&&void 0!==r?r:{})})(e,r)&&"A"===o.nodeName&&1===o.childNodes.length&&(n=e.parentNode);const a=n.closest("p,div");a?e.classList?(e.classList.contains("alignright")||e.classList.contains("alignleft")||e.classList.contains("aligncenter")||!a.textContent.trim())&&vo(n,a):vo(n,a):vo(n)}const Co=window.wp.shortcode,xo=e=>Array.isArray(e)?e:[e],Eo=/(\n|<p>)\s*$/,So=/^\s*(\n|<\/p>)/;const Bo=function e(t,r=0,n=[]){const o=Lr(Mr("from"),(e=>-1===n.indexOf(e.blockName)&&"shortcode"===e.type&&xo(e.tag).some((e=>(0,Co.regexp)(e).test(t)))));if(!o)return[t];const a=xo(o.tag).find((e=>(0,Co.regexp)(e).test(t)));let i;const s=r;if(i=(0,Co.next)(a,t,r)){r=i.index+i.content.length;const a=t.substr(0,i.index),c=t.substr(r);if(!(i.shortcode.content?.includes("<")||Eo.test(a)&&So.test(c)))return e(t,r);if(o.isMatch&&!o.isMatch(i.shortcode.attrs))return e(t,s,[...n,o.blockName]);let l=[];if("function"==typeof o.transform)l=[].concat(o.transform(i.shortcode.attrs,i)),l=l.map((e=>(e.originalContent=i.shortcode.content,uo(e,we(e.name)))));else{const e=Object.fromEntries(Object.entries(o.attributes).filter((([,e])=>e.shortcode)).map((([e,t])=>[e,t.shortcode(i.shortcode.attrs,i)]))),r=we(o.blockName);if(!r)return[t];const n={...r,attributes:o.attributes};let a=Tr(o.blockName,ao(n,i.shortcode.content,e));a.originalContent=i.shortcode.content,a=uo(a,n),l=[a]}return[...e(a.replace(Eo,"")),...l,...e(c.replace(So,""))]}return[t]};function Ao(e){return function(e,t){const r={phrasingContentSchema:(0,K.getPhrasingContentSchema)(t),isPaste:"paste"===t};function n(e,t,r){switch(r){case"children":return"*"===e||"*"===t?"*":{...e,...t};case"attributes":case"require":return[...e||[],...t||[]];case"isMatch":if(!e||!t)return;return(...r)=>e(...r)||t(...r)}}function o(e,t){for(const r in t)e[r]=e[r]?n(e[r],t[r],r):{...t[r]};return e}return e.map((({isMatch:e,blockName:t,schema:n})=>{const o=Ce(t,"anchor");return n="function"==typeof n?n(r):n,o||e?n?Object.fromEntries(Object.entries(n).map((([t,r])=>{let n=r.attributes||[];return o&&(n=[...n,"id"]),[t,{...r,attributes:n,isMatch:e||void 0}]}))):{}:n})).reduce((function(e,t){for(const r in t)e[r]=e[r]?o(e[r],t[r]):{...t[r]};return e}),{})}(go(),e)}function No(e,t,r,n){Array.from(e).forEach((e=>{No(e.childNodes,t,r,n),t.forEach((t=>{r.contains(e)&&t(e,r,n)}))}))}function Po(e,t=[],r){const n=document.implementation.createHTMLDocument("");return n.body.innerHTML=e,No(n.body.childNodes,t,n,r),n.body.innerHTML}function Oo(e,t){const r=e[`${t}Sibling`];if(r&&(0,K.isPhrasingContent)(r))return r;const{parentNode:n}=e;return n&&(0,K.isPhrasingContent)(n)?Oo(n,t):void 0}function Lo(e){return Q()("wp.blocks.getPhrasingContentSchema",{since:"5.6",alternative:"wp.dom.getPhrasingContentSchema"}),(0,K.getPhrasingContentSchema)(e)}function Mo({HTML:e=""}){if(-1!==e.indexOf("\x3c!-- wp:")){const t=ho(e);if(!(1===t.length&&"core/freeform"===t[0].name))return t}const t=Bo(e),r=Ao();return t.map((e=>{if("string"!=typeof e)return e;return mo(e=bo(e=Po(e,[ko,_o,To,wo({raw:!0})],r),{raw:!0}),Mo)})).flat().filter(Boolean)}function jo(e){e.nodeType===e.COMMENT_NODE&&(0,K.remove)(e)}function Do(e,t){return e.every((e=>function(e,t){if((0,K.isTextContent)(e))return!0;if(!t)return!1;const r=e.nodeName.toLowerCase();return[["ul","li","ol"],["h1","h2","h3","h4","h5","h6"]].some((e=>0===[r,t].filter((t=>!e.includes(t))).length))}(e,t)&&Do(Array.from(e.children),t)))}function zo(e){return"BR"===e.nodeName&&e.previousSibling&&"BR"===e.previousSibling.nodeName}function Io(e,t){if("SPAN"===e.nodeName&&e.style){const{fontWeight:r,fontStyle:n,textDecorationLine:o,textDecoration:a,verticalAlign:i}=e.style;"bold"!==r&&"700"!==r||(0,K.wrap)(t.createElement("strong"),e),"italic"===n&&(0,K.wrap)(t.createElement("em"),e),("line-through"===o||a.includes("line-through"))&&(0,K.wrap)(t.createElement("s"),e),"super"===i?(0,K.wrap)(t.createElement("sup"),e):"sub"===i&&(0,K.wrap)(t.createElement("sub"),e)}else"B"===e.nodeName?e=(0,K.replaceTag)(e,"strong"):"I"===e.nodeName?e=(0,K.replaceTag)(e,"em"):"A"===e.nodeName&&(e.target&&"_blank"===e.target.toLowerCase()?e.rel="noreferrer noopener":(e.removeAttribute("target"),e.removeAttribute("rel")),e.name&&!e.id&&(e.id=e.name),e.id&&!e.ownerDocument.querySelector(`[href="#${e.id}"]`)&&e.removeAttribute("id"))}function Ro(e){"SCRIPT"!==e.nodeName&&"NOSCRIPT"!==e.nodeName&&"TEMPLATE"!==e.nodeName&&"STYLE"!==e.nodeName||e.parentNode.removeChild(e)}function Ho(e){if(e.nodeType!==e.ELEMENT_NODE)return;const t=e.getAttribute("style");if(!t||!t.includes("mso-list"))return;"ignore"===t.split(";").reduce(((e,t)=>{const[r,n]=t.split(":");return r&&n&&(e[r.trim().toLowerCase()]=n.trim().toLowerCase()),e}),{})["mso-list"]&&e.remove()}function Vo(e){return"OL"===e.nodeName||"UL"===e.nodeName}function $o(e,t){if("P"!==e.nodeName)return;const r=e.getAttribute("style");if(!r||!r.includes("mso-list"))return;const n=e.previousElementSibling;if(!n||!Vo(n)){const r=e.textContent.trim().slice(0,1),n=/[1iIaA]/.test(r),o=t.createElement(n?"ol":"ul");n&&o.setAttribute("type",r),e.parentNode.insertBefore(o,e)}const o=e.previousElementSibling,a=o.nodeName,i=t.createElement("li");let s=o;i.innerHTML=Po(e.innerHTML,[Ho]);const c=/mso-list\s*:[^;]+level([0-9]+)/i.exec(r);let l=c&&parseInt(c[1],10)-1||0;for(;l--;)s=s.lastChild||s,Vo(s)&&(s=s.lastChild||s);Vo(s)||(s=s.appendChild(t.createElement(a))),s.appendChild(i),e.parentNode.removeChild(e)}const Uo=window.wp.blob;function Fo(e){if("IMG"===e.nodeName){if(0===e.src.indexOf("file:")&&(e.src=""),0===e.src.indexOf("data:")){const[t,r]=e.src.split(","),[n]=t.slice(5).split(";");if(!r||!n)return void(e.src="");let o;try{o=atob(r)}catch(t){return void(e.src="")}const a=new Uint8Array(o.length);for(let e=0;e<a.length;e++)a[e]=o.charCodeAt(e);const i=n.replace("/","."),s=new window.File([a],i,{type:n});e.src=(0,Uo.createBlobURL)(s)}1!==e.height&&1!==e.width||e.parentNode.removeChild(e)}}function qo(e){"DIV"===e.nodeName&&(e.innerHTML=bo(e.innerHTML))}var Go=r(1030);const Ko=new(r.n(Go)().Converter)({noHeaderId:!0,tables:!0,literalMidWordUnderscores:!0,omitExtraWLInCodeBlocks:!0,simpleLineBreaks:!0,strikethrough:!0});function Wo(e){if("IFRAME"===e.nodeName){const t=e.ownerDocument.createTextNode(e.src);e.parentNode.replaceChild(t,e)}}function Yo(e){e.id&&0===e.id.indexOf("docs-internal-guid-")&&("B"===e.tagName?(0,K.unwrap)(e):e.removeAttribute("id"))}function Qo(e){if(e.nodeType!==e.TEXT_NODE)return;let t=e;for(;t=t.parentNode;)if(t.nodeType===t.ELEMENT_NODE&&"PRE"===t.nodeName)return;let r=e.data.replace(/[ \r\n\t]+/g," ");if(" "===r[0]){const t=Oo(e,"previous");t&&"BR"!==t.nodeName&&" "!==t.textContent.slice(-1)||(r=r.slice(1))}if(" "===r[r.length-1]){const t=Oo(e,"next");(!t||"BR"===t.nodeName||t.nodeType===t.TEXT_NODE&&(" "===(n=t.textContent[0])||"\r"===n||"\n"===n||"\t"===n))&&(r=r.slice(0,-1))}var n;r?e.data=r:e.parentNode.removeChild(e)}function Zo(e){"BR"===e.nodeName&&(Oo(e,"next")||e.parentNode.removeChild(e))}function Xo(e){"P"===e.nodeName&&(e.hasChildNodes()||e.parentNode.removeChild(e))}function Jo(e){if("SPAN"!==e.nodeName)return;if("paragraph-break"!==e.getAttribute("data-stringify-type"))return;const{parentNode:t}=e;t.insertBefore(e.ownerDocument.createElement("br"),e),t.insertBefore(e.ownerDocument.createElement("br"),e),t.removeChild(e)}const ea=(...e)=>window?.console?.log?.(...e);function ta(e){return e=Po(e,[Ro,Yo,Ho,Io,jo]),e=Po(e=(0,K.removeInvalidHTML)(e,(0,K.getPhrasingContentSchema)("paste"),{inline:!0}),[Qo,Zo]),ea("Processed inline HTML:\n\n",e),e}function ra({HTML:e="",plainText:t="",mode:r="AUTO",tagName:n}){if(e=(e=(e=e.replace(/<meta[^>]+>/g,"")).replace(/^\s*<html[^>]*>\s*<body[^>]*>(?:\s*<!--\s*StartFragment\s*-->)?/i,"")).replace(/(?:<!--\s*EndFragment\s*-->\s*)?<\/body>\s*<\/html>\s*$/i,""),"INLINE"!==r){const r=e||t;if(-1!==r.indexOf("\x3c!-- wp:")){const e=ho(r);if(!(1===e.length&&"core/freeform"===e[0].name))return e}}String.prototype.normalize&&(e=e.normalize()),e=Po(e,[Jo]);const o=t&&(!e||function(e){return!/<(?!br[ />])/i.test(e)}(e));var a;o&&(e=t,/^\s+$/.test(t)||(a=e,e=Ko.makeHtml(function(e){return e.replace(/((?:^|\n)```)([^\n`]+)(```(?:$|\n))/,((e,t,r,n)=>`${t}\n${r}\n${n}`))}(function(e){return e.replace(/(^|\n)•( +)/g,"$1*$2")}(a)))));const i=Bo(e),s=i.length>1;if(o&&!s&&"AUTO"===r&&-1===t.indexOf("\n")&&0!==t.indexOf("<p>")&&0===e.indexOf("<p>")&&(r="INLINE"),"INLINE"===r)return ta(e);if("AUTO"===r&&!s&&function(e,t){const r=document.implementation.createHTMLDocument("");r.body.innerHTML=e;const n=Array.from(r.body.children);return!n.some(zo)&&Do(n,t)}(e,n))return ta(e);const c=(0,K.getPhrasingContentSchema)("paste"),l=Ao("paste"),u=i.map((e=>{if("string"!=typeof e)return e;const t=[Yo,$o,Ro,ko,Fo,Io,_o,jo,Wo,To,wo(),qo],r={...l,...c};return e=Po(e,t,l),e=Po(e=bo(e=(0,K.removeInvalidHTML)(e,r)),[Qo,Zo,Xo],l),ea("Processed HTML piece:\n\n",e),mo(e,ra)})).flat().filter(Boolean);if("AUTO"===r&&1===u.length&&Ce(u[0].name,"__unstablePasteTextInline",!1)){const e=/^[\n]+|[\n]+$/g,r=t.replace(e,"");if(""!==r&&-1===r.indexOf("\n"))return(0,K.removeInvalidHTML)(Xr(u[0]),c).replace(e,"")}return u}function na(){return(0,i.select)(gr).getCategories()}function oa(e){(0,i.dispatch)(gr).setCategories(e)}function aa(e,t){(0,i.dispatch)(gr).updateCategory(e,t)}function ia(e=[],t=[]){return e.length===t.length&&t.every((([t,,r],n)=>{const o=e[n];return t===o.name&&ia(o.innerBlocks,r)}))}const sa=e=>"html"===e?.source,ca=e=>"query"===e?.source;function la(e,t){return t?Object.fromEntries(Object.entries(t).map((([t,r])=>[t,ua(e[t],r)]))):{}}function ua(e,t){return sa(e)&&Array.isArray(t)?(0,G.renderToString)(t):ca(e)&&t?t.map((t=>la(e.query,t))):t}function da(e=[],t){return t?t.map((([t,r,n],o)=>{var a;const i=e[o];if(i&&i.name===t){const e=da(i.innerBlocks,n);return{...i,innerBlocks:e}}const s=we(t),c=la(null!==(a=s?.attributes)&&void 0!==a?a:{},r),[l,u]=Vn(t,c);return Tr(l,u,da([],n))})):e}const pa={};function fa(e){return Q()("wp.blocks.withBlockContentContext",{since:"6.1"}),e}ne(pa,{isContentBlock:function(e){const t=we(e)?.attributes;return!!t&&!!Object.keys(t)?.some((e=>{const r=t[e];return"content"===r?.role||"content"===r?.__experimentalRole}))}})})(),(window.wp=window.wp||{}).blocks=n})();
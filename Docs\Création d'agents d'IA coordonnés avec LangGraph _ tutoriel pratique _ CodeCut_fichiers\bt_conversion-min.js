function setAbstApprovalStatus(approved){window.abst.waitForApproval=!approved,approved?localStorage.setItem("abstApprovalStatus","approved"):localStorage.removeItem("abstApprovalStatus")}window.abst=window.abst||{},window.abst.eventQueue=[],window.abst.abconvertpartner={},window.abst.waitForApproval=!1,window.abst.timer=null===localStorage.getItem("absttimer")?{}:JSON.parse(localStorage.getItem("absttimer")||"{}"),window.abst.currscroll=window.scrollY,window.abst.currentMousePos=-1,window.abst.oldMousePos=-2,window.abst.abactive=!0,window.abst.timeoutTime=3e3,window.abst.intervals={};const services=["abawp","clarity","gai","abmix","abumav","umami","cabin","plausible","fathom","ga4","posthog"];function addServerEvents(data){const serverEvents=abstGetCookie("abst_server_events");if(serverEvents){const events=JSON.parse(decodeURIComponent(serverEvents));events.some((event=>event.eid===data.eid&&event.variation===data.variation&&event.type===data.type))||(events.push(data),abstSetCookie("abst_server_events",JSON.stringify(events),2),console.log("server event added to existing",data))}else abstSetCookie("abst_server_events",JSON.stringify([data]),2),console.log("server event added none existing creating",data)}function startTextWatcher(experimentId,word,goalId=null){goalId||(goalId=0),void 0===abst.intervals[experimentId]&&(abst.intervals[experimentId]={}),window.abst.intervals[experimentId]&&window.abst.intervals[experimentId][goalId]&&clearInterval(window.abst.intervals[experimentId][goalId]),window.abst.intervals[experimentId][goalId]=setInterval((function(){let found=!1;word.replace(/'/g,"\\'");Array.from(document.body.querySelectorAll("*")).forEach((function(node){if(node.textContent&&node.textContent.indexOf(word)>=0&&null!==node.offsetParent)return found=!0,!1})),found||document.querySelectorAll("iframe").forEach((function(iframe){try{const iframeBody=iframe.contentDocument?iframe.contentDocument.body:null;if(iframeBody)if(window.jQuery){if(jQuery(iframeBody).find("*").filter((function(){return this.textContent&&this.textContent.indexOf(word)>=0&&null!==this.offsetParent})).length>0)return found=!0,console.log("ABST: "+experimentId+" "+word),!1}else Array.from(iframeBody.querySelectorAll("*")).forEach((function(node){if(node.textContent&&node.textContent.indexOf(word)>=0&&null!==node.offsetParent)return found=!0,console.log("ABST: "+experimentId+" "+word),!1}))}catch(error){}})),found&&(0==goalId?abstConvert(experimentId):abstGoal(experimentId,goalId),clearInterval(window.abst.intervals[experimentId][goalId]))}),1e3)}function abstConvert(testId="",orderValue=1){if(!window.bt_experiments[testId])return!0;if(console.log("abstConvert",testId,orderValue),""!==testId){var btab=abstGetCookie("btab_"+testId);if(btab)if(0==(btab=JSON.parse(btab)).conversion){if(0==bt_experiments[testId].is_current_user_track)return!1;bt_experiment_w(testId,btab.variation,"conversion",!1,orderValue),btab.conversion=1,experiment_vars=JSON.stringify(btab),abstSetCookie("btab_"+testId,experiment_vars,1e3),abst.intervals[testId]&&clearInterval(abst.intervals[testId])}else console.log("ABST: "+bt_experiments[testId].name+": Visitor has already converted");else bt_experiments[testId]||console.log("ABST: Test ID not found or test not active")}}function abstGoal(testId="",goal=""){if(""!==testId&&""!==goal){console.log("start abstGoal",testId,goal);var btab=abstGetCookie("btab_"+testId);if(btab)if(1!==(btab=JSON.parse(btab)).conversion){if(0==bt_experiments[testId].is_current_user_track)return!1;Array.isArray(btab.goals)?btab.goals[goal]||(btab.goals[goal]=1,bt_experiment_w(testId,btab.variation,goal,!1,1),experiment_vars=JSON.stringify(btab),abstSetCookie("btab_"+testId,experiment_vars,1e3),abst.intervals[testId+""+goal]&&clearInterval(abst.intervals[testId+""+goal])):console.log("ABST: "+bt_experiments[testId].name+": Visitor has already goaled")}else console.log("no goals after conversion");else bt_experiments[testId]||console.log("ABST: Test ID not found or test not active")}}function showSkippedVisitorDefault(eid,createCookie=!1,variation=!1){if(console.log("showSkippedVisitorDefault",eid,createCookie,variation),!window.bt_experiments[eid])return btv=(el=document.querySelector('[bt-eid="'+eid+'"]'))?el.getAttribute("bt-variation"):null,document.querySelectorAll('[bt-eid="'+eid+'"][bt-variation="'+btv+'"]').forEach((function(el){el.classList.add("bt-show-variation")})),!0;var el;if(variation&&eid){if("css_test"==bt_experiments[eid].test_type)document.body.classList.add(variation);else{if("full_page"==bt_experiments[eid].test_type)return url=bt_experiments[eid].page_variations[variation],void 0!==url?(window.location.replace(abRedirectUrl(url)),!0):(console.log("No matching page found, must be default page no redirect."),document.body.classList.add("abst-show-page"),!1);if("magic"==bt_experiments[eid].test_type){console.log("magic test, showing ver "+variation);const letter=variation.replace("Variation ","").replace("(original)","").replace("(Original)","").trim().toLowerCase();if(1!==letter.length||!/[a-z]/.test(letter))return console.error("Invalid variation format:",variation),!1;const magVar=letter.charCodeAt(0)-"a".charCodeAt(0);if(magVar<0||magVar>25)return console.error("Invalid variation letter:",letter),!1;showMagicTest(eid,magVar)}else document.querySelectorAll('[bt-eid="'+eid+'"][bt-variation="'+variation+'"]').forEach((function(el){el.classList.add("bt-show-variation")}))}return abstShowPage(),createCookie&&skippedCookie(eid,variation),!0}if(""!==bt_experiments[eid].test_winner){if("full_page"==bt_experiments[eid].test_type){if(url=bt_experiments[eid].page_variations[bt_experiments[eid].test_winner],void 0!==url)return window.location.replace(abRedirectUrl(url)),!0;console.log("ABST: Full page test complete without matching page winner. Showing current page."),abstShowPage()}if("css_test"==bt_experiments[eid].test_type)return console.log("css test winner, showing ver "+bt_experiments[eid].test_winner),document.body.classList.add("test-css-"+eid+"-"+bt_experiments[eid].test_winner),!0;if("magic"==bt_experiments[eid].test_type)return console.log("magic test winner, showing ver "+bt_experiments[eid].test_winner),showMagicTest(eid,bt_experiments[eid].test_winner),!0}if("full_page"==bt_experiments[eid].test_type)return abstShowPage(),createCookie&&skippedCookie(eid,bt_experiments[eid].full_page_default_page),!0;if("css_test"==bt_experiments[eid].test_type)return document.body.classList.add("test-css-"+eid+"-1"),createCookie&&skippedCookie(eid,"test-css-"+eid+"-1"),!0;if(eid){var foundSpecial=!1;document.querySelectorAll('[bt-eid="'+eid+'"]').forEach((function(element,index){var variationName=element.getAttribute("bt-variation").toLowerCase();["original","one","1","default","standard","a","control"].includes(variationName)&&(btv=variationName,element.classList.add("bt-show-variation"),foundSpecial=!0)})),foundSpecial||(btv=function(){var el=document.querySelector('[bt-eid="'+eid+'"]');return el?el.getAttribute("bt-variation"):null}(),document.querySelectorAll('[bt-eid="'+eid+'"][bt-variation="'+btv+'"]').forEach((function(el){el.classList.add("bt-show-variation")}))),createCookie&&skippedCookie(eid,btv)}}function skippedCookie(eid,btv){var experiment_vars={eid:eid,variation:btv,conversion:1,skipped:1};return abstSetCookie("btab_"+eid,experiment_vars=JSON.stringify(experiment_vars),1e3),!0}function abRedirectUrl(url){return document.documentElement.style.opacity="0",document.documentElement.style.transition="none",(url=url+window.location.search+window.location.hash).startsWith("http")||url.startsWith("/")?url:"/"+url}function abstOneSecond(){Object.keys(window.abst.timer).length>0&&(Object.entries(window.abst.timer).forEach((([index,item])=>{if(window.abst.timer[index]>-1&&(window.abst.timer[index]=window.abst.timer[index]-1),0==window.abst.timer[index]){if(index.includes("goal-")){var parts=index.split("-");abstGoal(parts[1],parts[2])}else abstConvert(index);window.abst.timer[index]=-1}})),localStorage.setItem("absttimer",JSON.stringify(window.abst.timer)))}function userActiveNow(){window.abst.currscroll=window.scrollY,window.abst.abactive=!0,clearTimeout(window.abst.timeoutTimer),window.abst.timeoutTimer=setTimeout(abstActiveTimeout,window.abst.timeoutTime)}function abstActiveTimeout(){window.abst.abactive=!1}function getRandomInt(min,max){return min=Math.ceil(min),max=Math.floor(max),Math.floor(Math.random()*(max-min+1))+min}function abstSetCookie(c_name,value,exdays){if(btIsLocalhost())return btSetLocal(c_name,value);var domain=window.location.hostname.replace(/^www\./,"").split(".").slice(-2).join(".");domain="."+domain;var exdate=new Date;exdate.setDate(exdate.getDate()+exdays);var c_value=escape(value)+(null==exdays?"":";path=/; expires="+exdate.toUTCString())+"; SameSite=None; Secure; domain="+domain;document.cookie=c_name+"="+c_value}function deleteCookie(c_name){if(btIsLocalhost())return btDeleteLocal(c_name);var domain=window.location.hostname.replace(/^www\./,"").split(".").slice(-2).join(".");domain="."+domain,document.cookie=c_name+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=None; Secure; domain="+domain}function abstGetCookie(c_name){if(btIsLocalhost())return btGetLocal(c_name);var i,x,y,ARRcookies=document.cookie.split(";");for(i=0;i<ARRcookies.length;i++)if(x=ARRcookies[i].substr(0,ARRcookies[i].indexOf("=")),y=ARRcookies[i].substr(ARRcookies[i].indexOf("=")+1),(x=x.replace(/^\s+|\s+$/g,""))==c_name)return unescape(y);return!1}function abstShowPage(){document.body.classList.add("abst-show-page");try{parent.window.document.body.classList.add("abst-show-page")}catch(e){}}function btSetLocal(c_name,value){localStorage.setItem(c_name,value)}function btGetLocal(c_name){return localStorage.getItem(c_name)}function btDeleteLocal(c_name){localStorage.removeItem(c_name)}function btIsLocalhost(){return"localhost"===location.hostname||"127.0.0.1"===location.hostname||location.hostname.endsWith(".local")||location.hostname.endsWith(".test")}function bt_get_variations(eid){let variation=[];return document.querySelectorAll('[bt-eid="'+eid+'"]').forEach((function(el){var newVariation=el.getAttribute("bt-variation");-1===variation.indexOf(newVariation)&&variation.push(newVariation)})),"1"==btab_vars.is_free&&(variation=variation.slice(0,2)),variation}function bt_experiment_w(eid,variation,type,url,orderValue=1){if("_bt_skip_"==variation||btab_vars.is_preview||!eid||!variation)return!0;if(!bt_experiments[eid].is_current_user_track)return console.log("bt_experiment_w: ignoring "+eid+" because user is not tracked"),!0;if("magic"==bt_experiments[eid].test_type&&variation.includes("-")&&(variation="magic-"+variation.split("-").pop()),"fingerprint"==bt_experiments[eid].conversion_page&&!localStorage.getItem("ab-uuid"))return console.log("bt_exp_w: waiting for fingerprint"),setTimeout(bt_experiment_w,500,eid,variation,type,url,orderValue),!0;console.log("bt_experiment_w",eid,variation,type,url,orderValue);var data={action:"bt_experiment_w",eid:eid,variation:variation,type:type,size:abst.size,location:btab_vars.post_id,orderValue:orderValue},experiment_vars={eid:eid,variation:variation,conversion:0,goals:[]};"conversion"==type?experiment_vars.conversion=1:"visit"==type||(experiment_vars.goals[type]=1),"fingerprint"==bt_experiments[eid].conversion_page&&(data.uuid=localStorage.getItem("ab-uuid")),"1"==btab_vars.advanced_tracking&&(data.ab_advanced_id=localStorage.getItem("ab-advanced-id")),experiment_vars=JSON.stringify(experiment_vars),window.abst.waitForApproval?queueEventData(data,url):console.log("sent beacon",navigator.sendBeacon(bt_ajaxurl+"?action=bt_experiment_w&method=beacon",JSON.stringify(data))),abstSetCookie("btab_"+eid,experiment_vars,1e3),"time"==bt_experiments[eid].conversion_page&&(window.abst.timer[eid]=bt_experiments[eid].conversion_time,console.log("added timer conversion fr visit"+eid+"-"+bt_experiments[eid].conversion_time));var goals=bt_experiments[eid].goals;if(console.log("goals",goals,"type:",typeof goals),"visit"==type&&goals){if("string"==typeof goals)try{goals=JSON.parse(goals),console.log("Parsed goals from string:",goals)}catch(e){console.error("Error parsing goals string:",e),goals=[]}Object.entries(goals).forEach((([idx,goalDef])=>{const entries=Object.entries(goalDef);"time"===entries[0][0]&&(window.abst.timer["goal-"+eid+"-"+idx]=entries[0][1],console.log("Added timer for goal "+eid+"-"+idx+" with time: "+entries[0][1]))}))}return url&&"ex"!==url?(addServerEvents({eid:eid,variation:variation,type:"visit"}),window.location.replace(abRedirectUrl(url))):(btab_track_event(data),abstShowPage()),!0}function queueEventData(data,url){const queueData={data:data,timestamp:(new Date).getTime(),url:url};let queue=[];try{const queueString=localStorage.getItem("abstTestDataQueue");queueString&&(queue=JSON.parse(queueString))}catch(e){console.error("Error parsing abstTestDataQueue",e)}queue.push(queueData),localStorage.setItem("abstTestDataQueue",JSON.stringify(queue)),console.log("Event queued for approval",queueData)}function abst_process_approved_events(){setAbstApprovalStatus(!0);let queue=[];try{const queueString=localStorage.getItem("abstTestDataQueue");queueString&&(queue=JSON.parse(queueString))}catch(e){return console.error("Error parsing abstTestDataQueue",e),!1}return queue.length?(console.log("Processing "+queue.length+" queued events"),queue.forEach((queueItem=>{btab_track_event(queueItem.data),console.log("Sending queued beacon",queueItem.data),console.log(navigator.sendBeacon(bt_ajaxurl+"?action=bt_experiment_w&method=beacon",JSON.stringify(queueItem.data)))})),localStorage.removeItem("abstTestDataQueue"),!0):(console.log("No queued events to process"),!0)}async function btab_track_event(data){if(jQuery("body").trigger("abst_event",[data]),"1"==btab_vars.is_free)return!1;if("0"==btab_vars.tagging)return!1;if(window.abst.eventQueue.push(data),trackName=bt_experiments[data.eid].name||data.eid,gtm_data={event:"ab_split_test",test_name:trackName,test_variation:data.variation,test_event:data.type,test_id:data.eid},"fingerprint"==bt_experiments[data.eid].conversion_page?gtm_data.abuuid=localStorage.getItem("ab-uuid"):localStorage.getItem("ab-advanced-id")&&(gtm_data.abuuid=localStorage.getItem("ab-advanced-id")),window.dataLayer=window.dataLayer||[],window.dataLayer.push(gtm_data),window.abst.abconvertpartner.ga4&&gtag("event","ab_split_test",{test_name:data.eid,test_variation:data.variation,test_event:data.type}),window.abst.abconvertpartner.abawp&&AnalyticsWP.event("Test: "+bt_experiments[data.eid].name,{test_id:data.eid,test_name:bt_experiments[data.eid].name,test_variation:data.variation,test_visit_type:data.type}),window.abst.abconvertpartner.clarity&&clarity("set",bt_experiments[data.eid].name+"-"+data.type,data.variation),window.abst.abconvertpartner.gai&&"function"==typeof ga&&"function"==typeof ga.getAll){var trackers=ga.getAll(),tracker=trackers&&trackers[0];tracker&&(tracker.send("event",bt_experiments[data.eid].name,data.type,data.variation,{nonInteraction:!0}),window.abst.abconvertpartner.gai=!0)}window.abst.abconvertpartner.abmix&&mixpanel.track(bt_experiments[data.eid].name,{type:data.type,variation:data.variation},{send_immediately:!0}),window.abst.abconvertpartner.abumav&&usermaven("track",bt_experiments[data.eid].name,{type:data.type,variation:data.variation}),window.abst.abconvertpartner.umami&&umami.track(bt_experiments[data.eid].name,{type:data.type,variation:data.variation}),window.abst.abconvertpartner.cabin&&cabin.event(bt_experiments[data.eid].name+" | "+data.type+" | "+data.variation),window.abst.abconvertpartner.plausible&&plausible(bt_experiments[data.eid].name,{props:{type:data.type,variation:data.variation}}),window.abst.abconvertpartner.fathom&&fathom.trackGoal(bt_experiments[data.eid].name,{type:data.type,variation:data.variation}),window.abst.abconvertpartner.posthog&&posthog.capture(bt_experiments[data.eid].name,{type:data.type,variation:data.variation})}function abst_find_analytics(){if("1"==btab_vars.is_free)return!1;window.abeventstarted=(new Date).getTime(),window.dataLayer||(window.dataLayer=[]);const analyticsProviders=[{name:"ga4",check:()=>"function"==typeof gtag,process:()=>{window.abst.eventQueue.forEach((element=>{gtag("event","ab_split_test",{test_name:bt_experiments[element.eid].name,test_variation:element.variation,test_event:element.type,ab_uuid:element.uuid})}))}},{name:"clarity",check:()=>"function"==typeof clarity,process:()=>{window.abst.eventQueue.forEach((element=>{clarity("set",element.eid+"-"+element.type,element.variation)}))}},{name:"gai",check:()=>{if("function"==typeof ga&&"function"==typeof ga.getAll){const trackers=ga.getAll();return!!(trackers&&trackers[0])}return!1},process:()=>{if("function"==typeof ga&&"function"==typeof ga.getAll){const trackers=ga.getAll(),tracker=trackers&&trackers[0];tracker&&window.abst.eventQueue.forEach((element=>{tracker.send("event",element.eid,element.type,element.variation,{nonInteraction:!0})}))}}},{name:"fathom",check:()=>!!window.fathom,process:()=>{window.abst.eventQueue.forEach((element=>{window.fathom.trackEvent(element.eid+", "+element.type+": "+element.variation)}))}},{name:"posthog",check:()=>!!window.posthog,process:()=>{window.abst.eventQueue.forEach((element=>{posthog.capture(bt_experiments[element.eid].name,{type:element.type,variation:element.variation})}))}},{name:"abmix",check:()=>"object"==typeof mixpanel,process:()=>{window.abst.eventQueue.forEach((element=>{mixpanel.track(bt_experiments[element.eid].name,{type:element.type,variation:element.variation},{send_immediately:!0})}))}},{name:"abumav",check:()=>"function"==typeof usermaven,process:()=>{window.abst.eventQueue.forEach((element=>{usermaven("track",bt_experiments[element.eid].name,{type:element.type,variation:element.variation})}))}},{name:"umami",check:()=>!!window.umami,process:()=>{window.abst.eventQueue.forEach((element=>{umami.track(bt_experiments[element.eid].name,{type:element.type,variation:element.variation})}))}},{name:"cabin",check:()=>!!window.cabin,process:()=>{window.abst.eventQueue.forEach((element=>{cabin.event(bt_experiments[element.eid].name+" | "+element.type+" | "+element.variation)}))}},{name:"plausible",check:()=>!!window.plausible,process:()=>{window.abst.eventQueue.forEach((element=>{plausible(bt_experiments[element.eid].name,{props:{type:element.type,variation:element.variation}})}))}},{name:"abawp",check:()=>"object"==typeof AnalyticsWP,process:()=>{window.abst.eventQueue.forEach((element=>{AnalyticsWP.event("Test: "+bt_experiments[element.eid].name,{test_id:element.eid,test_name:bt_experiments[element.eid].name,test_variation:element.variation,test_visit_type:element.type})}))}}];window.abst.abconvertpartner=window.abst.abconvertpartner||{};const checkAllProviders=()=>analyticsProviders.map((provider=>(provider=>!!window.abst.abconvertpartner[provider.name]||!!provider.check()&&(window.abst.abconvertpartner[provider.name]=!0,provider.process(),!0))(provider))),startTime=(new Date).getTime();return new Promise((resolve=>{const checkAnalytics=()=>{const allFound=checkAllProviders().every((result=>!0===result)),timeElapsed=(new Date).getTime()-startTime;allFound||timeElapsed>1e4?resolve():setTimeout(checkAnalytics,500)};checkAnalytics()}))}function bt_getQueryVariable(variable){for(var vars=window.location.search.substring(1).split("&"),i=0;i<vars.length;i++){var pair=vars[i].split("=");if(pair[0]==variable)return null==pair[1]||pair[1]}return!1}function bt_replace_all(find,replace,location="body"){!function replaceText(node){if(node.nodeType===Node.TEXT_NODE){let text=node.textContent,div=document.createElement("div");div.innerHTML=text.replace(new RegExp(find,"gi"),replace),node.textContent=div.textContent||div.innerText||""}else node.childNodes.forEach(replaceText)}(document.querySelector(location))}function bt_replace_all_html(find,replace,location="body"){!function replaceHTML(node){node.nodeType===Node.ELEMENT_NODE?node.innerHTML=node.innerHTML.split(find).join(replace):node.childNodes.forEach(replaceHTML)}(document.querySelector(location))}function abLinkPatternListener(experimentId,conversionLinkPattern,goalId=0){abClickListener(experimentId,"a[href*='"+conversionLinkPattern+"']",goalId)}function abClickListener(experimentId,conversionSelector,goalId=0){var testCookie=abstGetCookie("btab_"+experimentId);if(testCookie)if(testCookie=JSON.parse(testCookie),0==goalId){if(1==testCookie.conversion)return}else if(1==testCookie.goals[goalId])return;var eventType="click";if(-1!==conversionSelector.indexOf("|")){var conversionParts=conversionSelector.split("|");conversionParts.length>=2&&(conversionSelector=conversionParts[0],eventType=conversionParts[1])}var subselector="ab-click-convert-"+experimentId;try{document.addEventListener(eventType,(function(event){for(var target=event.target;target&&target!==document;){if(conversionSelector&&target instanceof Element&&("string"==typeof conversionSelector&&""!==conversionSelector.trim()&&target.matches(conversionSelector)||("string"==typeof conversionSelector&&conversionSelector.trim(),0))){console.log(eventType+" conversion on "+conversionSelector+" type "+goalId),goalId>0?abstGoal(experimentId,goalId):abstConvert(experimentId);break}if(subselector&&"string"==typeof subselector&&""!==subselector.trim()&&target instanceof Element&&target.matches(subselector)){console.log(eventType+" subselector conversion on "+conversionSelector+" type "+goalId),goalId>0?abstGoal(experimentId,goalId):abstConvert(experimentId);break}target=target.parentNode}}),!0),document.addEventListener("click",(function(event){for(var target=event.target;target&&target!==document;){if("A"===target.tagName&&target.href)try{var url=new URL(target.href),params=new URLSearchParams(url.search);for(const[key,value]of params.entries()){if(key.startsWith("ab-click-convert-"))if(key.replace("ab-click-convert-","")===experimentId){event.preventDefault(),console.log("Query string conversion for experiment ID: "+experimentId),goalId>0?abstGoal(experimentId,goalId):abstConvert(experimentId),setTimeout((function(){window.location.href=target.href}),300);break}}}catch(e){}target=target.parentNode}}),!0)}catch(error){console.info("ABST: Invalid conversion selector:"+conversionSelector+" "+error+" "+goalId)}document.querySelectorAll("iframe").forEach((function(iframe){try{iframe.contentWindow.document.addEventListener(eventType,(function(event){event.target.matches(conversionSelector)&&(console.log(eventType+" IFRAME conversion on "+conversionSelector),abstConvert(experimentId))}),!0)}catch(error){}}))}async function setAbFingerprint(){if(localStorage.getItem("ab-uuid"))console.log("ab-uuid: already set: "+localStorage.getItem("ab-uuid"));else try{await import(window.bt_pluginurl+"/js/ab-fingerprint.js");const fp=await ThumbmarkJS.getFingerprint();localStorage.setItem("ab-uuid",fp),console.log("set Fingerprint: "+fp)}catch(error){console.error("Error setting fingerprint:",error)}}async function setAbCrypto(){if(!localStorage.getItem("ab-advanced-id")){let fp;if(crypto.randomUUID)try{fp=crypto.randomUUID(),console.log("Set advanced id: "+fp)}catch(e){fp="10000000-1000-4000-8000-100000000000".replace(/[018]/g,(c=>(+c^crypto.getRandomValues(new Uint8Array(1))[0]&15>>+c/4).toString(16))),console.log("Set advanced ID: "+fp)}else fp="10000000-1000-4000-8000-100000000000".replace(/[018]/g,(c=>(+c^crypto.getRandomValues(new Uint8Array(1))[0]&15>>+c/4).toString(16))),console.log("Set advanced ID: "+fp);localStorage.setItem("ab-advanced-id",fp),abstSetCookie("ab-advanced-id",fp,365)}}function abst_revoke_approval(){return setAbstApprovalStatus(!1),console.log("Approval revoked, events will be queued until approval is given again"),!0}function parseMagicTestDefinition(def){try{magic_definition=JSON.parse(def)}catch(e){try{const fixedJson=def.replace(/(?<=[a-zA-Z])"(?=[a-zA-Z])/g,'\\"');magic_definition=JSON.parse(fixedJson),console.log("Successfully fixed magic_definition quotes issue. Edit your split test in the WordPress admin to remove this console log.")}catch(e2){return console.warn("Failed to parse magic_definition after fixes. Please recreate this split test.",e2),null}}return magic_definition}function showMagicTest(eid,index,scroll=!1){var magic_definition=parseMagicTestDefinition(bt_experiments[eid].magic_definition);if(Array.isArray(magic_definition)&&magic_definition.length&&(magic_definition.forEach((function(swapElement){if(swapElement&&"string"==typeof swapElement.selector){var variation,elements=[];try{elements=document.querySelectorAll(swapElement.selector)}catch(e){return void console.error("ABST: Malformed selector in showMagicTest:",swapElement.selector,"-",e)}if(elements.length)Array.isArray(swapElement.variations)?(variation=swapElement.variations[index],console.log("Variation:",variation),null!=variation&&""!==variation||void 0===swapElement.variations[0]||(console.log("Variation element value not set - Using default value for:",swapElement.selector),variation=swapElement.variations[0])):variation=void 0,void 0!==variation?("text"!==swapElement.type&&"html"!==swapElement.type||elements.forEach((function(el){console.log(el);try{el.innerHTML=variation}catch(e){console.error("ABST: Error setting innerHTML in showMagicTest:",el,"-",e)}})),"image"===swapElement.type&&elements.forEach((function(el){try{el.setAttribute("src",variation)}catch(e){console.error("ABST: Error setting src in showMagicTest:",el,"-",e)}try{el.setAttribute("srcset",variation)}catch(e){console.error("ABST: Error setting srcset in showMagicTest:",el,"-",e)}}))):console.error("ABST: No variation found for selector in showMagicTest:",swapElement.selector)}else console.error("ABST: Invalid swapElement in showMagicTest:",swapElement)})),scroll)){var firstSelector=magic_definition[0]&&"string"==typeof magic_definition[0].selector?magic_definition[0].selector:null,firstElements=[];if(firstSelector)try{firstElements=document.querySelectorAll(firstSelector)}catch(e){console.error("ABST: Malformed firstSelector in showMagicTest:",firstSelector,"-",e),firstElements=[]}if(firstElements.length){try{var rect=firstElements[0].getBoundingClientRect(),scrollTop=window.pageYOffset+rect.top-window.innerHeight/3;window.scrollTo({top:scrollTop,behavior:"smooth"})}catch(e){console.error("ABST: Error in scrollTo in showMagicTest:",e)}firstElements.forEach((function(el){try{el.classList.add("ab-highlight")}catch(e){console.error("ABST: Error adding ab-highlight class in showMagicTest:",el,"-",e)}})),setTimeout((function(){firstElements.forEach((function(el){try{el.classList.remove("ab-highlight")}catch(e){console.error("ABST: Error removing ab-highlight class in showMagicTest:",el,"-",e)}}))}),3e3)}}}function ensureTrackerInitialized(){return window.btab_vars.tracker||(window.btab_vars.tracker={elements:{},active:!1,interval:null,trackedElements:new Set,checkVisibility:function(){for(const key in this.elements){const{eid:eid,selector:selector,variation:variation}=this.elements[key],els=jQuery(selector);let trulyVisible=!1;els.each((function(){let el=jQuery(this),isVisible=!0;for(;el.length&&1===el[0].nodeType;){if("hidden"===el.css("visibility")&&"0"===el.css("width")&&"0"===el.css("height")){isVisible=!1;break}if("none"===el.css("display")||0===parseFloat(el.css("opacity")||1)){isVisible=!1;break}el=el.parent()}if(isVisible)return trulyVisible=!0,!1}));let shouldFireVisit=trulyVisible;if(trulyVisible){const trackingKey=`${eid}_${variation}`;if(this.trackedElements.has(trackingKey))shouldFireVisit=!1;else{const cookieVal=abstGetCookie("btab_"+eid);if(cookieVal)try{JSON.parse(cookieVal);shouldFireVisit=!1}catch(e){console.error("Error parsing cookie for "+eid,e),shouldFireVisit=!0}}}else shouldFireVisit=!1;shouldFireVisit&&(bt_experiment_w(eid,variation,"visit",!1),this.trackedElements.add(`${eid}_${variation}`))}for(const eid in bt_experiments){if(!bt_experiments.hasOwnProperty(eid))continue;const exp=bt_experiments[eid];if(this.trackedElements.has(`${eid}_${exp.variation}`))continue;let variations=[exp.variation];"function"==typeof bt_get_variations&&(variations=bt_get_variations(eid)),variations.forEach((variation=>{const trackingKey=`${eid}_${variation}`;if(this.trackedElements.has(trackingKey))return;let selector=exp.selector||'[bt-eid="'+eid+'"]';exp.selectors&&exp.selectors[variation]&&(selector=exp.selectors[variation]),this.elements[`${eid}_${selector}`]||(this.elements[`${eid}_${selector}`]={eid:eid,selector:selector,variation:variation})}))}},start:function(){this.active||(this.interval=setInterval((()=>this.checkVisibility()),500),this.active=!0)},stop:function(){this.active&&this.interval&&(clearInterval(this.interval),this.interval=null,this.active=!1)},reset:function(){this.trackedElements.clear()}}),window.btab_vars.tracker}function watch_for_tag_event(eid,selector='[bt-eid="'+eid+'"]',variation=null){if(!variation)return;const tracker=ensureTrackerInitialized(),key=`${eid}_${selector}`;tracker.elements[key]={eid:eid,selector:selector,variation:variation},tracker.start()}function normalizeUrl(url){let page_url=url.replace(window.location.origin,"");return"/"==page_url.charAt(0)&&(page_url=page_url.substr(1)),"/"==page_url.charAt(page_url.length-1)&&(page_url=page_url.substr(0,page_url.length-1)),page_url}function abstContainsHtml(str){return!(!str||"string"!=typeof str)&&/<[a-z][\s\S]*>/i.test(str)}window.abst.abconvertpartner=Object.fromEntries(services.map((service=>[service,!1]))),window.innerWidth<768?window.abst.size="mobile":window.innerWidth<1024?window.abst.size="tablet":window.abst.size="desktop",window.btab_vars&&"1"==window.btab_vars.advanced_tracking&&setAbCrypto(),document.addEventListener("DOMContentLoaded",(function(){const serverEvents=abstGetCookie("abst_server_events");if(serverEvents){let events;console.log("server events found, gonna process em",serverEvents);try{events=JSON.parse(decodeURIComponent(serverEvents))}catch(e){events=[]}Array.isArray(events)&&events.forEach((event=>{btab_track_event(event)})),deleteCookie("abst_server_events"),console.log("server events processed")}window.btab_vars&&!window.btab_vars.is_preview&&document.querySelectorAll(".ab-test-page-redirect").forEach((function(el){el.remove()}));if(document.querySelectorAll('[bt_hidden="true"]').forEach((function(el){el.remove()})),document.addEventListener("mousemove",(function(event){window.abst.currentMousePos=event.pageX})),document.body.addEventListener("mousedown",userActiveNow),document.body.addEventListener("keydown",userActiveNow),document.body.addEventListener("touchstart",userActiveNow),"abConversionValue"in window.abst||(window.abst.abConversionValue=1),window.abst.timerInterval=setInterval((function(){window.abst.currscroll==window.scrollY&&window.abst.currentMousePos==window.abst.oldMousePos||(window.abst.currscroll=window.scrollY,window.abst.oldMousePos=window.abst.currentMousePos,userActiveNow()),window.abst.abactive&&abstOneSecond()}),1e3),"undefined"!=typeof conversion_details&&conversion_details){var eid=null,variation=null;page_url=normalizeUrl(window.location.href),Object.entries(conversion_details).forEach((function([key,detail]){if(detail.conversion_page_url&&page_url)var regexPattern=detail.conversion_page_url.replace(/([.+?^${}()|[\]\\])/g,"\\$1").replace(/\*/g,".*"),regex=new RegExp(`^${regexPattern}$`);if(void 0!==regex)regex.test(page_url)&&(eid=key);else if(detail.conversion_page_url===page_url&&void 0!==detail.conversion_page_url&&""!=detail.conversion_page_url)eid=key;else{if(!current_page.includes(detail.conversion_page_id)&&!current_page.includes(parseInt(detail.conversion_page_id)))return!0;eid=key}return!(variation=abstGetCookie("btab_"+eid))||(1==(variation=JSON.parse(variation)).conversion||(conversionValue=1,window.abst.abConversionValue&&1==detail.use_order_value&&(conversionValue=window.abst.abConversionValue),void bt_experiment_w(eid,variation.variation,"conversion",!1,conversionValue)))}))}if("undefined"!=typeof bt_experiments){document.querySelectorAll("[class^='ab-'],[class*=' ab-']").forEach((function(el,e){(el.className.includes("ab-var-")||el.className.includes("ab-convert"))&&(allClasses=el.className,allClasses=allClasses.split(" "),thisTestVar=!1,thisTestGoal=!1,thisTestId=!1,thisTestConversion=!1,allClasses.forEach((function(element){element.startsWith("ab-var-")?thisTestVar=element:element.startsWith("ab-goal-")?thisTestGoal=element:element.startsWith("ab-")&&!element.includes("ab-convert")&&(thisTestId=element),"ab-convert"==element&&(thisTestConversion=!0)})),!1!==thisTestVar&&!1!==thisTestId&&(el.setAttribute("bt-eid",thisTestId.replace("ab-","")),el.setAttribute("bt-variation",thisTestVar.replace("ab-var-","")),allClasses.forEach((function(className){(className.startsWith("ab-var-")||className===thisTestId)&&el.classList.remove(className)}))),1==thisTestConversion&&!1!==thisTestId&&abstConvert(thisTestId.replace("ab-","")),1==thisTestGoal&&!1!==thisTestId&&abstGoal(thisTestId.replace("ab-",""),thisTestGoal.replace("ab-goal-",""))),el.className.includes("ab-click-convert-")&&Array.from(el.classList).forEach((function(element){element.trim().startsWith("ab-click-convert-")&&abClickListener(element.replace("ab-click-convert-",""),"."+element)}))})),document.querySelectorAll("[data-bt-variation]").forEach((function(el){el.setAttribute("bt-variation",el.getAttribute("data-bt-variation")),el.setAttribute("bt-eid",el.getAttribute("data-bt-eid"))})),document.querySelectorAll(".bricks-element [bt-eid]").forEach((el=>{let parent=el.closest(".bricks-element");parent.setAttribute("bt-eid",el.getAttribute("bt-eid")),parent.setAttribute("bt-variation",el.getAttribute("bt-variation")),el.removeAttribute("bt-eid"),el.removeAttribute("bt-variation")}));let searchParams=new URLSearchParams(window.location.search);const abtv=searchParams.get("abtv"),abtid=searchParams.get("abtid");if(abtv&&abtid&&bt_experiments[abtid])return console.log("AB Split Test: URL variables detected. Skipping user."),showSkippedVisitorDefault(abtid,!0,abtv),document.body.classList.add("ab-test-setup-complete"),!0;bt_experiments=Object.entries(bt_experiments).sort(((a,b)=>"full_page"==a[1].test_type?-1:"full_page"==b[1].test_type?1:0)).reduce(((r,a)=>Object.assign(r,{[a[0]]:a[1]})),{}),Object.entries(bt_experiments).forEach((([experimentId,experiment])=>{if(experiment.test_winner)return"full_page"==experiment.test_type&&experiment.full_page_default_page==btab_vars.post_id?experiment.test_winner!==btab_vars.post_id?(null==experiment.page_variations[experiment.test_winner]||window.location.replace(abRedirectUrl(experiment.page_variations[experiment.test_winner])),!1):(console.log("Test winner is current page. Showing "+experiment.test_winner),document.body.classList.add("abst-show-page"),!1):("magic"==experiment.test_type?(console.log("Magic winner is "+experiment.test_winner+" showing magic test"),showMagicTest(experimentId,experiment.test_winner)):"css_test"==experiment.test_type?(console.log("Split Test CSS winner. Showing "+experiment.test_winner),document.body.classList.add(experiment.test_winner)):document.querySelectorAll('[bt-eid="'+experimentId+'"][bt-variation="'+experiment.test_winner+'"]').forEach((function(el){el.classList.add("bt-show-variation")})),!0);if("css_test"==experiment.test_type)for(var i=0;i<experiment.css_test_variations;i++){var script=document.createElement("script");script.className="bt-css-scripts",script.setAttribute("bt-variation","test-css-"+experimentId+"-"+(i+1)),script.setAttribute("bt-eid",experimentId),document.body.appendChild(script)}if("full_page"==experiment.test_type&&Array.isArray(window.current_page)&&window.current_page.some((page=>String(page)===String(experiment.full_page_default_page)))){console.log("Full Page Test: "+experimentId);var div=document.createElement("div");div.className="bt-redirect-handle",div.style.display="none",div.setAttribute("bt-variation",experiment.full_page_default_page),div.setAttribute("bt-eid",experimentId),document.body.appendChild(div),Object.entries(experiment.page_variations).forEach((function([varId,variation]){var div=document.createElement("div");div.className="bt-redirect-handle",div.style.display="none",div.setAttribute("bt-variation",varId),div.setAttribute("bt-eid",experimentId),div.setAttribute("bt-url",variation),document.body.appendChild(div)}))}if("selector"==experiment.conversion_page&&""!=experiment.conversion_selector){var conversionSelector=experiment.conversion_selector;abClickListener(experimentId,conversionSelector,0)}if("link"==experiment.conversion_page&&""!=experiment.conversion_link_pattern){var conversionLinkPattern=experiment.conversion_link_pattern;abLinkPatternListener(experimentId,conversionLinkPattern)}if(experiment.goals)for(i=1;i<Object.keys(experiment.goals).length;i++){const firstKey=Object.keys(experiment.goals[i])[0];if("selector"===firstKey&&abClickListener(experimentId,experiment.goals[i].selector,i),"link"===firstKey&&abLinkPatternListener(experimentId,experiment.goals[i].link,i),startInverval=!1,"text"===firstKey&&(startInverval=!0,convstatus=abstGetCookie("btab_"+experimentId),startInverval&&convstatus&&(convstatus=JSON.parse(convstatus),convstatus&&convstatus.goals&&1==convstatus.goals[i]&&(startInverval=!1)),""==experiment.goals[i].text&&(startInverval=!1),startInverval&&startTextWatcher(experimentId,experiment.goals[i].text,i)),"page"===firstKey)experiment.goals[i].page==btab_vars.post_id&&abstGoal(experimentId,i);"url"===firstKey&&(page_url=normalizeUrl(window.location.href),goal_url=normalizeUrl(experiment.goals[i].url),page_url==goal_url&&abstGoal(experimentId,i))}"text"==experiment.conversion_page&&(startInverval=!0,bt_experiments[experimentId]||(startInverval=!1),convstatus=abstGetCookie("btab_"+experimentId),startInverval&&convstatus&&(convstatus=JSON.parse(convstatus),1==convstatus.conversion&&(startInverval=!1)),""==experiment.conversion_text&&(startInverval=!1),startInverval&&startTextWatcher(experimentId,experiment.conversion_text)),"surecart-order-paid"==experiment.conversion_page&&window.scData&&document.addEventListener("scOrderPaid",(function(e){console.log("surecart OrderPaid");const checkout=e.detail;checkout&&checkout.amount_due&&(1==experiment.use_order_value&&(window.abst.abConversionValue=(checkout.amount_due/100).toFixed(2)),abstConvert(experimentId,window.abst.abConversionValue))})),"fingerprint"!=experiment.conversion_page||localStorage.getItem("ab-uuid")||(console.log("ab-uuid: set fingerprint"),setAbFingerprint())}));var experiments_el=document.querySelectorAll('[bt-eid]:not([bt-eid=""])[bt-variation]:not([bt-variation=""])'),current_exp={},exp_redirect={};experiments_el.forEach((function(el){var experimentId=el.getAttribute("bt-eid"),variation=el.getAttribute("bt-variation"),redirect_url=el.getAttribute("bt-url");void 0===current_exp[experimentId]&&(current_exp[experimentId]=[],exp_redirect[experimentId]=[]),current_exp[experimentId].includes(variation)||(current_exp[experimentId].push(variation),exp_redirect[experimentId][variation]=redirect_url)})),Object.keys(bt_experiments).forEach((function(experimentId){if("css_test"==bt_experiments[experimentId].test_type){current_exp[experimentId]=[],exp_redirect[experimentId]=[];for(var i=1;i<=parseInt(bt_experiments[experimentId].css_test_variations);i++)current_exp[experimentId].push("test-css-"+experimentId+"-"+i),exp_redirect[experimentId]["test-css-"+experimentId+"-"+i]=""}else if("magic"==bt_experiments[experimentId].test_type&&bt_experiments[experimentId].magic_definition&&bt_experiments[experimentId].magic_definition.length>0){magic_definition=parseMagicTestDefinition(bt_experiments[experimentId].magic_definition),current_exp[experimentId]=[],exp_redirect[experimentId]=[];var randVar=getRandomInt(0,magic_definition[0].variations.length-1);current_exp[experimentId].push("test-magic-"+experimentId+"-"+randVar),exp_redirect[experimentId]["test-magic-"+experimentId+"-"+randVar]=""}})),Object.keys(current_exp).forEach((function(experimentId){if(void 0===bt_experiments[experimentId])return console.info("ABST: Test ID "+experimentId+" does not exist."),showSkippedVisitorDefault(experimentId),!0;if(bt_experiments[experimentId].test_winner)return!0;if(0==bt_experiments[experimentId].is_current_user_track)return showSkippedVisitorDefault(experimentId),!0;if((btab=abstGetCookie("btab_"+experimentId))&&JSON.parse(btab).skipped&&(deleteCookie("btab_"+experimentId),console.info("ABST: previously skipped experiment will begin "+experimentId)),"publish"!==bt_experiments[experimentId].test_status)return showSkippedVisitorDefault(experimentId),!0;var targetVisitor=!0,btab=abstGetCookie("btab_"+experimentId),experimentVariation="";if(btab)try{var btab_cookie=JSON.parse(btab);experimentVariation=btab_cookie.variation}catch(err){console.log("Error parsing cookie data:",err)}else{if("css_test"==bt_experiments[experimentId].test_type)var randVar=getRandomInt(1,parseInt(bt_experiments[experimentId].css_test_variations))-1;else if("magic"==bt_experiments[experimentId].test_type&&bt_experiments[experimentId].magic_definition&&bt_experiments[experimentId].magic_definition.length>0){magic_definition=parseMagicTestDefinition(bt_experiments[experimentId].magic_definition);randVar=getRandomInt(0,magic_definition[0].variations.length-1);variations=magic_definition[0].variations}else randVar=getRandomInt(0,current_exp[experimentId].length-1);if("1"==btab_vars.is_free&&current_exp[experimentId].length>2){randVar=getRandomInt(0,1);console.info("Free version of AB Split Test is limited to 1 variation. Your others will not be shown. Upgrade: https://absplittest.com/pricing?ref=ug")}experimentVariation=current_exp[experimentId][randVar]}var variation_element=!1;if("css_test"==bt_experiments[experimentId].test_type?document.body.classList.add(experimentVariation):"magic"==bt_experiments[experimentId].test_type||(variation_element=document.querySelectorAll('[bt-eid="'+experimentId+'"][bt-variation="'+experimentVariation+'"]')),btab){btab=JSON.parse(btab);var redirect_url=exp_redirect[experimentId];if((redirect_url=redirect_url[experimentVariation])&&!btab_vars.is_preview)return window.location.replace(abRedirectUrl(redirect_url)),!0;if(abstShowPage(),variation_element&&variation_element.length>0&&variation_element.forEach((function(el){el.classList.add("bt-show-variation")})),"magic"==bt_experiments[experimentId].test_type&&bt_experiments[experimentId].magic_definition&&bt_experiments[experimentId].magic_definition.length>0){var vartn=btab.variation.replace("magic-","");showMagicTest(experimentId,vartn)}return!0}if(!btab){var targetPercentage=bt_experiments[experimentId].target_percentage;""==targetPercentage&&(targetPercentage=100);var url_query=bt_experiments[experimentId].url_query;if(urlQueryResult=null,""!==url_query){var isNot=!1;if(url_query.startsWith("NOT")&&(isNot=!0,url_query=url_query.replace("NOT ","").replace("NOT","")),url_query.includes("*"))url_query=url_query.replace(/\*/g,""),console.log("url_query",url_query),console.log("window.location.href",window.location.href),targetVisitor=window.location.href.includes(url_query);else{var exploded_query=url_query.trim().split("=");1==exploded_query.length?targetVisitor=bt_getQueryVariable(exploded_query[0]):2==exploded_query.length&&(urlQueryResult=bt_getQueryVariable(exploded_query[0]),targetVisitor=exploded_query[1]==urlQueryResult)}isNot&&(targetVisitor=!targetVisitor)}var target_option_device_size=bt_experiments[experimentId].target_option_device_size;if(targetVisitor&&"all"!=target_option_device_size){var device_size=window.abst.size;targetVisitor=target_option_device_size.includes(device_size)}if(!targetVisitor)return showSkippedVisitorDefault(experimentId),!0;if(targetPercentage<getRandomInt(1,100))return showSkippedVisitorDefault(experimentId,!0),console.log("ABST "+experimentId+" skipped not in percentage target"),!0;bt_experiments[experimentId].variations=bt_get_variations(experimentId)}if(variation_element&&!variation_element)return showSkippedVisitorDefault(experimentId),console.log("ABST variation doesnt exist, or doesnt match"),!0;redirect_url=Object.keys(exp_redirect).length>0?(redirect_url=exp_redirect[experimentId])[experimentVariation]:"","magic"==bt_experiments[experimentId].test_type&&showMagicTest(experimentId,parseInt(randVar)),variation_element&&variation_element.length>0&&variation_element.forEach((function(el){el&&el.classList.add("bt-show-variation")})),"ab_test"==bt_experiments[experimentId].test_type?watch_for_tag_event(experimentId,void 0,experimentVariation):"magic"==bt_experiments[experimentId].test_type?magic_definition.forEach(((element,index)=>{watch_for_tag_event(experimentId,element.selector,experimentVariation)})):bt_experiment_w(experimentId,experimentVariation,"visit",redirect_url)}))}btIsLocalhost()&&console.info("AB Split Test: It looks like you're on a localhost, using local storage instead of cookies. External Conversion Pixels will not work on Local web servers."),abst_find_analytics(),window.dispatchEvent(new Event("resize"));var event=new Event("ab-test-setup-complete");document.body.dispatchEvent(event),document.body.classList.add("ab-test-setup-complete")})),String.prototype.endsWith||(String.prototype.endsWith=function(search,this_len){return(void 0===this_len||this_len>this.length)&&(this_len=this.length),this.substring(this_len-search.length,this_len)===search}),window.btab_vars=window.btab_vars||{},window.btab_vars.setupConversionListeners=function(){if(bt_experiments)for(const eid in bt_experiments){if(!bt_experiments.hasOwnProperty(eid))continue;const exp=bt_experiments[eid];switch(Array.isArray(exp.goals)&&exp.goals.forEach(((goalDef,idx)=>{const[kind,value]=Object.entries(goalDef)[0];switch(kind){case"selector":abClickListener(eid,value,idx);break;case"text":startTextWatcher(eid,value,idx);break;case"url":const existingCookie=abstGetCookie("btab_"+eid);if(existingCookie){const cookieData=JSON.parse(existingCookie);if(cookieData.goals&&1===cookieData.goals[idx]){console.log("Goal already triggered:",eid,"Goal #",idx);break}}value&&value.length>1?normalizeUrl(location.href)===normalizeUrl(value)?(console.log("URL MATCH! Firing goal for",eid,idx),abstGoal(eid,idx)):console.log("URL DID NOT MATCH for goal",eid,idx):console.log("Invalid URL value for goal",eid,idx,"- skipping check");break;case"page":window.btab_vars.post_id==value&&abstGoal(eid,idx);break}})),exp.conversion_page){case"selector":abClickListener(eid,exp.conversion_selector);break;case"text":startTextWatcher(eid,exp.conversion_text);break;case"surecart-order-paid":document.addEventListener("scOrderPaid",(function(e){const amt=e.detail?.amount_due;amt&&(exp.use_order_value&&(window.abst.abConversionValue=(amt/100).toFixed(2)),abstConvert(eid,window.abst.abConversionValue))}));break;default:window.btab_vars.post_id==exp.conversion_page&&abstConvert(eid);break}exp.conversion_url&&""!==exp.conversion_url&&normalizeUrl(location.href)===normalizeUrl(exp.conversion_url)&&abstConvert(eid)}},window.btab_vars.abtracker=function(){ensureTrackerInitialized().checkVisibility()},document.addEventListener("DOMContentLoaded",(function(){window.btab_vars.setupConversionListeners(),!btab_vars.is_preview&&document.querySelectorAll(".conversion-module").length>0&&document.querySelectorAll(".conversion-module").forEach((function(el){el.remove()})),window.bt_conversion_vars&&Object.entries(bt_conversion_vars).forEach((function([key,conversion_el]){if("click"!==conversion_el.type){var eid=conversion_el.eid,variationObj=abstGetCookie("btab_"+eid);if(!variationObj)return!0;if(variationObj=JSON.parse(variationObj),void 0===bt_experiments[eid])return!1;if(1==variationObj.conversion)return console.info("AB Split test already converted."),!0;if(""!=bt_experiments[eid].conversion_url||""!=bt_experiments[eid].conversion_page){if("embed"!=bt_experiments[eid].conversion_url)return!0;console.info("AB Split Test conversion defined as external URL, but conversion module used. Please check your configuration settings. This is a soft error and a conversion event has not been blocked.")}variation=variationObj.variation;conversion_el.type;var convertClickSelector=conversion_el.selector;"undefined"!=typeof conversion_details&&void 0!==conversion_details[eid]&&console.log("Possible duplicate conversion event. Check your set up."),variation&&(bt_experiment_w(eid,variation,"conversion",!1),variationObj.conversion=1,abstSetCookie("btab_"+eid,variationObj=JSON.stringify(variationObj),1e3))}if("click"==conversion_el.type){convertClickSelector=conversion_el.selector;document.querySelectorAll(convertClickSelector).length>0||(document.querySelectorAll(convertClickSelector+" a").length>0?convertClickSelector+=" a":document.querySelectorAll(convertClickSelector+" img").length>0?convertClickSelector+=" img":console.log("no conversion elements found")),document.querySelectorAll(convertClickSelector).length>0&&document.body.addEventListener("click",(function(event){let clickTarget=event.target,convertElement=null;try{convertElement=clickTarget.matches(convertClickSelector)?clickTarget:clickTarget.closest(convertClickSelector)}catch(e){return}if(convertElement){var url=convertElement.getAttribute("href"),target=convertElement.getAttribute("target"),eid=conversion_el.eid,variationObj=abstGetCookie("btab_"+eid);if(variationObj=JSON.parse(variationObj),void 0===bt_experiments[eid])return!1;if(1==variationObj.conversion)return console.log("ab test already converted."),!0;variation=variationObj.variation,url&&"_blank"!==target?(event.preventDefault(),bt_experiment_w(eid,variation,"conversion",url)):bt_experiment_w(eid,variation,"conversion",!1),variationObj.conversion=1,abstSetCookie("btab_"+eid,variationObj=JSON.stringify(variationObj),1e3)}}))}}))}));
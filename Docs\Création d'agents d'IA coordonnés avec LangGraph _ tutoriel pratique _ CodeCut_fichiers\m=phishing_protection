"use strict";this.default_tr=this.default_tr||{};(function(_){var window=this;
try{
var tg,ug,vg,Jg,Sg,Yg,Zg,bh,ch,eh,ih,kh,jh,qh,uh,sh,th,pg;_.ng=function(a){return"function"==typeof _.mg&&a instanceof _.mg};_.og=function(a){if(_.ng(a))return a.g;throw Error("v");};_.qg=function(a){if(pg.test(a))return a};_.rg=function(a){return"function"==typeof _.mg&&a instanceof _.mg?_.og(a):_.qg(a)};_.sg=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};
tg=function(a){if(!_.Va||!_.Wa)return!1;for(var b=0;b<_.Wa.brands.length;b++){var c=_.Wa.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1};ug=function(){return _.v("Firefox")||_.v("FxiOS")};vg=function(){return _.Xa()?tg("Chromium"):(_.v("Chrome")||_.v("CriOS"))&&!(_.Xa()?0:_.v("Edge"))||_.v("Silk")};
_.wg=function(){return _.v("Safari")&&!(vg()||(_.Xa()?0:_.v("Coast"))||_.Ya()||(_.Xa()?0:_.v("Edge"))||(_.Xa()?tg("Microsoft Edge"):_.v("Edg/"))||(_.Xa()?tg("Opera"):_.v("OPR"))||ug()||_.v("Silk")||_.v("Android"))};_.xg=function(){return _.v("Android")&&!(vg()||ug()||_.Ya()||_.v("Silk"))};_.zg=function(a,b){if(a!=null){var c;var d=(c=yg)!=null?c:yg={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),_.sg(a,"incident"),_.lb(a))}};
_.Ag=function(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b};_.Eg=function(a,b){_.Bg||_.H in a||Cg(a,Dg);a[_.H]|=b};_.Fg=function(a,b){_.Bg||_.H in a||Cg(a,Dg);a[_.H]=b};_.Gg=function(a){_.Eg(a,34);return a};_.Ig=function(a,b){return b===void 0?a.g!==_.Hg&&!!(2&(a.H[_.H]|0)):!!(2&b)&&a.g!==_.Hg};Jg=function(a){a.zn=!0;return a};
_.Rg=function(a){var b=a;if(Kg(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Og(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Pg?BigInt(a):a=Qg(a)?a?"1":"0":Kg(a)?a.trim()||"0":String(a)};Sg=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};
_.Xg=function(a,b,c,d){if(a!=null&&a[Tg]===Ug)return a;if(!Array.isArray(a))return c?d&2?b[_.Vg]||(b[_.Vg]=_.Wg(b)):new b:void 0;c=a[_.H]|0;d=c|d&32|d&2;d!==c&&_.Fg(a,d);return new b(a)};_.Wg=function(a){a=new a;_.Gg(a.H);return a};Yg=function(a){return a};Zg=function(a){return a};bh=function(a,b,c,d,e,f){a=_.Xg(a,d,c,f);e&&(a=_.$g(a));return a};ch=function(a){return[a,this.get(a)]};eh=function(a,b){b<100||_.zg(dh,1)};
ih=function(a,b,c,d){var e=d!==void 0;d=!!d;var f=_.fh(_.gh),g;!e&&_.Bg&&f&&(g=a[f])&&g.Cm(eh);f=[];var h=a.length;g=4294967295;var k=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var p=h&&a[h-1];p!=null&&typeof p==="object"&&p.constructor===Object?(h--,g=h):p=void 0;if(m&&!(b&128)&&!e){k=!0;var q;g=((q=hh)!=null?q:Yg)(g-n,n,a,p,void 0)+n}}b=void 0;for(e=0;e<h;e++)if(q=a[e],q!=null&&(q=c(q,d))!=null)if(m&&e>=g){var r=e-n,u=void 0;((u=b)!=null?u:b={})[r]=q}else f[e]=q;if(p)for(var A in p)a=p[A],a!=
null&&(a=c(a,d))!=null&&(h=+A,e=void 0,m&&!Number.isNaN(h)&&(e=h+n)<g?f[e]=a:(h=void 0,((h=b)!=null?h:b={})[A]=a));b&&(k?f.push(b):f[g]=b);return f};kh=function(a){a[0]=jh(a[0]);a[1]=jh(a[1]);return a};
jh=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return(0,_.lh)(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[_.H]|0;return a.length===0&&b&1?void 0:ih(a,b,jh)}if(a!=null&&a[Tg]===Ug)return _.mh(a);if("function"==typeof _.nh&&a instanceof _.nh)return a.g();if(a instanceof _.oh)return a=a.size!==0?Array.from(_.ph.prototype.entries.call(a),kh):void 0,a;return}return a};
_.mh=function(a){a=a.H;return ih(a,a[_.H]|0,jh)};
_.I=function(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("ba");e=a[_.H]|0;if(_.xc&&1&e)throw Error("ca");2048&e&&!(2&e)&&qh();if(e&256)throw Error("da");if(e&64)return d!==0||e&2048||_.Fg(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("ea");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("ga");for(var k in h)f=
+k,f<g&&(c[f+b]=h[k],delete h[k]);e=e&-8380417|(g&1023)<<13;break a}}if(b){k=Math.max(b,f-(e&128?0:-1));if(k>1024)throw Error("ha");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);_.Fg(a,e);return a};qh=function(){if(_.xc)throw Error("fa");_.zg(rh,5)};
uh=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[_.H]|0;return a.length===0&&c&1?void 0:sh(a,c,b)}if(a!=null&&a[Tg]===Ug)return th(a);if(a instanceof _.oh){b=a.hc;if(b&2)return a;if(!a.size)return;c=_.Gg(Array.from(_.ph.prototype.entries.call(a)));if(a.bc)for(a=0;a<c.length;a++){var d=c[a],e=d[1];e==null||typeof e!=="object"?e=void 0:e!=null&&e[Tg]===Ug?e=th(e):Array.isArray(e)?e=sh(e,e[_.H]|0,!!(b&32)):e=void 0;d[1]=e}return c}if("function"==typeof _.nh&&a instanceof
_.nh)return a};sh=function(a,b,c){if(b&2)return a;!c||4096&b||16&b?a=_.vh(a,b,!1,c&&!(b&16)):(_.Eg(a,34),b&4&&Object.freeze(a));return a};_.wh=function(a,b,c){a=new a.constructor(b);c&&(a.g=_.Hg);a.h=_.Hg;return a};th=function(a){var b=a.H,c=b[_.H]|0;return _.Ig(a,c)?a:_.xh(a,b,c)?_.wh(a,b):_.vh(b,c)};_.vh=function(a,b,c,d){d!=null||(d=!!(34&b));a=ih(a,b,uh,d);d=32;c&&(d|=2);b=b&8380609|d;_.Fg(a,b);return a};
_.$g=function(a){var b=a.H,c=b[_.H]|0;return _.Ig(a,c)?_.xh(a,b,c)?_.wh(a,b,!0):new a.constructor(_.vh(b,c,!1)):a};_.xh=function(a,b,c){return c&2?!0:c&32&&!(c&4096)?(_.Fg(b,c|2),a.g=_.Hg,!0):!1};_.fh=function(a){return a};pg=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.yh=ug();_.zh=_.$a()||_.v("iPod");_.Ah=_.v("iPad");_.Bh=_.xg();_.Ch=vg();_.Dh=_.wg()&&!_.ab();_.Eh=!_.Bc&&typeof btoa==="function";var yg=void 0;var Fh,dh,rh,Tg;_.Bg=typeof Symbol==="function"&&typeof Symbol()==="symbol";Fh=_.Ag("jas",void 0,!0);_.Vg=_.Ag(void 0,"0di");_.gh=_.Ag(void 0,Symbol());_.Gh=_.Ag(void 0,"0ub");dh=_.Ag(void 0,"0ubs");rh=_.Ag(void 0,"0actk");Tg=_.Ag("m_m","Cn",!0);_.Hh=_.Ag();var Dg,Cg,Jh;Dg={Ij:{value:0,configurable:!0,writable:!0,enumerable:!1}};Cg=Object.defineProperties;_.H=_.Bg?Fh:"Ij";Jh=[];_.Fg(Jh,7);_.Ih=Object.freeze(Jh);var Ug,Kh;Ug={};_.Hg={};Kh=function(a,b,c){this.g=a;this.h=b;this.j=c};Kh.prototype.next=function(){var a=this.g.next();a.done||(a.value=this.h.call(this.j,a.value));return a};Kh.prototype[Symbol.iterator]=function(){return this};_.Lh=Object.freeze({});_.Mh=Object.freeze({});var Og=Jg(function(a){return typeof a==="number"}),Kg=Jg(function(a){return typeof a==="string"}),Qg=Jg(function(a){return typeof a==="boolean"});var Pg=typeof _.t.BigInt==="function"&&typeof _.t.BigInt(0)==="bigint";var Ph,Nh,Qh,Oh;_.lh=Jg(function(a){return Pg?a>=Nh&&a<=Oh:a[0]==="-"?Sg(a,Ph):Sg(a,Qh)});Ph=Number.MIN_SAFE_INTEGER.toString();Nh=Pg?BigInt(Number.MIN_SAFE_INTEGER):void 0;Qh=Number.MAX_SAFE_INTEGER.toString();Oh=Pg?BigInt(Number.MAX_SAFE_INTEGER):void 0;_.Rh=typeof BigInt==="function"?BigInt.asIntN:void 0;_.Sh=Number.isSafeInteger;_.Th=Number.isFinite;_.Uh=Math.trunc;var Vh=function(){try{var a=function(){return(0,_.Bb)(Map,[],this.constructor)};_.y(a,Map);_.zc(new a);return!1}catch(b){return!0}}(),Wh=function(){this.g=new Map};_.l=Wh.prototype;_.l.get=function(a){return this.g.get(a)};_.l.set=function(a,b){this.g.set(a,b);this.size=this.g.size;return this};_.l.delete=function(a){a=this.g.delete(a);this.size=this.g.size;return a};_.l.clear=function(){this.g.clear();this.size=this.g.size};_.l.has=function(a){return this.g.has(a)};_.l.entries=function(){return this.g.entries()};
_.l.keys=function(){return this.g.keys()};_.l.values=function(){return this.g.values()};_.l.forEach=function(a,b){return this.g.forEach(a,b)};Wh.prototype[Symbol.iterator]=function(){return this.entries()};_.ph=function(){if(Vh)return Object.setPrototypeOf(Wh.prototype,Map.prototype),Object.defineProperties(Wh.prototype,{size:{value:0,configurable:!0,enumerable:!0,writable:!0}}),Wh;var a=function(){return(0,_.Bb)(Map,[],this.constructor)};_.y(a,Map);return a}();
_.oh=function(a,b,c,d){c=c===void 0?Zg:c;d=d===void 0?Zg:d;var e=_.ph.call(this)||this;e.hc=a[_.H]|0;e.bc=b;e.Wd=c;e.Gh=e.bc?bh:d;for(var f=0;f<a.length;f++){var g=a[f],h=c(g[0],!1,!0),k=g[1];b?k===void 0&&(k=null):k=d(g[1],!1,!0,void 0,void 0,e.hc);_.ph.prototype.set.call(e,h,k)}return e};_.y(_.oh,_.ph);var Xh=function(a){if(a.hc&2)throw Error("aa");};_.l=_.oh.prototype;_.l.clear=function(){Xh(this);_.ph.prototype.clear.call(this)};
_.l.delete=function(a){Xh(this);return _.ph.prototype.delete.call(this,this.Wd(a,!0,!1))};_.l.entries=function(){if(this.bc){var a=_.ph.prototype.keys.call(this);a=new Kh(a,ch,this)}else a=_.ph.prototype.entries.call(this);return a};_.l.values=function(){if(this.bc){var a=_.ph.prototype.keys.call(this);a=new Kh(a,_.oh.prototype.get,this)}else a=_.ph.prototype.values.call(this);return a};
_.l.forEach=function(a,b){this.bc?_.ph.prototype.forEach.call(this,function(c,d,e){a.call(b,e.get(d),d,e)}):_.ph.prototype.forEach.call(this,a,b)};_.l.set=function(a,b){Xh(this);a=this.Wd(a,!0,!1);return a==null?this:b==null?(_.ph.prototype.delete.call(this,a),this):_.ph.prototype.set.call(this,a,this.Gh(b,!0,!0,this.bc,!1,this.hc))};_.l.has=function(a){return _.ph.prototype.has.call(this,this.Wd(a,!1,!1))};
_.l.get=function(a){a=this.Wd(a,!1,!1);var b=_.ph.prototype.get.call(this,a);if(b!==void 0){var c=this.bc;return c?(c=this.Gh(b,!1,!0,c,this.xm,this.hc),c!==b&&_.ph.prototype.set.call(this,a,c),c):b}};_.oh.prototype[Symbol.iterator]=function(){return this.entries()};_.oh.prototype.toJSON=void 0;var hh;_.Yh=_.Rg(0);_.J=function(a,b,c){this.H=_.I(a,b,c)};_.J.prototype.toJSON=function(){return _.mh(this)};_.J.prototype[Tg]=Ug;_.J.prototype.toString=function(){return this.H.toString()};
}catch(e){_._DumpException(e)}
try{
var ti,ui,vi,zi,Ci,Ai,Bi,Di,Ei,Gi,Hi;_.Zh=function(a){var b=_.Kb.apply(1,arguments);if(b.length===0)return _.Na(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.Na(c)};_.$h=function(a){_.Eg(a,32);return a};_.ai=function(a){return Array.prototype.slice.call(a)};_.bi=function(a){if(typeof a!=="boolean")throw Error("$`"+_.Tb(a)+"`"+a);return a};_.ci=function(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a};
_.di=function(a){return a==null||typeof a==="string"?a:void 0};_.ei=function(a){if(a.g!==_.Hg)return!1;var b=a.H;b=_.vh(b,b[_.H]|0);_.Eg(b,2048);a.H=b;a.g=void 0;a.h=void 0;return!0};_.fi=function(a){if(!_.ei(a)&&_.Ig(a,a.H[_.H]|0))throw Error();};_.gi=function(a,b){b===void 0&&(b=a[_.H]|0);b&32&&!(b&4096)&&_.Fg(a,b|4096)};
_.hi=function(a,b,c,d,e){var f=c+(e?0:-1),g=a.length-1;if(g>=1+(e?0:-1)&&f>=g){var h=a[g];if(h!=null&&typeof h==="object"&&h.constructor===Object)return h[c]=d,b}if(f<=g)return a[f]=d,b;if(d!==void 0){var k;g=((k=b)!=null?k:b=a[_.H]|0)>>13&1023||536870912;c>=g?d!=null&&(f={},a[g+(e?0:-1)]=(f[c]=d,f)):a[f]=d}return b};_.ji=function(a,b){a=_.ii(a,b,void 0);return Array.isArray(a)?a:_.Ih};_.ki=function(a,b){2&b&&(a|=2);return a|1};_.li=function(a,b){return a=(2&b?a|2:a&-3)&-273};
_.mi=function(a){return!!(2&a)&&!!(4&a)||!!(256&a)};_.ni=function(a,b,c,d,e,f,g,h){var k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?_.mi(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==k&&_.Fg(a,b),Object.freeze(a)):(f===2&&_.mi(b)&&(a=_.ai(a),k=0,b=_.li(b,d),d=_.hi(c,d,e,a)),_.mi(b)||(h||(b|=16),b!==k&&_.Fg(a,b)));2&b||!(4096&b||16&b)||_.gi(c,d);return a};_.oi=function(a,b,c,d){_.fi(a);var e=a.H;_.hi(e,e[_.H]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a};
_.pi=function(a,b,c,d,e){var f=!1;d=_.ii(a,d,e,function(g){var h=_.Xg(g,c,!1,b);f=h!==g&&h!=null;return h});if(d!=null)return f&&!_.Ig(d)&&_.gi(a,b),d};
_.qi=function(a,b,c,d,e,f,g,h){var k=_.Ig(a,c);f=k?1:f;g=!!g||f===3;k=h&&!k;(f===2||k)&&_.ei(a)&&(b=a.H,c=b[_.H]|0);a=_.ji(b,e);var m=a===_.Ih?7:a[_.H]|0,n=_.ki(m,c);if(h=!(4&n)){var p=a,q=c,r=!!(2&n);r&&(q|=2);for(var u=!r,A=!0,C=0,K=0;C<p.length;C++){var U=_.Xg(p[C],d,!1,q);if(U instanceof d){if(!r){var fa=_.Ig(U);u&&(u=!fa);A&&(A=fa)}p[K++]=U}}K<C&&(p.length=K);n|=4;n=A?n&-4097:n|4096;n=u?n|8:n&-9}n!==m&&(_.Fg(a,n),2&n&&Object.freeze(a));if(k&&!(8&n||!a.length&&(f===1||(f!==4?0:2&n||!(16&n)&&32&
c)))){_.mi(n)&&(a=_.ai(a),n=_.li(n,c),c=_.hi(b,c,e,a));d=a;k=n;for(m=0;m<d.length;m++)p=d[m],n=_.$g(p),p!==n&&(d[m]=n);k|=8;n=k=d.length?k|4096:k&-4097;_.Fg(a,n)}return a=_.ni(a,n,b,c,e,f,h,g)};_.ri=function(a){a==null&&(a=void 0);return a};_.si=function(a){return function(b){b=JSON.parse(b);if(!Array.isArray(b))throw Error("ia`"+_.Tb(b)+"`"+b);_.Gg(b);return new a(b)}};ti=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.L=function(a){return ti(a,a)};
ui=function(){this.B=!1;this.o=null;this.h=void 0;this.g=1;this.A=this.l=0;this.G=this.j=null};vi=function(a){if(a.B)throw new TypeError("h");a.B=!0};ui.prototype.D=function(a){this.h=a};var wi=function(a,b){a.j={pg:b,Pg:!0};a.g=a.l||a.A};ui.prototype.return=function(a){this.j={return:a};this.g=this.A};_.M=function(a,b,c){a.g=c;return{value:b}};ui.prototype.Ka=function(a){this.g=a};_.xi=function(a,b,c){a.l=b;c!=void 0&&(a.A=c)};_.yi=function(a){a.l=0;var b=a.j.pg;a.j=null;return b};
zi=function(a){this.g=new ui;this.h=a};Ci=function(a,b){vi(a.g);var c=a.g.o;if(c)return Ai(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return Bi(a)};Ai=function(a,b,c,d){try{var e=b.call(a.g.o,c);if(!(e instanceof Object))throw new TypeError("g`"+e);if(!e.done)return a.g.B=!1,e;var f=e.value}catch(g){return a.g.o=null,wi(a.g,g),Bi(a)}a.g.o=null;d.call(a.g,f);return Bi(a)};
Bi=function(a){for(;a.g.g;)try{var b=a.h(a.g);if(b)return a.g.B=!1,{value:b.value,done:!1}}catch(c){a.g.h=void 0,wi(a.g,c)}a.g.B=!1;if(a.g.j){b=a.g.j;a.g.j=null;if(b.Pg)throw b.pg;return{value:b.return,done:!0}}return{value:void 0,done:!0}};
Di=function(a){this.next=function(b){vi(a.g);a.g.o?b=Ai(a,a.g.o.next,b,a.g.D):(a.g.D(b),b=Bi(a));return b};this.throw=function(b){vi(a.g);a.g.o?b=Ai(a,a.g.o["throw"],b,a.g.D):(wi(a.g,b),b=Bi(a));return b};this.return=function(b){return Ci(a,b)};this[Symbol.iterator]=function(){return this}};Ei=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})};_.Fi=function(a){return Ei(new Di(new zi(a)))};
Gi=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};Hi=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};_.Ii=function(a){if(a instanceof _.de)return a;var b=new _.de(_.ae);_.ce(b,2,a);return b};
_.ii=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}};_.Ji=function(a,b,c,d){b=_.ii(a.H,b,c);if(b!==null||d&&a.h!==_.Hg)return b};_.Ki=function(a,b,c){_.fi(a);var d=a.H;_.hi(d,d[_.H]|0,b,c);return a};
_.Li=function(a,b,c,d){var e=a.H,f=e[_.H]|0;b=_.pi(e,f,b,c,d);if(b==null)return b;f=e[_.H]|0;if(!_.Ig(a,f)){var g=_.$g(b);g!==b&&(_.ei(a)&&(e=a.H,f=e[_.H]|0),b=g,f=_.hi(e,f,c,b,d),_.gi(e,f))}return b};_.Mi=function(a,b,c){var d=a.H;return _.qi(a,d,d[_.H]|0,b,c,void 0===_.Lh?2:4,!1,!0)};_.N=function(a,b,c){c=_.ri(c);_.Ki(a,b,c);c&&!_.Ig(c)&&_.gi(a.H);return a};_.Ni=function(a,b){var c=c===void 0?!1:c;a=_.ci(_.Ji(a,b));return a!=null?a:c};
_.Oi=function(a,b){var c=c===void 0?"":c;var d;return(d=_.di(_.Ji(a,b)))!=null?d:c};_.Pi=function(a,b){var c=c===void 0?0:c;a=_.Ji(a,b);a=a==null?a:(0,_.Th)(a)?a|0:void 0;return a!=null?a:c};_.Qi=function(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("ja");return new a(_.$h(b))};var Ti,Ri;_.Si=function(a,b,c){if(typeof b==="string")(b=Ri(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=Ri(c,d);f&&(c.style[f]=e)}};Ti={};Ri=function(a,b){var c=Ti[b];if(!c){var d=Gi(b);c=d;a.style[d]===void 0&&(d=(_.Ec?"Webkit":_.Dc?"Moz":null)+Hi(d),a.style[d]!==void 0&&(c=d));Ti[b]=c}return c};_.Ui=_.Dc?"MozUserSelect":_.Ec||_.Cc?"WebkitUserSelect":null;
}catch(e){_._DumpException(e)}
try{
var Wi,Xi,Yi,aj,bj,cj,dj,ej,fj,hj,jj,kj,lj,mj,nj,oj,tj,uj,yj,Hj,Ij,Lj,Mj,Nj,Oj,Pj,Qj,Sj,Zi,$i,gj,Zj;_.Vi=function(a){if(a instanceof _.nc)return a.g;throw Error("v");};Wi=function(a){var b=a.H,c=b[_.H]|0;return _.xh(a,b,c)?_.wh(a,b,!0):new a.constructor(_.vh(b,c,!1))};Xi=function(a){a=Error(a);_.sg(a,"warning");return a};Yi=function(){return typeof BigInt==="function"};aj=function(a){var b=a>>>0;Zi=b;$i=(a-b)/4294967296>>>0};bj=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};
cj=function(a){if(a<0){aj(-a);var b=_.z(bj(Zi,$i));a=b.next().value;b=b.next().value;Zi=a>>>0;$i=b>>>0}else aj(a)};dj=function(a){a=String(a);return"0000000".slice(a.length)+a};ej=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Yi()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+dj(c)+dj(a));return c};
fj=function(){var a=Zi,b=$i;b&2147483648?Yi()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=_.z(bj(a,b)),a=b.next().value,b=b.next().value,a="-"+ej(a,b)):a=ej(a,b);return a};hj=function(a){switch(typeof a){case "bigint":return!0;case "number":return(0,_.Th)(a);case "string":return gj.test(a);default:return!1}};_.ij=function(a){if(typeof a!=="number")throw Xi("int32");if(!(0,_.Th)(a))throw Xi("int32");return a|0};
jj=function(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return(0,_.Th)(a)?a|0:void 0};
kj=function(a){var b=a.length;if(a[0]==="-"?b<20||b===20&&a<="-9223372036854775808":b<19||b===19&&a<="9223372036854775807")return a;if(a.length<16)cj(Number(a));else if(Yi())a=BigInt(a),Zi=Number(a&BigInt(4294967295))>>>0,$i=Number(a>>BigInt(32)&BigInt(4294967295));else{b=+(a[0]==="-");$i=Zi=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),$i*=1E6,Zi=Zi*1E6+d,Zi>=4294967296&&($i+=Math.trunc(Zi/4294967296),$i>>>=0,Zi>>>=0);b&&(b=_.z(bj(Zi,$i)),a=b.next().value,b=b.next().value,
Zi=a,$i=b)}return fj()};lj=function(a){var b=(0,_.Uh)(Number(a));if((0,_.Sh)(b))return _.Rg(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return Yi()?_.Rg((0,_.Rh)(64,BigInt(a))):_.Rg(kj(a))};mj=function(a){var b=(0,_.Uh)(Number(a));if((0,_.Sh)(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return kj(a)};
nj=function(a){a=(0,_.Uh)(a);if(!(0,_.Sh)(a)){cj(a);var b=Zi,c=$i;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:ej(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a};oj=function(a){a=(0,_.Uh)(a);(0,_.Sh)(a)?a=String(a):(cj(a),a=fj());return a};tj=function(a){return(0,_.Sh)(a)?_.Rg(nj(a)):_.Rg(oj(a))};
uj=function(a){var b=0;b=b===void 0?0:b;if(!hj(a))throw Xi("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return mj(a);case "bigint":return String((0,_.Rh)(64,a));default:return oj(a)}case 1024:switch(c){case "string":return lj(a);case "bigint":return _.Rg((0,_.Rh)(64,a));default:return tj(a)}case 0:switch(c){case "string":return mj(a);case "bigint":return _.Rg((0,_.Rh)(64,a));default:return nj(a)}default:throw Error("Unknown format requested type for int64");}};
_.vj=function(a){return a==null?a:uj(a)};_.wj=function(a){if(a!=null&&typeof a!=="string")throw Error();return a};_.xj=function(a){return function(b){return _.Qi(a,b)}};
yj=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=g,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var u=e[1],A=e[2],C=e[3],K=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var U=C^u&(A^C);var fa=1518500249}else U=u^A^C,fa=1859775393;else r<60?(U=u&A|C&(u|A),fa=2400959708):(U=u^A^C,fa=3395469782);U=((p<<5|p>>>27)&4294967295)+
U+K+fa+q[r]&4294967295;K=C;C=A;A=(u<<30|u>>>2)&4294967295;u=p;p=U}e[0]=e[0]+p&4294967295;e[1]=e[1]+u&4294967295;e[2]=e[2]+A&4294967295;e[3]=e[3]+C&4294967295;e[4]=e[4]+K&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],u=0,A=p.length;u<A;++u)r.push(p.charCodeAt(u));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],
q=n*8;m<56?c(h,56-m):c(h,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var u=24;u>=0;u-=8)p[q++]=e[r]>>u&255;return p}for(var e=[],f=[],g=[],h=[128],k=1;k<64;++k)h[k]=0;var m,n;a();return{reset:a,update:c,digest:d,Ei:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}};
Hj=function(a,b){b=b===void 0?zj:b;if(!Aj){var c;a=(c=a.navigator)==null?void 0:c.userAgentData;if(!a||typeof a.getHighEntropyValues!=="function"||a.brands&&typeof a.brands.map!=="function")return Promise.reject(Error("oa"));Bj((a.brands||[]).map(function(e){var f=new Cj;f=_.Dj(f,1,e.brand);return _.Dj(f,2,e.version)}));typeof a.mobile==="boolean"&&_.Ej(Fj,2,a.mobile);Aj=a.getHighEntropyValues(b)}var d=new Set(b);return Aj.then(function(e){var f=Wi(Fj);d.has("platform")&&_.Dj(f,3,e.platform);d.has("platformVersion")&&
_.Dj(f,4,e.platformVersion);d.has("architecture")&&_.Dj(f,5,e.architecture);d.has("model")&&_.Dj(f,6,e.model);d.has("uaFullVersion")&&_.Dj(f,7,e.uaFullVersion);return _.Gj(f)}).catch(function(){return _.Gj(Fj)})};Ij=function(a){this.g=this.h=this.j=a};_.Kj=function(a,b){a.Da?b():(a.qa||(a.qa=[]),a.qa.push(b))};Lj={Fn:!0};_.Gj=function(a){return JSON.stringify(_.mh(a))};Mj=function(a){var b=a.G.splice(0)[0];(b=a.j=a.j||b)?b.Pg?a.g=a.l||a.A:b.Ka!=void 0&&a.A<b.Ka?(a.g=b.Ka,a.j=null):a.g=a.A:a.g=0};
Nj={};Oj=null;
Pj=function(a,b){b===void 0&&(b=0);if(!Oj){Oj={};for(var c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),d=["+/=","+/","-_=","-_.","-_"],e=0;e<5;e++){var f=c.concat(d[e].split(""));Nj[e]=f;for(var g=0;g<f.length;g++){var h=f[g];Oj[h]===void 0&&(Oj[h]=g)}}}b=Nj[b];c=Array(Math.floor(a.length/3));d=b[64]||"";for(e=f=0;f<a.length-2;f+=3){var k=a[f],m=a[f+1];h=a[f+2];g=b[k>>2];k=b[(k&3)<<4|m>>4];m=b[(m&15)<<2|h>>6];h=b[h&63];c[e++]=g+k+m+h}g=0;h=d;switch(a.length-f){case 2:g=
a[f+1],h=b[(g&15)<<2]||d;case 1:a=a[f],c[e]=b[a>>2]+b[(a&3)<<4|g>>4]+h+d}return c.join("")};Qj={};_.nh=function(a,b){if(b!==Qj)throw Error("Z");this.l=a;if(a!=null&&a.length===0)throw Error("Y");};_.nh.prototype.g=function(){var a=this.l;if(a==null)a="";else if(typeof a!=="string"){if(_.Eh){for(var b="",c=0,d=a.length-10240;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);a=btoa(b)}else a=Pj(a);a=this.l=a}return a};
_.Rj=_.Ag(void 0,"1oa");Sj={};Zi=0;$i=0;gj=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;_.Tj=function(a,b,c){_.fi(a);var d=a.H,e=d[_.H]|0;if(c==null)return _.hi(d,e,b),a;for(var f=c===_.Ih?7:c[_.H]|0,g=f,h=_.mi(f),k=h||Object.isFrozen(c),m=!0,n=!0,p=0;p<c.length;p++){var q=c[p];h||(q=_.Ig(q),m&&(m=!q),n&&(n=q))}h||(f=m?13:5,f=n?f&-4097:f|4096);k&&f===g||(c=_.ai(c),g=0,f=_.li(f,e));f!==g&&_.Fg(c,f);e=_.hi(d,e,b,c);2&f||!(4096&f||16&f)||_.gi(d,e);return a};
_.Uj=function(a,b,c){c=c===void 0?_.Yh:c;a=_.Ji(a,b);b=typeof a;a=a==null?a:b==="bigint"?_.Rg((0,_.Rh)(64,a)):hj(a)?b==="string"?lj(a):tj(a):void 0;return a!=null?a:c};_.Ej=function(a,b,c){return _.Ki(a,b,c==null?c:_.bi(c))};_.Vj=function(a,b,c){return _.Ki(a,b,c==null?c:_.ij(c))};_.Dj=function(a,b,c){return _.Ki(a,b,_.wj(c))};_.Wj=function(a,b,c){if(c!=null){if(!(0,_.Th)(c))throw Xi("enum");c|=0}return _.Ki(a,b,c)};
Zj=function(){var a=Xj,b=Yj;this.g=175237375;this.jc=b;this.h=_.Li;this.defaultValue=void 0;this.j=a.Bn!=null?Sj:void 0};Zj.prototype.register=function(){_.zc(this)};var ak=function(a){this.H=_.I(a)};_.y(ak,_.J);var bk=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("ka`"+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&c!=="moz-extension"&&
c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("la`"+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};var dk=function(a,b,c){var d=String(_.t.location.href);return d&&a&&b?[b,ck(bk(d),a,c||null)].join(" "):null},ck=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],_.dc(d,function(h){e.push(h)}),ek(e.join(" "));var f=[],g=[];_.dc(c,function(h){g.push(h.key);f.push(h.value)});c=Math.floor((new Date).getTime()/1E3);e=f.length==0?[c,b,a]:[f.join(":"),c,b,a];_.dc(d,function(h){e.push(h)});a=ek(e.join(" "));a=[c,a];g.length==0||a.push(g.join(""));return a.join("_")},ek=function(a){var b=
yj();b.update(a);return b.Ei().toLowerCase()};var fk=function(){this.g=document||{cookie:""}};_.l=fk.prototype;_.l.isEnabled=function(){if(!_.t.navigator.cookieEnabled)return!1;if(this.g.cookie)return!0;this.set("TESTCOOKIESENABLED","1",{Tg:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
_.l.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.Hn;d=c.In||!1;var f=c.domain||void 0;var g=c.path||void 0;var h=c.Tg}if(/[;=\s]/.test(a))throw Error("ma`"+a);if(/[;\r\n]/.test(b))throw Error("na`"+b);h===void 0&&(h=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(g?";path="+g:"")+(h<0?"":h==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+h*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};
_.l.get=function(a,b){for(var c=a+"=",d=(this.g.cookie||"").split(";"),e=0,f;e<d.length;e++){f=_.kc(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};_.l.remove=function(a,b,c){var d=this.get(a)!==void 0;this.set(a,"",{Tg:0,path:b,domain:c});return d};_.l.Fd=function(){return gk(this).keys};_.l.Ab=function(){return gk(this).values};_.l.clear=function(){for(var a=gk(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};
var gk=function(a){a=(a.g.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=_.kc(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};var hk=function(a,b,c,d){(a=_.t[a])||typeof document==="undefined"||(a=(new fk).get(b));return a?dk(a,c,d):null},ik=function(a){var b=bk(_.t==null?void 0:_.t.location.href),c=[],d;(d=_.t.__SAPISID||_.t.__APISID||_.t.__3PSAPISID||_.t.__1PSAPISID||_.t.__OVERRIDE_SID)?d=!0:(typeof document!=="undefined"&&(d=new fk,d=d.get("SAPISID")||d.get("APISID")||d.get("__Secure-3PAPISID")||d.get("__Secure-1PAPISID")),d=!!d);d&&(d=(b=b.indexOf("https:")==0||b.indexOf("chrome-extension:")==0||b.indexOf("chrome-untrusted://new-tab-page")==
0||b.indexOf("moz-extension:")==0)?_.t.__SAPISID:_.t.__APISID,d||typeof document==="undefined"||(d=new fk,d=d.get(b?"SAPISID":"APISID")||d.get("__Secure-3PAPISID")),(d=d?dk(d,b?"SAPISIDHASH":"APISIDHASH",a):null)&&c.push(d),b&&((b=hk("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",a))&&c.push(b),(a=hk("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",a))&&c.push(a)));return c.length==0?null:c.join(" ")};var kk;_.jk=function(){};kk=function(a){var b,c,d,e;return _.Fi(function(f){switch(f.g){case 1:return b=new CompressionStream("gzip"),c=(new Response(b.readable)).arrayBuffer(),d=b.writable.getWriter(),_.M(f,d.write((new TextEncoder).encode(a)),2);case 2:return _.M(f,d.close(),3);case 3:return e=Uint8Array,_.M(f,c,4);case 4:return f.return(new e(f.h))}})};_.jk.prototype.jb=function(a){return a<1024?!1:typeof CompressionStream!=="undefined"};var lk=function(a,b){this.h=a;this.o=b;this.g=!1;this.j=function(){return _.Zb()};this.l=this.j()},mk=function(a,b){a.h=b;a.timer&&a.g?(a.stop(),a.start()):a.timer&&a.stop()};lk.prototype.start=function(){var a=this;this.g=!0;this.timer||(this.timer=setTimeout(function(){a.tick()},this.h),this.l=this.j())};lk.prototype.stop=function(){this.g=!1;this.timer&&(clearTimeout(this.timer),this.timer=void 0)};
lk.prototype.tick=function(){var a=this;if(this.g){var b=Math.max(this.j()-this.l,0);b<this.h*.8?this.timer=setTimeout(function(){a.tick()},this.h-b):(this.timer&&(clearTimeout(this.timer),this.timer=void 0),this.o(),this.g&&(this.stop(),this.start()))}else this.timer=void 0};var nk=function(a){this.H=_.I(a)};_.y(nk,_.J);var ok=function(a){this.H=_.I(a)};_.y(ok,_.J);var pk=function(a){this.H=_.I(a)};_.y(pk,_.J);pk.prototype.Te=function(){return _.Pi(this,1)};var Cj=function(a){this.H=_.I(a)};_.y(Cj,_.J);var qk=function(a){this.H=_.I(a)};_.y(qk,_.J);var Bj=function(a){_.Tj(Fj,1,a)},rk=_.xj(qk);var sk=function(a){this.H=_.I(a)};_.y(sk,_.J);var zj=["platform","platformVersion","architecture","model","uaFullVersion"],Fj=new qk,Aj=null;var tk=function(a){this.H=_.I(a)};_.y(tk,_.J);var uk=function(a){return _.Wj(a,1,1)};var vk=function(a){this.H=_.I(a)};_.y(vk,_.J);vk.prototype.wa=function(){return _.Oi(this,2)};vk.prototype.Ia=function(a){_.Dj(this,2,a)};var wk=function(a){this.H=_.I(a,4)};_.y(wk,_.J);_.xk=function(a){this.H=_.I(a,36)};_.y(_.xk,_.J);_.xk.prototype.Ia=function(a,b){_.fi(this);var c=this.H,d=_.qi(this,c,c[_.H]|0,vk,3,2,!0);if(typeof a!=="number"||a<0||a>d.length)throw Error();a!=void 0?d.splice(a,1,b):d.push(b);var e=a=d===_.Ih?7:d[_.H]|0;(b=_.Ig(b))?(a&=-9,d.length===1&&(a&=-4097)):a|=4096;a!==e&&_.Fg(d,a);b||_.gi(c)};_.yk=function(a,b){return _.Dj(a,8,b)};var zk=function(a){this.H=_.I(a,19)};_.y(zk,_.J);zk.prototype.bd=function(a){return _.Wj(this,2,a)};var Bk=function(a,b){this.tb=b=b===void 0?!1:b;this.j=this.locale=null;this.h=0;this.l=!1;this.g=new zk;Number.isInteger(a)&&this.g.bd(a);b||(this.locale=document.documentElement.getAttribute("lang"));Ak(this,new tk)};Bk.prototype.bd=function(a){this.g.bd(a);return this};
var Ak=function(a,b){_.N(a.g,1,b);_.Pi(b,1)||uk(b);a.tb||(b=Ck(a),_.Oi(b,5)||_.Dj(b,5,a.locale));a.j&&(b=Ck(a),_.Li(b,qk,9)||_.N(b,9,a.j))},Dk=function(a,b){a.h=b},Ek=function(a){var b=b===void 0?zj:b;var c=a.tb?void 0:_.fb();c?Hj(c,b).then(function(d){a.j=rk(d!=null?d:"[]");d=Ck(a);_.N(d,9,a.j);return!0}).catch(function(){return!1}):Promise.resolve(!1)},Ck=function(a){var b=_.Li(a.g,tk,1);b||(b=new tk,Ak(a,b));a=b;b=_.Li(a,sk,11);b||(b=new sk,_.N(a,11,b));return b},Fk=function(a,b,c,d,e,f,g){c=c===
void 0?0:c;d=d===void 0?0:d;e=e===void 0?null:e;f=f===void 0?0:f;g=g===void 0?0:g;if(!a.tb){var h=Ck(a);var k=new pk;k=_.Wj(k,1,a.h);k=_.Ej(k,2,a.l);d=_.Vj(k,3,d>0?d:void 0);d=_.Vj(d,4,f>0?f:void 0);d=_.Vj(d,5,g>0?g:void 0);f=d.H;g=f[_.H]|0;d=_.Ig(d,g)?d:_.xh(d,f,g)?_.wh(d,f):new d.constructor(_.vh(f,g,!0));_.N(h,10,d)}a=Wi(a.g);a=_.Ki(a,4,_.vj(Date.now().toString()));b=_.Tj(a,3,b.slice());e&&(a=new nk,e=_.Vj(a,13,e),a=new ok,e=_.N(a,2,e),a=new wk,e=_.N(a,1,e),e=_.Wj(e,2,9),_.N(b,18,e));c&&_.Ki(b,
14,_.vj(c));return b};Ij.prototype.reset=function(){this.g=this.h=this.j};Ij.prototype.wa=function(){return this.h};var Xj=function(a){this.H=_.I(a,8)};_.y(Xj,_.J);var Gk=_.xj(Xj);var Yj=function(a){this.H=_.I(a)};_.y(Yj,_.J);var Hk;Hk=new Zj;var Lk=function(a){_.w.call(this);var b=this;this.g=[];this.Y="";this.J=this.O=!1;this.la=this.L=-1;this.S=!1;this.B=this.experimentIds=null;this.A=this.l=0;this.G=null;this.F=this.I=0;this.ua=1;this.oe=0;this.sc=a.sc;this.Qb=a.Qb||function(){};this.j=new Bk(a.sc,a.tb);this.Fa=a.Fa||null;this.Zb=a.Zb||null;this.D=a.Ek||null;this.Eb=a.Eb||null;this.Lc=a.Lc||!1;this.withCredentials=!a.Bd;this.tb=a.tb||!1;this.T=!this.tb&&!!_.fb()&&!!_.fb().navigator&&_.fb().navigator.sendBeacon!==void 0;this.X=typeof URLSearchParams!==
"undefined"&&!!(new URL(Ik())).searchParams&&!!(new URL(Ik())).searchParams.set;var c=uk(new tk);Ak(this.j,c);this.o=new Ij(1E4);a=Jk(this,a.dg);this.h=new lk(this.o.wa(),a);this.N=new lk(6E5,a);this.Lc||this.N.start();this.tb||(document.addEventListener("visibilitychange",function(){if(document.visibilityState==="hidden"){Kk(b);var d;(d=b.G)==null||d.flush()}}),document.addEventListener("pagehide",function(){Kk(b);var d;(d=b.G)==null||d.flush()}))},Mk,Pk,Nk;_.y(Lk,_.w);
var Jk=function(a,b){return a.X?b?function(){b().then(function(){a.flush()})}:function(){a.flush()}:function(){}};Lk.prototype.M=function(){Kk(this);this.h.stop();this.N.stop();_.w.prototype.M.call(this)};Mk=function(a){a.D||(a.D=Ik());try{return(new URL(a.D)).toString()}catch(b){return(new URL(a.D,_.fb().location.origin)).toString()}};_.Ok=function(a,b){if(b instanceof _.xk)a.log(b);else try{var c=_.yk(new _.xk,_.Gj(b));a.log(c)}catch(d){Nk(a,4,1)}};Pk=function(a,b){a.o=new Ij(b<1?1:b);mk(a.h,a.o.wa())};
Nk=function(a,b,c){a.G&&a.G.yn(b,c)};
Lk.prototype.log=function(a){Nk(this,2,1);if(this.X){a=Wi(a);var b=this.ua++;b=a=_.Ki(a,21,_.vj(b));var c=_.Ji(b,1);var d=d===void 0?!1:d;var e=typeof c;d=c==null?c:e==="bigint"?String((0,_.Rh)(64,c)):hj(c)?e==="string"?mj(c):d?oj(c):nj(c):void 0;d==null&&(d=Date.now(),d=Number.isFinite(d)?d.toString():"0",_.Ki(b,1,_.vj(d)));d=_.Ji(b,15);d!=null&&(typeof d==="bigint"?(0,_.lh)(d)?d=Number(d):(d=(0,_.Rh)(64,d),d=(0,_.lh)(d)?Number(d):String(d)):d=hj(d)?typeof d==="number"?nj(d):mj(d):void 0);d==null&&
_.Ki(b,15,_.vj((new Date).getTimezoneOffset()*60));this.experimentIds&&(d=Wi(this.experimentIds),_.N(b,16,d));Nk(this,1,1);b=this.g.length-1E3+1;b>0&&(this.g.splice(0,b),this.l+=b,Nk(this,3,b));this.g.push(a);this.Lc||this.h.g||this.h.start()}};
Lk.prototype.flush=function(a,b){var c=this;if(this.g.length===0)a&&a();else{var d=Date.now();if(this.la>d&&this.L<d)b&&b("throttled");else{this.Fa&&(typeof this.Fa.Te==="function"?Dk(this.j,this.Fa.Te()):this.j.h=0);var e=this.g.length,f=Fk(this.j,this.g,this.l,this.A,this.Zb,this.I,this.F),g=this.Qb();if(g&&this.Y===g)b&&b("stale-auth-token");else{this.g=[];this.h.g&&this.h.stop();this.l=0;d=_.Gj(f);var h;this.B&&this.B.jb(d.length)&&(h=kk(d));var k=Qk(this,d,g),m=function(q){c.o.reset();mk(c.h,
c.o.wa());if(q){var r=null;try{var u=JSON.stringify(JSON.parse(q.replace(")]}'\n","")));r=Gk(u)}catch(U){}if(r){q=Number(_.Uj(r,1,_.Rg("-1")));q>0&&(c.L=Date.now(),c.la=c.L+q);q=Hk.g;u=_.fh(_.gh);var A;_.Bg&&u&&((A=r.H[u])==null?void 0:A[q])!=null&&_.zg(_.Gh,3);a:{A=Hk.g;var C=C===void 0?!1:C;if(_.fh(_.Hh)&&_.fh(_.gh)&&void 0===_.Hh){q=r.H;u=q[_.gh];if(!u)break a;if(u=u.Gn)try{u(q,A,Lj);break a}catch(U){_.lb(U)}}C&&(C=r.H,(q=_.fh(_.gh))&&q in C&&(C=C[q])&&delete C[A])}C=Hk.jc?Hk.h(r,Hk.jc,Hk.g,Hk.j):
Hk.h(r,Hk.g,null,Hk.j);if(r=C===null?void 0:C){C=-1;C=C===void 0?0:C;var K;r=(K=jj(_.Ji(r,1)))!=null?K:C;r!==-1&&(c.S||Pk(c,r))}}}a&&a();c.A=0},n=function(q,r){var u=_.Mi(f,_.xk,3);var A=Number(_.Uj(f,14)),C=c.o;C.g=Math.min(3E5,C.g*2);C.h=Math.min(3E5,C.g+Math.round(.1*(Math.random()-.5)*2*C.g));mk(c.h,c.o.wa());q===401&&g&&(c.Y=g);A&&(c.l+=A);r===void 0&&(r=500<=q&&q<600||q===401||q===0);r&&(c.g=u.concat(c.g),c.Lc||c.h.g||c.h.start());Nk(c,7,1);b&&b("net-send-failed",q);++c.A},p=function(){c.Fa&&
c.Fa.send(k,m,n)};h?h.then(function(q){Nk(c,5,e);k.vf["Content-Encoding"]="gzip";k.vf["Content-Type"]="application/binary";k.body=q;k.Ai=2;p()},function(){Nk(c,6,e);p()}):p()}}}};
var Qk=function(a,b,c){c=c===void 0?null:c;var d=d===void 0?a.withCredentials:d;var e={},f=new URL(Mk(a));c&&(e.Authorization=c);a.Eb&&(e["X-Goog-AuthUser"]=a.Eb,f.searchParams.set("authuser",a.Eb));return{url:f.toString(),body:b,Ai:1,vf:e,pk:"POST",withCredentials:d,oe:a.oe}},Kk=function(a){a.j.l=!0;a.O&&(a.j.h=3,Rk(a));a.J&&(a.j.h=2,Sk(a));a.flush();a.j.l=!1},Rk=function(a){Tk(a,32,10,function(b,c){b=new URL(b);b.searchParams.set("format","json");var d=!1;try{d=_.fb().navigator.sendBeacon(b.toString(),
_.Gj(c))}catch(e){}d||(a.T=!1);return d})},Sk=function(a){Tk(a,6,5,function(b,c){b=new URL(b);b.searchParams.set("format","base64json");var d=b.searchParams,e=d.set;c=_.Gj(c);for(var f=[],g=0,h=0;h<c.length;h++){var k=c.charCodeAt(h);k>255&&(f[g++]=k&255,k>>=8);f[g++]=k}c=Pj(f,3);e.call(d,"p",c);b=b.toString();if(b.length>15360)return!1;(new Image).src=b;return!0})},Tk=function(a,b,c,d){if(a.g.length!==0){var e=new URL(Mk(a));e.searchParams.delete("format");var f=a.Qb();f&&e.searchParams.set("auth",
f);e.searchParams.set("authuser",a.Eb||"0");for(f=0;f<c&&a.g.length;++f){var g=a.g.slice(0,b),h=Fk(a.j,g,a.l,a.A,a.Zb,a.I,a.F);if(!d(e.toString(),h)){++a.A;break}a.l=0;a.A=0;a.I=0;a.F=0;a.g=a.g.slice(g.length)}a.h.g&&a.h.stop()}},Ik=function(){return"https://play.google.com/log?format=json&hasfast=true"};var Uk=function(){this.ui=typeof AbortController!=="undefined"};
Uk.prototype.send=function(a,b,c){var d=this,e,f,g,h,k,m,n,p,q,r;return _.Fi(function(u){switch(u.g){case 1:return f=(e=d.ui?new AbortController:void 0)?setTimeout(function(){e.abort()},a.oe):void 0,_.xi(u,2,3),g=Object.assign({},{method:a.pk,headers:Object.assign({},a.vf)},a.body&&{body:a.body},a.withCredentials&&{credentials:"include"},{signal:a.oe&&e?e.signal:null}),_.M(u,fetch(a.url,g),5);case 5:h=u.h;if(h.status!==200){(k=c)==null||k(h.status);u.Ka(3);break}if((m=b)==null){u.Ka(7);break}return _.M(u,
h.text(),8);case 8:m(u.h);case 7:case 3:u.G=[u.j];u.l=0;u.A=0;clearTimeout(f);Mj(u);break;case 2:n=_.yi(u);switch((p=n)==null?void 0:p.name){case "AbortError":(q=c)==null||q(408);break;default:(r=c)==null||r(400)}u.Ka(3)}})};Uk.prototype.Te=function(){return 4};_.Vk=function(a,b){b=b===void 0?"0":b;_.w.call(this);this.sc=a;this.Eb=b;this.B="https://play.google.com/log?format=json&hasfast=true";this.j=null;this.g=void 0;this.h=this.Zb=this.Fa=null;this.o=this.A=!1;this.l=null};_.y(_.Vk,_.w);_.Wk=function(a,b){a.Qb=b;return a};_.Xk=function(a){a.A=!0;return a};_.Vk.prototype.Bd=function(){this.D=!0;return this};
_.Yk=function(a){a.Fa||(a.Fa=new Uk);var b=new Lk({sc:a.sc,Qb:a.Qb?a.Qb:ik,Eb:a.Eb,Ek:a.B,tb:!1,Lc:!1,Bd:a.D,dg:a.dg,Fa:a.Fa});_.Kj(a,_.Yb(_.qa,b));if(a.j){var c=a.j,d=Ck(b.j);_.Dj(d,7,c)}a.g?b.B=a.g:a.g!==null&&(b.B=new _.jk);a.Zb&&(b.Zb=a.Zb);a.h&&((d=a.h)?(b.experimentIds||(b.experimentIds=new ak),c=b.experimentIds,d=_.Gj(d),_.Dj(c,4,d)):b.experimentIds&&_.Ki(b.experimentIds,4));a.A&&(b.O=a.A&&b.T);a.o&&(b.J=a.o);a.l&&(c=a.l,b.S=!0,Pk(b,c));Ek(b.j);a.Fa.bd&&a.Fa.bd(a.sc);a.Fa.uk&&a.Fa.uk(b);return b};_.Zk=function(a){this.H=_.I(a)};_.y(_.Zk,_.J);_.l=_.Zk.prototype;_.l.Ua=function(){return _.Oi(this,16)};_.l.lb=function(a){return _.Dj(this,16,a)};_.l.pa=function(){return _.Oi(this,1)};_.l.sa=function(a){return _.Dj(this,1,a)};_.l.Se=function(){return _.Pi(this,53)};
}catch(e){_._DumpException(e)}
try{
_.la("phishing_protection");
/*

 Copyright 2017 Google LLC
 SPDX-License-Identifier: BSD-3-Clause
*/
/*

 Copyright 2019 Google LLC
 SPDX-License-Identifier: BSD-3-Clause
*/
/*

 Copyright 2023 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
/*

 Copyright 2021 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var aA=function(){return _.Yk(Zz(_.Xk(_.Wk($z(),function(){return null}).Bd())))},cA=function(a){a=_.z(a);for(var b=a.next();!b.done;b=a.next())if(b=b.value,b.type==="childList"){b=_.z(b.addedNodes.values());for(var c=b.next();!c.done;c=b.next())c=c.value,c.nodeType===c.ELEMENT_NODE&&c.tagName.toLowerCase()==="input"&&bA(c)}},bA=function(a){[a.type,a.id,a.name,a.placeholder].some(dA)&&(a.disabled=!0,eA.log(fA))},dA=function(a){return a.toLowerCase().includes("username")||a.toLowerCase().includes("password")},
hA=function(a){a=a&&a.target instanceof Element?a.target:null;if(!a||!a.tagName)return!1;switch(a.tagName.toLowerCase()){case "select":return!0;case "textarea":return!0;case "input":return!!a.type&&a.type.toLowerCase()!=="search";case "button":return gA(a);default:return!1}},gA=function(a){return a.type.toLowerCase()==="submit"?!0:a.getAttributeNames().filter(function(b){return"form formaction formenctype formmethod formnovalidate formtarget value".split(" ").includes(b.toLowerCase())}).length>0},
jA=function(a){return function(b,c){if(typeof c==="object")b=iA(a,b,c);else{var d=b.hasOwnProperty(c);b.constructor.ba(c,a);b=d?Object.getOwnPropertyDescriptor(b,c):void 0}return b}},lA=function(a){return function(b,c){return kA(b,c,{get:function(){var d,e;return(e=(d=this.ac)==null?void 0:d.querySelector(a))!=null?e:null}})}},mA=function(){return jA(Object.assign({},void 0,{state:!0,Sa:!1}))},pA=function(a,b,c){if(a.nodeType!==1)return nA;b=b.toLowerCase();if(b==="innerhtml"||b==="innertext"||b===
"textcontent"||b==="outerhtml")return function(){return _.Vi(_.oc)};var d=oA.get(a.tagName+" "+b);return d!==void 0?d:/^on/.test(b)&&c==="attribute"&&(a=a.tagName.includes("-")?HTMLElement.prototype:a,b in a)?function(){throw Error("db");}:nA},sA=function(a,b){if(!qA(a)||!a.hasOwnProperty("raw"))throw Error("gb");return rA!==void 0?rA.createHTML(b):b},vA=function(a,b,c,d){c=c===void 0?a:c;if(b===tA)return b;var e,f=d!==void 0?(e=c.h)==null?void 0:e[d]:c.A;e=uA(b)?void 0:b._$litDirective$;var g;if(((g=
f)==null?void 0:g.constructor)!==e){var h,k;(h=f)==null||(k=h._$notifyDirectiveConnectionChanged)==null||k.call(h,!1);e===void 0?f=void 0:(f=new e(a),f.ki(a,c,d));if(d!==void 0){var m,n;((n=(m=c).h)!=null?n:m.h=[])[d]=f}else c.A=f}f!==void 0&&(b=vA(a,f.li(a,b.values),f,d));return b},wA=function(a){return a.replace("aria","aria-").replace(/Elements?/g,"").toLowerCase()},CA=function(a){var b=function(){var d=a.apply(this,arguments)||this;d[c]=new Set;return d};_.y(b,a);b.prototype.attributeChangedCallback=
function(d,e,f){if(!xA.includes(d))a.prototype.attributeChangedCallback.call(this,d,e,f);else if(!this[yA].has(d)){this[yA].add(d);this.removeAttribute(d);this[yA].delete(d);var g=zA(d);f===null?delete this.dataset[g]:this.dataset[g]=f;AA(this,zA(d),e)}};b.prototype.getAttribute=function(d){return xA.includes(d)?a.prototype.getAttribute.call(this,"data-"+d):a.prototype.getAttribute.call(this,d)};b.prototype.removeAttribute=function(d){a.prototype.removeAttribute.call(this,d);xA.includes(d)&&(a.prototype.removeAttribute.call(this,
"data-"+d),AA(this))};var c=yA;BA(b);return b},BA=function(a){for(var b=_.z(DA),c=b.next(),d={};!c.done;d={Kc:void 0,Hc:void 0},c=b.next()){d.Hc=c.value;c=wA(d.Hc);var e="data-"+c;d.Kc=zA(c);a.ba(d.Hc,{Sa:c,ah:!0});a.ba(Symbol(e),{Sa:e,ah:!0});Object.defineProperty(a.prototype,d.Hc,{configurable:!0,enumerable:!0,get:function(f){return function(){var g;return(g=this.dataset[f.Kc])!=null?g:null}}(d),set:function(f){return function(g){var h,k=(h=this.dataset[f.Kc])!=null?h:null;g!==k&&(g===null?delete this.dataset[f.Kc]:
this.dataset[f.Kc]=g,AA(this,f.Hc,k))}}(d)})}},zA=function(a){return a.replace(/-\w/,function(b){return b[1].toUpperCase()})},FA=function(){_.Fi(function(a){if(a.g==1)return EA=!0,_.M(a,null,2);EA=!1;a.g=0})},Zz=function(a){a.o=!0;return a},$z=function(){var a=new _.Vk(1056,"0");a.l=5E3;return a},GA=function(a){this.H=_.I(a)};_.y(GA,_.J);var HA=function(a){this.H=_.I(a)};_.y(HA,_.J);
var T=function(a,b,c,d){var e=arguments.length,f=e<3?b:d===null?d=Object.getOwnPropertyDescriptor(b,c):d,g;if(Reflect&&typeof Reflect==="object"&&typeof Reflect.decorate==="function")f=Reflect.decorate(a,b,c,d);else for(var h=a.length-1;h>=0;h--)if(g=a[h])f=(e<3?g(f):e>3?g(b,c,f):g(b,c))||f;e>3&&f&&Object.defineProperty(b,c,f)},V=function(a,b){if(Reflect&&typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(a,b)};var IA,JA=((IA=document.currentScript)==null?void 0:IA.getAttribute("data-source-url"))||"",KA=Math.floor(Math.random()*2147483647),LA=function(){var a=a===void 0?aA():a;var b=b===void 0?KA:b;var c=c===void 0?JA:c;this.h=a;this.g=b;this.Ma=c};LA.prototype.log=function(a){var b=new _.Zk;b=_.Wj(b,65,5);b=_.Wj(b,31,457);b=_.Wj(b,32,0);b=_.Wj(b,53,0);var c=this.Ma;try{var d=(new URL(c)).origin}catch(e){d=c}d=_.Dj(b,52,d);a=_.N(d,73,a);a=_.yk(new _.xk,_.Gj(a));a=_.Ki(a,12,_.vj(this.g));_.Ok(this.h,a)};var eA=new LA,fA,MA=new HA,NA,OA=new GA;NA=_.Ej(OA,1,!0);fA=_.N(MA,7,NA);var PA=function(a){return function(b,c){c!==void 0?c.addInitializer(function(){customElements.define(a,b)}):customElements.define(a,b)}};/*

 Copyright 2016 Google LLC
 SPDX-License-Identifier: BSD-3-Clause
*/
var QA=!!/^\s*class\s*\{\s*\}\s*$/.test(function(){}.toString())||HTMLElement.es5Shimmed||_.t.Reflect===void 0||_.t.customElements===void 0||_.t.customElements.polyfillWrapFlushCallback||!1,RA,SA=function(){var a=HTMLElement;if(QA)return a;if(RA!==void 0)return RA;var b=_.t.Reflect,c=function(){return b.construct(a,[],this.constructor)};c.prototype=a.prototype;c.prototype.constructor=c;c.es5Shimmed=!0;Object.setPrototypeOf(c,a);return RA=c},TA=!1;var UA=_.t.ShadowRoot&&(_.t.ShadyCSS===void 0||_.t.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,VA=Symbol(),WA=new WeakMap,XA=function(a,b,c){this._$cssResult$=!0;if(c!==VA)throw Error("$a");this.cssText=a;this.h=b};XA.prototype.toString=function(){return this.cssText};
_.yb.Object.defineProperties(XA.prototype,{g:{configurable:!0,enumerable:!0,get:function(){var a=this.j,b=this.h;if(UA&&a===void 0){var c=b!==void 0&&b.length===1;c&&(a=WA.get(b));a===void 0&&((this.j=a=new CSSStyleSheet).replaceSync(this.cssText),c&&WA.set(b,a))}return a}}});
var YA=function(a){var b=_.Kb.apply(1,arguments);return function(){var c=a.length===1?a[0]:b.reduce(function(d,e,f){if(e._$cssResult$===!0)e=e.cssText;else if(typeof e!=="number")throw Error("ab`"+e);return d+e+a[f+1]},a[0]);return new XA(c,a,VA)}()},ZA=function(a,b){if(UA)a.adoptedStyleSheets=b.map(function(f){return f instanceof CSSStyleSheet?f:f.g});else{b=_.z(b);for(var c=b.next();!c.done;c=b.next()){c=c.value;var d=document.createElement("style"),e=_.t.litNonce;e!==void 0&&d.setAttribute("nonce",
e);d.textContent=c.cssText;a.appendChild(d)}}},$A=UA?function(a){return a}:function(a){if(a instanceof CSSStyleSheet){var b="";a=_.z(a.cssRules);for(var c=a.next();!c.done;c=a.next())b+=c.value.cssText;b=new XA(typeof b==="string"?b:String(b),void 0,VA)}else b=a;return b};var aB=function(a){if(globalThis.ShadyCSS!==void 0&&(!globalThis.ShadyCSS.nativeShadow||globalThis.ShadyCSS.ApplyShim)){a=a.ReactiveElement.prototype;globalThis.ShadyDOM&&globalThis.ShadyDOM.inUse&&globalThis.ShadyDOM.noPatch===!0&&globalThis.ShadyDOM.patchElementProto(a);var b=a.xb;a.xb=function(){var e=this.localName;if(globalThis.ShadyCSS.nativeShadow)return b.call(this);if(!this.constructor.hasOwnProperty("__scoped")){this.constructor.__scoped=!0;var f=this.constructor.fa.map(function(m){return m instanceof
CSSStyleSheet?Array.from(m.cssRules).reduce(function(n,p){return n+p.cssText},""):m.cssText}),g,h;(g=globalThis.ShadyCSS)==null||(h=g.ScopingShim)==null||h.prepareAdoptedCssText(f,e);this.constructor.ji===void 0&&globalThis.ShadyCSS.prepareTemplateStyles(document.createElement("template"),e)}var k;return(k=this.shadowRoot)!=null?k:this.attachShadow(this.constructor.U)};var c=a.connectedCallback;a.connectedCallback=function(){c.call(this);this.Rb&&globalThis.ShadyCSS.styleElement(this)};var d=a.ze;
a.ze=function(e){this.Rb||globalThis.ShadyCSS.styleElement(this);d.call(this,e)}}},bB;(bB=globalThis).reactiveElementPolyfillSupport!=null||(bB.reactiveElementPolyfillSupport=aB);QA||TA||(TA=!0,_.t.HTMLElement=SA());
var cB=SA(),dB=Object,eB=dB.is,fB=dB.defineProperty,gB=dB.getOwnPropertyDescriptor,hB=dB.getOwnPropertyNames,iB=dB.getOwnPropertySymbols,jB=dB.getPrototypeOf,kB=_.t.trustedTypes,lB=kB?kB.emptyScript:"",mB=_.t.reactiveElementPolyfillSupport,nB={Ah:function(a,b){switch(b){case Boolean:a=a?lB:null;break;case Object:case Array:a=a==null?a:JSON.stringify(a)}return a},Re:function(a,b){var c=a;switch(b){case Boolean:c=a!==null;break;case Number:c=a===null?null:Number(a);break;case Object:case Array:try{c=
JSON.parse(a)}catch(d){c=null}}return c}},oB=function(a,b){return!eB(a,b)},pB={Sa:!0,type:String,Ob:nB,Ha:!1,Fh:!1,Gg:oB},qB;Symbol.metadata==null&&(Symbol.metadata=Symbol("bb"));qB=Symbol.metadata;var rB=new WeakMap,W=function(){var a=cB.call(this)||this;a.pd=void 0;a.Uc=!1;a.Rb=!1;a.ec=null;a.ni();return a};_.y(W,cB);W.addInitializer=function(a){this.ea();var b;((b=this.Gc)!=null?b:this.Gc=[]).push(a)};
W.ba=function(a,b){b=b===void 0?pB:b;b.state&&(b.Sa=!1);this.ea();this.prototype.hasOwnProperty(a)&&(b=Object.create(b),b.If=!0);this.hb.set(a,b);b.ah||(b=this.na(a,Symbol(),b),b!==void 0&&fB(this.prototype,a,b))};W.na=function(a,b,c){var d,e=(d=gB(this.prototype,a))!=null?d:{get:function(){return this[b]},set:function(h){this[b]=h}},f=e.get,g=e.set;return{get:f,set:function(h){var k=f==null?void 0:f.call(this);g==null||g.call(this,h);AA(this,a,k,c)},configurable:!0,enumerable:!0}};
W.ja=function(a){var b;return(b=this.hb.get(a))!=null?b:pB};W.ea=function(){if(!this.hasOwnProperty("hb")){var a=jB(this);a.ia();a.Gc!==void 0&&(this.Gc=[].concat(_.Hb(a.Gc)));this.hb=new Map(a.hb)}};
W.ia=function(){sB();if(!this.hasOwnProperty("Qe")){this.Qe=!0;this.ea();if(this.hasOwnProperty("properties")){var a=this.properties,b=[].concat(_.Hb(hB(a)),_.Hb(iB(a)));b=_.z(b);for(var c=b.next();!c.done;c=b.next())c=c.value,this.ba(c,a[c])}a=this[qB];if(a!==null&&(a=rB.get(a),a!==void 0))for(a=_.z(a),b=a.next();!b.done;b=a.next())c=_.z(b.value),b=c.next().value,c=c.next().value,this.hb.set(b,c);this.nd=new Map;a=_.z(this.hb);for(b=a.next();!b.done;b=a.next())c=_.z(b.value),b=c.next().value,c=c.next().value,
c=this.da(b,c),c!==void 0&&this.nd.set(c,b);this.fa=this.ma(this.Gb)}};W.ma=function(a){var b=[];if(Array.isArray(a)){a=new Set(a.flat(Infinity).reverse());a=_.z(a);for(var c=a.next();!c.done;c=a.next())b.unshift($A(c.value))}else a!==void 0&&b.push($A(a));return b};W.da=function(a,b){b=b.Sa;return b===!1?void 0:typeof b==="string"?b:typeof a==="string"?a.toLowerCase():void 0};_.l=W.prototype;
_.l.ni=function(){var a=this;this.Be=new Promise(function(c){return a.mg=c});this.Ec=new Map;this.pi();AA(this);var b;(b=this.constructor.Gc)==null||b.forEach(function(c){return c(a)})};_.l.pi=function(){for(var a=new Map,b=_.z(this.constructor.hb.keys()),c=b.next();!c.done;c=b.next())c=c.value,this.hasOwnProperty(c)&&(a.set(c,this[c]),delete this[c]);a.size>0&&(this.pd=a)};_.l.xb=function(){var a,b=(a=this.shadowRoot)!=null?a:this.attachShadow(this.constructor.U);ZA(b,this.constructor.fa);return b};
_.l.connectedCallback=function(){this.ac!=null||(this.ac=this.xb());this.mg(!0);var a;(a=this.Fc)==null||a.forEach(function(b){var c;return(c=b.bf)==null?void 0:c.call(b)})};_.l.mg=function(){};_.l.disconnectedCallback=function(){var a;(a=this.Fc)==null||a.forEach(function(b){var c;return(c=b.Gj)==null?void 0:c.call(b)})};_.l.attributeChangedCallback=function(a,b,c){this.ii(a,c)};
_.l.oi=function(a,b){var c=this.constructor.hb.get(a),d=this.constructor.da(a,c);if(d!==void 0&&c.Ha===!0){var e,f=(((e=c.Ob)==null?void 0:e.Ah)!==void 0?c.Ob:nB).Ah(b,c.type);this.ec=a;f==null?this.removeAttribute(d):this.setAttribute(d,f);this.ec=null}};
_.l.ii=function(a,b){var c=this.constructor;a=c.nd.get(a);if(a!==void 0&&this.ec!==a){c=c.ja(a);var d,e=typeof c.Ob==="function"?{Re:c.Ob}:((d=c.Ob)==null?void 0:d.Re)!==void 0?c.Ob:nB;this.ec=a;b=e.Re(b,c.type);var f,g;this[a]=(g=b!=null?b:(f=this.od)==null?void 0:f.get(a))!=null?g:b;this.ec=null}};
var AA=function(a,b,c,d){if(b!==void 0){var e=a.constructor,f=a[b];d!=null||(d=e.ja(b));var g,h;if(((h=d.Gg)!=null?h:oB)(f,c)||d.Fh&&d.Ha&&f===((g=a.od)==null?void 0:g.get(b))&&!a.hasAttribute(e.da(b,d)))a.ye(b,c,d);else return}a.Uc===!1&&(a.Be=a.mi())};
W.prototype.ye=function(a,b,c,d){var e=c.Fh;var f=c.Ha;c=c.If;var g;if(e&&!((g=this.od)!=null?g:this.od=new Map).has(a)){var h;this.od.set(a,(h=d!=null?d:b)!=null?h:this[a]);if(c!==!0||d!==void 0)return}this.Ec.has(a)||(this.Rb||e||(b=void 0),this.Ec.set(a,b));if(f===!0&&this.ec!==a){var k;((k=this.qd)!=null?k:this.qd=new Set).add(a)}};
W.prototype.mi=function(){var a=this,b,c;return _.Fi(function(d){switch(d.g){case 1:return a.Uc=!0,_.xi(d,2),_.M(d,a.Be,4);case 4:d.g=3;d.l=0;break;case 2:b=_.yi(d),a.Kn||Promise.reject(b);case 3:c=tB(a);if(c==null){d.Ka(5);break}return _.M(d,c,5);case 5:return d.return(!a.Uc)}})};
var tB=function(a){if(a.Uc){if(!a.Rb){a.ac!=null||(a.ac=a.xb());if(a.pd){for(var b=_.z(a.pd),c=b.next();!c.done;c=b.next()){var d=_.z(c.value);c=d.next().value;d=d.next().value;a[c]=d}a.pd=void 0}b=a.constructor.hb;if(b.size>0)for(b=_.z(b),c=b.next();!c.done;c=b.next()){d=_.z(c.value);c=d.next().value;d=d.next().value;var e=a[c];d.If!==!0||a.Ec.has(c)||e===void 0||a.ye(c,void 0,d,e)}}b=!1;c=a.Ec;try{b=!0;var f;(f=a.Fc)==null||f.forEach(function(g){var h;return(h=g.vn)==null?void 0:h.call(g)});a.update(c)}catch(g){throw b=
!1,a.Yf(),g;}b&&a.ze(c)}};W.prototype.ze=function(){var a;(a=this.Fc)==null||a.forEach(function(b){var c;return(c=b.wn)==null?void 0:c.call(b)});this.Rb||(this.Rb=!0,this.rg())};W.prototype.Yf=function(){this.Ec=new Map;this.Uc=!1};W.prototype.update=function(){var a=this;this.qd&&(this.qd=this.qd.forEach(function(b){return a.oi(b,a[b])}));this.Yf()};W.prototype.rg=function(){};_.yb.Object.defineProperties(W.prototype,{Eh:{configurable:!0,enumerable:!0,get:function(){return this.Be}}});
_.yb.Object.defineProperties(W,{observedAttributes:{configurable:!0,enumerable:!0,get:function(){this.ia();return this.nd&&[].concat(_.Hb(this.nd.keys()))}}});W.fa=[];W.U={mode:"open"};W.hb=new Map;W.Qe=new Map;mB==null||mB({ReactiveElement:W});var sB=function(){var a;((a=_.t.reactiveElementVersions)!=null?a:_.t.reactiveElementVersions=[]).push("2.0.4");sB=function(){}};var uB={Sa:!0,type:String,Ob:nB,Ha:!1,Gg:oB},iA=function(a,b,c){a=a===void 0?uB:a;var d=c.kind,e=c.metadata,f=rB.get(e);f===void 0&&rB.set(e,f=new Map);d==="setter"&&(a=Object.create(a),a.If=!0);f.set(c.name,a);if(d==="accessor"){var g=c.name;return{set:function(k){var m=b.get.call(this);b.set.call(this,k);AA(this,g,m,a)},init:function(k){k!==void 0&&this.ye(g,void 0,a,k);return k}}}if(d==="setter"){var h=c.name;return function(k){var m=this[h];b.call(this,k);AA(this,h,m,a)}}throw Error("cb`"+d);
};var kA=function(a,b,c){c.configurable=!0;c.enumerable=!0;Reflect.Ta&&typeof b!=="object"&&Object.defineProperty(a,b,c);return c};var vB=new Set,wB=new Map,xB=function(a,b){if(globalThis.ShadyCSS!==void 0&&(!globalThis.ShadyCSS.nativeShadow||globalThis.ShadyCSS.ApplyShim)){var c,d,e=((c=globalThis.ShadyDOM)==null?0:c.inUse)&&((d=globalThis.ShadyDOM)==null?void 0:d.noPatch)===!0?globalThis.ShadyDOM.wrap:function(p){return p},f=function(p){var q=wB.get(p);q===void 0&&wB.set(p,q=[]);return q},g=new Map,h=a.createElement;a.createElement=function(p,q){p=h.call(a,p,q);q=q==null?void 0:q.scope;q!==void 0&&(globalThis.ShadyCSS.nativeShadow||
globalThis.ShadyCSS.prepareTemplateDom(p,q),q===void 0||vB.has(q)||(q=f(q),q.push.apply(q,_.Hb(Array.from(p.content.querySelectorAll("style")).map(function(r){var u;(u=r.parentNode)==null||u.removeChild(r);return r.textContent})))));return p};var k=document.createDocumentFragment(),m=document.createComment("");b=b.prototype;var n=b.ob;b.ob=function(p,q){q=q===void 0?this:q;var r=e(this.pb).parentNode,u,A=(u=this.options)==null?void 0:u.scope,C;if((r instanceof ShadowRoot||r===((C=this.options)==null?
void 0:C.En))&&A!==void 0&&!vB.has(A)){u=this.pb;C=this.dc;k.appendChild(m);this.pb=m;this.dc=null;n.call(this,p,q);p=(p==null?0:p._$litType$)?this.Z.Ae.lc:document.createElement("template");q=f(A);var K=q.length!==0;if(K){var U=document.createElement("style");U.textContent=q.join("\n");p.content.appendChild(U)}vB.add(A);wB.delete(A);globalThis.ShadyCSS.prepareTemplateStyles(p,A);K&&globalThis.ShadyCSS.nativeShadow&&(A=p.content.querySelector("style"),A!==null&&p.content.appendChild(A));k.removeChild(m);
var fa;if((fa=globalThis.ShadyCSS)==null?0:fa.nativeShadow)fa=p.content.querySelector("style"),fa!==null&&k.appendChild(fa.cloneNode(!0));r.insertBefore(k,C);this.pb=u;this.dc=C}else n.call(this,p,q)};b.Xf=function(p){var q,r=(q=this.options)==null?void 0:q.scope;q=g.get(r);q===void 0&&g.set(r,q=new Map);r=q.get(p.Na);r===void 0&&q.set(p.Na,r=new a(p,this.options));return r}}},yB;(yB=globalThis).litHtmlPolyfillSupport!=null||(yB.litHtmlPolyfillSupport=xB);var zB="";
if(globalThis.Symbol){var AB=Symbol();typeof AB!=="symbol"&&(zB=Object.keys(AB)[0])}var BB=zB!=="",CB=BB?function(a){return a!=null&&a[zB]!==void 0}:function(){return!1};if(BB&&!globalThis.Symbol.for){var DB=new Map;globalThis.Symbol.for=function(a){DB.has(a)||DB.set(a,Symbol(a));return DB.get(a)}};var EB=function(a){a=a.LitElement;if(globalThis.ShadyCSS!==void 0&&(!globalThis.ShadyCSS.nativeShadow||globalThis.ShadyCSS.ApplyShim)){a.ji=!0;a=a.prototype;var b=a.xb;a.xb=function(){this.he.scope=this.localName;return b.call(this)}}},FB;(FB=globalThis).litElementPolyfillSupport!=null||(FB.litElementPolyfillSupport=EB);var GB=_.L(["about:invalid#zClosurez"]),HB=pA,IB=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i,JB=_.Oa(_.Zh(GB)),nA=function(a){return a},KB=function(a){return IB.test(String(a))?a:JB},LB=function(){return JB},MB=function(a){return a instanceof _.Ma?_.Oa(a):JB},oA=new Map([["A href",KB],["AREA href",KB],["BASE href",LB],["BUTTON formaction",KB],["EMBED src",LB],["FORM action",KB],["FRAME src",LB],["IFRAME src",MB],["IFRAME srcdoc",function(a){return a instanceof _.nc?_.Vi(a):_.Vi(_.oc)}],
["INPUT formaction",KB],["LINK href",MB],["OBJECT codebase",LB],["OBJECT data",LB],["SCRIPT href",MB],["SCRIPT src",MB],["SCRIPT text",LB],["USE href",MB]]);var NB,OB,PB,QB=((NB=_.t.ShadyDOM)==null?0:NB.inUse)&&(((OB=_.t.ShadyDOM)==null?void 0:OB.noPatch)===!0||((PB=_.t.ShadyDOM)==null?void 0:PB.noPatch)==="on-demand")?_.t.ShadyDOM.wrap:function(a){return a},RB=_.t.trustedTypes,rA=RB?RB.createPolicy("lit-html",{createHTML:function(a){return a}}):void 0,SB=function(a){return a},TB=function(){return SB},UB="lit$"+Math.random().toFixed(9).slice(2)+"$",VB="?"+UB,WB="<"+VB+">",XB=document,uA=function(a){return a===null||typeof a!="object"&&typeof a!="function"||
CB(a)},qA=Array.isArray,YB=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,ZB=/--\x3e/g,$B=/>/g,aC=RegExp(">|[ \t\n\f\r](?:([^\\s\"'>=/]+)([ \t\n\f\r]*=[ \t\n\f\r]*(?:[^ \t\n\f\r\"'`<>=]|(\"|')|))|$)","g"),bC=/'/g,cC=/"/g,dC=/^(?:script|style|textarea|title)$/i,eC=function(a){var b=_.Kb.apply(1,arguments),c={};return c._$litType$=1,c.Na=a,c.values=b,c},tA=Symbol.for?Symbol.for("lit-noChange"):Symbol("eb"),X=Symbol.for?Symbol.for("lit-nothing"):Symbol("fb"),fC=new WeakMap,gC=XB.createTreeWalker(XB,
129),hC=function(a,b){var c=a.Na;var d=a._$litType$;this.V=[];for(var e=a=0,f=c.length-1,g=this.V,h=c.length-1,k=[],m=d===2?"<svg>":d===3?"<math>":"",n,p=YB,q=0;q<h;q++){for(var r=c[q],u=-1,A=void 0,C=0,K;C<r.length;){p.lastIndex=C;K=p.exec(r);if(K===null)break;C=p.lastIndex;p===YB?K[1]==="!--"?p=ZB:K[1]!==void 0?p=$B:K[2]!==void 0?(dC.test(K[2])&&(n=new RegExp("</"+K[2],"g")),p=aC):K[3]!==void 0&&(p=aC):p===aC?K[0]===">"?(u=void 0,p=(u=n)!=null?u:YB,u=-1):K[1]===void 0?u=-2:(u=p.lastIndex-K[2].length,
A=K[1],p=K[3]===void 0?aC:K[3]==='"'?cC:bC):p===cC||p===bC?p=aC:p===ZB||p===$B?p=YB:(p=aC,n=void 0)}C=p===aC&&c[q+1].startsWith("/>")?" ":"";m+=p===YB?r+WB:u>=0?(k.push(A),r.slice(0,u)+"$lit$"+r.slice(u))+UB+C:r+UB+(u===-2?q:C)}c=[sA(c,m+(c[h]||"<?>")+(d===2?"</svg>":d===3?"</math>":"")),k];c=_.z(c);n=c.next().value;c=c.next().value;this.lc=hC.createElement(n,b);gC.currentNode=this.lc.content;if(d===2||d===3)b=this.lc.content.firstChild,b.replaceWith.apply(b,_.Hb(b.childNodes));for(;(b=gC.nextNode())!==
null&&g.length<f;){if(b.nodeType===1){if(b.hasAttributes())for(d=_.z(b.getAttributeNames()),n=d.next();!n.done;n=d.next())n=n.value,n.endsWith("$lit$")?(k=c[e++],h=b.getAttribute(n).split(UB),k=/([.?@])?(.*)/.exec(k),g.push({type:1,index:a,name:k[2],Na:h,jc:k[1]==="."?iC:k[1]==="?"?jC:k[1]==="@"?kC:lC}),b.removeAttribute(n)):n.startsWith(UB)&&(g.push({type:6,index:a}),b.removeAttribute(n));if(dC.test(b.tagName)&&(d=b.textContent.split(UB),n=d.length-1,n>0)){b.textContent=RB?RB.emptyScript:"";for(h=
0;h<n;h++)b.append(d[h],XB.createComment("")),gC.nextNode(),g.push({type:2,index:++a});b.append(d[n],XB.createComment(""))}}else if(b.nodeType===8)if(b.data===VB)g.push({type:2,index:a});else for(d=-1;(d=b.data.indexOf(UB,d+1))!==-1;)g.push({type:7,index:a}),d+=UB.length-1;a++}};hC.createElement=function(a){var b=XB.createElement("template");b.innerHTML=a;return b};var mC=function(a,b){this.h=[];this.j=void 0;this.Ae=a;this.g=b};
mC.prototype.o=function(a){var b=this.Ae,c=b.lc.content;b=b.V;var d,e=((d=a==null?void 0:a.zm)!=null?d:XB).importNode(c,!0);gC.currentNode=e;c=gC.nextNode();for(var f=d=0,g=b[0];g!==void 0;){if(d===g.index){var h=void 0;g.type===2?h=new nC(c,c.nextSibling,this,a):g.type===1?h=new g.jc(c,g.name,g.Na,this,a):g.type===6&&(h=new oC(c,this,a));this.h.push(h);g=b[++f]}h=void 0;d!==((h=g)==null?void 0:h.index)&&(c=gC.nextNode(),d++)}gC.currentNode=XB;return e};
mC.prototype.l=function(a){for(var b=0,c=_.z(this.h),d=c.next();!d.done;d=c.next())d=d.value,d!==void 0&&(d.Na!==void 0?(d.ob(a,d,b),b+=d.Na.length-2):d.ob(a[b])),b++};_.yb.Object.defineProperties(mC.prototype,{parentNode:{configurable:!0,enumerable:!0,get:function(){return this.g.parentNode}},nb:{configurable:!0,enumerable:!0,get:function(){return this.g.nb}}});
var nC=function(a,b,c,d){this.type=2;this.Z=X;this.j=void 0;this.pb=a;this.dc=b;this.g=c;this.options=d;var e;this.l=(e=d==null?void 0:d.isConnected)!=null?e:!0;this.h=void 0};_.l=nC.prototype;_.l.ob=function(a,b){a=vA(this,a,b===void 0?this:b);uA(a)?a===X||a==null||a===""?(this.Z!==X&&this.md(),this.Z=X):a!==this.Z&&a!==tA&&this.Zf(a):a._$litType$!==void 0?this.ti(a):a.nodeType!==void 0?this.Ce(a):qA(a)||typeof(a==null?void 0:a[Symbol.iterator])==="function"?this.ri(a):this.Zf(a)};
_.l.Ee=function(a){return QB(QB(this.pb).parentNode).insertBefore(a,this.dc)};_.l.Ce=function(a){if(this.Z!==a){this.md();if(HB!==TB){var b,c=(b=this.pb.parentNode)==null?void 0:b.nodeName;if(c==="STYLE"||c==="SCRIPT")throw Error("hb");}this.Z=this.Ee(a)}};
_.l.Zf=function(a){if(this.Z!==X&&uA(this.Z)){var b=QB(this.pb).nextSibling;this.h===void 0&&(this.h=HB(b,"data","property"));a=this.h(a);b.data=a}else b=XB.createTextNode(""),this.Ce(b),this.h===void 0&&(this.h=HB(b,"data","property")),a=this.h(a),b.data=a;this.Z=a};
_.l.ti=function(a){var b=a.values,c=a._$litType$;a=typeof c==="number"?this.Xf(a):(c.lc===void 0&&(c.lc=hC.createElement(sA(c.Xi,c.Xi[0]),this.options)),c);var d;((d=this.Z)==null?void 0:d.Ae)===a?this.Z.l(b):(d=new mC(a,this),a=d.o(this.options),d.l(b),this.Ce(a),this.Z=d)};_.l.Xf=function(a){var b=fC.get(a.Na);b===void 0&&fC.set(a.Na,b=new hC(a));return b};
_.l.ri=function(a){qA(this.Z)||(this.Z=[],this.md());var b=this.Z,c=0,d;a=_.z(a);for(var e=a.next();!e.done;e=a.next())e=e.value,c===b.length?b.push(d=new nC(this.Ee(XB.createComment("")),this.Ee(XB.createComment("")),this,this.options)):d=b[c],d.ob(e),c++;c<b.length&&(this.md(d&&QB(d.dc).nextSibling,c),b.length=c)};_.l.md=function(a,b){a=a===void 0?QB(this.pb).nextSibling:a;var c;for((c=this.o)==null||c.call(this,!1,!0,b);a&&a!==this.dc;)b=QB(a).nextSibling,QB(a).remove(),a=b};
var pC=function(a,b){if(a.g===void 0){a.l=b;var c;(c=a.o)==null||c.call(a,b)}};_.yb.Object.defineProperties(nC.prototype,{nb:{configurable:!0,enumerable:!0,get:function(){var a,b;return(b=(a=this.g)==null?void 0:a.nb)!=null?b:this.l}},parentNode:{configurable:!0,enumerable:!0,get:function(){var a=QB(this.pb).parentNode,b=this.g,c;b!==void 0&&((c=a)==null?void 0:c.nodeType)===11&&(a=b.parentNode);return a}}});
var lC=function(a,b,c,d,e){this.type=1;this.Z=X;this.j=void 0;this.element=a;this.name=b;this.g=d;this.options=e;c.length>2||c[0]!==""||c[1]!==""?(this.Z=Array(c.length-1).fill(new String),this.Na=c):this.Z=X;this.fc=void 0};
lC.prototype.ob=function(a,b,c,d){b=b===void 0?this:b;var e=this.Na,f=!1;if(e===void 0){if(a=vA(this,a,b,0),f=!uA(a)||a!==this.Z&&a!==tA)this.Z=a}else{var g=a;a=e[0];var h;for(h=0;h<e.length-1;h++){var k=vA(this,g[c+h],b,h);k===tA&&(k=this.Z[h]);f||(f=!uA(k)||k!==this.Z[h]);if(k===X)a=X;else if(a!==X){var m=void 0;a+=((m=k)!=null?m:"")+e[h+1]}this.Z[h]=k}}f&&!d&&this.De(a)};
lC.prototype.De=function(a){if(a===X)QB(this.element).removeAttribute(this.name);else{this.fc===void 0&&(this.fc=HB(this.element,this.name,"attribute"));var b;a=this.fc((b=a)!=null?b:"");var c;QB(this.element).setAttribute(this.name,(c=a)!=null?c:"")}};_.yb.Object.defineProperties(lC.prototype,{tagName:{configurable:!0,enumerable:!0,get:function(){return this.element.tagName}},nb:{configurable:!0,enumerable:!0,get:function(){return this.g.nb}}});
var iC=function(){lC.apply(this,arguments);this.type=3};_.y(iC,lC);iC.prototype.De=function(a){this.fc===void 0&&(this.fc=HB(this.element,this.name,"property"));a=this.fc(a);this.element[this.name]=a===X?void 0:a};var jC=function(){lC.apply(this,arguments);this.type=4};_.y(jC,lC);jC.prototype.De=function(a){QB(this.element).toggleAttribute(this.name,!!a&&a!==X)};var kC=function(a,b,c,d,e){lC.call(this,a,b,c,d,e);this.type=5};_.y(kC,lC);
kC.prototype.ob=function(a,b){var c;a=(c=vA(this,a,b===void 0?this:b,0))!=null?c:X;if(a!==tA){b=this.Z;c=a===X&&b!==X||a.capture!==b.capture||a.once!==b.once||a.passive!==b.passive;var d=a!==X&&(b===X||c);c&&this.element.removeEventListener(this.name,this,b);d&&this.element.addEventListener(this.name,this,a);this.Z=a}};kC.prototype.handleEvent=function(a){if(typeof this.Z==="function"){var b,c;this.Z.call((c=(b=this.options)==null?void 0:b.host)!=null?c:this.element,a)}else this.Z.handleEvent(a)};
var oC=function(a,b,c){this.element=a;this.type=6;this.j=void 0;this.g=b;this.options=c};oC.prototype.ob=function(a){vA(this,a)};_.yb.Object.defineProperties(oC.prototype,{nb:{configurable:!0,enumerable:!0,get:function(){return this.g.nb}}});var qC;(qC=globalThis.litHtmlPolyfillSupport)==null||qC(hC,nC);var rC,sC;((sC=_.t.litHtmlVersions)!=null?sC:_.t.litHtmlVersions=[]).push("3.2.1");
rC=function(a,b,c){var d,e=(d=c==null?void 0:c.Xc)!=null?d:b;d=e._$litPart$;if(d===void 0){var f;d=(f=c==null?void 0:c.Xc)!=null?f:null;e._$litPart$=d=new nC(b.insertBefore(XB.createComment(""),d),d,void 0,c!=null?c:{})}d.ob(a);return d};var Y=function(){var a=W.apply(this,arguments)||this;a.he={host:a};a.g=void 0;return a};_.y(Y,W);Y.U=W.U;Y.fa=W.fa;Y.da=W.da;Y.ma=W.ma;Y.ea=W.ea;Y.ja=W.ja;Y.na=W.na;Y.ba=W.ba;Y.addInitializer=W.addInitializer;_.l=Y.prototype;_.l.xb=function(){var a=W.prototype.xb.call(this),b;(b=this.he).Xc!=null||(b.Xc=a.firstChild);return a};_.l.update=function(a){var b=this.ha();this.Rb||(this.he.isConnected=this.isConnected);W.prototype.update.call(this,a);this.g=rC(b,this.ac,this.he)};
_.l.connectedCallback=function(){W.prototype.connectedCallback.call(this);var a;(a=this.g)==null||pC(a,!0)};_.l.disconnectedCallback=function(){W.prototype.disconnectedCallback.call(this);var a;(a=this.g)==null||pC(a,!1)};_.l.ha=function(){return tA};Y.ia=function(){tC();return W.ia.call(this)};Y._$litElement$=!0;Y.Qe=!0;var uC;(uC=_.t.litElementPolyfillSupport)==null||uC({LitElement:Y});var tC=function(){var a;((a=_.t.litElementVersions)!=null?a:_.t.litElementVersions=[]).push("4.1.1");tC=function(){}};/*

 Copyright 2024 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var vC=_.L([":host{animation-delay:0s,calc(var(--md-focus-ring-duration, .6s)*.25);animation-duration:calc(var(--md-focus-ring-duration, .6s)*.25),calc(var(--md-focus-ring-duration, .6s)*.75);animation-timing-function:cubic-bezier(.2,0,0,1);box-sizing:border-box;color:var(--md-focus-ring-color,var(--md-sys-color-secondary,#625b71));display:none;pointer-events:none;position:absolute}:host([visible]){display:flex}:host(:not([inward])){animation-name:outward-grow,outward-shrink;border-end-end-radius:calc(var(--md-focus-ring-shape-end-end, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) + var(--md-focus-ring-outward-offset, 2px));border-end-start-radius:calc(var(--md-focus-ring-shape-end-start, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) + var(--md-focus-ring-outward-offset, 2px));border-start-end-radius:calc(var(--md-focus-ring-shape-start-end, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) + var(--md-focus-ring-outward-offset, 2px));border-start-start-radius:calc(var(--md-focus-ring-shape-start-start, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) + var(--md-focus-ring-outward-offset, 2px));inset:calc(var(--md-focus-ring-outward-offset, 2px)*-1);outline:var(--md-focus-ring-width,3px) solid currentColor}:host([inward]){animation-name:inward-grow,inward-shrink;border-end-end-radius:calc(var(--md-focus-ring-shape-end-end, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) - var(--md-focus-ring-inward-offset, 0px));border-end-start-radius:calc(var(--md-focus-ring-shape-end-start, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) - var(--md-focus-ring-inward-offset, 0px));border-start-end-radius:calc(var(--md-focus-ring-shape-start-end, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) - var(--md-focus-ring-inward-offset, 0px));border-start-start-radius:calc(var(--md-focus-ring-shape-start-start, var(--md-focus-ring-shape, var(--md-sys-shape-corner-full, 9999px))) - var(--md-focus-ring-inward-offset, 0px));border:var(--md-focus-ring-width,3px) solid currentColor;inset:var(--md-focus-ring-inward-offset,0)}@keyframes outward-grow{0%{outline-width:0}to{outline-width:var(--md-focus-ring-active-width,8px)}}@keyframes outward-shrink{0%{outline-width:var(--md-focus-ring-active-width,8px)}}@keyframes inward-grow{0%{border-width:0}to{border-width:var(--md-focus-ring-active-width,8px)}}@keyframes inward-shrink{0%{border-width:var(--md-focus-ring-active-width,8px)}}@media (prefers-reduced-motion){:host{animation:none}}\n"]),
wC=YA(vC);var xC=Symbol("ib"),yC;yC=new MutationObserver(function(a){a=_.z(a);for(var b=a.next();!b.done;b=a.next()){var c=void 0;(c=b.value.target[xC])==null||c.bf()}});var zC=function(a,b){this.host=a;this.Vc=b;this.Ad=null;var c;((c=a.Fc)!=null?c:a.Fc=new Set).add(this);if(a.ac!==void 0&&a.isConnected){var d;(d=this.bf)==null||d.call(this)}a[xC]=this;yC==null||yC.observe(a,{attributeFilter:["for"]})};zC.prototype.attach=function(a){a!==this.Ad&&(AC(this,a),this.host.removeAttribute("for"))};
zC.prototype.detach=function(){AC(this,null);this.host.setAttribute("for","")};zC.prototype.bf=function(){AC(this,this.control)};zC.prototype.Gj=function(){AC(this,null)};var AC=function(a,b){a.Vc(a.Ad,b);a.Ad=b};
_.yb.Object.defineProperties(zC.prototype,{htmlFor:{configurable:!0,enumerable:!0,get:function(){return this.host.getAttribute("for")},set:function(a){a===null?this.host.removeAttribute("for"):this.host.setAttribute("for",a)}},control:{configurable:!0,enumerable:!0,get:function(){return this.host.hasAttribute("for")?this.htmlFor&&this.host.isConnected?this.host.getRootNode().querySelector("#"+this.htmlFor):null:this.Ad||this.host.parentElement},set:function(a){a?this.attach(a):this.detach()}}});/*

 Copyright 2021 Google LLC
 SPDX-License-Identifier: BSD-3-Clause
*/
var BC=["focusin","focusout","pointerdown"],CC=function(){var a=Y.apply(this,arguments)||this;a.visible=!1;a.inward=!1;a.Ra=new zC(a,a.Vc.bind(a));return a};_.y(CC,Y);CC.U=Y.U;CC.fa=Y.fa;CC.da=Y.da;CC.ma=Y.ma;CC.ea=Y.ea;CC.ja=Y.ja;CC.na=Y.na;CC.ba=Y.ba;CC.addInitializer=Y.addInitializer;CC.ia=Y.ia;_.l=CC.prototype;_.l.attach=function(a){this.Ra.attach(a)};_.l.detach=function(){this.Ra.detach()};
_.l.connectedCallback=function(){Y.prototype.connectedCallback.call(this);this.setAttribute("aria-hidden","true")};_.l.handleEvent=function(a){if(!a[DC]){switch(a.type){default:return;case "focusin":var b,c;this.visible=(c=(b=this.control)==null?void 0:b.matches(":focus-visible"))!=null?c:!1;break;case "focusout":case "pointerdown":this.visible=!1}a[DC]=!0}};
_.l.Vc=function(a,b){for(var c=_.z(BC),d=c.next();!d.done;d=c.next()){d=d.value;var e=void 0;(e=a)==null||e.removeEventListener(d,this);e=void 0;(e=b)==null||e.addEventListener(d,this)}};_.l.update=function(a){a.has("visible")&&this.dispatchEvent(new Event("visibility-changed"));Y.prototype.update.call(this,a)};
_.yb.Object.defineProperties(CC.prototype,{htmlFor:{configurable:!0,enumerable:!0,get:function(){return this.Ra.htmlFor},set:function(a){this.Ra.htmlFor=a}},control:{configurable:!0,enumerable:!0,get:function(){return this.Ra.control},set:function(a){this.Ra.control=a}}});T([jA({type:Boolean,Ha:!0}),V("design:type",Object)],CC.prototype,"visible",void 0);T([jA({type:Boolean,Ha:!0}),V("design:type",Object)],CC.prototype,"inward",void 0);var DC=Symbol("jb");var EC=function(){return CC.apply(this,arguments)||this};_.y(EC,CC);EC.U=CC.U;EC.fa=CC.fa;EC.da=CC.da;EC.ma=CC.ma;EC.ea=CC.ea;EC.ja=CC.ja;EC.na=CC.na;EC.ba=CC.ba;EC.addInitializer=CC.addInitializer;EC.ia=CC.ia;EC.Gb=[wC];T([PA("md-focus-ring")],EC);var DA="role ariaAtomic ariaAutoComplete ariaBusy ariaChecked ariaColCount ariaColIndex ariaColSpan ariaCurrent ariaDisabled ariaExpanded ariaHasPopup ariaHidden ariaInvalid ariaKeyShortcuts ariaLabel ariaLevel ariaLive ariaModal ariaMultiLine ariaMultiSelectable ariaOrientation ariaPlaceholder ariaPosInSet ariaPressed ariaReadOnly ariaRequired ariaRoleDescription ariaRowCount ariaRowIndex ariaRowSpan ariaSelected ariaSetSize ariaSort ariaValueMax ariaValueMin ariaValueNow ariaValueText".split(" "),
xA=DA.map(wA);var yA=Symbol("kb");var FC=Symbol("lb"),GC=Symbol("mb");var EA=!1;var HC=_.L([':host {\n  display: flex;\n  margin: auto;\n  pointer-events: none;\n}\n\n:host([disabled]) {\n  display: none;\n}\n\n@media (forced-colors: active) {\n  :host {\n    display: none;\n  }\n}\n:host,\n.surface {\n  border-radius: inherit;\n  position: absolute;\n  inset: 0;\n  overflow: hidden;\n}\n\n.surface {\n  -webkit-tap-highlight-color: transparent;\n}\n.surface::before, .surface::after {\n  content: "";\n  opacity: 0;\n  position: absolute;\n}\n.surface::before {\n  background-color: var(--md-ripple-hover-color, var(--md-sys-color-on-surface, #1d1b20));\n  inset: 0;\n  transition: opacity 15ms linear, background-color 15ms linear;\n}\n.surface::after {\n  background: radial-gradient(closest-side, var(--md-ripple-pressed-color, var(--md-sys-color-on-surface, #1d1b20)) max(100% - 70px, 65%), transparent 100%);\n  transform-origin: center center;\n  transition: opacity 375ms linear;\n}\n\n.hovered::before {\n  background-color: var(--md-ripple-hover-color, var(--md-sys-color-on-surface, #1d1b20));\n  opacity: var(--md-ripple-hover-opacity, 0.08);\n}\n\n.pressed::after {\n  opacity: var(--md-ripple-pressed-opacity, 0.12);\n  transition-duration: 105ms;\n}\n\n']),
IC=YA(HC);var JC=function(){};JC.prototype.ki=function(a,b,c){this.o=a;this.g=b;this.l=c};JC.prototype.li=function(a,b){return this.update(a,b)};JC.prototype.update=function(a,b){return this.ha.apply(this,_.Hb(b))};_.yb.Object.defineProperties(JC.prototype,{nb:{configurable:!0,enumerable:!0,get:function(){return this.g.nb}}});/*

 Copyright 2018 Google LLC
 SPDX-License-Identifier: BSD-3-Clause
*/
var KC=function(a){var b;if(a.type!==1||a.name!=="class"||((b=a.Na)==null?void 0:b.length)>2)throw Error("nb");};_.y(KC,JC);KC.prototype.ha=function(a){return" "+Object.keys(a).filter(function(b){return a[b]}).join(" ")+" "};
KC.prototype.update=function(a,b){b=_.z(b).next().value;if(this.h===void 0){this.h=new Set;a.Na!==void 0&&(this.j=new Set(a.Na.join(" ").split(/\s/).filter(function(f){return f!==""})));for(var c in b){var d=void 0;!b[c]||((d=this.j)==null?0:d.has(c))||this.h.add(c)}return this.ha(b)}c=a.element.classList;a=_.z(this.h);for(var e=a.next();!e.done;e=a.next())e=e.value,e in b||(c.remove(e),this.h.delete(e));for(d in b)a=!!b[d],e=void 0,a===this.h.has(d)||(e=this.j)!=null&&e.has(d)||(a?(c.add(d),this.h.add(d)):
(c.remove(d),this.h.delete(d)));return tA};var LC=function(a){return function(){var b=_.Kb.apply(0,arguments),c={};return c._$litDirective$=a,c.values=b,c}}(KC);/*

 Copyright 2022 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var MC=_.L(['<div class="surface ','"></div>']),NC="click contextmenu pointercancel pointerdown pointerenter pointerleave pointerup".split(" "),OC=window.matchMedia("(forced-colors: active)"),Z=function(){var a=Y.apply(this,arguments)||this;a.disabled=!1;a.Sc=!1;a.pressed=!1;a.Yc="";a.oh="";a.Tc=0;a.state=0;a.Ra=new zC(a,a.Vc.bind(a));return a};_.y(Z,Y);Z.U=Y.U;Z.fa=Y.fa;Z.da=Y.da;Z.ma=Y.ma;Z.ea=Y.ea;Z.ja=Y.ja;Z.na=Y.na;Z.ba=Y.ba;Z.addInitializer=Y.addInitializer;Z.ia=Y.ia;_.l=Z.prototype;
_.l.attach=function(a){this.Ra.attach(a)};_.l.detach=function(){this.Ra.detach()};_.l.connectedCallback=function(){Y.prototype.connectedCallback.call(this);this.setAttribute("aria-hidden","true")};_.l.ha=function(){return eC(MC,LC({hovered:this.Sc,pressed:this.pressed}))};_.l.update=function(a){a.has("disabled")&&this.disabled&&(this.pressed=this.Sc=!1);Y.prototype.update.call(this,a)};
var RC=function(a,b){return _.Fi(function(c){if(c.g==1){if(!PC(a,b))return c.return();a.ie=b;if(b.pointerType!=="touch")return a.state=3,QC(a,b),c.return();a.state=1;return _.M(c,new Promise(function(d){setTimeout(d,150)}),2)}if(a.state!==1)return c.return();a.state=2;QC(a,b);c.g=0})};Z.prototype.We=function(){this.disabled||(this.state===3?SC(this):this.state===0&&(QC(this),SC(this)))};
var QC=function(a,b){var c;if(a.Yg){a.pressed=!0;var d;(d=a.Ue)==null||d.cancel();var e=a.getBoundingClientRect();d=e.height;var f=e.width,g=Math.max(d,f);e=Math.max(.35*g,75);var h,k=(h=a.currentCSSZoom)!=null?h:1;h=Math.floor(g*.2/k);d=Math.sqrt(Math.pow(f,2)+Math.pow(d,2))+10;a.Tc=h;a.oh=""+(d+e)/h/k;a.Yc=h+"px";d=a.getBoundingClientRect();e=d.height;h=d.width;var m;k=(m=a.currentCSSZoom)!=null?m:1;m=(h/k-a.Tc)/2;d=(e/k-a.Tc)/2;b instanceof PointerEvent?(h=window,e=h.scrollX,h=h.scrollY,k=a.getBoundingClientRect(),
e+=k.left,h+=k.top,k=b.pageX,b=b.pageY,f=(c=a.currentCSSZoom)!=null?c:1,c={x:(k-e)/f,y:(b-h)/f}):c={x:h/k/2,y:e/k/2};c={x:c.x-a.Tc/2,y:c.y-a.Tc/2};a.Ue=a.Yg.animate({top:[0,0],left:[0,0],height:[a.Yc,a.Yc],width:[a.Yc,a.Yc],transform:["translate("+(c.x+"px, "+c.y+"px) scale(1)"),"translate("+(m+"px, "+d+"px) scale(")+a.oh+")"]},{pseudoElement:"::after",duration:450,easing:"cubic-bezier(0.2, 0, 0, 1)",fill:"forwards"})}},SC=function(a){var b,c,d,e;_.Fi(function(f){if(f.g==1){a.ie=void 0;a.state=0;
b=a.Ue;c=Infinity;if(typeof((d=b)==null?void 0:d.currentTime)==="number")c=b.currentTime;else if((e=b)==null?0:e.currentTime)c=b.currentTime.to("ms").value;return c>=225?(a.pressed=!1,f.return()):_.M(f,new Promise(function(g){setTimeout(g,225-c)}),2)}if(a.Ue!==b)return f.return();a.pressed=!1;f.g=0})},PC=function(a,b){if(a.disabled||!b.isPrimary||a.ie&&a.ie.pointerId!==b.pointerId)return!1;if(b.type==="pointerenter"||b.type==="pointerleave")return b.pointerType!=="touch";a=b.buttons===1;return b.pointerType===
"touch"||a};
Z.prototype.handleEvent=function(a){var b=this,c;return _.Fi(function(d){if(d.g==1){if((c=OC)==null?0:c.matches)return d.return();switch(a.type){case "click":b.We();break;case "contextmenu":b.disabled||SC(b);break;case "pointercancel":PC(b,a)&&SC(b);break;case "pointerdown":return d.Ka(2);case "pointerenter":PC(b,a)&&(b.Sc=!0);break;case "pointerleave":PC(b,a)&&(b.Sc=!1,b.state!==0&&SC(b));break;case "pointerup":PC(b,a)&&(b.state===2?b.state=3:b.state===1&&(b.state=3,QC(b,b.ie)))}return d.Ka(0)}return d.g!=4?
_.M(d,RC(b,a),4):d.Ka(0)})};Z.prototype.Vc=function(a,b){for(var c=_.z(NC),d=c.next();!d.done;d=c.next()){d=d.value;var e=void 0;(e=a)==null||e.removeEventListener(d,this);e=void 0;(e=b)==null||e.addEventListener(d,this)}};_.yb.Object.defineProperties(Z.prototype,{htmlFor:{configurable:!0,enumerable:!0,get:function(){return this.Ra.htmlFor},set:function(a){this.Ra.htmlFor=a}},control:{configurable:!0,enumerable:!0,get:function(){return this.Ra.control},set:function(a){this.Ra.control=a}}});
T([jA({type:Boolean,Ha:!0}),V("design:type",Object)],Z.prototype,"disabled",void 0);T([mA(),V("design:type",Object)],Z.prototype,"Sc",void 0);T([mA(),V("design:type",Object)],Z.prototype,"pressed",void 0);T([lA(".surface"),V("design:type",Object)],Z.prototype,"Yg",void 0);var TC=function(){return Z.apply(this,arguments)||this};_.y(TC,Z);TC.U=Z.U;TC.fa=Z.fa;TC.da=Z.da;TC.ma=Z.ma;TC.ea=Z.ea;TC.ja=Z.ja;TC.na=Z.na;TC.ba=Z.ba;TC.addInitializer=Z.addInitializer;TC.ia=Z.ia;TC.Gb=[IC];T([PA("md-ripple")],TC);/*

 Copyright 2019 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var UC=_.L('\n      ;\n      <div class="background"></div>\n      <md-focus-ring part="focus-ring" for=;></md-focus-ring>\n      <md-ripple\n        part="ripple"\n        for=;\n        ?disabled=";"></md-ripple>\n      ;\n    '.split(";")),VC=_.L('<button\n      id="button"\n      class="button"\n      ?disabled=;\n      aria-disabled=;\n      aria-label=";"\n      aria-haspopup=";"\n      aria-expanded=";">\n      ;\n    </button>'.split(";")),WC=_.L('<a\n      id="link"\n      class="button"\n      aria-label=";"\n      aria-haspopup=";"\n      aria-expanded=";"\n      aria-disabled=;\n      tabindex=";"\n      href=;\n      download=;\n      target=;\n      >;\n    </a>'.split(";")),
XC=_.L(['<slot\n      name="icon"\n      @slotchange="','"></slot>']),YC=_.L(['\n      <span class="touch"></span>\n      ','\n      <span class="label"><slot></slot></span>\n      ',"\n    "]),ZC=CA(function(a){var b=function(){return a.apply(this,arguments)||this};_.y(b,a);_.yb.Object.defineProperty(b.prototype,FC,{configurable:!0,enumerable:!0,get:function(){this[GC]||(this[GC]=this.attachInternals());return this[GC]}});return b}(Y)),$C=function(){var a=ZC.call(this)||this;a.disabled=!1;a.softDisabled=
!1;a.href="";a.download="";a.target="";a.trailingIcon=!1;a.hasIcon=!1;a.type="submit";a.value="";a.addEventListener("click",a.We.bind(a));return a};_.y($C,ZC);$C.prototype.focus=function(){var a;(a=this.vd)==null||a.focus()};$C.prototype.blur=function(){var a;(a=this.vd)==null||a.blur()};
$C.prototype.ha=function(){var a=this.disabled||this.softDisabled,b=this.href?eC(WC,this.ariaLabel||X,this.ariaHasPopup||X,this.ariaExpanded||X,this.disabled||this.softDisabled||X,this.disabled&&!this.softDisabled?-1:X,this.href,this.download||X,this.target||X,aD(this)):eC(VC,this.disabled,this.softDisabled||X,this.ariaLabel||X,this.ariaHasPopup||X,this.ariaExpanded||X,aD(this)),c=this.href?"link":"button",d;return eC(UC,(d=this.nk)==null?void 0:d.call(this),c,c,a,b)};
var aD=function(a){var b=eC(XC,a.yj);return eC(YC,a.trailingIcon?X:b,a.trailingIcon?b:X)};$C.prototype.We=function(a){if(this.softDisabled||this.disabled&&this.href)a.stopImmediatePropagation(),a.preventDefault();else{if(a.currentTarget!==a.target||a.composedPath()[0]!==a.target||a.target.disabled)a=!1;else{var b=EA;b&&(a.preventDefault(),a.stopImmediatePropagation());FA();a=!b}a&&this.vd&&(this.focus(),a=this.vd,b=new MouseEvent("click",{bubbles:!0}),a.dispatchEvent(b))}};
$C.prototype.yj=function(){this.hasIcon=this.yi.length>0};_.yb.Object.defineProperties($C.prototype,{name:{configurable:!0,enumerable:!0,get:function(){var a;return(a=this.getAttribute("name"))!=null?a:""},set:function(a){this.setAttribute("name",a)}},form:{configurable:!0,enumerable:!0,get:function(){return this[FC].form}}});
$C.addInitializer(function(a){a.addEventListener("click",function(b){var c,d,e,f,g;return _.Fi(function(h){if(h.g==1)return c=a,d=c.type,f=e=c[FC],(g=f.form)&&d!=="button"?_.M(h,new Promise(function(k){setTimeout(k)}),2):h.return();if(b.defaultPrevented)return h.return();if(d==="reset")return g.reset(),h.return();g.addEventListener("submit",function(k){Object.defineProperty(k,"submitter",{configurable:!0,enumerable:!0,get:function(){return a}})},{capture:!0,once:!0});e.setFormValue(a.value);g.requestSubmit();
h.g=0})})});$C.formAssociated=!0;$C.U={mode:"open",delegatesFocus:!0};T([jA({type:Boolean,Ha:!0}),V("design:type",Object)],$C.prototype,"disabled",void 0);T([jA({type:Boolean,Sa:"soft-disabled",Ha:!0}),V("design:type",Object)],$C.prototype,"softDisabled",void 0);T([jA(),V("design:type",Object)],$C.prototype,"href",void 0);T([jA(),V("design:type",Object)],$C.prototype,"download",void 0);T([jA(),V("design:type",String)],$C.prototype,"target",void 0);
T([jA({type:Boolean,Sa:"trailing-icon",Ha:!0}),V("design:type",Object)],$C.prototype,"trailingIcon",void 0);T([jA({type:Boolean,Sa:"has-icon",Ha:!0}),V("design:type",Object)],$C.prototype,"hasIcon",void 0);T([jA(),V("design:type",String)],$C.prototype,"type",void 0);T([jA({Ha:!0}),V("design:type",Object)],$C.prototype,"value",void 0);T([lA(".button"),V("design:type",Object)],$C.prototype,"vd",void 0);
T([function(a){return function(b,c){var d=a!=null?a:{},e=d.slot,f=d.Jn,g="slot"+(e?"[name="+e+"]":":not([name])");return kA(b,c,{get:function(){var h,k,m,n=(m=(k=(h=this.ac)==null?void 0:h.querySelector(g))==null?void 0:k.assignedElements(a))!=null?m:[];return f===void 0?n:n.filter(function(p){return p.matches(f)})}})}}({slot:"icon",flatten:!0}),V("design:type",Array)],$C.prototype,"yi",void 0);var bD=_.L(['.shadow,.shadow:after,.shadow:before,:host{border-radius:inherit;inset:0;position:absolute;transition-duration:inherit;transition-property:inherit;transition-timing-function:inherit}:host{display:flex;pointer-events:none;transition-property:box-shadow,opacity}.shadow:after,.shadow:before{content:"";transition-property:box-shadow,opacity;--_level:var(--md-elevation-level,0);--_shadow-color:var(--md-elevation-shadow-color,var(--md-sys-color-shadow,#000))}.shadow:before{box-shadow:0 calc(1px*(clamp(0, var(--_level), 1) + clamp(0, var(--_level) - 3, 1) + clamp(0, var(--_level) - 4, 1)*2)) calc(1px*(2*clamp(0, var(--_level), 1) + clamp(0, var(--_level) - 2, 1) + clamp(0, var(--_level) - 4, 1))) 0 var(--_shadow-color);opacity:.3}.shadow:after{box-shadow:0 calc(1px*(clamp(0, var(--_level), 1) + clamp(0, var(--_level) - 1, 1) + clamp(0, var(--_level) - 2, 3)*2)) calc(1px*(clamp(0, var(--_level), 2)*3 + clamp(0, var(--_level) - 2, 3)*2)) calc(1px*(clamp(0, var(--_level), 4) + 2*clamp(0, var(--_level) - 4, 1))) var(--_shadow-color);opacity:.15}\n']),
cD=YA(bD);var dD=_.L(['<span class="shadow"></span>']),eD=function(){return Y.apply(this,arguments)||this};_.y(eD,Y);eD.U=Y.U;eD.fa=Y.fa;eD.da=Y.da;eD.ma=Y.ma;eD.ea=Y.ea;eD.ja=Y.ja;eD.na=Y.na;eD.ba=Y.ba;eD.addInitializer=Y.addInitializer;eD.ia=Y.ia;eD.prototype.connectedCallback=function(){Y.prototype.connectedCallback.call(this);this.setAttribute("aria-hidden","true")};eD.prototype.ha=function(){return eC(dD)};var fD=function(){return eD.apply(this,arguments)||this};_.y(fD,eD);fD.U=eD.U;fD.fa=eD.fa;fD.da=eD.da;fD.ma=eD.ma;fD.ea=eD.ea;fD.ja=eD.ja;fD.na=eD.na;fD.ba=eD.ba;fD.addInitializer=eD.addInitializer;fD.ia=eD.ia;fD.Gb=[cD];T([PA("md-elevation")],fD);var gD=_.L(['<md-elevation part="elevation"></md-elevation>']),hD=function(){return $C.apply(this,arguments)||this};_.y(hD,$C);hD.U=$C.U;hD.formAssociated=$C.formAssociated;hD.prototype.nk=function(){return eC(gD)};var iD=_.L([":host{--_container-color:var(--md-filled-button-container-color,var(--md-sys-color-primary,#6750a4));--_container-elevation:var(--md-filled-button-container-elevation,0);--_container-height:var(--md-filled-button-container-height,40px);--_container-shadow-color:var(--md-filled-button-container-shadow-color,var(--md-sys-color-shadow,#000));--_disabled-container-color:var(--md-filled-button-disabled-container-color,var(--md-sys-color-on-surface,#1d1b20));--_disabled-container-elevation:var(--md-filled-button-disabled-container-elevation,0);--_disabled-container-opacity:var(--md-filled-button-disabled-container-opacity,0.12);--_disabled-label-text-color:var(--md-filled-button-disabled-label-text-color,var(--md-sys-color-on-surface,#1d1b20));--_disabled-label-text-opacity:var(--md-filled-button-disabled-label-text-opacity,0.38);--_focus-container-elevation:var(--md-filled-button-focus-container-elevation,0);--_focus-label-text-color:var(--md-filled-button-focus-label-text-color,var(--md-sys-color-on-primary,#fff));--_hover-container-elevation:var(--md-filled-button-hover-container-elevation,1);--_hover-label-text-color:var(--md-filled-button-hover-label-text-color,var(--md-sys-color-on-primary,#fff));--_hover-state-layer-color:var(--md-filled-button-hover-state-layer-color,var(--md-sys-color-on-primary,#fff));--_hover-state-layer-opacity:var(--md-filled-button-hover-state-layer-opacity,0.08);--_label-text-color:var(--md-filled-button-label-text-color,var(--md-sys-color-on-primary,#fff));--_label-text-font:var(--md-filled-button-label-text-font,var(--md-sys-typescale-label-large-font,var(--md-ref-typeface-plain,Roboto)));--_label-text-line-height:var(--md-filled-button-label-text-line-height,var(--md-sys-typescale-label-large-line-height,1.25rem));--_label-text-size:var(--md-filled-button-label-text-size,var(--md-sys-typescale-label-large-size,0.875rem));--_label-text-weight:var(--md-filled-button-label-text-weight,var(--md-sys-typescale-label-large-weight,var(--md-ref-typeface-weight-medium,500)));--_pressed-container-elevation:var(--md-filled-button-pressed-container-elevation,0);--_pressed-label-text-color:var(--md-filled-button-pressed-label-text-color,var(--md-sys-color-on-primary,#fff));--_pressed-state-layer-color:var(--md-filled-button-pressed-state-layer-color,var(--md-sys-color-on-primary,#fff));--_pressed-state-layer-opacity:var(--md-filled-button-pressed-state-layer-opacity,0.12);--_disabled-icon-color:var(--md-filled-button-disabled-icon-color,var(--md-sys-color-on-surface,#1d1b20));--_disabled-icon-opacity:var(--md-filled-button-disabled-icon-opacity,0.38);--_focus-icon-color:var(--md-filled-button-focus-icon-color,var(--md-sys-color-on-primary,#fff));--_hover-icon-color:var(--md-filled-button-hover-icon-color,var(--md-sys-color-on-primary,#fff));--_icon-color:var(--md-filled-button-icon-color,var(--md-sys-color-on-primary,#fff));--_icon-size:var(--md-filled-button-icon-size,18px);--_pressed-icon-color:var(--md-filled-button-pressed-icon-color,var(--md-sys-color-on-primary,#fff));--_container-shape-start-start:var(--md-filled-button-container-shape-start-start,var(--md-filled-button-container-shape,var(--md-sys-shape-corner-full,9999px)));--_container-shape-start-end:var(--md-filled-button-container-shape-start-end,var(--md-filled-button-container-shape,var(--md-sys-shape-corner-full,9999px)));--_container-shape-end-end:var(--md-filled-button-container-shape-end-end,var(--md-filled-button-container-shape,var(--md-sys-shape-corner-full,9999px)));--_container-shape-end-start:var(--md-filled-button-container-shape-end-start,var(--md-filled-button-container-shape,var(--md-sys-shape-corner-full,9999px)));--_leading-space:var(--md-filled-button-leading-space,24px);--_trailing-space:var(--md-filled-button-trailing-space,24px);--_with-leading-icon-leading-space:var(--md-filled-button-with-leading-icon-leading-space,16px);--_with-leading-icon-trailing-space:var(--md-filled-button-with-leading-icon-trailing-space,24px);--_with-trailing-icon-leading-space:var(--md-filled-button-with-trailing-icon-leading-space,24px);--_with-trailing-icon-trailing-space:var(--md-filled-button-with-trailing-icon-trailing-space,16px)}\n"]),
jD=YA(iD);var kD=_.L(["md-elevation{transition-duration:.28s}:host(:is([disabled],[soft-disabled])) md-elevation{transition:none}md-elevation{--md-elevation-level:var(--_container-elevation);--md-elevation-shadow-color:var(--_container-shadow-color)}:host(:focus-within) md-elevation{--md-elevation-level:var(--_focus-container-elevation)}:host(:hover) md-elevation{--md-elevation-level:var(--_hover-container-elevation)}:host(:active) md-elevation{--md-elevation-level:var(--_pressed-container-elevation)}:host(:is([disabled],[soft-disabled])) md-elevation{--md-elevation-level:var(--_disabled-container-elevation)}\n"]),
lD=YA(kD);var mD=_.L([":host{border-start-start-radius:var(--_container-shape-start-start);border-start-end-radius:var(--_container-shape-start-end);border-end-start-radius:var(--_container-shape-end-start);border-end-end-radius:var(--_container-shape-end-end);box-sizing:border-box;cursor:pointer;display:inline-flex;gap:8px;min-height:var(--_container-height);outline:none;padding-block:calc((var(--_container-height) - max(var(--_label-text-line-height), var(--_icon-size)))/2);padding-inline-start:var(--_leading-space);padding-inline-end:var(--_trailing-space);place-content:center;place-items:center;position:relative;font-family:var(--_label-text-font);font-size:var(--_label-text-size);line-height:var(--_label-text-line-height);font-weight:var(--_label-text-weight);text-overflow:ellipsis;text-wrap:nowrap;-webkit-user-select:none;user-select:none;-webkit-tap-highlight-color:transparent;vertical-align:top;--md-ripple-hover-color:var(--_hover-state-layer-color);--md-ripple-pressed-color:var(--_pressed-state-layer-color);--md-ripple-hover-opacity:var(--_hover-state-layer-opacity);--md-ripple-pressed-opacity:var(--_pressed-state-layer-opacity)}md-focus-ring{--md-focus-ring-shape-start-start:var(--_container-shape-start-start);--md-focus-ring-shape-start-end:var(--_container-shape-start-end);--md-focus-ring-shape-end-end:var(--_container-shape-end-end);--md-focus-ring-shape-end-start:var(--_container-shape-end-start)}:host(:is([disabled],[soft-disabled])){cursor:default;pointer-events:none}.button{border-radius:inherit;cursor:inherit;display:inline-flex;align-items:center;justify-content:center;border:none;outline:none;-webkit-appearance:none;vertical-align:middle;background:transparent;text-decoration:none;min-width:calc(64px - var(--_leading-space) - var(--_trailing-space));width:100%;z-index:0;height:100%;font:inherit;color:var(--_label-text-color);padding:0;gap:inherit;text-transform:inherit}.button::-moz-focus-inner{padding:0;border:0}:host(:hover) .button{color:var(--_hover-label-text-color)}:host(:focus-within) .button{color:var(--_focus-label-text-color)}:host(:active) .button{color:var(--_pressed-label-text-color)}.background{background:var(--_container-color);border-radius:inherit;inset:0;position:absolute}.label{overflow:hidden}.label ::slotted(*),:is(.button,.label,.label slot){text-overflow:inherit}:host(:is([disabled],[soft-disabled])) .label{color:var(--_disabled-label-text-color);opacity:var(--_disabled-label-text-opacity)}:host(:is([disabled],[soft-disabled])) .background{background:var(--_disabled-container-color);opacity:var(--_disabled-container-opacity)}@media (forced-colors:active){.background{border:1px solid CanvasText}:host(:is([disabled],[soft-disabled])){--_disabled-icon-color:GrayText;--_disabled-icon-opacity:1;--_disabled-container-opacity:1;--_disabled-label-text-color:GrayText;--_disabled-label-text-opacity:1}}:host([has-icon]:not([trailing-icon])){padding-inline-start:var(--_with-leading-icon-leading-space);padding-inline-end:var(--_with-leading-icon-trailing-space)}:host([has-icon][trailing-icon]){padding-inline-start:var(--_with-trailing-icon-leading-space);padding-inline-end:var(--_with-trailing-icon-trailing-space)}::slotted([slot=icon]){display:inline-flex;position:relative;writing-mode:horizontal-tb;fill:currentColor;flex-shrink:0;color:var(--_icon-color);font-size:var(--_icon-size);inline-size:var(--_icon-size);block-size:var(--_icon-size)}:host(:hover) ::slotted([slot=icon]){color:var(--_hover-icon-color)}:host(:focus-within) ::slotted([slot=icon]){color:var(--_focus-icon-color)}:host(:active) ::slotted([slot=icon]){color:var(--_pressed-icon-color)}:host(:is([disabled],[soft-disabled])) ::slotted([slot=icon]){color:var(--_disabled-icon-color);opacity:var(--_disabled-icon-opacity)}.touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}:host([touch-target=wrapper]){margin:max(0px,(48px - var(--_container-height))/2) 0}:host([touch-target=none]) .touch{display:none}\n"]),
nD=YA(mD);var oD=function(){return hD.apply(this,arguments)||this};_.y(oD,hD);oD.U=hD.U;oD.formAssociated=hD.formAssociated;oD.Gb=[nD,lD,jD];T([PA("md-filled-button")],oD);var pD=function(){return $C.apply(this,arguments)||this};_.y(pD,$C);pD.U=$C.U;pD.formAssociated=$C.formAssociated;var qD=_.L([":host{--_container-height:var(--md-text-button-container-height,40px);--_disabled-label-text-color:var(--md-text-button-disabled-label-text-color,var(--md-sys-color-on-surface,#1d1b20));--_disabled-label-text-opacity:var(--md-text-button-disabled-label-text-opacity,0.38);--_focus-label-text-color:var(--md-text-button-focus-label-text-color,var(--md-sys-color-primary,#6750a4));--_hover-label-text-color:var(--md-text-button-hover-label-text-color,var(--md-sys-color-primary,#6750a4));--_hover-state-layer-color:var(--md-text-button-hover-state-layer-color,var(--md-sys-color-primary,#6750a4));--_hover-state-layer-opacity:var(--md-text-button-hover-state-layer-opacity,0.08);--_label-text-color:var(--md-text-button-label-text-color,var(--md-sys-color-primary,#6750a4));--_label-text-font:var(--md-text-button-label-text-font,var(--md-sys-typescale-label-large-font,var(--md-ref-typeface-plain,Roboto)));--_label-text-line-height:var(--md-text-button-label-text-line-height,var(--md-sys-typescale-label-large-line-height,1.25rem));--_label-text-size:var(--md-text-button-label-text-size,var(--md-sys-typescale-label-large-size,0.875rem));--_label-text-weight:var(--md-text-button-label-text-weight,var(--md-sys-typescale-label-large-weight,var(--md-ref-typeface-weight-medium,500)));--_pressed-label-text-color:var(--md-text-button-pressed-label-text-color,var(--md-sys-color-primary,#6750a4));--_pressed-state-layer-color:var(--md-text-button-pressed-state-layer-color,var(--md-sys-color-primary,#6750a4));--_pressed-state-layer-opacity:var(--md-text-button-pressed-state-layer-opacity,0.12);--_disabled-icon-color:var(--md-text-button-disabled-icon-color,var(--md-sys-color-on-surface,#1d1b20));--_disabled-icon-opacity:var(--md-text-button-disabled-icon-opacity,0.38);--_focus-icon-color:var(--md-text-button-focus-icon-color,var(--md-sys-color-primary,#6750a4));--_hover-icon-color:var(--md-text-button-hover-icon-color,var(--md-sys-color-primary,#6750a4));--_icon-color:var(--md-text-button-icon-color,var(--md-sys-color-primary,#6750a4));--_icon-size:var(--md-text-button-icon-size,18px);--_pressed-icon-color:var(--md-text-button-pressed-icon-color,var(--md-sys-color-primary,#6750a4));--_container-shape-start-start:var(--md-text-button-container-shape-start-start,var(--md-text-button-container-shape,var(--md-sys-shape-corner-full,9999px)));--_container-shape-start-end:var(--md-text-button-container-shape-start-end,var(--md-text-button-container-shape,var(--md-sys-shape-corner-full,9999px)));--_container-shape-end-end:var(--md-text-button-container-shape-end-end,var(--md-text-button-container-shape,var(--md-sys-shape-corner-full,9999px)));--_container-shape-end-start:var(--md-text-button-container-shape-end-start,var(--md-text-button-container-shape,var(--md-sys-shape-corner-full,9999px)));--_leading-space:var(--md-text-button-leading-space,12px);--_trailing-space:var(--md-text-button-trailing-space,12px);--_with-leading-icon-leading-space:var(--md-text-button-with-leading-icon-leading-space,12px);--_with-leading-icon-trailing-space:var(--md-text-button-with-leading-icon-trailing-space,16px);--_with-trailing-icon-leading-space:var(--md-text-button-with-trailing-icon-leading-space,16px);--_with-trailing-icon-trailing-space:var(--md-text-button-with-trailing-icon-trailing-space,12px);--_container-color:none;--_disabled-container-color:none;--_disabled-container-opacity:0}\n"]),
rD=YA(qD);var sD=function(){return pD.apply(this,arguments)||this};_.y(sD,pD);sD.U=pD.U;sD.formAssociated=pD.formAssociated;sD.Gb=[nD,rD];T([PA("md-text-button")],sD);var tD=_.L([':host{border-start-start-radius:var(--md-dialog-container-shape-start-start,var(--md-dialog-container-shape,var(--md-sys-shape-corner-extra-large,28px)));border-start-end-radius:var(--md-dialog-container-shape-start-end,var(--md-dialog-container-shape,var(--md-sys-shape-corner-extra-large,28px)));border-end-end-radius:var(--md-dialog-container-shape-end-end,var(--md-dialog-container-shape,var(--md-sys-shape-corner-extra-large,28px)));border-end-start-radius:var(--md-dialog-container-shape-end-start,var(--md-dialog-container-shape,var(--md-sys-shape-corner-extra-large,28px)));display:contents;margin:auto;max-height:min(560px,100% - 48px);max-width:min(560px,100% - 48px);min-height:140px;min-width:280px;position:fixed;height:-moz-fit-content;height:fit-content;width:-moz-fit-content;width:fit-content}dialog{background:transparent;border:none;border-radius:inherit;flex-direction:column;height:inherit;margin:inherit;max-height:inherit;max-width:inherit;min-height:inherit;min-width:inherit;outline:none;overflow:visible;padding:0;width:inherit}dialog[open]{display:flex}::backdrop{background:none}.scrim{background:var(--md-sys-color-scrim,#000);display:none;inset:0;opacity:32%;pointer-events:none;position:fixed;z-index:1}:host([open]) .scrim{display:flex}h2{all:unset;align-self:stretch}.headline{align-items:center;color:var(--md-dialog-headline-color,var(--md-sys-color-on-surface,#1d1b20));display:flex;flex-direction:column;font-family:var(--md-dialog-headline-font,var(--md-sys-typescale-headline-small-font,var(--md-ref-typeface-brand,Roboto)));font-size:var(--md-dialog-headline-size,var(--md-sys-typescale-headline-small-size,1.5rem));line-height:var(--md-dialog-headline-line-height,var(--md-sys-typescale-headline-small-line-height,2rem));font-weight:var(--md-dialog-headline-weight,var(--md-sys-typescale-headline-small-weight,var(--md-ref-typeface-weight-regular,400)));position:relative}slot[name=headline]::slotted(*){align-items:center;align-self:stretch;box-sizing:border-box;display:flex;gap:8px;padding:24px 24px 0}.icon{display:flex}slot[name=icon]::slotted(*){color:var(--md-dialog-icon-color,var(--md-sys-color-secondary,#625b71));fill:currentColor;font-size:var(--md-dialog-icon-size,24px);margin-top:24px;height:var(--md-dialog-icon-size,24px);width:var(--md-dialog-icon-size,24px)}.has-icon slot[name=headline]::slotted(*){justify-content:center;padding-top:16px}.scrollable slot[name=headline]::slotted(*){padding-bottom:16px}.scrollable.has-headline slot[name=content]::slotted(*){padding-top:8px}.container{border-radius:inherit;display:flex;flex-direction:column;flex-grow:1;overflow:hidden;position:relative;transform-origin:top}.container:before{background:var(--md-dialog-container-color,var(--md-sys-color-surface-container-high,#ece6f0));border-radius:inherit;content:"";inset:0;position:absolute}.scroller{display:flex;flex:1;flex-direction:column;overflow:hidden;z-index:1}.scrollable .scroller{overflow-y:scroll}.content{color:var(--md-dialog-supporting-text-color,var(--md-sys-color-on-surface-variant,#49454f));font-family:var(--md-dialog-supporting-text-font,var(--md-sys-typescale-body-medium-font,var(--md-ref-typeface-plain,Roboto)));font-size:var(--md-dialog-supporting-text-size,var(--md-sys-typescale-body-medium-size,.875rem));line-height:var(--md-dialog-supporting-text-line-height,var(--md-sys-typescale-body-medium-line-height,1.25rem));flex:1;font-weight:var(--md-dialog-supporting-text-weight,var(--md-sys-typescale-body-medium-weight,var(--md-ref-typeface-weight-regular,400)));height:min-content;position:relative}slot[name=content]::slotted(*){box-sizing:border-box;padding:24px}.anchor{position:absolute}.top.anchor{top:0}.bottom.anchor{bottom:0}.actions{position:relative}slot[name=actions]::slotted(*){box-sizing:border-box;display:flex;gap:8px;justify-content:flex-end;padding:16px 24px 24px}.has-actions slot[name=content]::slotted(*){padding-bottom:8px}md-divider{display:none;position:absolute}.has-actions.show-bottom-divider .actions md-divider,.has-headline.show-top-divider .headline md-divider{display:flex}.headline md-divider{bottom:0}.actions md-divider{top:0}@media (forced-colors:active){dialog{outline:2px solid WindowText}}\n']),
uD=YA(tD);var vD={qb:[[[{transform:"translateY(-50px)"},{transform:"translateY(0)"}],{duration:500,easing:"cubic-bezier(.3,0,0,1)"}]],je:[[[{opacity:0},{opacity:.32}],{duration:500,easing:"linear"}]],container:[[[{opacity:0},{opacity:1}],{duration:50,easing:"linear",pseudoElement:"::before"}],[[{height:"35%"},{height:"100%"}],{duration:500,easing:"cubic-bezier(.3,0,0,1)",pseudoElement:"::before"}]],Pd:[[[{opacity:0},{opacity:0,offset:.2},{opacity:1}],{duration:250,easing:"linear",fill:"forwards"}]],content:[[[{opacity:0},
{opacity:0,offset:.2},{opacity:1}],{duration:250,easing:"linear",fill:"forwards"}]],actions:[[[{opacity:0},{opacity:0,offset:.5},{opacity:1}],{duration:300,easing:"linear",fill:"forwards"}]]},wD={qb:[[[{transform:"translateY(0)"},{transform:"translateY(-50px)"}],{duration:150,easing:"cubic-bezier(.3,0,.8,.15)"}]],je:[[[{opacity:.32},{opacity:0}],{duration:150,easing:"linear"}]],container:[[[{height:"100%"},{height:"35%"}],{duration:150,easing:"cubic-bezier(.3,0,.8,.15)",pseudoElement:"::before"}],
[[{opacity:"1"},{opacity:"0"}],{delay:100,duration:50,easing:"linear",pseudoElement:"::before"}]],Pd:[[[{opacity:1},{opacity:0}],{duration:100,easing:"linear",fill:"forwards"}]],content:[[[{opacity:1},{opacity:0}],{duration:100,easing:"linear",fill:"forwards"}]],actions:[[[{opacity:1},{opacity:0}],{duration:100,easing:"linear",fill:"forwards"}]]};var xD=_.L([':host{box-sizing:border-box;color:var(--md-divider-color,var(--md-sys-color-outline-variant,#cac4d0));display:flex;height:var(--md-divider-thickness,1px);width:100%}:host([inset-start]),:host([inset]){padding-inline-start:16px}:host([inset-end]),:host([inset]){padding-inline-end:16px}:host:before{background:currentColor;content:"";height:100%;width:100%}@media (forced-colors:active){:host:before{background:CanvasText}}\n']),yD=YA(xD);var zD=function(){var a=Y.apply(this,arguments)||this;a.inset=!1;a.insetStart=!1;a.insetEnd=!1;return a};_.y(zD,Y);zD.U=Y.U;zD.fa=Y.fa;zD.da=Y.da;zD.ma=Y.ma;zD.ea=Y.ea;zD.ja=Y.ja;zD.na=Y.na;zD.ba=Y.ba;zD.addInitializer=Y.addInitializer;zD.ia=Y.ia;T([jA({type:Boolean,Ha:!0}),V("design:type",Object)],zD.prototype,"inset",void 0);T([jA({type:Boolean,Ha:!0,Sa:"inset-start"}),V("design:type",Object)],zD.prototype,"insetStart",void 0);
T([jA({type:Boolean,Ha:!0,Sa:"inset-end"}),V("design:type",Object)],zD.prototype,"insetEnd",void 0);var AD=function(){return zD.apply(this,arguments)||this};_.y(AD,zD);AD.U=zD.U;AD.fa=zD.fa;AD.da=zD.da;AD.ma=zD.ma;AD.ea=zD.ea;AD.ja=zD.ja;AD.na=zD.na;AD.ba=zD.ba;AD.addInitializer=zD.addInitializer;AD.ia=zD.ia;AD.Gb=[yD];T([PA("md-divider")],AD);var BD=_.L(['\n      <div\n        class="focus-trap"\n        tabindex="0"\n        aria-hidden="true"\n        @focus=',"></div>\n    "]),CD=_.L('\n      <div class="scrim"></div>\n      <dialog\n        class=;\n        aria-label=;\n        aria-labelledby=;\n        role=;\n        @cancel=;\n        @click=;\n        @close=;\n        @keydown=;\n        .returnValue=;>\n        ;\n        <div class="container" @click=;>\n          <div class="headline">\n            <div class="icon" aria-hidden="true">\n              <slot name="icon" @slotchange=;></slot>\n            </div>\n            <h2 id="headline" aria-hidden=;>\n              <slot\n                name="headline"\n                @slotchange=;></slot>\n            </h2>\n            <md-divider></md-divider>\n          </div>\n          <div class="scroller">\n            <div class="content">\n              <div class="top anchor"></div>\n              <slot name="content"></slot>\n              <div class="bottom anchor"></div>\n            </div>\n          </div>\n          <div class="actions">\n            <md-divider></md-divider>\n            <slot name="actions" @slotchange=;></slot>\n          </div>\n        </div>\n        ;\n      </dialog>\n    '.split(";")),
DD=CA(Y),FD=function(){var a=DD.call(this)||this;a.quick=!1;a.returnValue="";a.noFocusTrap=!1;a.Oi=function(){return vD};a.Ni=function(){return wD};a.qc=!1;a.Vb=!1;a.Og=ED(a);a.Td=!1;a.Sd=!1;a.mf=!1;a.Rc=!1;a.af=!1;a.hasIcon=!1;a.Oc=!1;a.fd=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT);a.addEventListener("submit",a.Aj);return a};_.y(FD,DD);_.l=FD.prototype;
_.l.show=function(){var a=this,b,c,d;return _.Fi(function(e){switch(e.g){case 1:return a.Vb=!0,_.M(e,a.Og,2);case 2:return _.M(e,a.Eh,3);case 3:b=a.qb;if(b.open||!a.Vb)return a.Vb=!1,e.return();if(c=!a.dispatchEvent(new Event("open",{cancelable:!0})))return a.open=!1,a.Vb=!1,e.return();b.showModal();a.open=!0;a.zf&&(a.zf.scrollTop=0);(d=a.querySelector("[autofocus]"))==null||d.focus();return _.M(e,GD(a,a.Oi()),4);case 4:a.dispatchEvent(new Event("opened")),a.Vb=!1,e.g=0}})};
_.l.close=function(a){a=a===void 0?this.returnValue:a;var b=this,c,d,e;return _.Fi(function(f){if(f.g==1)return b.Vb=!1,b.isConnected?_.M(f,b.Eh,2):(b.open=!1,f.return());if(f.g!=3){c=b.qb;if(!c.open||b.Vb)return b.open=!1,f.return();d=b.returnValue;b.returnValue=a;return(e=!b.dispatchEvent(new Event("close",{cancelable:!0})))?(b.returnValue=d,f.return()):_.M(f,GD(b,b.Ni()),3)}c.close(a);b.open=!1;b.dispatchEvent(new Event("closed"));f.g=0})};
_.l.connectedCallback=function(){DD.prototype.connectedCallback.call(this);this.Jj()};_.l.disconnectedCallback=function(){DD.prototype.disconnectedCallback.call(this);this.Og=ED(this)};
_.l.ha=function(){var a=this.open&&!(this.Td&&this.Sd),b=this.open&&!this.noFocusTrap,c=eC(BD,this.kj),d=this.ariaLabel;return eC(CD,LC({"has-headline":this.Rc,"has-actions":this.af,"has-icon":this.hasIcon,scrollable:a,"show-top-divider":a&&!this.Td,"show-bottom-divider":a&&!this.Sd}),d||X,this.Rc?"headline":X,this.type==="alert"?"alertdialog":X,this.ej,this.ij,this.fj,this.sj,this.returnValue||X,b?c:X,this.gj,this.oj,!this.Rc||X,this.nj,this.Yi,b?c:X)};
_.l.rg=function(){var a=this;this.Ng=new IntersectionObserver(function(b){b=_.z(b);for(var c=b.next();!c.done;c=b.next()){var d=a,e=c.value;c=e.target;e=e.isIntersecting;c===d.Bh&&(d.Td=e);c===d.eg&&(d.Sd=e)}},{root:this.zf});this.Ng.observe(this.Bh);this.Ng.observe(this.eg)};_.l.ij=function(){this.mf?this.mf=!1:this.dispatchEvent(new Event("cancel",{cancelable:!0}))&&this.close()};_.l.gj=function(){this.mf=!0};
_.l.Aj=function(a){var b=a.submitter;if(a.target.getAttribute("method")==="dialog"&&b){var c;this.close((c=b.getAttribute("value"))!=null?c:this.returnValue)}};_.l.ej=function(a){if(a.target===this.qb){this.Oc=!1;!a.bubbles||this.shadowRoot&&!a.composed||a.stopPropagation();var b=Reflect.construct(a.constructor,[a.type,a]);(b=this.dispatchEvent(b))||a.preventDefault();b=!b;a.preventDefault();b||this.close()}};
_.l.fj=function(){if(this.Oc){this.Oc=!1;var a;(a=this.qb)==null||a.dispatchEvent(new Event("cancel",{cancelable:!0}))}};_.l.sj=function(a){var b=this;a.key==="Escape"&&(this.Oc=!0,setTimeout(function(){b.Oc=!1}))};
var GD=function(a,b){var c,d,e,f,g,h,k,m,n,p,q,r,u,A,C,K,U,fa,Lg,pj,qj,rj,Mg,Ng,cf,sj,Rn,Hv,Iv,Sn,Jv,Tn,Jj,ah,Kv;return _.Fi(function(Un){(c=a.gg)==null||c.abort();a.gg=new AbortController;if(a.quick)return Un.return();d=a;e=d.qb;f=d.je;g=d.container;h=d.Pd;k=d.content;m=d.actions;if(!(e&&f&&g&&h&&k&&m))return Un.return();n=b;p=n.container;q=n.qb;r=n.je;u=n.Pd;A=n.content;C=n.actions;rj=[[e,(K=q)!=null?K:[]],[f,(U=r)!=null?U:[]],[g,(fa=p)!=null?fa:[]],[h,(Lg=u)!=null?Lg:[]],[k,(pj=A)!=null?pj:[]],
[m,(qj=C)!=null?qj:[]]];Mg=[];Ng=_.z(rj);for(cf=Ng.next();!cf.done;cf=Ng.next())for(sj=cf.value,Rn=_.z(sj),Hv=Rn.next().value,Iv=Rn.next().value,Sn=Hv,Jv=Iv,Tn=_.z(Jv),Jj=Tn.next(),ah={};!Jj.done;ah={Ge:void 0},Jj=Tn.next())Kv=Jj.value,ah.Ge=Sn.animate.apply(Sn,_.Hb(Kv)),a.gg.signal.addEventListener("abort",function(Vn){return function(){Vn.Ge.cancel()}}(ah)),Mg.push(ah.Ge);return _.M(Un,Promise.all(Mg.map(function(Vn){return Vn.finished.catch(function(){})})),0)})};
FD.prototype.nj=function(a){this.Rc=a.target.assignedElements().length>0};FD.prototype.Yi=function(a){this.af=a.target.assignedElements().length>0};FD.prototype.oj=function(a){this.hasIcon=a.target.assignedElements().length>0};var ED=function(a){return new Promise(function(b){a.Jj=b})};
FD.prototype.kj=function(a){var b;if(this.fd){var c=b=null;for(this.fd.currentNode=this.fd.root;this.fd.nextNode();){var d=this.fd.currentNode;var e=void 0,f=void 0;var g=d.matches(':is(button,input,select,textarea,object,:is(a,area)[href],[tabindex],[contenteditable=true]):not(:disabled,[disabled]):not([tabindex^="-"])')?!0:d.localName.includes("-")&&d.matches(":not(:disabled,[disabled])")?(e=(f=d.shadowRoot)==null?void 0:f.delegatesFocus)!=null?e:!1:!1;g&&(b||(b=d),c=d)}b=[b,c]}else b=[null,null];
c=_.z(b);b=c.next().value;c=c.next().value;if(b&&c){var h=a.target===this.Li;d=!h;g=a.relatedTarget===b;a=a.relatedTarget===c;e=!g&&!a;d&&a||h&&e?b.focus():(h&&g||d&&e)&&c.focus()}else(h=this.qb)==null||h.focus()};_.yb.Object.defineProperties(FD.prototype,{open:{configurable:!0,enumerable:!0,get:function(){return this.qc},set:function(a){a!==this.qc&&((this.qc=a)?(this.setAttribute("open",""),this.show()):(this.removeAttribute("open"),this.close()))}}});
T([jA({type:Boolean}),V("design:type",Boolean),V("design:paramtypes",[Boolean])],FD.prototype,"open",null);T([jA({type:Boolean}),V("design:type",Object)],FD.prototype,"quick",void 0);T([jA({Sa:!1}),V("design:type",Object)],FD.prototype,"returnValue",void 0);T([jA(),V("design:type",String)],FD.prototype,"type",void 0);T([jA({type:Boolean,Sa:"no-focus-trap"}),V("design:type",Object)],FD.prototype,"noFocusTrap",void 0);T([lA("dialog"),V("design:type",Object)],FD.prototype,"qb",void 0);
T([lA(".scrim"),V("design:type",Object)],FD.prototype,"je",void 0);T([lA(".container"),V("design:type",Object)],FD.prototype,"container",void 0);T([lA(".headline"),V("design:type",Object)],FD.prototype,"Pd",void 0);T([lA(".content"),V("design:type",Object)],FD.prototype,"content",void 0);T([lA(".actions"),V("design:type",Object)],FD.prototype,"actions",void 0);T([mA(),V("design:type",Object)],FD.prototype,"Td",void 0);T([mA(),V("design:type",Object)],FD.prototype,"Sd",void 0);
T([lA(".scroller"),V("design:type",Object)],FD.prototype,"zf",void 0);T([lA(".top.anchor"),V("design:type",Object)],FD.prototype,"Bh",void 0);T([lA(".bottom.anchor"),V("design:type",Object)],FD.prototype,"eg",void 0);T([lA(".focus-trap"),V("design:type",Object)],FD.prototype,"Li",void 0);T([mA(),V("design:type",Object)],FD.prototype,"Rc",void 0);T([mA(),V("design:type",Object)],FD.prototype,"af",void 0);T([mA(),V("design:type",Object)],FD.prototype,"hasIcon",void 0);var HD=function(){return FD.apply(this,arguments)||this};_.y(HD,FD);HD.Gb=[uD];T([PA("md-dialog")],HD);var ID=_.L([":host{font-size:var(--md-icon-size,24px);width:var(--md-icon-size,24px);height:var(--md-icon-size,24px);color:inherit;font-variation-settings:inherit;font-weight:400;font-family:var(--md-icon-font,Material Symbols Outlined);display:inline-flex;font-style:normal;place-items:center;place-content:center;line-height:1;overflow:hidden;letter-spacing:normal;text-transform:none;-webkit-user-select:none;user-select:none;white-space:nowrap;word-wrap:normal;flex-shrink:0;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale}::slotted(svg){fill:currentColor}::slotted(*){height:100%;width:100%}\n"]),
JD=YA(ID);var KD=_.L(["<slot></slot>"]),LD=function(){return Y.apply(this,arguments)||this};_.y(LD,Y);LD.U=Y.U;LD.fa=Y.fa;LD.da=Y.da;LD.ma=Y.ma;LD.ea=Y.ea;LD.ja=Y.ja;LD.na=Y.na;LD.ba=Y.ba;LD.addInitializer=Y.addInitializer;LD.ia=Y.ia;LD.prototype.ha=function(){return eC(KD)};LD.prototype.connectedCallback=function(){Y.prototype.connectedCallback.call(this);this.getAttribute("aria-hidden")==="false"?this.removeAttribute("aria-hidden"):this.setAttribute("aria-hidden","true")};var MD=function(){return LD.apply(this,arguments)||this};_.y(MD,LD);MD.U=LD.U;MD.fa=LD.fa;MD.da=LD.da;MD.ma=LD.ma;MD.ea=LD.ea;MD.ja=LD.ja;MD.na=LD.na;MD.ba=LD.ba;MD.addInitializer=LD.addInitializer;MD.ia=LD.ia;MD.Gb=[JD];T([PA("md-icon")],MD);var OD=function(){this.g=ND};var PD,QD=((PD=document.currentScript)==null?void 0:PD.getAttribute("data-phishing-protection-enabled"))==="true",RD,SD=((RD=document.currentScript)==null?void 0:RD.getAttribute("data-forms-warning-enabled"))==="true",TD,ND=(TD=document.currentScript)==null?void 0:TD.getAttribute("data-source-url");
QD&&_.E(window,"load",function(){try{for(var a=_.z(document.body.getElementsByTagName("input")),b=a.next();!b.done;b=a.next())bA(b.value);a={};(new MutationObserver(cA)).observe(document.body,(a.childList=!0,a))}catch(c){console.error(c)}});
if(SD&&ND){var UD=new OD,VD=document.createElement("md-dialog");VD.setAttribute("type","alert");var WD=VD.appendChild,XD=document.createElement("div");XD.setAttribute("slot","headline");XD.textContent="Formulaire non accept\u00e9";WD.call(VD,XD);var YD=VD.appendChild,ZD=document.createElement("form");ZD.setAttribute("slot","content");ZD.setAttribute("id","KucnDc");ZD.setAttribute("method","dialog");ZD.textContent="Pour des raisons de s\u00e9curit\u00e9 lorsque vous utilisez Google\u00a0Traduction, n'envoyez pas d'informations dans ce type de formulaire.";
YD.call(VD,ZD);var $D=VD.appendChild,aE=document.createElement("div");aE.setAttribute("slot","actions");var bE=document.createElement("md-text-button");bE.textContent="OK";bE.setAttribute("form","KucnDc");bE.value="cancel";var cE=document.createElement("md-filled-button");cE.textContent="Acc\u00e9der \u00e0 l'URL d'origine";cE.setAttribute("form","KucnDc");cE.target="_blank";cE.href=UD.g;cE.value="submit";var dE=document.createElement("md-icon");dE.slot="icon";dE.textContent="open_in_new";dE.setAttribute("translate",
"no");cE.appendChild(dE);cE.hasIcon=!0;cE.trailingIcon=!0;aE.appendChild(bE);aE.appendChild(cE);$D.call(VD,aE);var eE=document.createElement("style");eE.textContent="\n    :root {\n      /* System tokens */\n      --md-sys-typescale-body-medium: system-ui 16px/24px;\n      --md-sys-typescale-headline-small: system-ui 24px/32px;\n\n      /* Component tokens */\n      --md-dialog-container-shape: 8px;\n      --md-dialog-container-color: #ffffff;\n      --md-filled-button-container-shape: 4px;\n      --md-filled-button-container-color: #1a73e8;\n      --md-text-button-container-shape: 4px;\n      --md-text-button-container-color: #1a73e8;\n      --md-text-button-label-text-color: #1a73e8;\n      --md-text-button-focus-label-text-color: #1a73e8;\n      --md-text-button-hover-label-text-color: #1a73e8;\n      --md-text-button-pressed-label-text-color: #1a73e8;\n    }";
window.addEventListener("DOMContentLoaded",function(){document.body.appendChild(eE);document.body.appendChild(VD);VD.open=!1});window.addEventListener("focusin",function(a){hA(a)&&(VD.open=!0)})};
_.na();
}catch(e){_._DumpException(e)}
}).call(this,this.default_tr);
// Google Inc.

.woosb-wrap {
    display: block;
    width: 100%;
    clear: both;
}

.woosb-wrap .woosb-text, .woosb-wrap .woosb-products, .woosb-bundles .woosb-text, .woosb-bundles .woosb-products {
    margin: 0 0 15px 0;
}

.woosb-wrap .woosb-text, .woosb-bundles .woosb-text {
    white-space: pre-line;
}

.woosb-wrap .woosb-summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.woosb-wrap .woosb-summary .woosb-total {
    flex-grow: 1;
}

.woosb-products {
    border-width: 1px 0 0 0;
    border-style: dotted;
    border-color: #e5e5e5;
}

.woosb-products .woosb-item-text {
    padding: 10px 0;
    margin: 0;
    border-bottom: 1px dotted #e5e5e5;
}

.woosb-item-text-type-h1 > h1, .woosb-item-text-type-h2 > h2, .woosb-item-text-type-h3 > h3, .woosb-item-text-type-h4 > h4, .woosb-item-text-type-h5 > h5, .woosb-item-text-type-h6 > h6, .woosb-item-text-type-p > p {
    margin: 0 !important;
}

.woosb-products .woosb-product {
    display: flex;
    align-items: center;
    padding: 10px 0;
    margin: 0;
    border-bottom: 1px dotted #e5e5e5;
}

.woosb-products .woosb-product.woosb-product-hidden {
    display: none;
}

.woosb-products[data-exclude-unpurchasable="yes"] .woosb-product.woosb-product-unpurchasable {
    display: none;
}

.woosb-products .woosb-product > div {
    padding: 0 5px;
}

.woosb-products .woosb-product .woosb-thumb {
    width: 50px;
    flex: 0 0 50px;
}

.woosb-products .woosb-product .woosb-thumb img {
    width: 100%;
    max-width: 100%;
    height: auto;
    display: block;
}

.woosb-products .woosb-product .woosb-quantity {
    width: 80px;
    flex: 0 0 80px;
}

.woosb-products .woosb-product .woosb-quantity.woosb-quantity-plus-minus {
    width: 130px;
    flex: 0 0 130px;
}

.woosb-products .woosb-product .woosb-quantity label {
    display: none;
}

.woosb-products .woosb-product .woosb-quantity input {
    width: 100%;
    min-width: 40px;
    text-align: center;
}

.woosb-products .woosb-product .woosb-price {
    width: 100px;
    flex: 0 0 100px;
    text-align: end;
}

.woosb-products .woosb-product .woosb-price * {
    font-size: inherit !important;
}

.woosb-products .woosb-product .woosb-price del {
    opacity: .5;
    text-decoration: line-through;
}

.woosb-products .woosb-product .woosb-price del span {
    text-decoration: line-through;
}

.woosb-products .woosb-product .woosb-price ins {
    font-weight: normal;
}

.woosb-products .woosb-product .woosb-price .amount {
    display: inline-block;
}

.woosb-products .woosb-product .woosb-title {
    flex-grow: 1;
    text-align: start;
}

.woosb-products .woosb-product .woosb-title .woosb-name {
    display: block;
    overflow: hidden;
}

.woosb-products .woosb-product .woosb-title .woosb-name a {
    text-decoration: none;
    box-shadow: none;
}

.woosb-products .woosb-product .woosb-title .woosb-price {
    width: auto;
    flex: 0 0 auto;
    text-align: inherit;
}

.woosb-products .woosb-product .woosb-title .woovr-variations-select .woovr-variation-image, .woosb-products .woosb-product .woosb-title .woovr-variations-select .woovr-variation-price {
    display: none !important;
}

.woosb-products .woosb-product .variations_form {
    margin: 0;
    padding: 0;
}

.woosb-products .woosb-product .woosb-title .stock {
    font-size: 14px;
    margin: 0;
    padding: 0;
}

.woosb-products .woosb-product .woosb-title .stock.in-stock {
    color: green;
}

.woosb-products .woosb-product .woosb-title .stock.available-on-backorder {
    color: orange;
}

.woosb-products .woosb-product .woosb-title .stock.out-of-stock {
    color: red;
}

.woosb-products .woosb-product .woosb-description, .woosb-products .woosb-product .woosb-variation-description {
    font-size: 14px;
}

.woosb-products .woosb-product .variations_form .variations {
    display: inline-block;
    margin: 0 -3px;
    padding: 0;
}

.woosb-products .woosb-product .variations_form .variations .variation {
    display: inline-block;
    border-width: 1px;
    border-style: solid;
    border-color: #e5e5e5;
    padding: 5px 10px;
    margin: 5px 3px 0 3px;
    max-width: 100%;
    border-radius: 2px;
    float: none;
    vertical-align: top;
}

.woosb-products .woosb-product .variations_form .variations .variation > div {
    display: block;
}

.woosb-products .woosb-product .variations_form .variations .reset {
    display: inline-block;
}

.woosb-products .woosb-product .variations_form .variations .variation:last-child {
    margin-bottom: 0;
}

.woosb-products .woosb-product .variations_form .variations .variation .label {
    font-size: 12px;
    line-height: 1;
}

.woosb-products .woosb-product .variations_form .variations .variation .value {
    line-height: 1;
}

.woosb-products .woosb-product .variations_form .variations .variation .value select {
    width: 100%;
    height: auto;
    line-height: 1;
    padding: 0 16px 0 0;
    margin: 0;
    background-color: transparent;
    border-radius: 0;
    border: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    outline: none;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAECAYAAACtBE5DAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyRpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoTWFjaW50b3NoKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpGNEQzN0Y2NTY0NjcxMUU3QjU5NUI5N0U0NjlDMTIzNiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDpGNEQzN0Y2NjY0NjcxMUU3QjU5NUI5N0U0NjlDMTIzNiI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOkY0RDM3RjYzNjQ2NzExRTdCNTk1Qjk3RTQ2OUMxMjM2IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOkY0RDM3RjY0NjQ2NzExRTdCNTk1Qjk3RTQ2OUMxMjM2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+pzgqpQAAADdJREFUeNpirKioWMDAwBDPgAoWMQGJLCC+hiR4HYgzQRLfgDgUSoNwCIhmgaq6BtXJCNMNEGAAbM0MK7OUU3AAAAAASUVORK5CYII=');
    background-repeat: no-repeat;
    background-position: 100% 50%;
    background-size: auto;
}

.woosb-products .woosb-product .variations_form .variations a.reset_variations {
    margin: 0;
    font-size: 12px;
}

.woosb-item-child .product-price .amount, .woosb-item-child .product-price .tax_label,
.woosb-item-child .product-subtotal .amount, .woosb-item-child .product-subtotal .tax_label,
.woosb-item-child .product-total .amount, .woosb-item-child .product-total .tax_label {
    opacity: .3;
}

.woosb-alert {
    padding: 6px 10px 6px 14px;
    border-left: 4px solid #222;
    background-color: #eeeeee;
    font-size: 14px;
}

body.woocommerce-no-js .woosb-wrap + form.cart {
    display: none;
}

.woosb-disabled {
    opacity: .5 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}


.woosb-quantity-input {
    display: flex;
    align-items: center;
}

.woosb-quantity-input .quantity {
    flex-grow: 1;
    height: auto;
    margin: 0;
}

.woosb-quantity-plus-minus .plus, .woosb-quantity-plus-minus .minus, .woosb-quantity-plus-minus .quantity-minus, .woosb-quantity-plus-minus .quantity-plus, .woosb-quantity-plus-minus .plus-btn, .woosb-quantity-plus-minus .minus-btn {
    display: none !important;
}

.woosb-quantity-input-plus, .woosb-quantity-input-minus {
    width: 40px;
    height: 40px;
    flex: 0 0 40px;
    line-height: 38px;
    text-align: center;
    cursor: pointer;
    background-color: #fefefe;
    border: 1px solid #e5e5e5;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.woosb-quantity-input .woosb-qty {
    width: 100%;
    min-width: 40px;
    height: 40px;
    line-height: 38px;
    margin: 0;
    padding: 0 5px;
    box-shadow: none;
    border-width: 1px 0;
    border-style: solid;
    border-color: #e5e5e5;
    border-radius: 0;
    background-color: #fefefe;
    -moz-appearance: textfield;
}

.woosb-quantity-input .woosb-qty:focus {
    outline: none;
    outline-color: transparent;
}

.woosb-quantity-input-plus:hover, .woosb-quantity-input-minus:hover {
    background-color: #f0f0f0;
}

.woosb-quantity-input input::-webkit-outer-spin-button, .woosb-quantity-input input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Grid */

.woosb-products[class*="woosb-products-layout-grid"] {
    display: flex;
    flex-wrap: wrap;
    margin-left: -10px;
    margin-right: -10px;
    border: none;
}

.woosb-products[class*="woosb-products-layout-grid"] .woosb-item-text {
    width: 100%;
    margin: 10px;
    padding: 10px 0;
}

.woosb-products[class*="woosb-products-layout-grid"] .woosb-product {
    width: calc(50% - 20px);
    margin: 10px;
    padding: 10px;
    border: 1px solid #dddddd;
    font-size: 14px;
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column;
}

.woosb-products[class*="woosb-products-layout-grid"] .woosb-product:hover {
    border-color: green;
}

.woosb-products[class*="woosb-products-layout-grid"] .woosb-product .woosb-name {
    font-weight: 700;
}

.woosb-products.woosb-products-layout-grid-1 .woosb-product {
    width: calc(100% - 20px);
}

.woosb-products.woosb-products-layout-grid-3 .woosb-product {
    width: calc(100% / 3 - 20px);
}

.woosb-products.woosb-products-layout-grid-4 .woosb-product {
    width: calc(25% - 20px);
}

.woosb-products[class*="woosb-products-layout-grid"] .woosb-product > div {
    width: 100% !important;
    flex: 0 0 auto !important;
    text-align: center;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    margin-bottom: 5px;
}

.woosb-products[class*="woosb-products-layout-grid"] .woosb-product > div:last-child {
    margin-bottom: 0;
}

.woosb-products[class*="woosb-products-layout-grid"] .woosb-product select {
    text-align: center;
}

.woosb-products[class*="woosb-products-layout-grid"] .woosb-product .variations_form .variations .reset {
    display: none;
}

@media only screen and (max-width: 767px) {
    .woosb-products[class*="woosb-products-layout-grid"] .woosb-product {
        width: calc(50% - 20px);
    }
}

@media only screen and (max-width: 479px) {
    .woosb-products[class*="woosb-products-layout-grid"] .woosb-product {
        width: calc(100% - 20px);
    }
}

/* RTL */

body.rtl .woosb-alert {
    padding: 6px 14px 6px 10px;
    border-right: 4px solid #222;
    border-left: none;
}
#!/bin/bash
# Script bash pour exécuter les tests SEO AI Agents

echo "🧪 Tests SEO AI Agents"
echo "======================"

# Fonction d'aide
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --unit          Tests unitaires seulement"
    echo "  --integration   Tests d'intégration seulement"
    echo "  --coverage      Avec rapport de couverture"
    echo "  --html          Rapport HTML"
    echo "  --file FILE     Fichier de test spécifique"
    echo "  --help          Afficher cette aide"
    echo ""
    echo "Exemples:"
    echo "  $0                              # Tous les tests"
    echo "  $0 --unit                       # Tests unitaires"
    echo "  $0 --coverage                   # Avec couverture"
    echo "  $0 --file test_web_scrape.py    # Fichier spécifique"
}

# Variables
PYTEST_CMD="python -m pytest"
TEST_DIR="tests/"
COVERAGE=""
HTML=""
MARKERS=""
SPECIFIC_FILE=""

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --unit)
            MARKERS="-m unit"
            shift
            ;;
        --integration)
            MARKERS="-m integration"
            shift
            ;;
        --coverage)
            COVERAGE="--cov=. --cov-report=term-missing"
            shift
            ;;
        --html)
            HTML="--html=reports/test_report.html --self-contained-html"
            mkdir -p reports
            shift
            ;;
        --file)
            SPECIFIC_FILE="tests/$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
done

# Construction de la commande
if [[ -n "$SPECIFIC_FILE" ]]; then
    CMD="$PYTEST_CMD $COVERAGE $HTML $MARKERS $SPECIFIC_FILE"
else
    CMD="$PYTEST_CMD $COVERAGE $HTML $MARKERS $TEST_DIR"
fi

# Exécution
echo "🚀 Commande: $CMD"
echo ""

eval $CMD
EXIT_CODE=$?

echo ""
if [[ $EXIT_CODE -eq 0 ]]; then
    echo "✅ Tous les tests sont passés avec succès!"
else
    echo "❌ Certains tests ont échoué (code de sortie: $EXIT_CODE)"
fi

exit $EXIT_CODE
